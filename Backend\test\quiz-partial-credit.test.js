/**
 * Test file for the Dynamic Quiz System with Partial Credit
 * This file tests the new partial credit scoring functionality
 */

const { calculateQuestionScore, calculateQuizScore, validateQuizConfiguration } = require('../utils/quizScoring');

// Test data
const sampleQuestions = {
  singleChoice: {
    question: "What is 2 + 2?",
    options: ["3", "4", "5", "6"],
    questionType: "single-choice",
    points: 1,
    answerOptions: [
      { optionIndex: 0, isCorrect: false, creditPercentage: 0 },
      { optionIndex: 1, isCorrect: true, creditPercentage: 100 },
      { optionIndex: 2, isCorrect: false, creditPercentage: 0 },
      { optionIndex: 3, isCorrect: false, creditPercentage: 0 }
    ]
  },

  multipleChoice: {
    question: "Which of the following are programming languages?",
    options: ["JavaScript", "HTML", "Python", "CSS"],
    questionType: "multiple-choice",
    points: 2,
    answerOptions: [
      { optionIndex: 0, isCorrect: true, creditPercentage: 50 },
      { optionIndex: 1, isCorrect: false, creditPercentage: 0 },
      { optionIndex: 2, isCorrect: true, creditPercentage: 50 },
      { optionIndex: 3, isCorrect: false, creditPercentage: 0 }
    ]
  },

  partialCredit: {
    question: "Which statements about JavaScript are correct?",
    options: [
      "JavaScript is interpreted",
      "JavaScript is compiled",
      "JavaScript runs in browsers",
      "JavaScript cannot run on servers"
    ],
    questionType: "partial-credit",
    points: 3,
    answerOptions: [
      { optionIndex: 0, isCorrect: true, creditPercentage: 40 },
      { optionIndex: 1, isCorrect: false, creditPercentage: 0 },
      { optionIndex: 2, isCorrect: true, creditPercentage: 40 },
      { optionIndex: 3, isCorrect: false, creditPercentage: 0 }
    ]
  }
};

const sampleQuiz = {
  title: "Sample Quiz",
  passingScore: 60,
  certificateEligible: true,
  minimumScoreForCertificate: 70,
  maxAttempts: 3,
  questions: [
    sampleQuestions.singleChoice,
    sampleQuestions.multipleChoice,
    sampleQuestions.partialCredit
  ]
};

// Test functions
function testSingleChoiceScoring() {
  console.log("Testing Single Choice Scoring...");

  // Correct answer
  let result = calculateQuestionScore(sampleQuestions.singleChoice, ["1"]);
  console.log("Correct answer:", result);
  console.assert(result.isCorrect === true, "Should be correct");
  console.assert(result.pointsEarned === 1, "Should earn full points");
  console.assert(result.creditPercentage === 100, "Should get 100% credit");

  // Incorrect answer
  result = calculateQuestionScore(sampleQuestions.singleChoice, ["0"]);
  console.log("Incorrect answer:", result);
  console.assert(result.isCorrect === false, "Should be incorrect");
  console.assert(result.pointsEarned === 0, "Should earn no points");
  console.assert(result.creditPercentage === 0, "Should get 0% credit");

  console.log("✓ Single Choice Scoring tests passed\n");
}

function testMultipleChoiceScoring() {
  console.log("Testing Multiple Choice Scoring...");

  // All correct answers
  let result = calculateQuestionScore(sampleQuestions.multipleChoice, ["0", "2"]);
  console.log("All correct:", result);
  console.assert(result.isCorrect === true, "Should be correct");
  console.assert(result.pointsEarned === 2, "Should earn full points");
  console.assert(result.creditPercentage === 100, "Should get 100% credit");

  // Partial correct (one correct, one incorrect)
  result = calculateQuestionScore(sampleQuestions.multipleChoice, ["0", "1"]);
  console.log("Partial correct:", result);
  console.assert(result.isCorrect === false, "Should be incorrect");
  console.assert(result.pointsEarned === 0.5, "Should earn partial points (0.5)");
  console.assert(result.creditPercentage === 25, "Should get 25% credit (50% - 25% penalty)");

  // Only one correct answer
  result = calculateQuestionScore(sampleQuestions.multipleChoice, ["0"]);
  console.log("One correct:", result);
  console.assert(result.isCorrect === false, "Should be incorrect");
  console.assert(result.pointsEarned === 1, "Should earn partial points");
  console.assert(result.creditPercentage === 50, "Should get 50% credit");

  console.log("✓ Multiple Choice Scoring tests passed\n");
}

function testPartialCreditScoring() {
  console.log("Testing Partial Credit Scoring...");

  // Select both correct answers
  let result = calculateQuestionScore(sampleQuestions.partialCredit, ["0", "2"]);
  console.log("Both correct:", result);
  console.assert(result.pointsEarned === 2.4, "Should earn 2.4 points (80% of 3)");
  console.assert(result.creditPercentage === 80, "Should get 80% credit");

  // Select one correct answer
  result = calculateQuestionScore(sampleQuestions.partialCredit, ["0"]);
  console.log("One correct:", result);
  console.assert(result.pointsEarned === 1.2, "Should earn 1.2 points (40% of 3)");
  console.assert(result.creditPercentage === 40, "Should get 40% credit");

  // Select incorrect answer
  result = calculateQuestionScore(sampleQuestions.partialCredit, ["1"]);
  console.log("Incorrect:", result);
  console.assert(result.pointsEarned === 0, "Should earn no points");
  console.assert(result.creditPercentage === 0, "Should get 0% credit");

  console.log("✓ Partial Credit Scoring tests passed\n");
}

function testQuizScoring() {
  console.log("Testing Complete Quiz Scoring...");

  // Perfect answers
  let answers = {
    "q1": ["1"],           // Single choice - correct
    "q2": ["0", "2"],      // Multiple choice - all correct
    "q3": ["0", "2"]       // Partial credit - both correct
  };

  // Mock quiz with question IDs
  const mockQuiz = {
    ...sampleQuiz,
    questions: sampleQuiz.questions.map((q, index) => ({
      ...q,
      _id: { toString: () => `q${index + 1}` }
    }))
  };

  let result = calculateQuizScore(mockQuiz, answers);
  console.log("Perfect score:", result);
  console.assert(result.totalScore === 5.4, "Should earn 5.4 total points");
  console.assert(result.percentage === 90, "Should get 90%");
  console.assert(result.passed === true, "Should pass");
  console.assert(result.certificateEligible === true, "Should be certificate eligible");

  // Partial answers
  answers = {
    "q1": ["0"],           // Single choice - incorrect
    "q2": ["0"],           // Multiple choice - partial
    "q3": ["0"]            // Partial credit - partial
  };

  result = calculateQuizScore(mockQuiz, answers);
  console.log("Partial score:", result);
  console.assert(result.totalScore === 2.2, "Should earn 2.2 total points");
  console.assert(result.percentage === 37, "Should get 37%");
  console.assert(result.passed === false, "Should not pass");
  console.assert(result.certificateEligible === false, "Should not be certificate eligible");

  console.log("✓ Quiz Scoring tests passed\n");
}

function testQuizValidation() {
  console.log("Testing Quiz Validation...");

  // Valid quiz
  let result = validateQuizConfiguration(sampleQuiz);
  console.log("Valid quiz:", result);
  console.assert(result.isValid === true, "Should be valid");
  console.assert(result.errors.length === 0, "Should have no errors");

  // Invalid quiz - no title
  const invalidQuiz = { ...sampleQuiz, title: "" };
  result = validateQuizConfiguration(invalidQuiz);
  console.log("Invalid quiz (no title):", result);
  console.assert(result.isValid === false, "Should be invalid");
  console.assert(result.errors.length > 0, "Should have errors");

  // Invalid quiz - no questions
  const noQuestionsQuiz = { ...sampleQuiz, questions: [] };
  result = validateQuizConfiguration(noQuestionsQuiz);
  console.log("Invalid quiz (no questions):", result);
  console.assert(result.isValid === false, "Should be invalid");
  console.assert(result.errors.includes("At least one question is required"), "Should have specific error");

  console.log("✓ Quiz Validation tests passed\n");
}

// Run all tests
function runAllTests() {
  console.log("=== Running Dynamic Quiz System Tests ===\n");

  try {
    testSingleChoiceScoring();
    testMultipleChoiceScoring();
    testPartialCreditScoring();
    testQuizScoring();
    testQuizValidation();

    console.log("🎉 All tests passed successfully!");
    console.log("\n=== Test Summary ===");
    console.log("✓ Single Choice Scoring");
    console.log("✓ Multiple Choice Scoring");
    console.log("✓ Partial Credit Scoring");
    console.log("✓ Complete Quiz Scoring");
    console.log("✓ Quiz Validation");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error(error.stack);
  }
}

// Export for use in other test files
module.exports = {
  runAllTests,
  testSingleChoiceScoring,
  testMultipleChoiceScoring,
  testPartialCreditScoring,
  testQuizScoring,
  testQuizValidation,
  sampleQuestions,
  sampleQuiz
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}
