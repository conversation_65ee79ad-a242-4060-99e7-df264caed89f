import React, { useState, useEffect } from 'react';
import './QuizSubmission.css';
import { showWarningAlert, showErrorAlert } from '../utils/alertService';

const QuizSubmission = ({ quiz, onSubmit, existingAttempt = null }) => {
  const [answers, setAnswers] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);

  // Check if this quiz has existing attempts when the component mounts or quiz changes
  useEffect(() => {
    // Reset state when the component mounts or quiz changes
    setSubmitted(false);
    setResults(null);
    setAnswers({});
    // Only if there's an existing attempt for the current user, show the results
    if (existingAttempt) {
      setSubmitted(true);
      setResults(existingAttempt);
      // Reconstruct answers from the attempt data if available
      if (existingAttempt.answers && Array.isArray(existingAttempt.answers)) {
        const reconstructedAnswers = {};
        existingAttempt.answers.forEach(answer => {
          if (answer.questionId) {
            // Handle both single and multiple answers
            if (answer.selectedAnswers && answer.selectedAnswers.length > 0) {
              reconstructedAnswers[answer.questionId] = answer.selectedAnswers;
            } else {
              reconstructedAnswers[answer.questionId] = answer.selectedAnswer;
            }
          }
        });
        setAnswers(reconstructedAnswers);
      }
    }
  }, [existingAttempt, quiz._id]);

  const handleAnswerSelect = (questionId, answerIndex, question) => {
    const answerIndexStr = answerIndex.toString();

    if (question.questionType === 'multiple-choice' || question.allowMultipleAnswers) {
      // Handle multiple selection
      const currentAnswers = Array.isArray(answers[questionId]) ? answers[questionId] : [];
      let newAnswers;

      if (currentAnswers.includes(answerIndexStr)) {
        // Remove if already selected
        newAnswers = currentAnswers.filter(ans => ans !== answerIndexStr);
      } else {
        // Add to selection
        newAnswers = [...currentAnswers, answerIndexStr];
      }

      setAnswers({
        ...answers,
        [questionId]: newAnswers
      });
    } else {
      // Handle single selection
      setAnswers({
        ...answers,
        [questionId]: answerIndexStr
      });
    }
  };

  const isAnswerSelected = (questionId, answerIndex, question) => {
    const answerIndexStr = answerIndex.toString();

    if (question.questionType === 'multiple-choice' || question.allowMultipleAnswers) {
      const currentAnswers = Array.isArray(answers[questionId]) ? answers[questionId] : [];
      return currentAnswers.includes(answerIndexStr);
    } else {
      return answers[questionId] === answerIndexStr;
    }
  };

  const getPartialCreditInfo = (question, optionIndex) => {
    if (question.answerOptions && question.answerOptions.length > 0) {
      const answerOption = question.answerOptions.find(opt => opt.optionIndex === optionIndex);
      if (answerOption && answerOption.creditPercentage > 0 && answerOption.creditPercentage < 100) {
        return answerOption.creditPercentage;
      }
    }
    return null;
  };
  const handleSubmit = async () => {
    // Check if all questions are answered
    const allAnswered = quiz.questions.every(q => answers[q._id] !== undefined);
    if (!allAnswered) {
      showWarningAlert('Please answer all questions before submitting.');
      return;
    }
    try {
      setLoading(true);
      const result = await onSubmit(answers);
      setResults(result);
      setSubmitted(true);
    } catch (error) {
      console.error('Error submitting quiz:', error);
      showErrorAlert('Failed to submit quiz. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  const handleRetry = () => {
    // Only reset the local state, don't delete the attempt from the database
    setAnswers({});
    setSubmitted(false);
    setResults(null);
  };
  return (
    <div className="quiz-submission-container">
      {!submitted ? (
        <>
          <div className="quiz-header">
            <h2>{quiz.title}</h2>
            <p>{quiz.description}</p>
            <div className="quiz-meta">
              {/* <span>Time Limit: {quiz.timeLimit} minutes</span>
              <span>Passing Score: {quiz.passingScore}%</span> */}
            </div>
          </div>
          <div className="quiz-questions">
            {quiz.questions.map((question, qIndex) => {
              const isMultipleChoice = question.questionType === 'multiple-choice' || question.allowMultipleAnswers;
              const isPartialCredit = question.questionType === 'partial-credit';

              return (
                <div key={question._id} className="quiz-question">
                  <h3>Question {qIndex + 1}: {question.question}</h3>
                  {question.points && question.points > 1 && (
                    <p className="question-points">Points: {question.points}</p>
                  )}
                  {isMultipleChoice && (
                    <p className="question-instruction">
                      <em>Select all correct answers</em>
                    </p>
                  )}
                  {isPartialCredit && (
                    <p className="question-instruction">
                      <em>Partial credit available for partially correct answers</em>
                    </p>
                  )}
                  <div className="quiz-options">
                    {question.options.map((option, oIndex) => {
                      const partialCredit = getPartialCreditInfo(question, oIndex);
                      const inputType = isMultipleChoice ? 'checkbox' : 'radio';

                      return (
                        <label key={oIndex} className="quiz-option">
                          <input
                            type={inputType}
                            name={isMultipleChoice ? undefined : `question-${question._id}`}
                            checked={isAnswerSelected(question._id, oIndex, question)}
                            onChange={() => handleAnswerSelect(question._id, oIndex, question)}
                          />
                          <span className="option-text">
                            {option}
                            {partialCredit && (
                              <span className="partial-credit-indicator">
                                ({partialCredit}% credit)
                              </span>
                            )}
                          </span>
                        </label>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
          <div className="quiz-actions">
            <button
              className="quiz-submit-btn"
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? 'Submitting...' : 'Submit Quiz'}
            </button>
          </div>
        </>
      ) : (
        <div className="quiz-results">
          <div className="quiz-result-header">
            <h2>{results.passed ? 'Congratulations!' : 'Quiz Results'}</h2>
            <div className="quiz-score">
              <div className={`score-circle ${results.passed ? 'passed' : 'failed'}`}>
                {results.percentage}%
              </div>
              <p>You {results.passed ? 'passed' : 'did not pass'} the quiz.</p>
              <p>Score: {results.score}/{results.totalPoints}</p>
              {results.certificateEligible !== undefined && (
                <p className={`certificate-status ${results.certificateEligible ? 'eligible' : 'not-eligible'}`}>
                  Certificate: {results.certificateEligible ? 'Eligible' : 'Not Eligible'}
                  {!results.certificateEligible && quiz.minimumScoreForCertificate && (
                    <span className="certificate-requirement">
                      (Minimum {quiz.minimumScoreForCertificate}% required)
                    </span>
                  )}
                </p>
              )}
              {results.attemptNumber && (
                <p>Attempt: {results.attemptNumber}/{quiz.maxAttempts || 3}</p>
              )}
              {results.remainingAttempts > 0 && (
                <p>Remaining attempts: {results.remainingAttempts}</p>
              )}
            </div>
          </div>
          <div className="quiz-answers-review">
            <h3>Review Your Answers</h3>
            {quiz.questions.map((question, qIndex) => {
              // Find the corresponding answer from results if available
              const answerFromResults = results.answers &&
                results.answers.find(a => a.questionIndex === qIndex);

              // Use answer from results if available, otherwise fallback to local state
              const userAnswers = answerFromResults ?
                (answerFromResults.selectedAnswers || [answerFromResults.selectedAnswer]) :
                (Array.isArray(answers[question._id]) ? answers[question._id] : [answers[question._id]]);

              const isCorrect = answerFromResults ? answerFromResults.isCorrect : false;
              const pointsEarned = answerFromResults ? answerFromResults.pointsEarned : 0;
              const maxPoints = answerFromResults ? answerFromResults.maxPoints : (question.points || 1);
              const creditPercentage = answerFromResults ? answerFromResults.creditPercentage : 0;

              return (
                <div key={question._id} className={`review-question ${isCorrect ? 'correct' : 'incorrect'}`}>
                  <h4>Question {qIndex + 1}: {question.question}</h4>

                  {/* Score information */}
                  <div className="question-score">
                    <span className="points-earned">
                      Points: {pointsEarned.toFixed(1)}/{maxPoints}
                    </span>
                    {creditPercentage > 0 && creditPercentage < 100 && (
                      <span className="partial-credit">
                        ({creditPercentage.toFixed(1)}% credit)
                      </span>
                    )}
                  </div>

                  <div className="review-options">
                    {question.options.map((option, oIndex) => {
                      const isSelected = userAnswers.includes(oIndex.toString());
                      const isCorrectOption = question.answerOptions ?
                        question.answerOptions.find(opt => opt.optionIndex === oIndex && opt.isCorrect) :
                        question.correctAnswer === oIndex.toString();
                      const partialCredit = getPartialCreditInfo(question, oIndex);

                      return (
                        <div
                          key={oIndex}
                          className={`review-option ${isSelected ? 'selected' : ''} ${isCorrectOption ? 'correct-answer' : ''}`}
                        >
                          <span className="option-text">
                            {option}
                            {partialCredit && (
                              <span className="partial-credit-indicator">
                                ({partialCredit}% credit)
                              </span>
                            )}
                          </span>
                          {isSelected && !isCorrectOption && (
                            <span className="wrong-indicator">✗</span>
                          )}
                          {isCorrectOption && (
                            <span className="correct-indicator">✓</span>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {question.explanation && (
                    <div className="explanation">
                      <p><strong>Explanation:</strong> {question.explanation}</p>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          <div className="quiz-actions">
            <button className="quiz-retry-btn" onClick={handleRetry}>
              Try Again
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
export default QuizSubmission;