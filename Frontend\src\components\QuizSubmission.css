.quiz-submission-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
}

.quiz-header {
  margin-bottom: 24px;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 16px;
}

.quiz-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.quiz-header p {
  color: #666;
  margin-bottom: 12px;
}

.quiz-meta {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.quiz-meta span {
  background-color: #f5f5f5;
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 0.85rem;
  color: #555;
}

.quiz-questions {
  margin-bottom: 24px;
}

.quiz-question {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eaeaea;
}

.quiz-question h3 {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.quiz-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quiz-option {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background-color: #f9f9f9;
  border-radius: var(--border-small-radius);
  cursor: pointer;
  transition: background-color 0.2s;
}

.quiz-option:hover {
  background-color: #f0f0f0;
}

.quiz-option input[type="radio"],
.quiz-option input[type="checkbox"] {
  margin-right: 10px;
}

.question-points {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.question-instruction {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 12px;
}

.partial-credit-indicator {
  font-size: 0.8rem;
  color: #ff9800;
  font-weight: 500;
  margin-left: 8px;
}

.certificate-status {
  font-weight: 600;
  margin-top: 8px;
}

.certificate-status.eligible {
  color: #4caf50;
}

.certificate-status.not-eligible {
  color: #f44336;
}

.certificate-requirement {
  font-size: 0.9rem;
  font-weight: normal;
  color: #666;
}

.question-score {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 0.9rem;
}

.points-earned {
  font-weight: 600;
  color: #333;
}

.partial-credit {
  color: #ff9800;
  font-weight: 500;
}

.explanation {
  margin-top: 12px;
  padding: 12px;
  background-color: rgba(33, 150, 243, 0.1);
  border-radius: var(--border-small-radius);
  border-left: 4px solid #2196f3;
}

.explanation p {
  margin: 0;
  color: #333;
  font-size: 0.9rem;
}

.quiz-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.quiz-submit-btn,
.quiz-retry-btn {
  padding: 10px 24px;
  border-radius: var(--border-small-radius);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
  font-size: 1rem;
}

.quiz-submit-btn {
  background-color: #4a6cf7;
  color: white;
}

.quiz-submit-btn:hover {
  background-color: #3a5ce5;
}

.quiz-retry-btn {
  background-color: #f0f0f0;
  color: #333;
}

.quiz-retry-btn:hover {
  background-color: #e0e0e0;
}

/* Results styles */
.quiz-results {
  animation: fadeIn 0.5s ease;
}

.quiz-result-header {
  text-align: center;
  margin-bottom: 32px;
}

.quiz-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16px;
}

.score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: white;
}

.score-circle.passed {
  background-color: #4caf50;
}

.score-circle.failed {
  background-color: #f44336;
}

.quiz-answers-review {
  margin-top: 32px;
}

.quiz-answers-review h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 8px;
}

.review-question {
  margin-bottom: 24px;
  padding: 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.review-question.correct {
  border-left: 4px solid #4caf50;
}

.review-question.incorrect {
  border-left: 4px solid #f44336;
}

.review-question h4 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.review-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.review-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border-radius: var(--border-small-radius);
  background-color: #fff;
  border: 1px solid #e0e0e0;
}

.review-option.selected {
  background-color: #f0f0f0;
}

.review-option.correct-answer {
  border: 1px solid #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.review-option.selected:not(.correct-answer) {
  border: 1px solid #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.option-text {
  flex: 1;
}

.correct-indicator {
  color: #4caf50;
  font-weight: bold;
  margin-left: 8px;
}

.wrong-indicator {
  color: #f44336;
  font-weight: bold;
  margin-left: 8px;
}

.correct-answer-note {
  margin-top: 12px;
  padding: 8px 12px;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: var(--border-small-radius);
  color: #4caf50;
  font-weight: 500;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
