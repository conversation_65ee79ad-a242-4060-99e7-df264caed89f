import React, { useState, useEffect } from 'react';
import './QuizCreator.css';
import { showSuccessAlert, showErrorAlert } from '../utils/alertService';

const QuizCreator = ({ quiz = null, onSave, onCancel }) => {
  const [quizData, setQuizData] = useState({
    title: '',
    description: '',
    timeLimit: 30,
    passingScore: 60,
    certificateEligible: true,
    minimumScoreForCertificate: 70,
    maxAttempts: 3,
    questions: []
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (quiz) {
      setQuizData({
        title: quiz.title || '',
        description: quiz.description || '',
        timeLimit: quiz.timeLimit || 30,
        passingScore: quiz.passingScore || 60,
        certificateEligible: quiz.certificateEligible !== undefined ? quiz.certificateEligible : true,
        minimumScoreForCertificate: quiz.minimumScoreForCertificate || 70,
        maxAttempts: quiz.maxAttempts || 3,
        questions: quiz.questions || []
      });
    }
  }, [quiz]);

  const addQuestion = () => {
    const newQuestion = {
      question: '',
      options: ['', '', '', ''],
      questionType: 'single-choice',
      allowMultipleAnswers: false,
      points: 1,
      explanation: '',
      answerOptions: [
        { optionIndex: 0, isCorrect: false, creditPercentage: 0 },
        { optionIndex: 1, isCorrect: false, creditPercentage: 0 },
        { optionIndex: 2, isCorrect: false, creditPercentage: 0 },
        { optionIndex: 3, isCorrect: false, creditPercentage: 0 }
      ]
    };

    setQuizData({
      ...quizData,
      questions: [...quizData.questions, newQuestion]
    });
  };

  const updateQuestion = (questionIndex, field, value) => {
    const updatedQuestions = [...quizData.questions];
    updatedQuestions[questionIndex] = {
      ...updatedQuestions[questionIndex],
      [field]: value
    };

    // If changing question type, reset answer options
    if (field === 'questionType') {
      const question = updatedQuestions[questionIndex];
      if (value === 'single-choice') {
        question.allowMultipleAnswers = false;
        question.answerOptions = question.options.map((_, index) => ({
          optionIndex: index,
          isCorrect: false,
          creditPercentage: 0
        }));
      } else if (value === 'multiple-choice') {
        question.allowMultipleAnswers = true;
        question.answerOptions = question.options.map((_, index) => ({
          optionIndex: index,
          isCorrect: false,
          creditPercentage: 0
        }));
      } else if (value === 'partial-credit') {
        question.allowMultipleAnswers = false;
        question.answerOptions = question.options.map((_, index) => ({
          optionIndex: index,
          isCorrect: false,
          creditPercentage: 0
        }));
      }
    }

    setQuizData({
      ...quizData,
      questions: updatedQuestions
    });
  };

  const updateOption = (questionIndex, optionIndex, value) => {
    const updatedQuestions = [...quizData.questions];
    updatedQuestions[questionIndex].options[optionIndex] = value;
    
    setQuizData({
      ...quizData,
      questions: updatedQuestions
    });
  };

  const updateAnswerOption = (questionIndex, optionIndex, field, value) => {
    const updatedQuestions = [...quizData.questions];
    const question = updatedQuestions[questionIndex];
    
    if (!question.answerOptions) {
      question.answerOptions = question.options.map((_, index) => ({
        optionIndex: index,
        isCorrect: false,
        creditPercentage: 0
      }));
    }

    const answerOption = question.answerOptions.find(opt => opt.optionIndex === optionIndex);
    if (answerOption) {
      answerOption[field] = value;
    }

    setQuizData({
      ...quizData,
      questions: updatedQuestions
    });
  };

  const removeQuestion = (questionIndex) => {
    const updatedQuestions = quizData.questions.filter((_, index) => index !== questionIndex);
    setQuizData({
      ...quizData,
      questions: updatedQuestions
    });
  };

  const addOption = (questionIndex) => {
    const updatedQuestions = [...quizData.questions];
    const question = updatedQuestions[questionIndex];
    const newOptionIndex = question.options.length;
    
    question.options.push('');
    question.answerOptions.push({
      optionIndex: newOptionIndex,
      isCorrect: false,
      creditPercentage: 0
    });

    setQuizData({
      ...quizData,
      questions: updatedQuestions
    });
  };

  const removeOption = (questionIndex, optionIndex) => {
    const updatedQuestions = [...quizData.questions];
    const question = updatedQuestions[questionIndex];
    
    if (question.options.length <= 2) {
      showErrorAlert('A question must have at least 2 options');
      return;
    }

    question.options.splice(optionIndex, 1);
    question.answerOptions = question.answerOptions
      .filter(opt => opt.optionIndex !== optionIndex)
      .map(opt => ({
        ...opt,
        optionIndex: opt.optionIndex > optionIndex ? opt.optionIndex - 1 : opt.optionIndex
      }));

    setQuizData({
      ...quizData,
      questions: updatedQuestions
    });
  };

  const validateQuiz = () => {
    if (!quizData.title.trim()) {
      showErrorAlert('Quiz title is required');
      return false;
    }

    if (quizData.questions.length === 0) {
      showErrorAlert('At least one question is required');
      return false;
    }

    for (let i = 0; i < quizData.questions.length; i++) {
      const question = quizData.questions[i];
      
      if (!question.question.trim()) {
        showErrorAlert(`Question ${i + 1} text is required`);
        return false;
      }

      if (question.options.some(opt => !opt.trim())) {
        showErrorAlert(`All options for question ${i + 1} must be filled`);
        return false;
      }

      // Validate answer options
      if (question.questionType === 'single-choice') {
        const correctAnswers = question.answerOptions.filter(opt => opt.isCorrect);
        if (correctAnswers.length !== 1) {
          showErrorAlert(`Question ${i + 1} must have exactly one correct answer`);
          return false;
        }
      } else if (question.questionType === 'multiple-choice') {
        const correctAnswers = question.answerOptions.filter(opt => opt.isCorrect);
        if (correctAnswers.length === 0) {
          showErrorAlert(`Question ${i + 1} must have at least one correct answer`);
          return false;
        }
      } else if (question.questionType === 'partial-credit') {
        const totalCredit = question.answerOptions.reduce((sum, opt) => sum + (opt.creditPercentage || 0), 0);
        if (totalCredit === 0) {
          showErrorAlert(`Question ${i + 1} must have at least some credit assigned`);
          return false;
        }
      }
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateQuiz()) return;

    setLoading(true);
    try {
      await onSave(quizData);
      showSuccessAlert('Quiz saved successfully');
    } catch (error) {
      console.error('Error saving quiz:', error);
      showErrorAlert('Failed to save quiz');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="quiz-creator">
      <div className="quiz-creator-header">
        <h2>{quiz ? 'Edit Quiz' : 'Create New Quiz'}</h2>
      </div>

      <div className="quiz-basic-info">
        <div className="form-group">
          <label>Quiz Title *</label>
          <input
            type="text"
            value={quizData.title}
            onChange={(e) => setQuizData({ ...quizData, title: e.target.value })}
            placeholder="Enter quiz title"
          />
        </div>

        <div className="form-group">
          <label>Description</label>
          <textarea
            value={quizData.description}
            onChange={(e) => setQuizData({ ...quizData, description: e.target.value })}
            placeholder="Enter quiz description"
            rows="3"
          />
        </div>

        <div className="form-row">
          <div className="form-group">
            <label>Time Limit (minutes)</label>
            <input
              type="number"
              value={quizData.timeLimit}
              onChange={(e) => setQuizData({ ...quizData, timeLimit: parseInt(e.target.value) || 0 })}
              min="1"
            />
          </div>

          <div className="form-group">
            <label>Passing Score (%)</label>
            <input
              type="number"
              value={quizData.passingScore}
              onChange={(e) => setQuizData({ ...quizData, passingScore: parseInt(e.target.value) || 0 })}
              min="0"
              max="100"
            />
          </div>

          <div className="form-group">
            <label>Max Attempts</label>
            <input
              type="number"
              value={quizData.maxAttempts}
              onChange={(e) => setQuizData({ ...quizData, maxAttempts: parseInt(e.target.value) || 1 })}
              min="1"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label>
              <input
                type="checkbox"
                checked={quizData.certificateEligible}
                onChange={(e) => setQuizData({ ...quizData, certificateEligible: e.target.checked })}
              />
              Certificate Eligible
            </label>
          </div>

          {quizData.certificateEligible && (
            <div className="form-group">
              <label>Minimum Score for Certificate (%)</label>
              <input
                type="number"
                value={quizData.minimumScoreForCertificate}
                onChange={(e) => setQuizData({ ...quizData, minimumScoreForCertificate: parseInt(e.target.value) || 0 })}
                min="0"
                max="100"
              />
            </div>
          )}
        </div>
      </div>

      <div className="quiz-questions-section">
        <div className="section-header">
          <h3>Questions</h3>
          <button type="button" onClick={addQuestion} className="add-question-btn">
            Add Question
          </button>
        </div>

        {quizData.questions.map((question, questionIndex) => (
          <QuestionEditor
            key={questionIndex}
            question={question}
            questionIndex={questionIndex}
            onUpdate={updateQuestion}
            onUpdateOption={updateOption}
            onUpdateAnswerOption={updateAnswerOption}
            onRemove={removeQuestion}
            onAddOption={addOption}
            onRemoveOption={removeOption}
          />
        ))}
      </div>

      <div className="quiz-creator-actions">
        <button type="button" onClick={onCancel} className="cancel-btn">
          Cancel
        </button>
        <button type="button" onClick={handleSave} disabled={loading} className="save-btn">
          {loading ? 'Saving...' : 'Save Quiz'}
        </button>
      </div>
    </div>
  );
};

// Question Editor Component (will be created separately)
const QuestionEditor = ({ question, questionIndex, onUpdate, onUpdateOption, onUpdateAnswerOption, onRemove, onAddOption, onRemoveOption }) => {
  return (
    <div className="question-editor">
      {/* Question editor implementation will be added in the next part */}
      <p>Question Editor Component - To be implemented</p>
    </div>
  );
};

export default QuizCreator;
