/**
 * Quiz Scoring Utilities
 * Provides functions for calculating partial credit scores and validating quiz configurations
 */

/**
 * Calculate partial credit score for a single question
 * @param {Object} question - The question object
 * @param {Array} selectedAnswers - Array of selected answer indices (as strings)
 * @returns {Object} - Score calculation result
 */
const calculateQuestionScore = (question, selectedAnswers) => {
  const maxPoints = question.points || 1;
  let pointsEarned = 0;
  let creditPercentage = 0;
  let isCorrect = false;

  // Ensure selectedAnswers is an array
  if (!Array.isArray(selectedAnswers)) {
    selectedAnswers = selectedAnswers ? [selectedAnswers] : [];
  }

  // Handle different question types
  switch (question.questionType) {
    case 'partial-credit':
      // Sum up credit percentages for selected answers
      selectedAnswers.forEach(answer => {
        const answerIndex = parseInt(answer);
        const answerOption = question.answerOptions?.find(opt => opt.optionIndex === answerIndex);
        if (answerOption) {
          creditPercentage += answerOption.creditPercentage || 0;
        }
      });

      // Cap at 100% and calculate points
      creditPercentage = Math.min(creditPercentage, 100);
      pointsEarned = (creditPercentage / 100) * maxPoints;
      isCorrect = creditPercentage >= 100;
      break;

    case 'multiple-choice':
      // Calculate score based on correct vs incorrect selections
      let correctSelections = 0;
      let incorrectSelections = 0;
      const totalCorrectOptions = question.answerOptions?.filter(opt => opt.isCorrect).length || 0;

      selectedAnswers.forEach(answer => {
        const answerIndex = parseInt(answer);
        const answerOption = question.answerOptions?.find(opt => opt.optionIndex === answerIndex);
        if (answerOption && answerOption.isCorrect) {
          correctSelections++;
        } else if (answerOption) {
          incorrectSelections++;
        }
      });

      // Calculate partial credit: (correct selections / total correct) - penalty for incorrect
      if (totalCorrectOptions > 0) {
        const correctRatio = correctSelections / totalCorrectOptions;
        const penaltyRatio = incorrectSelections / totalCorrectOptions;
        creditPercentage = Math.max(0, (correctRatio * 100) - (penaltyRatio * 50)); // 50% penalty for wrong answers
        pointsEarned = (creditPercentage / 100) * maxPoints;
        isCorrect = correctSelections === totalCorrectOptions && incorrectSelections === 0;
      }
      break;

    case 'single-choice':
    default:
      // Single correct answer
      if (selectedAnswers.length > 0) {
        const answerIndex = parseInt(selectedAnswers[0]);

        if (question.answerOptions && question.answerOptions.length > 0) {
          const answerOption = question.answerOptions.find(opt => opt.optionIndex === answerIndex);
          if (answerOption) {
            creditPercentage = answerOption.creditPercentage || 0;
            pointsEarned = (creditPercentage / 100) * maxPoints;
            isCorrect = answerOption.isCorrect;
          }
        } else {
          // Legacy format with correctAnswer field
          isCorrect = selectedAnswers[0] === question.correctAnswer;
          creditPercentage = isCorrect ? 100 : 0;
          pointsEarned = isCorrect ? maxPoints : 0;
        }
      }
      break;
  }

  return {
    pointsEarned: Math.round(pointsEarned * 100) / 100,
    maxPoints,
    creditPercentage: Math.round(creditPercentage * 100) / 100,
    isCorrect
  };
};

/**
 * Calculate total quiz score
 * @param {Object} quiz - The quiz object
 * @param {Object} answers - User's answers (questionId -> answer(s))
 * @returns {Object} - Complete scoring result
 */
const calculateQuizScore = (quiz, answers) => {
  let totalScore = 0;
  let totalPossibleScore = 0;
  const questionResults = [];

  quiz.questions.forEach((question, index) => {
    const questionId = question._id.toString();
    const maxPoints = question.points || 1;
    totalPossibleScore += maxPoints;

    // Get user's answers for this question
    let selectedAnswers = [];
    if (Array.isArray(answers)) {
      // Legacy array format
      selectedAnswers = [answers[index]];
    } else if (answers[questionId]) {
      // Object format with question IDs
      selectedAnswers = Array.isArray(answers[questionId]) ?
        answers[questionId] : [answers[questionId]];
    }

    // Calculate score for this question
    const questionScore = calculateQuestionScore(question, selectedAnswers);
    totalScore += questionScore.pointsEarned;

    questionResults.push({
      questionIndex: index,
      questionId: questionId,
      selectedAnswers: selectedAnswers,
      selectedAnswer: selectedAnswers[0] || null, // For backward compatibility
      ...questionScore
    });
  });

  const percentage = Math.round((totalScore / totalPossibleScore) * 100);
  const passed = percentage >= quiz.passingScore;
  const certificateEligible = quiz.certificateEligible &&
    percentage >= quiz.minimumScoreForCertificate;

  return {
    totalScore: Math.round(totalScore * 100) / 100,
    totalPossibleScore,
    percentage,
    passed,
    certificateEligible,
    questionResults
  };
};

/**
 * Validate quiz configuration
 * @param {Object} quiz - The quiz object to validate
 * @returns {Object} - Validation result
 */
const validateQuizConfiguration = (quiz) => {
  const errors = [];

  // Basic validation
  if (!quiz.title || quiz.title.trim().length === 0) {
    errors.push('Quiz title is required');
  }

  if (!quiz.questions || quiz.questions.length === 0) {
    errors.push('At least one question is required');
  }

  // Validate each question
  quiz.questions?.forEach((question, index) => {
    const questionNum = index + 1;

    if (!question.question || question.question.trim().length === 0) {
      errors.push(`Question ${questionNum}: Question text is required`);
    }

    if (!question.options || question.options.length < 2) {
      errors.push(`Question ${questionNum}: At least 2 options are required`);
    }

    if (question.options?.some(opt => !opt || opt.trim().length === 0)) {
      errors.push(`Question ${questionNum}: All options must have text`);
    }

    // Validate answer configuration based on question type
    if (question.answerOptions && question.answerOptions.length > 0) {
      switch (question.questionType) {
        case 'single-choice':
          const correctAnswers = question.answerOptions.filter(opt => opt.isCorrect);
          if (correctAnswers.length !== 1) {
            errors.push(`Question ${questionNum}: Single-choice questions must have exactly one correct answer`);
          }
          break;

        case 'multiple-choice':
          const multipleCorrect = question.answerOptions.filter(opt => opt.isCorrect);
          if (multipleCorrect.length === 0) {
            errors.push(`Question ${questionNum}: Multiple-choice questions must have at least one correct answer`);
          }
          break;

        case 'partial-credit':
          const totalCredit = question.answerOptions.reduce((sum, opt) => sum + (opt.creditPercentage || 0), 0);
          if (totalCredit === 0) {
            errors.push(`Question ${questionNum}: Partial-credit questions must have at least some credit assigned`);
          }

          // Check for invalid credit percentages
          const invalidCredit = question.answerOptions.some(opt =>
            opt.creditPercentage < 0 || opt.creditPercentage > 100
          );
          if (invalidCredit) {
            errors.push(`Question ${questionNum}: Credit percentages must be between 0 and 100`);
          }
          break;
      }
    } else if (!question.correctAnswer) {
      errors.push(`Question ${questionNum}: No correct answer specified`);
    }
  });

  // Validate score thresholds
  if (quiz.passingScore < 0 || quiz.passingScore > 100) {
    errors.push('Passing score must be between 0 and 100');
  }

  if (quiz.minimumScoreForCertificate < 0 || quiz.minimumScoreForCertificate > 100) {
    errors.push('Minimum score for certificate must be between 0 and 100');
  }

  if (quiz.certificateEligible && quiz.minimumScoreForCertificate < quiz.passingScore) {
    errors.push('Minimum score for certificate should not be less than passing score');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Convert legacy quiz format to new format
 * @param {Object} quiz - Quiz object in legacy format
 * @returns {Object} - Quiz object in new format
 */
const convertLegacyQuiz = (quiz) => {
  const convertedQuiz = { ...quiz };

  convertedQuiz.questions = quiz.questions.map(question => {
    const convertedQuestion = { ...question };

    // Set default values for new fields
    if (!convertedQuestion.questionType) {
      convertedQuestion.questionType = 'single-choice';
    }

    if (convertedQuestion.allowMultipleAnswers === undefined) {
      convertedQuestion.allowMultipleAnswers = false;
    }

    // Convert correctAnswer to answerOptions format
    if (question.correctAnswer && (!question.answerOptions || question.answerOptions.length === 0)) {
      convertedQuestion.answerOptions = question.options.map((option, index) => ({
        optionIndex: index,
        isCorrect: index.toString() === question.correctAnswer,
        creditPercentage: index.toString() === question.correctAnswer ? 100 : 0
      }));
    }

    return convertedQuestion;
  });

  // Set default values for new quiz fields
  if (convertedQuiz.certificateEligible === undefined) {
    convertedQuiz.certificateEligible = true;
  }

  if (!convertedQuiz.minimumScoreForCertificate) {
    convertedQuiz.minimumScoreForCertificate = 70;
  }

  if (!convertedQuiz.maxAttempts) {
    convertedQuiz.maxAttempts = 3;
  }

  return convertedQuiz;
};

module.exports = {
  calculateQuestionScore,
  calculateQuizScore,
  validateQuizConfiguration,
  convertLegacyQuiz
};
