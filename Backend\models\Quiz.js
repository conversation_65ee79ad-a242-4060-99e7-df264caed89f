const mongoose = require('mongoose');

const quizSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String },
  course: { type: mongoose.Schema.Types.ObjectId, ref: 'Course', required: true },
  questions: [{
    question: { type: String, required: true },
    options: [{ type: String, required: true }],
    // Enhanced answer structure for partial credit support
    answerOptions: [{
      optionIndex: { type: Number, required: true }, // Index of the option (0, 1, 2, etc.)
      isCorrect: { type: Boolean, default: false },
      creditPercentage: { type: Number, default: 0, min: 0, max: 100 } // Percentage of credit for this option
    }],
    // Legacy field for backward compatibility
    correctAnswer: { type: String },
    explanation: { type: String },
    points: { type: Number, default: 1 },
    questionType: {
      type: String,
      enum: ['single-choice', 'multiple-choice', 'partial-credit'],
      default: 'single-choice'
    },
    allowMultipleAnswers: { type: Boolean, default: false }
  }],
  timeLimit: { type: Number }, // in minutes
  passingScore: { type: Number, default: 60 }, // percentage
  // Enhanced quiz settings for certificate eligibility
  certificateEligible: { type: Boolean, default: true },
  minimumScoreForCertificate: { type: Number, default: 70 }, // Custom threshold for certificate
  maxAttempts: { type: Number, default: 3 }, // Maximum allowed attempts
  attempts: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    score: { type: Number },
    totalPossibleScore: { type: Number },
    percentage: { type: Number },
    passed: { type: Boolean },
    certificateEligible: { type: Boolean },
    answers: [{
      questionIndex: { type: Number },
      questionId: { type: mongoose.Schema.Types.ObjectId },
      selectedAnswers: [{ type: String }], // Array to support multiple selections
      selectedAnswer: { type: String }, // Legacy field for backward compatibility
      isCorrect: { type: Boolean },
      pointsEarned: { type: Number, default: 0 },
      maxPoints: { type: Number, default: 1 },
      creditPercentage: { type: Number, default: 0 }
    }],
    completedAt: { type: Date, default: Date.now },
    attemptNumber: { type: Number, default: 1 }
  }]
}, { timestamps: true });

// Pre-save middleware to ensure backward compatibility
quizSchema.pre('save', function (next) {
  // Convert legacy correctAnswer format to new answerOptions format
  this.questions.forEach(question => {
    if (question.correctAnswer && (!question.answerOptions || question.answerOptions.length === 0)) {
      question.answerOptions = question.options.map((option, index) => ({
        optionIndex: index,
        isCorrect: index.toString() === question.correctAnswer,
        creditPercentage: index.toString() === question.correctAnswer ? 100 : 0
      }));
      question.questionType = 'single-choice';
    }
  });
  next();
});

module.exports = mongoose.model('Quiz', quizSchema);