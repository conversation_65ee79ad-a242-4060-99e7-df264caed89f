/* Quiz Creation Options */
.quiz-creation-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* Add Quiz Button */
.add-quiz-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.add-quiz-button.basic {
  background-color: #4a6cf7;
  color: white;
}

.add-quiz-button.basic:hover {
  background-color: #3a5ce5;
}

.add-quiz-button.enhanced {
  background-color: #28a745;
  color: white;
}

.add-quiz-button.enhanced:hover {
  background-color: #218838;
}

/* Enhanced Quiz Creator Wrapper */
.enhanced-quiz-creator-wrapper {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #28a745;
}

/* Quiz Editor Styles */
.quiz-editor {
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-top: 16px;
}

.quiz-editor.in-module {
  margin-top: 20px;
  border-color: #4a6cf7;
}

.quiz-editor h4 {
  margin-bottom: 16px;
  color: #333;
  font-size: 1.1rem;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 6px;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.cancel-button,
.save-button {
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
  font-size: 14px;
}

.cancel-button {
  background-color: #f0f0f0;
  color: #333;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.save-button {
  background-color: #4caf50;
  color: white;
}

.save-button:hover {
  background-color: #45a049;
}

/* Question Editor Styles */
.add-question-form {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.add-question-form h5 {
  margin-bottom: 16px;
  color: #333;
}

.options-container {
  margin-bottom: 16px;
}

.options-container label {
  display: block;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.option-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.option-row input[type="text"] {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.correct-toggle {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f8f9fa;
  color: #666;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.correct-toggle.active {
  background-color: #28a745;
  color: white;
  border-color: #28a745;
}

.correct-toggle:hover {
  background-color: #e9ecef;
}

.correct-toggle.active:hover {
  background-color: #218838;
}

.question-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.question-form-actions button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
}

.question-form-actions .cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.question-form-actions .save-btn {
  background-color: #4a6cf7;
  color: white;
}

.question-form-actions .cancel-btn:hover {
  background-color: #e0e0e0;
}

.question-form-actions .save-btn:hover {
  background-color: #3a5ce5;
}

/* Questions List */
.questions-list {
  margin-bottom: 16px;
}

.question-item {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.question-text {
  font-weight: 500;
  color: #333;
}

.question-actions {
  display: flex;
  gap: 8px;
}

.question-actions button {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
}

/* Quiz Item Actions */
.quiz-item-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.quiz-item-actions button {
  position: relative;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  gap: 4px;
}

.edit-basic-btn {
  background-color: #4a6cf7;
  color: white;
}

.edit-basic-btn:hover {
  background-color: #3a5ce5;
}

.edit-enhanced-btn {
  background-color: #28a745;
  color: white;
}

.edit-enhanced-btn:hover {
  background-color: #218838;
}

.enhanced-indicator {
  font-size: 10px;
  font-weight: bold;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
}

.delete-btn:hover {
  background-color: #c82333;
}

/* Enhanced Quiz Badge */
.enhanced-quiz-badge {
  margin-left: 8px;
  font-size: 16px;
  opacity: 0.8;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.edit-question-btn {
  background-color: #4a6cf7;
  color: white;
}

.delete-question-btn {
  background-color: #dc3545;
  color: white;
}

.edit-question-btn:hover {
  background-color: #3a5ce5;
}

.delete-question-btn:hover {
  background-color: #c82333;
}

.options-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.options-list li {
  padding: 4px 0;
  color: #666;
}

.options-list li.correct {
  color: #28a745;
  font-weight: 500;
}

/* No items state */
.no-items {
  color: #666;
  font-style: italic;
  margin-bottom: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .quiz-creation-options {
    flex-direction: column;
  }
  
  .add-quiz-button {
    justify-content: center;
  }
  
  .enhanced-quiz-creator-wrapper {
    padding: 16px;
  }
  
  .option-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .question-actions {
    flex-direction: column;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
