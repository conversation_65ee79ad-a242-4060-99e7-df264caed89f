{"version": 3, "sources": ["../../@hello-pangea/dnd/dist/dnd.esm.js", "../../tiny-invariant/dist/esm/tiny-invariant.js", "../../css-box-model/dist/css-box-model.esm.js", "../../raf-schd/dist/raf-schd.esm.js", "../../@babel/runtime/helpers/esm/extends.js"], "sourcesContent": ["import React, { useState, useRef, useEffect, useLayoutEffect, useContext } from 'react';\nimport ReactDOM, { flushSync } from 'react-dom';\nimport { createStore as createStore$1, compose, applyMiddleware, bindActionCreators } from 'redux';\nimport { Provider, connect } from 'react-redux';\nimport { getRect, expand, offset, withScroll, calculateBox, getBox, createBox } from 'css-box-model';\nimport rafSchd from 'raf-schd';\nimport _extends from '@babel/runtime/helpers/esm/extends';\n\nconst isProduction$1 = process.env.NODE_ENV === 'production';\nconst spacesAndTabs = /[ \\t]{2,}/g;\nconst lineStartWithSpaces = /^[ \\t]*/gm;\nconst clean$2 = value => value.replace(spacesAndTabs, ' ').replace(lineStartWithSpaces, '').trim();\nconst getDevMessage = message => clean$2(`\n  %c@hello-pangea/dnd\n\n  %c${clean$2(message)}\n\n  %c👷‍ This is a development only message. It will be removed in production builds.\n`);\nconst getFormattedMessage = message => [getDevMessage(message), 'color: #00C584; font-size: 1.2em; font-weight: bold;', 'line-height: 1.5', 'color: #723874;'];\nconst isDisabledFlag = '__@hello-pangea/dnd-disable-dev-warnings';\nfunction log(type, message) {\n  if (isProduction$1) {\n    return;\n  }\n  if (typeof window !== 'undefined' && window[isDisabledFlag]) {\n    return;\n  }\n  console[type](...getFormattedMessage(message));\n}\nconst warning = log.bind(null, 'warn');\nconst error = log.bind(null, 'error');\n\nfunction noop$2() {}\n\nfunction getOptions(shared, fromBinding) {\n  return {\n    ...shared,\n    ...fromBinding\n  };\n}\nfunction bindEvents(el, bindings, sharedOptions) {\n  const unbindings = bindings.map(binding => {\n    const options = getOptions(sharedOptions, binding.options);\n    el.addEventListener(binding.eventName, binding.fn, options);\n    return function unbind() {\n      el.removeEventListener(binding.eventName, binding.fn, options);\n    };\n  });\n  return function unbindAll() {\n    unbindings.forEach(unbind => {\n      unbind();\n    });\n  };\n}\n\nconst isProduction = process.env.NODE_ENV === 'production';\nconst prefix$1 = 'Invariant failed';\nclass RbdInvariant extends Error {}\nRbdInvariant.prototype.toString = function toString() {\n  return this.message;\n};\nfunction invariant(condition, message) {\n  if (isProduction) {\n    throw new RbdInvariant(prefix$1);\n  } else {\n    throw new RbdInvariant(`${prefix$1}: ${message || ''}`);\n  }\n}\n\nclass ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.callbacks = null;\n    this.unbind = noop$2;\n    this.onWindowError = event => {\n      const callbacks = this.getCallbacks();\n      if (callbacks.isDragging()) {\n        callbacks.tryAbort();\n        process.env.NODE_ENV !== \"production\" ? warning(`\n        An error was caught by our window 'error' event listener while a drag was occurring.\n        The active drag has been aborted.\n      `) : void 0;\n      }\n      const err = event.error;\n      if (err instanceof RbdInvariant) {\n        event.preventDefault();\n        if (process.env.NODE_ENV !== 'production') {\n          error(err.message);\n        }\n      }\n    };\n    this.getCallbacks = () => {\n      if (!this.callbacks) {\n        throw new Error('Unable to find AppCallbacks in <ErrorBoundary/>');\n      }\n      return this.callbacks;\n    };\n    this.setCallbacks = callbacks => {\n      this.callbacks = callbacks;\n    };\n  }\n  componentDidMount() {\n    this.unbind = bindEvents(window, [{\n      eventName: 'error',\n      fn: this.onWindowError\n    }]);\n  }\n  componentDidCatch(err) {\n    if (err instanceof RbdInvariant) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(err.message);\n      }\n      this.setState({});\n      return;\n    }\n    throw err;\n  }\n  componentWillUnmount() {\n    this.unbind();\n  }\n  render() {\n    return this.props.children(this.setCallbacks);\n  }\n}\n\nconst dragHandleUsageInstructions = `\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n`;\nconst position = index => index + 1;\nconst onDragStart = start => `\n  You have lifted an item in position ${position(start.source.index)}\n`;\nconst withLocation = (source, destination) => {\n  const isInHomeList = source.droppableId === destination.droppableId;\n  const startPosition = position(source.index);\n  const endPosition = position(destination.index);\n  if (isInHomeList) {\n    return `\n      You have moved the item from position ${startPosition}\n      to position ${endPosition}\n    `;\n  }\n  return `\n    You have moved the item from position ${startPosition}\n    in list ${source.droppableId}\n    to list ${destination.droppableId}\n    in position ${endPosition}\n  `;\n};\nconst withCombine = (id, source, combine) => {\n  const inHomeList = source.droppableId === combine.droppableId;\n  if (inHomeList) {\n    return `\n      The item ${id}\n      has been combined with ${combine.draggableId}`;\n  }\n  return `\n      The item ${id}\n      in list ${source.droppableId}\n      has been combined with ${combine.draggableId}\n      in list ${combine.droppableId}\n    `;\n};\nconst onDragUpdate = update => {\n  const location = update.destination;\n  if (location) {\n    return withLocation(update.source, location);\n  }\n  const combine = update.combine;\n  if (combine) {\n    return withCombine(update.draggableId, update.source, combine);\n  }\n  return 'You are over an area that cannot be dropped on';\n};\nconst returnedToStart = source => `\n  The item has returned to its starting position\n  of ${position(source.index)}\n`;\nconst onDragEnd = result => {\n  if (result.reason === 'CANCEL') {\n    return `\n      Movement cancelled.\n      ${returnedToStart(result.source)}\n    `;\n  }\n  const location = result.destination;\n  const combine = result.combine;\n  if (location) {\n    return `\n      You have dropped the item.\n      ${withLocation(result.source, location)}\n    `;\n  }\n  if (combine) {\n    return `\n      You have dropped the item.\n      ${withCombine(result.draggableId, result.source, combine)}\n    `;\n  }\n  return `\n    The item has been dropped while not over a drop area.\n    ${returnedToStart(result.source)}\n  `;\n};\nconst preset = {\n  dragHandleUsageInstructions,\n  onDragStart,\n  onDragUpdate,\n  onDragEnd\n};\n\nfunction isEqual$2(first, second) {\n  if (first === second) {\n    return true;\n  }\n  if (Number.isNaN(first) && Number.isNaN(second)) {\n    return true;\n  }\n  return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n  if (newInputs.length !== lastInputs.length) {\n    return false;\n  }\n  for (let i = 0; i < newInputs.length; i++) {\n    if (!isEqual$2(newInputs[i], lastInputs[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction useMemo(getResult, inputs) {\n  const initial = useState(() => ({\n    inputs,\n    result: getResult()\n  }))[0];\n  const isFirstRun = useRef(true);\n  const committed = useRef(initial);\n  const useCache = isFirstRun.current || Boolean(inputs && committed.current.inputs && areInputsEqual(inputs, committed.current.inputs));\n  const cache = useCache ? committed.current : {\n    inputs,\n    result: getResult()\n  };\n  useEffect(() => {\n    isFirstRun.current = false;\n    committed.current = cache;\n  }, [cache]);\n  return cache.result;\n}\nfunction useCallback(callback, inputs) {\n  return useMemo(() => callback, inputs);\n}\n\nconst origin = {\n  x: 0,\n  y: 0\n};\nconst add = (point1, point2) => ({\n  x: point1.x + point2.x,\n  y: point1.y + point2.y\n});\nconst subtract = (point1, point2) => ({\n  x: point1.x - point2.x,\n  y: point1.y - point2.y\n});\nconst isEqual$1 = (point1, point2) => point1.x === point2.x && point1.y === point2.y;\nconst negate = point => ({\n  x: point.x !== 0 ? -point.x : 0,\n  y: point.y !== 0 ? -point.y : 0\n});\nconst patch = (line, value, otherValue = 0) => {\n  if (line === 'x') {\n    return {\n      x: value,\n      y: otherValue\n    };\n  }\n  return {\n    x: otherValue,\n    y: value\n  };\n};\nconst distance = (point1, point2) => Math.sqrt((point2.x - point1.x) ** 2 + (point2.y - point1.y) ** 2);\nconst closest$1 = (target, points) => Math.min(...points.map(point => distance(target, point)));\nconst apply = fn => point => ({\n  x: fn(point.x),\n  y: fn(point.y)\n});\n\nvar executeClip = (frame, subject) => {\n  const result = getRect({\n    top: Math.max(subject.top, frame.top),\n    right: Math.min(subject.right, frame.right),\n    bottom: Math.min(subject.bottom, frame.bottom),\n    left: Math.max(subject.left, frame.left)\n  });\n  if (result.width <= 0 || result.height <= 0) {\n    return null;\n  }\n  return result;\n};\n\nconst offsetByPosition = (spacing, point) => ({\n  top: spacing.top + point.y,\n  left: spacing.left + point.x,\n  bottom: spacing.bottom + point.y,\n  right: spacing.right + point.x\n});\nconst getCorners = spacing => [{\n  x: spacing.left,\n  y: spacing.top\n}, {\n  x: spacing.right,\n  y: spacing.top\n}, {\n  x: spacing.left,\n  y: spacing.bottom\n}, {\n  x: spacing.right,\n  y: spacing.bottom\n}];\nconst noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\n\nconst scroll$1 = (target, frame) => {\n  if (!frame) {\n    return target;\n  }\n  return offsetByPosition(target, frame.scroll.diff.displacement);\n};\nconst increase = (target, axis, withPlaceholder) => {\n  if (withPlaceholder && withPlaceholder.increasedBy) {\n    return {\n      ...target,\n      [axis.end]: target[axis.end] + withPlaceholder.increasedBy[axis.line]\n    };\n  }\n  return target;\n};\nconst clip = (target, frame) => {\n  if (frame && frame.shouldClipSubject) {\n    return executeClip(frame.pageMarginBox, target);\n  }\n  return getRect(target);\n};\nvar getSubject = ({\n  page,\n  withPlaceholder,\n  axis,\n  frame\n}) => {\n  const scrolled = scroll$1(page.marginBox, frame);\n  const increased = increase(scrolled, axis, withPlaceholder);\n  const clipped = clip(increased, frame);\n  return {\n    page,\n    withPlaceholder,\n    active: clipped\n  };\n};\n\nvar scrollDroppable = (droppable, newScroll) => {\n  !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  const scrollable = droppable.frame;\n  const scrollDiff = subtract(newScroll, scrollable.scroll.initial);\n  const scrollDisplacement = negate(scrollDiff);\n  const frame = {\n    ...scrollable,\n    scroll: {\n      initial: scrollable.scroll.initial,\n      current: newScroll,\n      diff: {\n        value: scrollDiff,\n        displacement: scrollDisplacement\n      },\n      max: scrollable.scroll.max\n    }\n  };\n  const subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: droppable.subject.withPlaceholder,\n    axis: droppable.axis,\n    frame\n  });\n  const result = {\n    ...droppable,\n    frame,\n    subject\n  };\n  return result;\n};\n\nfunction memoizeOne(resultFn, isEqual = areInputsEqual) {\n  let cache = null;\n  function memoized(...newArgs) {\n    if (cache && cache.lastThis === this && isEqual(newArgs, cache.lastArgs)) {\n      return cache.lastResult;\n    }\n    const lastResult = resultFn.apply(this, newArgs);\n    cache = {\n      lastResult,\n      lastArgs: newArgs,\n      lastThis: this\n    };\n    return lastResult;\n  }\n  memoized.clear = function clear() {\n    cache = null;\n  };\n  return memoized;\n}\n\nconst toDroppableMap = memoizeOne(droppables => droppables.reduce((previous, current) => {\n  previous[current.descriptor.id] = current;\n  return previous;\n}, {}));\nconst toDraggableMap = memoizeOne(draggables => draggables.reduce((previous, current) => {\n  previous[current.descriptor.id] = current;\n  return previous;\n}, {}));\nconst toDroppableList = memoizeOne(droppables => Object.values(droppables));\nconst toDraggableList = memoizeOne(draggables => Object.values(draggables));\n\nvar getDraggablesInsideDroppable = memoizeOne((droppableId, draggables) => {\n  const result = toDraggableList(draggables).filter(draggable => droppableId === draggable.descriptor.droppableId).sort((a, b) => a.descriptor.index - b.descriptor.index);\n  return result;\n});\n\nfunction tryGetDestination(impact) {\n  if (impact.at && impact.at.type === 'REORDER') {\n    return impact.at.destination;\n  }\n  return null;\n}\nfunction tryGetCombine(impact) {\n  if (impact.at && impact.at.type === 'COMBINE') {\n    return impact.at.combine;\n  }\n  return null;\n}\n\nvar removeDraggableFromList = memoizeOne((remove, list) => list.filter(item => item.descriptor.id !== remove.descriptor.id));\n\nvar moveToNextCombine = ({\n  isMovingForward,\n  draggable,\n  destination,\n  insideDestination,\n  previousImpact\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const location = tryGetDestination(previousImpact);\n  if (!location) {\n    return null;\n  }\n  function getImpact(target) {\n    const at = {\n      type: 'COMBINE',\n      combine: {\n        draggableId: target,\n        droppableId: destination.descriptor.id\n      }\n    };\n    return {\n      ...previousImpact,\n      at\n    };\n  }\n  const all = previousImpact.displaced.all;\n  const closestId = all.length ? all[0] : null;\n  if (isMovingForward) {\n    return closestId ? getImpact(closestId) : null;\n  }\n  const withoutDraggable = removeDraggableFromList(draggable, insideDestination);\n  if (!closestId) {\n    if (!withoutDraggable.length) {\n      return null;\n    }\n    const last = withoutDraggable[withoutDraggable.length - 1];\n    return getImpact(last.descriptor.id);\n  }\n  const indexOfClosest = withoutDraggable.findIndex(d => d.descriptor.id === closestId);\n  !(indexOfClosest !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find displaced item in set') : invariant() : void 0;\n  const proposedIndex = indexOfClosest - 1;\n  if (proposedIndex < 0) {\n    return null;\n  }\n  const before = withoutDraggable[proposedIndex];\n  return getImpact(before.descriptor.id);\n};\n\nvar isHomeOf = (draggable, destination) => draggable.descriptor.droppableId === destination.descriptor.id;\n\nconst noDisplacedBy = {\n  point: origin,\n  value: 0\n};\nconst emptyGroups = {\n  invisible: {},\n  visible: {},\n  all: []\n};\nconst noImpact = {\n  displaced: emptyGroups,\n  displacedBy: noDisplacedBy,\n  at: null\n};\n\nvar isWithin = (lowerBound, upperBound) => value => lowerBound <= value && value <= upperBound;\n\nvar isPartiallyVisibleThroughFrame = frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    const isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    if (isContained) {\n      return true;\n    }\n    const isPartiallyVisibleVertically = isWithinVertical(subject.top) || isWithinVertical(subject.bottom);\n    const isPartiallyVisibleHorizontally = isWithinHorizontal(subject.left) || isWithinHorizontal(subject.right);\n    const isPartiallyContained = isPartiallyVisibleVertically && isPartiallyVisibleHorizontally;\n    if (isPartiallyContained) {\n      return true;\n    }\n    const isBiggerVertically = subject.top < frame.top && subject.bottom > frame.bottom;\n    const isBiggerHorizontally = subject.left < frame.left && subject.right > frame.right;\n    const isTargetBiggerThanFrame = isBiggerVertically && isBiggerHorizontally;\n    if (isTargetBiggerThanFrame) {\n      return true;\n    }\n    const isTargetBiggerOnOneAxis = isBiggerVertically && isPartiallyVisibleHorizontally || isBiggerHorizontally && isPartiallyVisibleVertically;\n    return isTargetBiggerOnOneAxis;\n  };\n};\n\nvar isTotallyVisibleThroughFrame = frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    const isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    return isContained;\n  };\n};\n\nconst vertical = {\n  direction: 'vertical',\n  line: 'y',\n  crossAxisLine: 'x',\n  start: 'top',\n  end: 'bottom',\n  size: 'height',\n  crossAxisStart: 'left',\n  crossAxisEnd: 'right',\n  crossAxisSize: 'width'\n};\nconst horizontal = {\n  direction: 'horizontal',\n  line: 'x',\n  crossAxisLine: 'y',\n  start: 'left',\n  end: 'right',\n  size: 'width',\n  crossAxisStart: 'top',\n  crossAxisEnd: 'bottom',\n  crossAxisSize: 'height'\n};\n\nvar isTotallyVisibleThroughFrameOnAxis = axis => frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    if (axis === vertical) {\n      return isWithinVertical(subject.top) && isWithinVertical(subject.bottom);\n    }\n    return isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n  };\n};\n\nconst getDroppableDisplaced = (target, destination) => {\n  const displacement = destination.frame ? destination.frame.scroll.diff.displacement : origin;\n  return offsetByPosition(target, displacement);\n};\nconst isVisibleInDroppable = (target, destination, isVisibleThroughFrameFn) => {\n  if (!destination.subject.active) {\n    return false;\n  }\n  return isVisibleThroughFrameFn(destination.subject.active)(target);\n};\nconst isVisibleInViewport = (target, viewport, isVisibleThroughFrameFn) => isVisibleThroughFrameFn(viewport)(target);\nconst isVisible$1 = ({\n  target: toBeDisplaced,\n  destination,\n  viewport,\n  withDroppableDisplacement,\n  isVisibleThroughFrameFn\n}) => {\n  const displacedTarget = withDroppableDisplacement ? getDroppableDisplaced(toBeDisplaced, destination) : toBeDisplaced;\n  return isVisibleInDroppable(displacedTarget, destination, isVisibleThroughFrameFn) && isVisibleInViewport(displacedTarget, viewport, isVisibleThroughFrameFn);\n};\nconst isPartiallyVisible = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isPartiallyVisibleThroughFrame\n});\nconst isTotallyVisible = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isTotallyVisibleThroughFrame\n});\nconst isTotallyVisibleOnAxis = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isTotallyVisibleThroughFrameOnAxis(args.destination.axis)\n});\n\nconst getShouldAnimate = (id, last, forceShouldAnimate) => {\n  if (typeof forceShouldAnimate === 'boolean') {\n    return forceShouldAnimate;\n  }\n  if (!last) {\n    return true;\n  }\n  const {\n    invisible,\n    visible\n  } = last;\n  if (invisible[id]) {\n    return false;\n  }\n  const previous = visible[id];\n  return previous ? previous.shouldAnimate : true;\n};\nfunction getTarget(draggable, displacedBy) {\n  const marginBox = draggable.page.marginBox;\n  const expandBy = {\n    top: displacedBy.point.y,\n    right: 0,\n    bottom: 0,\n    left: displacedBy.point.x\n  };\n  return getRect(expand(marginBox, expandBy));\n}\nfunction getDisplacementGroups({\n  afterDragging,\n  destination,\n  displacedBy,\n  viewport,\n  forceShouldAnimate,\n  last\n}) {\n  return afterDragging.reduce(function process(groups, draggable) {\n    const target = getTarget(draggable, displacedBy);\n    const id = draggable.descriptor.id;\n    groups.all.push(id);\n    const isVisible = isPartiallyVisible({\n      target,\n      destination,\n      viewport,\n      withDroppableDisplacement: true\n    });\n    if (!isVisible) {\n      groups.invisible[draggable.descriptor.id] = true;\n      return groups;\n    }\n    const shouldAnimate = getShouldAnimate(id, last, forceShouldAnimate);\n    const displacement = {\n      draggableId: id,\n      shouldAnimate\n    };\n    groups.visible[id] = displacement;\n    return groups;\n  }, {\n    all: [],\n    visible: {},\n    invisible: {}\n  });\n}\n\nfunction getIndexOfLastItem(draggables, options) {\n  if (!draggables.length) {\n    return 0;\n  }\n  const indexOfLastItem = draggables[draggables.length - 1].descriptor.index;\n  return options.inHomeList ? indexOfLastItem : indexOfLastItem + 1;\n}\nfunction goAtEnd({\n  insideDestination,\n  inHomeList,\n  displacedBy,\n  destination\n}) {\n  const newIndex = getIndexOfLastItem(insideDestination, {\n    inHomeList\n  });\n  return {\n    displaced: emptyGroups,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index: newIndex\n      }\n    }\n  };\n}\nfunction calculateReorderImpact({\n  draggable,\n  insideDestination,\n  destination,\n  viewport,\n  displacedBy,\n  last,\n  index,\n  forceShouldAnimate\n}) {\n  const inHomeList = isHomeOf(draggable, destination);\n  if (index == null) {\n    return goAtEnd({\n      insideDestination,\n      inHomeList,\n      displacedBy,\n      destination\n    });\n  }\n  const match = insideDestination.find(item => item.descriptor.index === index);\n  if (!match) {\n    return goAtEnd({\n      insideDestination,\n      inHomeList,\n      displacedBy,\n      destination\n    });\n  }\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const sliceFrom = insideDestination.indexOf(match);\n  const impacted = withoutDragging.slice(sliceFrom);\n  const displaced = getDisplacementGroups({\n    afterDragging: impacted,\n    destination,\n    displacedBy,\n    last,\n    viewport: viewport.frame,\n    forceShouldAnimate\n  });\n  return {\n    displaced,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index\n      }\n    }\n  };\n}\n\nfunction didStartAfterCritical(draggableId, afterCritical) {\n  return Boolean(afterCritical.effected[draggableId]);\n}\n\nvar fromCombine = ({\n  isMovingForward,\n  destination,\n  draggables,\n  combine,\n  afterCritical\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const combineId = combine.draggableId;\n  const combineWith = draggables[combineId];\n  const combineWithIndex = combineWith.descriptor.index;\n  const didCombineWithStartAfterCritical = didStartAfterCritical(combineId, afterCritical);\n  if (didCombineWithStartAfterCritical) {\n    if (isMovingForward) {\n      return combineWithIndex;\n    }\n    return combineWithIndex - 1;\n  }\n  if (isMovingForward) {\n    return combineWithIndex + 1;\n  }\n  return combineWithIndex;\n};\n\nvar fromReorder = ({\n  isMovingForward,\n  isInHomeList,\n  insideDestination,\n  location\n}) => {\n  if (!insideDestination.length) {\n    return null;\n  }\n  const currentIndex = location.index;\n  const proposedIndex = isMovingForward ? currentIndex + 1 : currentIndex - 1;\n  const firstIndex = insideDestination[0].descriptor.index;\n  const lastIndex = insideDestination[insideDestination.length - 1].descriptor.index;\n  const upperBound = isInHomeList ? lastIndex : lastIndex + 1;\n  if (proposedIndex < firstIndex) {\n    return null;\n  }\n  if (proposedIndex > upperBound) {\n    return null;\n  }\n  return proposedIndex;\n};\n\nvar moveToNextIndex = ({\n  isMovingForward,\n  isInHomeList,\n  draggable,\n  draggables,\n  destination,\n  insideDestination,\n  previousImpact,\n  viewport,\n  afterCritical\n}) => {\n  const wasAt = previousImpact.at;\n  !wasAt ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot move in direction without previous impact location') : invariant() : void 0;\n  if (wasAt.type === 'REORDER') {\n    const newIndex = fromReorder({\n      isMovingForward,\n      isInHomeList,\n      location: wasAt.destination,\n      insideDestination\n    });\n    if (newIndex == null) {\n      return null;\n    }\n    return calculateReorderImpact({\n      draggable,\n      insideDestination,\n      destination,\n      viewport,\n      last: previousImpact.displaced,\n      displacedBy: previousImpact.displacedBy,\n      index: newIndex\n    });\n  }\n  const newIndex = fromCombine({\n    isMovingForward,\n    destination,\n    displaced: previousImpact.displaced,\n    draggables,\n    combine: wasAt.combine,\n    afterCritical\n  });\n  if (newIndex == null) {\n    return null;\n  }\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    last: previousImpact.displaced,\n    displacedBy: previousImpact.displacedBy,\n    index: newIndex\n  });\n};\n\nvar getCombinedItemDisplacement = ({\n  displaced,\n  afterCritical,\n  combineWith,\n  displacedBy\n}) => {\n  const isDisplaced = Boolean(displaced.visible[combineWith] || displaced.invisible[combineWith]);\n  if (didStartAfterCritical(combineWith, afterCritical)) {\n    return isDisplaced ? origin : negate(displacedBy.point);\n  }\n  return isDisplaced ? displacedBy.point : origin;\n};\n\nvar whenCombining = ({\n  afterCritical,\n  impact,\n  draggables\n}) => {\n  const combine = tryGetCombine(impact);\n  !combine ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  const combineWith = combine.draggableId;\n  const center = draggables[combineWith].page.borderBox.center;\n  const displaceBy = getCombinedItemDisplacement({\n    displaced: impact.displaced,\n    afterCritical,\n    combineWith,\n    displacedBy: impact.displacedBy\n  });\n  return add(center, displaceBy);\n};\n\nconst distanceFromStartToBorderBoxCenter = (axis, box) => box.margin[axis.start] + box.borderBox[axis.size] / 2;\nconst distanceFromEndToBorderBoxCenter = (axis, box) => box.margin[axis.end] + box.borderBox[axis.size] / 2;\nconst getCrossAxisBorderBoxCenter = (axis, target, isMoving) => target[axis.crossAxisStart] + isMoving.margin[axis.crossAxisStart] + isMoving.borderBox[axis.crossAxisSize] / 2;\nconst goAfter = ({\n  axis,\n  moveRelativeTo,\n  isMoving\n}) => patch(axis.line, moveRelativeTo.marginBox[axis.end] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\nconst goBefore = ({\n  axis,\n  moveRelativeTo,\n  isMoving\n}) => patch(axis.line, moveRelativeTo.marginBox[axis.start] - distanceFromEndToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\nconst goIntoStart = ({\n  axis,\n  moveInto,\n  isMoving\n}) => patch(axis.line, moveInto.contentBox[axis.start] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveInto.contentBox, isMoving));\n\nvar whenReordering = ({\n  impact,\n  draggable,\n  draggables,\n  droppable,\n  afterCritical\n}) => {\n  const insideDestination = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  const draggablePage = draggable.page;\n  const axis = droppable.axis;\n  if (!insideDestination.length) {\n    return goIntoStart({\n      axis,\n      moveInto: droppable.page,\n      isMoving: draggablePage\n    });\n  }\n  const {\n    displaced,\n    displacedBy\n  } = impact;\n  const closestAfter = displaced.all[0];\n  if (closestAfter) {\n    const closest = draggables[closestAfter];\n    if (didStartAfterCritical(closestAfter, afterCritical)) {\n      return goBefore({\n        axis,\n        moveRelativeTo: closest.page,\n        isMoving: draggablePage\n      });\n    }\n    const withDisplacement = offset(closest.page, displacedBy.point);\n    return goBefore({\n      axis,\n      moveRelativeTo: withDisplacement,\n      isMoving: draggablePage\n    });\n  }\n  const last = insideDestination[insideDestination.length - 1];\n  if (last.descriptor.id === draggable.descriptor.id) {\n    return draggablePage.borderBox.center;\n  }\n  if (didStartAfterCritical(last.descriptor.id, afterCritical)) {\n    const page = offset(last.page, negate(afterCritical.displacedBy.point));\n    return goAfter({\n      axis,\n      moveRelativeTo: page,\n      isMoving: draggablePage\n    });\n  }\n  return goAfter({\n    axis,\n    moveRelativeTo: last.page,\n    isMoving: draggablePage\n  });\n};\n\nvar withDroppableDisplacement = (droppable, point) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return point;\n  }\n  return add(point, frame.scroll.diff.displacement);\n};\n\nconst getResultWithoutDroppableDisplacement = ({\n  impact,\n  draggable,\n  droppable,\n  draggables,\n  afterCritical\n}) => {\n  const original = draggable.page.borderBox.center;\n  const at = impact.at;\n  if (!droppable) {\n    return original;\n  }\n  if (!at) {\n    return original;\n  }\n  if (at.type === 'REORDER') {\n    return whenReordering({\n      impact,\n      draggable,\n      draggables,\n      droppable,\n      afterCritical\n    });\n  }\n  return whenCombining({\n    impact,\n    draggables,\n    afterCritical\n  });\n};\nvar getPageBorderBoxCenterFromImpact = args => {\n  const withoutDisplacement = getResultWithoutDroppableDisplacement(args);\n  const droppable = args.droppable;\n  const withDisplacement = droppable ? withDroppableDisplacement(droppable, withoutDisplacement) : withoutDisplacement;\n  return withDisplacement;\n};\n\nvar scrollViewport = (viewport, newScroll) => {\n  const diff = subtract(newScroll, viewport.scroll.initial);\n  const displacement = negate(diff);\n  const frame = getRect({\n    top: newScroll.y,\n    bottom: newScroll.y + viewport.frame.height,\n    left: newScroll.x,\n    right: newScroll.x + viewport.frame.width\n  });\n  const updated = {\n    frame,\n    scroll: {\n      initial: viewport.scroll.initial,\n      max: viewport.scroll.max,\n      current: newScroll,\n      diff: {\n        value: diff,\n        displacement\n      }\n    }\n  };\n  return updated;\n};\n\nfunction getDraggables$1(ids, draggables) {\n  return ids.map(id => draggables[id]);\n}\nfunction tryGetVisible(id, groups) {\n  for (let i = 0; i < groups.length; i++) {\n    const displacement = groups[i].visible[id];\n    if (displacement) {\n      return displacement;\n    }\n  }\n  return null;\n}\nvar speculativelyIncrease = ({\n  impact,\n  viewport,\n  destination,\n  draggables,\n  maxScrollChange\n}) => {\n  const scrolledViewport = scrollViewport(viewport, add(viewport.scroll.current, maxScrollChange));\n  const scrolledDroppable = destination.frame ? scrollDroppable(destination, add(destination.frame.scroll.current, maxScrollChange)) : destination;\n  const last = impact.displaced;\n  const withViewportScroll = getDisplacementGroups({\n    afterDragging: getDraggables$1(last.all, draggables),\n    destination,\n    displacedBy: impact.displacedBy,\n    viewport: scrolledViewport.frame,\n    last,\n    forceShouldAnimate: false\n  });\n  const withDroppableScroll = getDisplacementGroups({\n    afterDragging: getDraggables$1(last.all, draggables),\n    destination: scrolledDroppable,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    last,\n    forceShouldAnimate: false\n  });\n  const invisible = {};\n  const visible = {};\n  const groups = [last, withViewportScroll, withDroppableScroll];\n  last.all.forEach(id => {\n    const displacement = tryGetVisible(id, groups);\n    if (displacement) {\n      visible[id] = displacement;\n      return;\n    }\n    invisible[id] = true;\n  });\n  const newImpact = {\n    ...impact,\n    displaced: {\n      all: last.all,\n      invisible,\n      visible\n    }\n  };\n  return newImpact;\n};\n\nvar withViewportDisplacement = (viewport, point) => add(viewport.scroll.diff.displacement, point);\n\nvar getClientFromPageBorderBoxCenter = ({\n  pageBorderBoxCenter,\n  draggable,\n  viewport\n}) => {\n  const withoutPageScrollChange = withViewportDisplacement(viewport, pageBorderBoxCenter);\n  const offset = subtract(withoutPageScrollChange, draggable.page.borderBox.center);\n  return add(draggable.client.borderBox.center, offset);\n};\n\nvar isTotallyVisibleInNewLocation = ({\n  draggable,\n  destination,\n  newPageBorderBoxCenter,\n  viewport,\n  withDroppableDisplacement,\n  onlyOnMainAxis = false\n}) => {\n  const changeNeeded = subtract(newPageBorderBoxCenter, draggable.page.borderBox.center);\n  const shifted = offsetByPosition(draggable.page.borderBox, changeNeeded);\n  const args = {\n    target: shifted,\n    destination,\n    withDroppableDisplacement,\n    viewport\n  };\n  return onlyOnMainAxis ? isTotallyVisibleOnAxis(args) : isTotallyVisible(args);\n};\n\nvar moveToNextPlace = ({\n  isMovingForward,\n  draggable,\n  destination,\n  draggables,\n  previousImpact,\n  viewport,\n  previousPageBorderBoxCenter,\n  previousClientSelection,\n  afterCritical\n}) => {\n  if (!destination.isEnabled) {\n    return null;\n  }\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const isInHomeList = isHomeOf(draggable, destination);\n  const impact = moveToNextCombine({\n    isMovingForward,\n    draggable,\n    destination,\n    insideDestination,\n    previousImpact\n  }) || moveToNextIndex({\n    isMovingForward,\n    isInHomeList,\n    draggable,\n    draggables,\n    destination,\n    insideDestination,\n    previousImpact,\n    viewport,\n    afterCritical\n  });\n  if (!impact) {\n    return null;\n  }\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    afterCritical\n  });\n  const isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n    draggable,\n    destination,\n    newPageBorderBoxCenter: pageBorderBoxCenter,\n    viewport: viewport.frame,\n    withDroppableDisplacement: false,\n    onlyOnMainAxis: true\n  });\n  if (isVisibleInNewLocation) {\n    const clientSelection = getClientFromPageBorderBoxCenter({\n      pageBorderBoxCenter,\n      draggable,\n      viewport\n    });\n    return {\n      clientSelection,\n      impact,\n      scrollJumpRequest: null\n    };\n  }\n  const distance = subtract(pageBorderBoxCenter, previousPageBorderBoxCenter);\n  const cautious = speculativelyIncrease({\n    impact,\n    viewport,\n    destination,\n    draggables,\n    maxScrollChange: distance\n  });\n  return {\n    clientSelection: previousClientSelection,\n    impact: cautious,\n    scrollJumpRequest: distance\n  };\n};\n\nconst getKnownActive = droppable => {\n  const rect = droppable.subject.active;\n  !rect ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get clipped area from droppable') : invariant() : void 0;\n  return rect;\n};\nvar getBestCrossAxisDroppable = ({\n  isMovingForward,\n  pageBorderBoxCenter,\n  source,\n  droppables,\n  viewport\n}) => {\n  const active = source.subject.active;\n  if (!active) {\n    return null;\n  }\n  const axis = source.axis;\n  const isBetweenSourceClipped = isWithin(active[axis.start], active[axis.end]);\n  const candidates = toDroppableList(droppables).filter(droppable => droppable !== source).filter(droppable => droppable.isEnabled).filter(droppable => Boolean(droppable.subject.active)).filter(droppable => isPartiallyVisibleThroughFrame(viewport.frame)(getKnownActive(droppable))).filter(droppable => {\n    const activeOfTarget = getKnownActive(droppable);\n    if (isMovingForward) {\n      return active[axis.crossAxisEnd] < activeOfTarget[axis.crossAxisEnd];\n    }\n    return activeOfTarget[axis.crossAxisStart] < active[axis.crossAxisStart];\n  }).filter(droppable => {\n    const activeOfTarget = getKnownActive(droppable);\n    const isBetweenDestinationClipped = isWithin(activeOfTarget[axis.start], activeOfTarget[axis.end]);\n    return isBetweenSourceClipped(activeOfTarget[axis.start]) || isBetweenSourceClipped(activeOfTarget[axis.end]) || isBetweenDestinationClipped(active[axis.start]) || isBetweenDestinationClipped(active[axis.end]);\n  }).sort((a, b) => {\n    const first = getKnownActive(a)[axis.crossAxisStart];\n    const second = getKnownActive(b)[axis.crossAxisStart];\n    if (isMovingForward) {\n      return first - second;\n    }\n    return second - first;\n  }).filter((droppable, index, array) => getKnownActive(droppable)[axis.crossAxisStart] === getKnownActive(array[0])[axis.crossAxisStart]);\n  if (!candidates.length) {\n    return null;\n  }\n  if (candidates.length === 1) {\n    return candidates[0];\n  }\n  const contains = candidates.filter(droppable => {\n    const isWithinDroppable = isWithin(getKnownActive(droppable)[axis.start], getKnownActive(droppable)[axis.end]);\n    return isWithinDroppable(pageBorderBoxCenter[axis.line]);\n  });\n  if (contains.length === 1) {\n    return contains[0];\n  }\n  if (contains.length > 1) {\n    return contains.sort((a, b) => getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start])[0];\n  }\n  return candidates.sort((a, b) => {\n    const first = closest$1(pageBorderBoxCenter, getCorners(getKnownActive(a)));\n    const second = closest$1(pageBorderBoxCenter, getCorners(getKnownActive(b)));\n    if (first !== second) {\n      return first - second;\n    }\n    return getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start];\n  })[0];\n};\n\nconst getCurrentPageBorderBoxCenter = (draggable, afterCritical) => {\n  const original = draggable.page.borderBox.center;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? subtract(original, afterCritical.displacedBy.point) : original;\n};\nconst getCurrentPageBorderBox = (draggable, afterCritical) => {\n  const original = draggable.page.borderBox;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? offsetByPosition(original, negate(afterCritical.displacedBy.point)) : original;\n};\n\nvar getClosestDraggable = ({\n  pageBorderBoxCenter,\n  viewport,\n  destination,\n  insideDestination,\n  afterCritical\n}) => {\n  const sorted = insideDestination.filter(draggable => isTotallyVisible({\n    target: getCurrentPageBorderBox(draggable, afterCritical),\n    destination,\n    viewport: viewport.frame,\n    withDroppableDisplacement: true\n  })).sort((a, b) => {\n    const distanceToA = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(a, afterCritical)));\n    const distanceToB = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(b, afterCritical)));\n    if (distanceToA < distanceToB) {\n      return -1;\n    }\n    if (distanceToB < distanceToA) {\n      return 1;\n    }\n    return a.descriptor.index - b.descriptor.index;\n  });\n  return sorted[0] || null;\n};\n\nvar getDisplacedBy = memoizeOne(function getDisplacedBy(axis, displaceBy) {\n  const displacement = displaceBy[axis.line];\n  return {\n    value: displacement,\n    point: patch(axis.line, displacement)\n  };\n});\n\nconst getRequiredGrowthForPlaceholder = (droppable, placeholderSize, draggables) => {\n  const axis = droppable.axis;\n  if (droppable.descriptor.mode === 'virtual') {\n    return patch(axis.line, placeholderSize[axis.line]);\n  }\n  const availableSpace = droppable.subject.page.contentBox[axis.size];\n  const insideDroppable = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  const spaceUsed = insideDroppable.reduce((sum, dimension) => sum + dimension.client.marginBox[axis.size], 0);\n  const requiredSpace = spaceUsed + placeholderSize[axis.line];\n  const needsToGrowBy = requiredSpace - availableSpace;\n  if (needsToGrowBy <= 0) {\n    return null;\n  }\n  return patch(axis.line, needsToGrowBy);\n};\nconst withMaxScroll = (frame, max) => ({\n  ...frame,\n  scroll: {\n    ...frame.scroll,\n    max\n  }\n});\nconst addPlaceholder = (droppable, draggable, draggables) => {\n  const frame = droppable.frame;\n  !!isHomeOf(draggable, droppable) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should not add placeholder space to home list') : invariant() : void 0;\n  !!droppable.subject.withPlaceholder ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot add placeholder size to a subject when it already has one') : invariant() : void 0;\n  const placeholderSize = getDisplacedBy(droppable.axis, draggable.displaceBy).point;\n  const requiredGrowth = getRequiredGrowthForPlaceholder(droppable, placeholderSize, draggables);\n  const added = {\n    placeholderSize,\n    increasedBy: requiredGrowth,\n    oldFrameMaxScroll: droppable.frame ? droppable.frame.scroll.max : null\n  };\n  if (!frame) {\n    const subject = getSubject({\n      page: droppable.subject.page,\n      withPlaceholder: added,\n      axis: droppable.axis,\n      frame: droppable.frame\n    });\n    return {\n      ...droppable,\n      subject\n    };\n  }\n  const maxScroll = requiredGrowth ? add(frame.scroll.max, requiredGrowth) : frame.scroll.max;\n  const newFrame = withMaxScroll(frame, maxScroll);\n  const subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: added,\n    axis: droppable.axis,\n    frame: newFrame\n  });\n  return {\n    ...droppable,\n    subject,\n    frame: newFrame\n  };\n};\nconst removePlaceholder = droppable => {\n  const added = droppable.subject.withPlaceholder;\n  !added ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot remove placeholder form subject when there was none') : invariant() : void 0;\n  const frame = droppable.frame;\n  if (!frame) {\n    const subject = getSubject({\n      page: droppable.subject.page,\n      axis: droppable.axis,\n      frame: null,\n      withPlaceholder: null\n    });\n    return {\n      ...droppable,\n      subject\n    };\n  }\n  const oldMaxScroll = added.oldFrameMaxScroll;\n  !oldMaxScroll ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected droppable with frame to have old max frame scroll when removing placeholder') : invariant() : void 0;\n  const newFrame = withMaxScroll(frame, oldMaxScroll);\n  const subject = getSubject({\n    page: droppable.subject.page,\n    axis: droppable.axis,\n    frame: newFrame,\n    withPlaceholder: null\n  });\n  return {\n    ...droppable,\n    subject,\n    frame: newFrame\n  };\n};\n\nvar moveToNewDroppable = ({\n  previousPageBorderBoxCenter,\n  moveRelativeTo,\n  insideDestination,\n  draggable,\n  draggables,\n  destination,\n  viewport,\n  afterCritical\n}) => {\n  if (!moveRelativeTo) {\n    if (insideDestination.length) {\n      return null;\n    }\n    const proposed = {\n      displaced: emptyGroups,\n      displacedBy: noDisplacedBy,\n      at: {\n        type: 'REORDER',\n        destination: {\n          droppableId: destination.descriptor.id,\n          index: 0\n        }\n      }\n    };\n    const proposedPageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n      impact: proposed,\n      draggable,\n      droppable: destination,\n      draggables,\n      afterCritical\n    });\n    const withPlaceholder = isHomeOf(draggable, destination) ? destination : addPlaceholder(destination, draggable, draggables);\n    const isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n      draggable,\n      destination: withPlaceholder,\n      newPageBorderBoxCenter: proposedPageBorderBoxCenter,\n      viewport: viewport.frame,\n      withDroppableDisplacement: false,\n      onlyOnMainAxis: true\n    });\n    return isVisibleInNewLocation ? proposed : null;\n  }\n  const isGoingBeforeTarget = Boolean(previousPageBorderBoxCenter[destination.axis.line] <= moveRelativeTo.page.borderBox.center[destination.axis.line]);\n  const proposedIndex = (() => {\n    const relativeTo = moveRelativeTo.descriptor.index;\n    if (moveRelativeTo.descriptor.id === draggable.descriptor.id) {\n      return relativeTo;\n    }\n    if (isGoingBeforeTarget) {\n      return relativeTo;\n    }\n    return relativeTo + 1;\n  })();\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    displacedBy,\n    last: emptyGroups,\n    index: proposedIndex\n  });\n};\n\nvar moveCrossAxis = ({\n  isMovingForward,\n  previousPageBorderBoxCenter,\n  draggable,\n  isOver,\n  draggables,\n  droppables,\n  viewport,\n  afterCritical\n}) => {\n  const destination = getBestCrossAxisDroppable({\n    isMovingForward,\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    source: isOver,\n    droppables,\n    viewport\n  });\n  if (!destination) {\n    return null;\n  }\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const moveRelativeTo = getClosestDraggable({\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    viewport,\n    destination,\n    insideDestination,\n    afterCritical\n  });\n  const impact = moveToNewDroppable({\n    previousPageBorderBoxCenter,\n    destination,\n    draggable,\n    draggables,\n    moveRelativeTo,\n    insideDestination,\n    viewport,\n    afterCritical\n  });\n  if (!impact) {\n    return null;\n  }\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    afterCritical\n  });\n  const clientSelection = getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter,\n    draggable,\n    viewport\n  });\n  return {\n    clientSelection,\n    impact,\n    scrollJumpRequest: null\n  };\n};\n\nvar whatIsDraggedOver = impact => {\n  const at = impact.at;\n  if (!at) {\n    return null;\n  }\n  if (at.type === 'REORDER') {\n    return at.destination.droppableId;\n  }\n  return at.combine.droppableId;\n};\n\nconst getDroppableOver$1 = (impact, droppables) => {\n  const id = whatIsDraggedOver(impact);\n  return id ? droppables[id] : null;\n};\nvar moveInDirection = ({\n  state,\n  type\n}) => {\n  const isActuallyOver = getDroppableOver$1(state.impact, state.dimensions.droppables);\n  const isMainAxisMovementAllowed = Boolean(isActuallyOver);\n  const home = state.dimensions.droppables[state.critical.droppable.id];\n  const isOver = isActuallyOver || home;\n  const direction = isOver.axis.direction;\n  const isMovingOnMainAxis = direction === 'vertical' && (type === 'MOVE_UP' || type === 'MOVE_DOWN') || direction === 'horizontal' && (type === 'MOVE_LEFT' || type === 'MOVE_RIGHT');\n  if (isMovingOnMainAxis && !isMainAxisMovementAllowed) {\n    return null;\n  }\n  const isMovingForward = type === 'MOVE_DOWN' || type === 'MOVE_RIGHT';\n  const draggable = state.dimensions.draggables[state.critical.draggable.id];\n  const previousPageBorderBoxCenter = state.current.page.borderBoxCenter;\n  const {\n    draggables,\n    droppables\n  } = state.dimensions;\n  return isMovingOnMainAxis ? moveToNextPlace({\n    isMovingForward,\n    previousPageBorderBoxCenter,\n    draggable,\n    destination: isOver,\n    draggables,\n    viewport: state.viewport,\n    previousClientSelection: state.current.client.selection,\n    previousImpact: state.impact,\n    afterCritical: state.afterCritical\n  }) : moveCrossAxis({\n    isMovingForward,\n    previousPageBorderBoxCenter,\n    draggable,\n    isOver,\n    draggables,\n    droppables,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n};\n\nfunction isMovementAllowed(state) {\n  return state.phase === 'DRAGGING' || state.phase === 'COLLECTING';\n}\n\nfunction isPositionInFrame(frame) {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function run(point) {\n    return isWithinVertical(point.y) && isWithinHorizontal(point.x);\n  };\n}\n\nfunction getHasOverlap(first, second) {\n  return first.left < second.right && first.right > second.left && first.top < second.bottom && first.bottom > second.top;\n}\nfunction getFurthestAway({\n  pageBorderBox,\n  draggable,\n  candidates\n}) {\n  const startCenter = draggable.page.borderBox.center;\n  const sorted = candidates.map(candidate => {\n    const axis = candidate.axis;\n    const target = patch(candidate.axis.line, pageBorderBox.center[axis.line], candidate.page.borderBox.center[axis.crossAxisLine]);\n    return {\n      id: candidate.descriptor.id,\n      distance: distance(startCenter, target)\n    };\n  }).sort((a, b) => b.distance - a.distance);\n  return sorted[0] ? sorted[0].id : null;\n}\nfunction getDroppableOver({\n  pageBorderBox,\n  draggable,\n  droppables\n}) {\n  const candidates = toDroppableList(droppables).filter(item => {\n    if (!item.isEnabled) {\n      return false;\n    }\n    const active = item.subject.active;\n    if (!active) {\n      return false;\n    }\n    if (!getHasOverlap(pageBorderBox, active)) {\n      return false;\n    }\n    if (isPositionInFrame(active)(pageBorderBox.center)) {\n      return true;\n    }\n    const axis = item.axis;\n    const childCenter = active.center[axis.crossAxisLine];\n    const crossAxisStart = pageBorderBox[axis.crossAxisStart];\n    const crossAxisEnd = pageBorderBox[axis.crossAxisEnd];\n    const isContained = isWithin(active[axis.crossAxisStart], active[axis.crossAxisEnd]);\n    const isStartContained = isContained(crossAxisStart);\n    const isEndContained = isContained(crossAxisEnd);\n    if (!isStartContained && !isEndContained) {\n      return true;\n    }\n    if (isStartContained) {\n      return crossAxisStart < childCenter;\n    }\n    return crossAxisEnd > childCenter;\n  });\n  if (!candidates.length) {\n    return null;\n  }\n  if (candidates.length === 1) {\n    return candidates[0].descriptor.id;\n  }\n  return getFurthestAway({\n    pageBorderBox,\n    draggable,\n    candidates\n  });\n}\n\nconst offsetRectByPosition = (rect, point) => getRect(offsetByPosition(rect, point));\n\nvar withDroppableScroll = (droppable, area) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return area;\n  }\n  return offsetRectByPosition(area, frame.scroll.diff.value);\n};\n\nfunction getIsDisplaced({\n  displaced,\n  id\n}) {\n  return Boolean(displaced.visible[id] || displaced.invisible[id]);\n}\n\nfunction atIndex({\n  draggable,\n  closest,\n  inHomeList\n}) {\n  if (!closest) {\n    return null;\n  }\n  if (!inHomeList) {\n    return closest.descriptor.index;\n  }\n  if (closest.descriptor.index > draggable.descriptor.index) {\n    return closest.descriptor.index - 1;\n  }\n  return closest.descriptor.index;\n}\nvar getReorderImpact = ({\n  pageBorderBoxWithDroppableScroll: targetRect,\n  draggable,\n  destination,\n  insideDestination,\n  last,\n  viewport,\n  afterCritical\n}) => {\n  const axis = destination.axis;\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  const displacement = displacedBy.value;\n  const targetStart = targetRect[axis.start];\n  const targetEnd = targetRect[axis.end];\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const closest = withoutDragging.find(child => {\n    const id = child.descriptor.id;\n    const childCenter = child.page.borderBox.center[axis.line];\n    const didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    const isDisplaced = getIsDisplaced({\n      displaced: last,\n      id\n    });\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd <= childCenter;\n      }\n      return targetStart < childCenter - displacement;\n    }\n    if (isDisplaced) {\n      return targetEnd <= childCenter + displacement;\n    }\n    return targetStart < childCenter;\n  }) || null;\n  const newIndex = atIndex({\n    draggable,\n    closest,\n    inHomeList: isHomeOf(draggable, destination)\n  });\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    last,\n    displacedBy,\n    index: newIndex\n  });\n};\n\nconst combineThresholdDivisor = 4;\nvar getCombineImpact = ({\n  draggable,\n  pageBorderBoxWithDroppableScroll: targetRect,\n  previousImpact,\n  destination,\n  insideDestination,\n  afterCritical\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const axis = destination.axis;\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  const displacement = displacedBy.value;\n  const targetStart = targetRect[axis.start];\n  const targetEnd = targetRect[axis.end];\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const combineWith = withoutDragging.find(child => {\n    const id = child.descriptor.id;\n    const childRect = child.page.borderBox;\n    const childSize = childRect[axis.size];\n    const threshold = childSize / combineThresholdDivisor;\n    const didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    const isDisplaced = getIsDisplaced({\n      displaced: previousImpact.displaced,\n      id\n    });\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd > childRect[axis.start] + threshold && targetEnd < childRect[axis.end] - threshold;\n      }\n      return targetStart > childRect[axis.start] - displacement + threshold && targetStart < childRect[axis.end] - displacement - threshold;\n    }\n    if (isDisplaced) {\n      return targetEnd > childRect[axis.start] + displacement + threshold && targetEnd < childRect[axis.end] + displacement - threshold;\n    }\n    return targetStart > childRect[axis.start] + threshold && targetStart < childRect[axis.end] - threshold;\n  });\n  if (!combineWith) {\n    return null;\n  }\n  const impact = {\n    displacedBy,\n    displaced: previousImpact.displaced,\n    at: {\n      type: 'COMBINE',\n      combine: {\n        draggableId: combineWith.descriptor.id,\n        droppableId: destination.descriptor.id\n      }\n    }\n  };\n  return impact;\n};\n\nvar getDragImpact = ({\n  pageOffset,\n  draggable,\n  draggables,\n  droppables,\n  previousImpact,\n  viewport,\n  afterCritical\n}) => {\n  const pageBorderBox = offsetRectByPosition(draggable.page.borderBox, pageOffset);\n  const destinationId = getDroppableOver({\n    pageBorderBox,\n    draggable,\n    droppables\n  });\n  if (!destinationId) {\n    return noImpact;\n  }\n  const destination = droppables[destinationId];\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const pageBorderBoxWithDroppableScroll = withDroppableScroll(destination, pageBorderBox);\n  return getCombineImpact({\n    pageBorderBoxWithDroppableScroll,\n    draggable,\n    previousImpact,\n    destination,\n    insideDestination,\n    afterCritical\n  }) || getReorderImpact({\n    pageBorderBoxWithDroppableScroll,\n    draggable,\n    destination,\n    insideDestination,\n    last: previousImpact.displaced,\n    viewport,\n    afterCritical\n  });\n};\n\nvar patchDroppableMap = (droppables, updated) => ({\n  ...droppables,\n  [updated.descriptor.id]: updated\n});\n\nconst clearUnusedPlaceholder = ({\n  previousImpact,\n  impact,\n  droppables\n}) => {\n  const last = whatIsDraggedOver(previousImpact);\n  const now = whatIsDraggedOver(impact);\n  if (!last) {\n    return droppables;\n  }\n  if (last === now) {\n    return droppables;\n  }\n  const lastDroppable = droppables[last];\n  if (!lastDroppable.subject.withPlaceholder) {\n    return droppables;\n  }\n  const updated = removePlaceholder(lastDroppable);\n  return patchDroppableMap(droppables, updated);\n};\nvar recomputePlaceholders = ({\n  draggable,\n  draggables,\n  droppables,\n  previousImpact,\n  impact\n}) => {\n  const cleaned = clearUnusedPlaceholder({\n    previousImpact,\n    impact,\n    droppables\n  });\n  const isOver = whatIsDraggedOver(impact);\n  if (!isOver) {\n    return cleaned;\n  }\n  const droppable = droppables[isOver];\n  if (isHomeOf(draggable, droppable)) {\n    return cleaned;\n  }\n  if (droppable.subject.withPlaceholder) {\n    return cleaned;\n  }\n  const patched = addPlaceholder(droppable, draggable, draggables);\n  return patchDroppableMap(cleaned, patched);\n};\n\nvar update = ({\n  state,\n  clientSelection: forcedClientSelection,\n  dimensions: forcedDimensions,\n  viewport: forcedViewport,\n  impact: forcedImpact,\n  scrollJumpRequest\n}) => {\n  const viewport = forcedViewport || state.viewport;\n  const dimensions = forcedDimensions || state.dimensions;\n  const clientSelection = forcedClientSelection || state.current.client.selection;\n  const offset = subtract(clientSelection, state.initial.client.selection);\n  const client = {\n    offset,\n    selection: clientSelection,\n    borderBoxCenter: add(state.initial.client.borderBoxCenter, offset)\n  };\n  const page = {\n    selection: add(client.selection, viewport.scroll.current),\n    borderBoxCenter: add(client.borderBoxCenter, viewport.scroll.current),\n    offset: add(client.offset, viewport.scroll.diff.value)\n  };\n  const current = {\n    client,\n    page\n  };\n  if (state.phase === 'COLLECTING') {\n    return {\n      ...state,\n      dimensions,\n      viewport,\n      current\n    };\n  }\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const newImpact = forcedImpact || getDragImpact({\n    pageOffset: page.offset,\n    draggable,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact: state.impact,\n    viewport,\n    afterCritical: state.afterCritical\n  });\n  const withUpdatedPlaceholders = recomputePlaceholders({\n    draggable,\n    impact: newImpact,\n    previousImpact: state.impact,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables\n  });\n  const result = {\n    ...state,\n    current,\n    dimensions: {\n      draggables: dimensions.draggables,\n      droppables: withUpdatedPlaceholders\n    },\n    impact: newImpact,\n    viewport,\n    scrollJumpRequest: scrollJumpRequest || null,\n    forceShouldAnimate: scrollJumpRequest ? false : null\n  };\n  return result;\n};\n\nfunction getDraggables(ids, draggables) {\n  return ids.map(id => draggables[id]);\n}\nvar recompute = ({\n  impact,\n  viewport,\n  draggables,\n  destination,\n  forceShouldAnimate\n}) => {\n  const last = impact.displaced;\n  const afterDragging = getDraggables(last.all, draggables);\n  const displaced = getDisplacementGroups({\n    afterDragging,\n    destination,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    forceShouldAnimate,\n    last\n  });\n  return {\n    ...impact,\n    displaced\n  };\n};\n\nvar getClientBorderBoxCenter = ({\n  impact,\n  draggable,\n  droppable,\n  draggables,\n  viewport,\n  afterCritical\n}) => {\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    draggables,\n    droppable,\n    afterCritical\n  });\n  return getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter,\n    draggable,\n    viewport\n  });\n};\n\nvar refreshSnap = ({\n  state,\n  dimensions: forcedDimensions,\n  viewport: forcedViewport\n}) => {\n  !(state.movementMode === 'SNAP') ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  const needsVisibilityCheck = state.impact;\n  const viewport = forcedViewport || state.viewport;\n  const dimensions = forcedDimensions || state.dimensions;\n  const {\n    draggables,\n    droppables\n  } = dimensions;\n  const draggable = draggables[state.critical.draggable.id];\n  const isOver = whatIsDraggedOver(needsVisibilityCheck);\n  !isOver ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must be over a destination in SNAP movement mode') : invariant() : void 0;\n  const destination = droppables[isOver];\n  const impact = recompute({\n    impact: needsVisibilityCheck,\n    viewport,\n    destination,\n    draggables\n  });\n  const clientSelection = getClientBorderBoxCenter({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    viewport,\n    afterCritical: state.afterCritical\n  });\n  return update({\n    impact,\n    clientSelection,\n    state,\n    dimensions,\n    viewport\n  });\n};\n\nvar getHomeLocation = descriptor => ({\n  index: descriptor.index,\n  droppableId: descriptor.droppableId\n});\n\nvar getLiftEffect = ({\n  draggable,\n  home,\n  draggables,\n  viewport\n}) => {\n  const displacedBy = getDisplacedBy(home.axis, draggable.displaceBy);\n  const insideHome = getDraggablesInsideDroppable(home.descriptor.id, draggables);\n  const rawIndex = insideHome.indexOf(draggable);\n  !(rawIndex !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected draggable to be inside home list') : invariant() : void 0;\n  const afterDragging = insideHome.slice(rawIndex + 1);\n  const effected = afterDragging.reduce((previous, item) => {\n    previous[item.descriptor.id] = true;\n    return previous;\n  }, {});\n  const afterCritical = {\n    inVirtualList: home.descriptor.mode === 'virtual',\n    displacedBy,\n    effected\n  };\n  const displaced = getDisplacementGroups({\n    afterDragging,\n    destination: home,\n    displacedBy,\n    last: null,\n    viewport: viewport.frame,\n    forceShouldAnimate: false\n  });\n  const impact = {\n    displaced,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: getHomeLocation(draggable.descriptor)\n    }\n  };\n  return {\n    impact,\n    afterCritical\n  };\n};\n\nvar patchDimensionMap = (dimensions, updated) => ({\n  draggables: dimensions.draggables,\n  droppables: patchDroppableMap(dimensions.droppables, updated)\n});\n\nconst start = key => {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\nconst finish = key => {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\n\nvar offsetDraggable = ({\n  draggable,\n  offset: offset$1,\n  initialWindowScroll\n}) => {\n  const client = offset(draggable.client, offset$1);\n  const page = withScroll(client, initialWindowScroll);\n  const moved = {\n    ...draggable,\n    placeholder: {\n      ...draggable.placeholder,\n      client\n    },\n    client,\n    page\n  };\n  return moved;\n};\n\nvar getFrame = droppable => {\n  const frame = droppable.frame;\n  !frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected Droppable to have a frame') : invariant() : void 0;\n  return frame;\n};\n\nvar adjustAdditionsForScrollChanges = ({\n  additions,\n  updatedDroppables,\n  viewport\n}) => {\n  const windowScrollChange = viewport.scroll.diff.value;\n  return additions.map(draggable => {\n    const droppableId = draggable.descriptor.droppableId;\n    const modified = updatedDroppables[droppableId];\n    const frame = getFrame(modified);\n    const droppableScrollChange = frame.scroll.diff.value;\n    const totalChange = add(windowScrollChange, droppableScrollChange);\n    const moved = offsetDraggable({\n      draggable,\n      offset: totalChange,\n      initialWindowScroll: viewport.scroll.initial\n    });\n    return moved;\n  });\n};\n\nvar publishWhileDraggingInVirtual = ({\n  state,\n  published\n}) => {\n  start();\n  const withScrollChange = published.modified.map(update => {\n    const existing = state.dimensions.droppables[update.droppableId];\n    const scrolled = scrollDroppable(existing, update.scroll);\n    return scrolled;\n  });\n  const droppables = {\n    ...state.dimensions.droppables,\n    ...toDroppableMap(withScrollChange)\n  };\n  const updatedAdditions = toDraggableMap(adjustAdditionsForScrollChanges({\n    additions: published.additions,\n    updatedDroppables: droppables,\n    viewport: state.viewport\n  }));\n  const draggables = {\n    ...state.dimensions.draggables,\n    ...updatedAdditions\n  };\n  published.removals.forEach(id => {\n    delete draggables[id];\n  });\n  const dimensions = {\n    droppables,\n    draggables\n  };\n  const wasOverId = whatIsDraggedOver(state.impact);\n  const wasOver = wasOverId ? dimensions.droppables[wasOverId] : null;\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const home = dimensions.droppables[state.critical.droppable.id];\n  const {\n    impact: onLiftImpact,\n    afterCritical\n  } = getLiftEffect({\n    draggable,\n    home,\n    draggables,\n    viewport: state.viewport\n  });\n  const previousImpact = wasOver && wasOver.isCombineEnabled ? state.impact : onLiftImpact;\n  const impact = getDragImpact({\n    pageOffset: state.current.page.offset,\n    draggable: dimensions.draggables[state.critical.draggable.id],\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact,\n    viewport: state.viewport,\n    afterCritical\n  });\n  finish();\n  const draggingState = {\n    ...state,\n    phase: 'DRAGGING',\n    impact,\n    onLiftImpact,\n    dimensions,\n    afterCritical,\n    forceShouldAnimate: false\n  };\n  if (state.phase === 'COLLECTING') {\n    return draggingState;\n  }\n  const dropPending = {\n    ...draggingState,\n    phase: 'DROP_PENDING',\n    reason: state.reason,\n    isWaiting: false\n  };\n  return dropPending;\n};\n\nconst isSnapping = state => state.movementMode === 'SNAP';\nconst postDroppableChange = (state, updated, isEnabledChanging) => {\n  const dimensions = patchDimensionMap(state.dimensions, updated);\n  if (!isSnapping(state) || isEnabledChanging) {\n    return update({\n      state,\n      dimensions\n    });\n  }\n  return refreshSnap({\n    state,\n    dimensions\n  });\n};\nfunction removeScrollJumpRequest(state) {\n  if (state.isDragging && state.movementMode === 'SNAP') {\n    return {\n      ...state,\n      scrollJumpRequest: null\n    };\n  }\n  return state;\n}\nconst idle$2 = {\n  phase: 'IDLE',\n  completed: null,\n  shouldFlush: false\n};\nvar reducer = (state = idle$2, action) => {\n  if (action.type === 'FLUSH') {\n    return {\n      ...idle$2,\n      shouldFlush: true\n    };\n  }\n  if (action.type === 'INITIAL_PUBLISH') {\n    !(state.phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'INITIAL_PUBLISH must come after a IDLE phase') : invariant() : void 0;\n    const {\n      critical,\n      clientSelection,\n      viewport,\n      dimensions,\n      movementMode\n    } = action.payload;\n    const draggable = dimensions.draggables[critical.draggable.id];\n    const home = dimensions.droppables[critical.droppable.id];\n    const client = {\n      selection: clientSelection,\n      borderBoxCenter: draggable.client.borderBox.center,\n      offset: origin\n    };\n    const initial = {\n      client,\n      page: {\n        selection: add(client.selection, viewport.scroll.initial),\n        borderBoxCenter: add(client.selection, viewport.scroll.initial),\n        offset: add(client.selection, viewport.scroll.diff.value)\n      }\n    };\n    const isWindowScrollAllowed = toDroppableList(dimensions.droppables).every(item => !item.isFixedOnPage);\n    const {\n      impact,\n      afterCritical\n    } = getLiftEffect({\n      draggable,\n      home,\n      draggables: dimensions.draggables,\n      viewport\n    });\n    const result = {\n      phase: 'DRAGGING',\n      isDragging: true,\n      critical,\n      movementMode,\n      dimensions,\n      initial,\n      current: initial,\n      isWindowScrollAllowed,\n      impact,\n      afterCritical,\n      onLiftImpact: impact,\n      viewport,\n      scrollJumpRequest: null,\n      forceShouldAnimate: null\n    };\n    return result;\n  }\n  if (action.type === 'COLLECTION_STARTING') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Collection cannot start from phase ${state.phase}`) : invariant() : void 0;\n    const result = {\n      ...state,\n      phase: 'COLLECTING'\n    };\n    return result;\n  }\n  if (action.type === 'PUBLISH_WHILE_DRAGGING') {\n    !(state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Unexpected ${action.type} received in phase ${state.phase}`) : invariant() : void 0;\n    return publishWhileDraggingInVirtual({\n      state,\n      published: action.payload\n    });\n  }\n  if (action.type === 'MOVE') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} not permitted in phase ${state.phase}`) : invariant() : void 0;\n    const {\n      client: clientSelection\n    } = action.payload;\n    if (isEqual$1(clientSelection, state.current.client.selection)) {\n      return state;\n    }\n    return update({\n      state,\n      clientSelection,\n      impact: isSnapping(state) ? state.impact : null\n    });\n  }\n  if (action.type === 'UPDATE_DROPPABLE_SCROLL') {\n    if (state.phase === 'DROP_PENDING') {\n      return removeScrollJumpRequest(state);\n    }\n    if (state.phase === 'COLLECTING') {\n      return removeScrollJumpRequest(state);\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} not permitted in phase ${state.phase}`) : invariant() : void 0;\n    const {\n      id,\n      newScroll\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    if (!target) {\n      return state;\n    }\n    const scrolled = scrollDroppable(target, newScroll);\n    return postDroppableChange(state, scrolled, false);\n  }\n  if (action.type === 'UPDATE_DROPPABLE_IS_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Attempting to move in an unsupported phase ${state.phase}`) : invariant() : void 0;\n    const {\n      id,\n      isEnabled\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    !target ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find Droppable[id: ${id}] to toggle its enabled state`) : invariant() : void 0;\n    !(target.isEnabled !== isEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Trying to set droppable isEnabled to ${String(isEnabled)}\n      but it is already ${String(target.isEnabled)}`) : invariant() : void 0;\n    const updated = {\n      ...target,\n      isEnabled\n    };\n    return postDroppableChange(state, updated, true);\n  }\n  if (action.type === 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Attempting to move in an unsupported phase ${state.phase}`) : invariant() : void 0;\n    const {\n      id,\n      isCombineEnabled\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    !target ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find Droppable[id: ${id}] to toggle its isCombineEnabled state`) : invariant() : void 0;\n    !(target.isCombineEnabled !== isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Trying to set droppable isCombineEnabled to ${String(isCombineEnabled)}\n      but it is already ${String(target.isCombineEnabled)}`) : invariant() : void 0;\n    const updated = {\n      ...target,\n      isCombineEnabled\n    };\n    return postDroppableChange(state, updated, true);\n  }\n  if (action.type === 'MOVE_BY_WINDOW_SCROLL') {\n    if (state.phase === 'DROP_PENDING' || state.phase === 'DROP_ANIMATING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot move by window in phase ${state.phase}`) : invariant() : void 0;\n    !state.isWindowScrollAllowed ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Window scrolling is currently not supported for fixed lists') : invariant() : void 0;\n    const newScroll = action.payload.newScroll;\n    if (isEqual$1(state.viewport.scroll.current, newScroll)) {\n      return removeScrollJumpRequest(state);\n    }\n    const viewport = scrollViewport(state.viewport, newScroll);\n    if (isSnapping(state)) {\n      return refreshSnap({\n        state,\n        viewport\n      });\n    }\n    return update({\n      state,\n      viewport\n    });\n  }\n  if (action.type === 'UPDATE_VIEWPORT_MAX_SCROLL') {\n    if (!isMovementAllowed(state)) {\n      return state;\n    }\n    const maxScroll = action.payload.maxScroll;\n    if (isEqual$1(maxScroll, state.viewport.scroll.max)) {\n      return state;\n    }\n    const withMaxScroll = {\n      ...state.viewport,\n      scroll: {\n        ...state.viewport.scroll,\n        max: maxScroll\n      }\n    };\n    return {\n      ...state,\n      viewport: withMaxScroll\n    };\n  }\n  if (action.type === 'MOVE_UP' || action.type === 'MOVE_DOWN' || action.type === 'MOVE_LEFT' || action.type === 'MOVE_RIGHT') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} received while not in DRAGGING phase`) : invariant() : void 0;\n    const result = moveInDirection({\n      state,\n      type: action.type\n    });\n    if (!result) {\n      return state;\n    }\n    return update({\n      state,\n      impact: result.impact,\n      clientSelection: result.clientSelection,\n      scrollJumpRequest: result.scrollJumpRequest\n    });\n  }\n  if (action.type === 'DROP_PENDING') {\n    const reason = action.payload.reason;\n    !(state.phase === 'COLLECTING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only move into the DROP_PENDING phase from the COLLECTING phase') : invariant() : void 0;\n    const newState = {\n      ...state,\n      phase: 'DROP_PENDING',\n      isWaiting: true,\n      reason\n    };\n    return newState;\n  }\n  if (action.type === 'DROP_ANIMATE') {\n    const {\n      completed,\n      dropDuration,\n      newHomeClientOffset\n    } = action.payload;\n    !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot animate drop from phase ${state.phase}`) : invariant() : void 0;\n    const result = {\n      phase: 'DROP_ANIMATING',\n      completed,\n      dropDuration,\n      newHomeClientOffset,\n      dimensions: state.dimensions\n    };\n    return result;\n  }\n  if (action.type === 'DROP_COMPLETE') {\n    const {\n      completed\n    } = action.payload;\n    return {\n      phase: 'IDLE',\n      completed,\n      shouldFlush: false\n    };\n  }\n  return state;\n};\n\nfunction guard(action, predicate) {\n  return action instanceof Object && 'type' in action && action.type === predicate;\n}\nconst beforeInitialCapture = args => ({\n  type: 'BEFORE_INITIAL_CAPTURE',\n  payload: args\n});\nconst lift$1 = args => ({\n  type: 'LIFT',\n  payload: args\n});\nconst initialPublish = args => ({\n  type: 'INITIAL_PUBLISH',\n  payload: args\n});\nconst publishWhileDragging = args => ({\n  type: 'PUBLISH_WHILE_DRAGGING',\n  payload: args\n});\nconst collectionStarting = () => ({\n  type: 'COLLECTION_STARTING',\n  payload: null\n});\nconst updateDroppableScroll = args => ({\n  type: 'UPDATE_DROPPABLE_SCROLL',\n  payload: args\n});\nconst updateDroppableIsEnabled = args => ({\n  type: 'UPDATE_DROPPABLE_IS_ENABLED',\n  payload: args\n});\nconst updateDroppableIsCombineEnabled = args => ({\n  type: 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED',\n  payload: args\n});\nconst move = args => ({\n  type: 'MOVE',\n  payload: args\n});\nconst moveByWindowScroll = args => ({\n  type: 'MOVE_BY_WINDOW_SCROLL',\n  payload: args\n});\nconst updateViewportMaxScroll = args => ({\n  type: 'UPDATE_VIEWPORT_MAX_SCROLL',\n  payload: args\n});\nconst moveUp = () => ({\n  type: 'MOVE_UP',\n  payload: null\n});\nconst moveDown = () => ({\n  type: 'MOVE_DOWN',\n  payload: null\n});\nconst moveRight = () => ({\n  type: 'MOVE_RIGHT',\n  payload: null\n});\nconst moveLeft = () => ({\n  type: 'MOVE_LEFT',\n  payload: null\n});\nconst flush = () => ({\n  type: 'FLUSH',\n  payload: null\n});\nconst animateDrop = args => ({\n  type: 'DROP_ANIMATE',\n  payload: args\n});\nconst completeDrop = args => ({\n  type: 'DROP_COMPLETE',\n  payload: args\n});\nconst drop = args => ({\n  type: 'DROP',\n  payload: args\n});\nconst dropPending = args => ({\n  type: 'DROP_PENDING',\n  payload: args\n});\nconst dropAnimationFinished = () => ({\n  type: 'DROP_ANIMATION_FINISHED',\n  payload: null\n});\n\nfunction checkIndexes(insideDestination) {\n  if (insideDestination.length <= 1) {\n    return;\n  }\n  const indexes = insideDestination.map(d => d.descriptor.index);\n  const errors = {};\n  for (let i = 1; i < indexes.length; i++) {\n    const current = indexes[i];\n    const previous = indexes[i - 1];\n    if (current !== previous + 1) {\n      errors[current] = true;\n    }\n  }\n  if (!Object.keys(errors).length) {\n    return;\n  }\n  const formatted = indexes.map(index => {\n    const hasError = Boolean(errors[index]);\n    return hasError ? `[🔥${index}]` : `${index}`;\n  }).join(', ');\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    Detected non-consecutive <Draggable /> indexes.\n\n    (This can cause unexpected bugs)\n\n    ${formatted}\n  `) : void 0;\n}\nfunction validateDimensions(critical, dimensions) {\n  if (process.env.NODE_ENV !== 'production') {\n    const insideDestination = getDraggablesInsideDroppable(critical.droppable.id, dimensions.draggables);\n    checkIndexes(insideDestination);\n  }\n}\n\nvar lift = marshal => ({\n  getState,\n  dispatch\n}) => next => action => {\n  if (!guard(action, 'LIFT')) {\n    next(action);\n    return;\n  }\n  const {\n    id,\n    clientSelection,\n    movementMode\n  } = action.payload;\n  const initial = getState();\n  if (initial.phase === 'DROP_ANIMATING') {\n    dispatch(completeDrop({\n      completed: initial.completed\n    }));\n  }\n  !(getState().phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase to start a drag') : invariant() : void 0;\n  dispatch(flush());\n  dispatch(beforeInitialCapture({\n    draggableId: id,\n    movementMode\n  }));\n  const scrollOptions = {\n    shouldPublishImmediately: movementMode === 'SNAP'\n  };\n  const request = {\n    draggableId: id,\n    scrollOptions\n  };\n  const {\n    critical,\n    dimensions,\n    viewport\n  } = marshal.startPublishing(request);\n  validateDimensions(critical, dimensions);\n  dispatch(initialPublish({\n    critical,\n    dimensions,\n    clientSelection,\n    movementMode,\n    viewport\n  }));\n};\n\nvar style = marshal => () => next => action => {\n  if (guard(action, 'INITIAL_PUBLISH')) {\n    marshal.dragging();\n  }\n  if (guard(action, 'DROP_ANIMATE')) {\n    marshal.dropping(action.payload.completed.result.reason);\n  }\n  if (guard(action, 'FLUSH') || guard(action, 'DROP_COMPLETE')) {\n    marshal.resting();\n  }\n  next(action);\n};\n\nconst curves = {\n  outOfTheWay: 'cubic-bezier(0.2, 0, 0, 1)',\n  drop: 'cubic-bezier(.2,1,.1,1)'\n};\nconst combine = {\n  opacity: {\n    drop: 0,\n    combining: 0.7\n  },\n  scale: {\n    drop: 0.75\n  }\n};\nconst timings = {\n  outOfTheWay: 0.2,\n  minDropTime: 0.33,\n  maxDropTime: 0.55\n};\nconst outOfTheWayTiming = `${timings.outOfTheWay}s ${curves.outOfTheWay}`;\nconst transitions = {\n  fluid: `opacity ${outOfTheWayTiming}`,\n  snap: `transform ${outOfTheWayTiming}, opacity ${outOfTheWayTiming}`,\n  drop: duration => {\n    const timing = `${duration}s ${curves.drop}`;\n    return `transform ${timing}, opacity ${timing}`;\n  },\n  outOfTheWay: `transform ${outOfTheWayTiming}`,\n  placeholder: `height ${outOfTheWayTiming}, width ${outOfTheWayTiming}, margin ${outOfTheWayTiming}`\n};\nconst moveTo = offset => isEqual$1(offset, origin) ? undefined : `translate(${offset.x}px, ${offset.y}px)`;\nconst transforms = {\n  moveTo,\n  drop: (offset, isCombining) => {\n    const translate = moveTo(offset);\n    if (!translate) {\n      return undefined;\n    }\n    if (!isCombining) {\n      return translate;\n    }\n    return `${translate} scale(${combine.scale.drop})`;\n  }\n};\n\nconst {\n  minDropTime,\n  maxDropTime\n} = timings;\nconst dropTimeRange = maxDropTime - minDropTime;\nconst maxDropTimeAtDistance = 1500;\nconst cancelDropModifier = 0.6;\nvar getDropDuration = ({\n  current,\n  destination,\n  reason\n}) => {\n  const distance$1 = distance(current, destination);\n  if (distance$1 <= 0) {\n    return minDropTime;\n  }\n  if (distance$1 >= maxDropTimeAtDistance) {\n    return maxDropTime;\n  }\n  const percentage = distance$1 / maxDropTimeAtDistance;\n  const duration = minDropTime + dropTimeRange * percentage;\n  const withDuration = reason === 'CANCEL' ? duration * cancelDropModifier : duration;\n  return Number(withDuration.toFixed(2));\n};\n\nvar getNewHomeClientOffset = ({\n  impact,\n  draggable,\n  dimensions,\n  viewport,\n  afterCritical\n}) => {\n  const {\n    draggables,\n    droppables\n  } = dimensions;\n  const droppableId = whatIsDraggedOver(impact);\n  const destination = droppableId ? droppables[droppableId] : null;\n  const home = droppables[draggable.descriptor.droppableId];\n  const newClientCenter = getClientBorderBoxCenter({\n    impact,\n    draggable,\n    draggables,\n    afterCritical,\n    droppable: destination || home,\n    viewport\n  });\n  const offset = subtract(newClientCenter, draggable.client.borderBox.center);\n  return offset;\n};\n\nvar getDropImpact = ({\n  draggables,\n  reason,\n  lastImpact,\n  home,\n  viewport,\n  onLiftImpact\n}) => {\n  if (!lastImpact.at || reason !== 'DROP') {\n    const recomputedHomeImpact = recompute({\n      draggables,\n      impact: onLiftImpact,\n      destination: home,\n      viewport,\n      forceShouldAnimate: true\n    });\n    return {\n      impact: recomputedHomeImpact,\n      didDropInsideDroppable: false\n    };\n  }\n  if (lastImpact.at.type === 'REORDER') {\n    return {\n      impact: lastImpact,\n      didDropInsideDroppable: true\n    };\n  }\n  const withoutMovement = {\n    ...lastImpact,\n    displaced: emptyGroups\n  };\n  return {\n    impact: withoutMovement,\n    didDropInsideDroppable: true\n  };\n};\n\nconst dropMiddleware = ({\n  getState,\n  dispatch\n}) => next => action => {\n  if (!guard(action, 'DROP')) {\n    next(action);\n    return;\n  }\n  const state = getState();\n  const reason = action.payload.reason;\n  if (state.phase === 'COLLECTING') {\n    dispatch(dropPending({\n      reason\n    }));\n    return;\n  }\n  if (state.phase === 'IDLE') {\n    return;\n  }\n  const isWaitingForDrop = state.phase === 'DROP_PENDING' && state.isWaiting;\n  !!isWaitingForDrop ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A DROP action occurred while DROP_PENDING and still waiting') : invariant() : void 0;\n  !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot drop in phase: ${state.phase}`) : invariant() : void 0;\n  const critical = state.critical;\n  const dimensions = state.dimensions;\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const {\n    impact,\n    didDropInsideDroppable\n  } = getDropImpact({\n    reason,\n    lastImpact: state.impact,\n    afterCritical: state.afterCritical,\n    onLiftImpact: state.onLiftImpact,\n    home: state.dimensions.droppables[state.critical.droppable.id],\n    viewport: state.viewport,\n    draggables: state.dimensions.draggables\n  });\n  const destination = didDropInsideDroppable ? tryGetDestination(impact) : null;\n  const combine = didDropInsideDroppable ? tryGetCombine(impact) : null;\n  const source = {\n    index: critical.draggable.index,\n    droppableId: critical.droppable.id\n  };\n  const result = {\n    draggableId: draggable.descriptor.id,\n    type: draggable.descriptor.type,\n    source,\n    reason,\n    mode: state.movementMode,\n    destination,\n    combine\n  };\n  const newHomeClientOffset = getNewHomeClientOffset({\n    impact,\n    draggable,\n    dimensions,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n  const completed = {\n    critical: state.critical,\n    afterCritical: state.afterCritical,\n    result,\n    impact\n  };\n  const isAnimationRequired = !isEqual$1(state.current.client.offset, newHomeClientOffset) || Boolean(result.combine);\n  if (!isAnimationRequired) {\n    dispatch(completeDrop({\n      completed\n    }));\n    return;\n  }\n  const dropDuration = getDropDuration({\n    current: state.current.client.offset,\n    destination: newHomeClientOffset,\n    reason\n  });\n  const args = {\n    newHomeClientOffset,\n    dropDuration,\n    completed\n  };\n  dispatch(animateDrop(args));\n};\n\nvar getWindowScroll = () => ({\n  x: window.pageXOffset,\n  y: window.pageYOffset\n});\n\nfunction getWindowScrollBinding(update) {\n  return {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: event => {\n      if (event.target !== window && event.target !== window.document) {\n        return;\n      }\n      update();\n    }\n  };\n}\nfunction getScrollListener({\n  onWindowScroll\n}) {\n  function updateScroll() {\n    onWindowScroll(getWindowScroll());\n  }\n  const scheduled = rafSchd(updateScroll);\n  const binding = getWindowScrollBinding(scheduled);\n  let unbind = noop$2;\n  function isActive() {\n    return unbind !== noop$2;\n  }\n  function start() {\n    !!isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start scroll listener when already active') : invariant() : void 0;\n    unbind = bindEvents(window, [binding]);\n  }\n  function stop() {\n    !isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop scroll listener when not active') : invariant() : void 0;\n    scheduled.cancel();\n    unbind();\n    unbind = noop$2;\n  }\n  return {\n    start,\n    stop,\n    isActive\n  };\n}\n\nconst shouldStop$1 = action => guard(action, 'DROP_COMPLETE') || guard(action, 'DROP_ANIMATE') || guard(action, 'FLUSH');\nconst scrollListener = store => {\n  const listener = getScrollListener({\n    onWindowScroll: newScroll => {\n      store.dispatch(moveByWindowScroll({\n        newScroll\n      }));\n    }\n  });\n  return next => action => {\n    if (!listener.isActive() && guard(action, 'INITIAL_PUBLISH')) {\n      listener.start();\n    }\n    if (listener.isActive() && shouldStop$1(action)) {\n      listener.stop();\n    }\n    next(action);\n  };\n};\n\nvar getExpiringAnnounce = announce => {\n  let wasCalled = false;\n  let isExpired = false;\n  const timeoutId = setTimeout(() => {\n    isExpired = true;\n  });\n  const result = message => {\n    if (wasCalled) {\n      process.env.NODE_ENV !== \"production\" ? warning('Announcement already made. Not making a second announcement') : void 0;\n      return;\n    }\n    if (isExpired) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Announcements cannot be made asynchronously.\n        Default message has already been announced.\n      `) : void 0;\n      return;\n    }\n    wasCalled = true;\n    announce(message);\n    clearTimeout(timeoutId);\n  };\n  result.wasCalled = () => wasCalled;\n  return result;\n};\n\nvar getAsyncMarshal = () => {\n  const entries = [];\n  const execute = timerId => {\n    const index = entries.findIndex(item => item.timerId === timerId);\n    !(index !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find timer') : invariant() : void 0;\n    const [entry] = entries.splice(index, 1);\n    entry.callback();\n  };\n  const add = fn => {\n    const timerId = setTimeout(() => execute(timerId));\n    const entry = {\n      timerId,\n      callback: fn\n    };\n    entries.push(entry);\n  };\n  const flush = () => {\n    if (!entries.length) {\n      return;\n    }\n    const shallow = [...entries];\n    entries.length = 0;\n    shallow.forEach(entry => {\n      clearTimeout(entry.timerId);\n      entry.callback();\n    });\n  };\n  return {\n    add,\n    flush\n  };\n};\n\nconst areLocationsEqual = (first, second) => {\n  if (first == null && second == null) {\n    return true;\n  }\n  if (first == null || second == null) {\n    return false;\n  }\n  return first.droppableId === second.droppableId && first.index === second.index;\n};\nconst isCombineEqual = (first, second) => {\n  if (first == null && second == null) {\n    return true;\n  }\n  if (first == null || second == null) {\n    return false;\n  }\n  return first.draggableId === second.draggableId && first.droppableId === second.droppableId;\n};\nconst isCriticalEqual = (first, second) => {\n  if (first === second) {\n    return true;\n  }\n  const isDraggableEqual = first.draggable.id === second.draggable.id && first.draggable.droppableId === second.draggable.droppableId && first.draggable.type === second.draggable.type && first.draggable.index === second.draggable.index;\n  const isDroppableEqual = first.droppable.id === second.droppable.id && first.droppable.type === second.droppable.type;\n  return isDraggableEqual && isDroppableEqual;\n};\n\nconst withTimings = (key, fn) => {\n  start();\n  fn();\n  finish();\n};\nconst getDragStart = (critical, mode) => ({\n  draggableId: critical.draggable.id,\n  type: critical.droppable.type,\n  source: {\n    droppableId: critical.droppable.id,\n    index: critical.draggable.index\n  },\n  mode\n});\nfunction execute(responder, data, announce, getDefaultMessage) {\n  if (!responder) {\n    announce(getDefaultMessage(data));\n    return;\n  }\n  const willExpire = getExpiringAnnounce(announce);\n  const provided = {\n    announce: willExpire\n  };\n  responder(data, provided);\n  if (!willExpire.wasCalled()) {\n    announce(getDefaultMessage(data));\n  }\n}\nvar getPublisher = (getResponders, announce) => {\n  const asyncMarshal = getAsyncMarshal();\n  let dragging = null;\n  const beforeCapture = (draggableId, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeCapture as a drag start has already been published') : invariant() : void 0;\n    withTimings('onBeforeCapture', () => {\n      const fn = getResponders().onBeforeCapture;\n      if (fn) {\n        const before = {\n          draggableId,\n          mode\n        };\n        fn(before);\n      }\n    });\n  };\n  const beforeStart = (critical, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant() : void 0;\n    withTimings('onBeforeDragStart', () => {\n      const fn = getResponders().onBeforeDragStart;\n      if (fn) {\n        fn(getDragStart(critical, mode));\n      }\n    });\n  };\n  const start = (critical, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant() : void 0;\n    const data = getDragStart(critical, mode);\n    dragging = {\n      mode,\n      lastCritical: critical,\n      lastLocation: data.source,\n      lastCombine: null\n    };\n    asyncMarshal.add(() => {\n      withTimings('onDragStart', () => execute(getResponders().onDragStart, data, announce, preset.onDragStart));\n    });\n  };\n  const update = (critical, impact) => {\n    const location = tryGetDestination(impact);\n    const combine = tryGetCombine(impact);\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragMove when onDragStart has not been called') : invariant() : void 0;\n    const hasCriticalChanged = !isCriticalEqual(critical, dragging.lastCritical);\n    if (hasCriticalChanged) {\n      dragging.lastCritical = critical;\n    }\n    const hasLocationChanged = !areLocationsEqual(dragging.lastLocation, location);\n    if (hasLocationChanged) {\n      dragging.lastLocation = location;\n    }\n    const hasGroupingChanged = !isCombineEqual(dragging.lastCombine, combine);\n    if (hasGroupingChanged) {\n      dragging.lastCombine = combine;\n    }\n    if (!hasCriticalChanged && !hasLocationChanged && !hasGroupingChanged) {\n      return;\n    }\n    const data = {\n      ...getDragStart(critical, dragging.mode),\n      combine,\n      destination: location\n    };\n    asyncMarshal.add(() => {\n      withTimings('onDragUpdate', () => execute(getResponders().onDragUpdate, data, announce, preset.onDragUpdate));\n    });\n  };\n  const flush = () => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only flush responders while dragging') : invariant() : void 0;\n    asyncMarshal.flush();\n  };\n  const drop = result => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragEnd when there is no matching onDragStart') : invariant() : void 0;\n    dragging = null;\n    withTimings('onDragEnd', () => execute(getResponders().onDragEnd, result, announce, preset.onDragEnd));\n  };\n  const abort = () => {\n    if (!dragging) {\n      return;\n    }\n    const result = {\n      ...getDragStart(dragging.lastCritical, dragging.mode),\n      combine: null,\n      destination: null,\n      reason: 'CANCEL'\n    };\n    drop(result);\n  };\n  return {\n    beforeCapture,\n    beforeStart,\n    start,\n    update,\n    flush,\n    drop,\n    abort\n  };\n};\n\nvar responders = (getResponders, announce) => {\n  const publisher = getPublisher(getResponders, announce);\n  return store => next => action => {\n    if (guard(action, 'BEFORE_INITIAL_CAPTURE')) {\n      publisher.beforeCapture(action.payload.draggableId, action.payload.movementMode);\n      return;\n    }\n    if (guard(action, 'INITIAL_PUBLISH')) {\n      const critical = action.payload.critical;\n      publisher.beforeStart(critical, action.payload.movementMode);\n      next(action);\n      publisher.start(critical, action.payload.movementMode);\n      return;\n    }\n    if (guard(action, 'DROP_COMPLETE')) {\n      const result = action.payload.completed.result;\n      publisher.flush();\n      next(action);\n      publisher.drop(result);\n      return;\n    }\n    next(action);\n    if (guard(action, 'FLUSH')) {\n      publisher.abort();\n      return;\n    }\n    const state = store.getState();\n    if (state.phase === 'DRAGGING') {\n      publisher.update(state.critical, state.impact);\n    }\n  };\n};\n\nconst dropAnimationFinishMiddleware = store => next => action => {\n  if (!guard(action, 'DROP_ANIMATION_FINISHED')) {\n    next(action);\n    return;\n  }\n  const state = store.getState();\n  !(state.phase === 'DROP_ANIMATING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot finish a drop animating when no drop is occurring') : invariant() : void 0;\n  store.dispatch(completeDrop({\n    completed: state.completed\n  }));\n};\n\nconst dropAnimationFlushOnScrollMiddleware = store => {\n  let unbind = null;\n  let frameId = null;\n  function clear() {\n    if (frameId) {\n      cancelAnimationFrame(frameId);\n      frameId = null;\n    }\n    if (unbind) {\n      unbind();\n      unbind = null;\n    }\n  }\n  return next => action => {\n    if (guard(action, 'FLUSH') || guard(action, 'DROP_COMPLETE') || guard(action, 'DROP_ANIMATION_FINISHED')) {\n      clear();\n    }\n    next(action);\n    if (!guard(action, 'DROP_ANIMATE')) {\n      return;\n    }\n    const binding = {\n      eventName: 'scroll',\n      options: {\n        capture: true,\n        passive: false,\n        once: true\n      },\n      fn: function flushDropAnimation() {\n        const state = store.getState();\n        if (state.phase === 'DROP_ANIMATING') {\n          store.dispatch(dropAnimationFinished());\n        }\n      }\n    };\n    frameId = requestAnimationFrame(() => {\n      frameId = null;\n      unbind = bindEvents(window, [binding]);\n    });\n  };\n};\n\nvar dimensionMarshalStopper = marshal => () => next => action => {\n  if (guard(action, 'DROP_COMPLETE') || guard(action, 'FLUSH') || guard(action, 'DROP_ANIMATE')) {\n    marshal.stopPublishing();\n  }\n  next(action);\n};\n\nvar focus = marshal => {\n  let isWatching = false;\n  return () => next => action => {\n    if (guard(action, 'INITIAL_PUBLISH')) {\n      isWatching = true;\n      marshal.tryRecordFocus(action.payload.critical.draggable.id);\n      next(action);\n      marshal.tryRestoreFocusRecorded();\n      return;\n    }\n    next(action);\n    if (!isWatching) {\n      return;\n    }\n    if (guard(action, 'FLUSH')) {\n      isWatching = false;\n      marshal.tryRestoreFocusRecorded();\n      return;\n    }\n    if (guard(action, 'DROP_COMPLETE')) {\n      isWatching = false;\n      const result = action.payload.completed.result;\n      if (result.combine) {\n        marshal.tryShiftRecord(result.draggableId, result.combine.draggableId);\n      }\n      marshal.tryRestoreFocusRecorded();\n    }\n  };\n};\n\nconst shouldStop = action => guard(action, 'DROP_COMPLETE') || guard(action, 'DROP_ANIMATE') || guard(action, 'FLUSH');\nvar autoScroll = autoScroller => store => next => action => {\n  if (shouldStop(action)) {\n    autoScroller.stop();\n    next(action);\n    return;\n  }\n  if (guard(action, 'INITIAL_PUBLISH')) {\n    next(action);\n    const state = store.getState();\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected phase to be DRAGGING after INITIAL_PUBLISH') : invariant() : void 0;\n    autoScroller.start(state);\n    return;\n  }\n  next(action);\n  autoScroller.scroll(store.getState());\n};\n\nconst pendingDrop = store => next => action => {\n  next(action);\n  if (!guard(action, 'PUBLISH_WHILE_DRAGGING')) {\n    return;\n  }\n  const postActionState = store.getState();\n  if (postActionState.phase !== 'DROP_PENDING') {\n    return;\n  }\n  if (postActionState.isWaiting) {\n    return;\n  }\n  store.dispatch(drop({\n    reason: postActionState.reason\n  }));\n};\n\nconst composeEnhancers = process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({\n  name: '@hello-pangea/dnd'\n}) : compose;\nvar createStore = ({\n  dimensionMarshal,\n  focusMarshal,\n  styleMarshal,\n  getResponders,\n  announce,\n  autoScroller\n}) => createStore$1(reducer, composeEnhancers(applyMiddleware(style(styleMarshal), dimensionMarshalStopper(dimensionMarshal), lift(dimensionMarshal), dropMiddleware, dropAnimationFinishMiddleware, dropAnimationFlushOnScrollMiddleware, pendingDrop, autoScroll(autoScroller), scrollListener, focus(focusMarshal), responders(getResponders, announce))));\n\nconst clean$1 = () => ({\n  additions: {},\n  removals: {},\n  modified: {}\n});\nfunction createPublisher({\n  registry,\n  callbacks\n}) {\n  let staging = clean$1();\n  let frameId = null;\n  const collect = () => {\n    if (frameId) {\n      return;\n    }\n    callbacks.collectionStarting();\n    frameId = requestAnimationFrame(() => {\n      frameId = null;\n      start();\n      const {\n        additions,\n        removals,\n        modified\n      } = staging;\n      const added = Object.keys(additions).map(id => registry.draggable.getById(id).getDimension(origin)).sort((a, b) => a.descriptor.index - b.descriptor.index);\n      const updated = Object.keys(modified).map(id => {\n        const entry = registry.droppable.getById(id);\n        const scroll = entry.callbacks.getScrollWhileDragging();\n        return {\n          droppableId: id,\n          scroll\n        };\n      });\n      const result = {\n        additions: added,\n        removals: Object.keys(removals),\n        modified: updated\n      };\n      staging = clean$1();\n      finish();\n      callbacks.publish(result);\n    });\n  };\n  const add = entry => {\n    const id = entry.descriptor.id;\n    staging.additions[id] = entry;\n    staging.modified[entry.descriptor.droppableId] = true;\n    if (staging.removals[id]) {\n      delete staging.removals[id];\n    }\n    collect();\n  };\n  const remove = entry => {\n    const descriptor = entry.descriptor;\n    staging.removals[descriptor.id] = true;\n    staging.modified[descriptor.droppableId] = true;\n    if (staging.additions[descriptor.id]) {\n      delete staging.additions[descriptor.id];\n    }\n    collect();\n  };\n  const stop = () => {\n    if (!frameId) {\n      return;\n    }\n    cancelAnimationFrame(frameId);\n    frameId = null;\n    staging = clean$1();\n  };\n  return {\n    add,\n    remove,\n    stop\n  };\n}\n\nvar getMaxScroll = ({\n  scrollHeight,\n  scrollWidth,\n  height,\n  width\n}) => {\n  const maxScroll = subtract({\n    x: scrollWidth,\n    y: scrollHeight\n  }, {\n    x: width,\n    y: height\n  });\n  const adjustedMaxScroll = {\n    x: Math.max(0, maxScroll.x),\n    y: Math.max(0, maxScroll.y)\n  };\n  return adjustedMaxScroll;\n};\n\nvar getDocumentElement = () => {\n  const doc = document.documentElement;\n  !doc ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.documentElement') : invariant() : void 0;\n  return doc;\n};\n\nvar getMaxWindowScroll = () => {\n  const doc = getDocumentElement();\n  const maxScroll = getMaxScroll({\n    scrollHeight: doc.scrollHeight,\n    scrollWidth: doc.scrollWidth,\n    width: doc.clientWidth,\n    height: doc.clientHeight\n  });\n  return maxScroll;\n};\n\nvar getViewport = () => {\n  const scroll = getWindowScroll();\n  const maxScroll = getMaxWindowScroll();\n  const top = scroll.y;\n  const left = scroll.x;\n  const doc = getDocumentElement();\n  const width = doc.clientWidth;\n  const height = doc.clientHeight;\n  const right = left + width;\n  const bottom = top + height;\n  const frame = getRect({\n    top,\n    left,\n    right,\n    bottom\n  });\n  const viewport = {\n    frame,\n    scroll: {\n      initial: scroll,\n      current: scroll,\n      max: maxScroll,\n      diff: {\n        value: origin,\n        displacement: origin\n      }\n    }\n  };\n  return viewport;\n};\n\nvar getInitialPublish = ({\n  critical,\n  scrollOptions,\n  registry\n}) => {\n  start();\n  const viewport = getViewport();\n  const windowScroll = viewport.scroll.current;\n  const home = critical.droppable;\n  const droppables = registry.droppable.getAllByType(home.type).map(entry => entry.callbacks.getDimensionAndWatchScroll(windowScroll, scrollOptions));\n  const draggables = registry.draggable.getAllByType(critical.draggable.type).map(entry => entry.getDimension(windowScroll));\n  const dimensions = {\n    draggables: toDraggableMap(draggables),\n    droppables: toDroppableMap(droppables)\n  };\n  finish();\n  const result = {\n    dimensions,\n    critical,\n    viewport\n  };\n  return result;\n};\n\nfunction shouldPublishUpdate(registry, dragging, entry) {\n  if (entry.descriptor.id === dragging.id) {\n    return false;\n  }\n  if (entry.descriptor.type !== dragging.type) {\n    return false;\n  }\n  const home = registry.droppable.getById(entry.descriptor.droppableId);\n  if (home.descriptor.mode !== 'virtual') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      You are attempting to add or remove a Draggable [id: ${entry.descriptor.id}]\n      while a drag is occurring. This is only supported for virtual lists.\n\n      See https://github.com/hello-pangea/dnd/blob/main/docs/patterns/virtual-lists.md\n    `) : void 0;\n    return false;\n  }\n  return true;\n}\nvar createDimensionMarshal = (registry, callbacks) => {\n  let collection = null;\n  const publisher = createPublisher({\n    callbacks: {\n      publish: callbacks.publishWhileDragging,\n      collectionStarting: callbacks.collectionStarting\n    },\n    registry\n  });\n  const updateDroppableIsEnabled = (id, isEnabled) => {\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update is enabled flag of Droppable ${id} as it is not registered`) : invariant() : void 0;\n    if (!collection) {\n      return;\n    }\n    callbacks.updateDroppableIsEnabled({\n      id,\n      isEnabled\n    });\n  };\n  const updateDroppableIsCombineEnabled = (id, isCombineEnabled) => {\n    if (!collection) {\n      return;\n    }\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update isCombineEnabled flag of Droppable ${id} as it is not registered`) : invariant() : void 0;\n    callbacks.updateDroppableIsCombineEnabled({\n      id,\n      isCombineEnabled\n    });\n  };\n  const updateDroppableScroll = (id, newScroll) => {\n    if (!collection) {\n      return;\n    }\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update the scroll on Droppable ${id} as it is not registered`) : invariant() : void 0;\n    callbacks.updateDroppableScroll({\n      id,\n      newScroll\n    });\n  };\n  const scrollDroppable = (id, change) => {\n    if (!collection) {\n      return;\n    }\n    registry.droppable.getById(id).callbacks.scroll(change);\n  };\n  const stopPublishing = () => {\n    if (!collection) {\n      return;\n    }\n    publisher.stop();\n    const home = collection.critical.droppable;\n    registry.droppable.getAllByType(home.type).forEach(entry => entry.callbacks.dragStopped());\n    collection.unsubscribe();\n    collection = null;\n  };\n  const subscriber = event => {\n    !collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should only be subscribed when a collection is occurring') : invariant() : void 0;\n    const dragging = collection.critical.draggable;\n    if (event.type === 'ADDITION') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.add(event.value);\n      }\n    }\n    if (event.type === 'REMOVAL') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.remove(event.value);\n      }\n    }\n  };\n  const startPublishing = request => {\n    !!collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start capturing critical dimensions as there is already a collection') : invariant() : void 0;\n    const entry = registry.draggable.getById(request.draggableId);\n    const home = registry.droppable.getById(entry.descriptor.droppableId);\n    const critical = {\n      draggable: entry.descriptor,\n      droppable: home.descriptor\n    };\n    const unsubscribe = registry.subscribe(subscriber);\n    collection = {\n      critical,\n      unsubscribe\n    };\n    return getInitialPublish({\n      critical,\n      registry,\n      scrollOptions: request.scrollOptions\n    });\n  };\n  const marshal = {\n    updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled,\n    scrollDroppable,\n    updateDroppableScroll,\n    startPublishing,\n    stopPublishing\n  };\n  return marshal;\n};\n\nvar canStartDrag = (state, id) => {\n  if (state.phase === 'IDLE') {\n    return true;\n  }\n  if (state.phase !== 'DROP_ANIMATING') {\n    return false;\n  }\n  if (state.completed.result.draggableId === id) {\n    return false;\n  }\n  return state.completed.result.reason === 'DROP';\n};\n\nvar scrollWindow = change => {\n  window.scrollBy(change.x, change.y);\n};\n\nconst getScrollableDroppables = memoizeOne(droppables => toDroppableList(droppables).filter(droppable => {\n  if (!droppable.isEnabled) {\n    return false;\n  }\n  if (!droppable.frame) {\n    return false;\n  }\n  return true;\n}));\nconst getScrollableDroppableOver = (target, droppables) => {\n  const maybe = getScrollableDroppables(droppables).find(droppable => {\n    !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Invalid result') : invariant() : void 0;\n    return isPositionInFrame(droppable.frame.pageMarginBox)(target);\n  }) || null;\n  return maybe;\n};\nvar getBestScrollableDroppable = ({\n  center,\n  destination,\n  droppables\n}) => {\n  if (destination) {\n    const dimension = droppables[destination];\n    if (!dimension.frame) {\n      return null;\n    }\n    return dimension;\n  }\n  const dimension = getScrollableDroppableOver(center, droppables);\n  return dimension;\n};\n\nconst defaultAutoScrollerOptions = {\n  startFromPercentage: 0.25,\n  maxScrollAtPercentage: 0.05,\n  maxPixelScroll: 28,\n  ease: percentage => percentage ** 2,\n  durationDampening: {\n    stopDampeningAt: 1200,\n    accelerateAt: 360\n  },\n  disabled: false\n};\n\nvar getDistanceThresholds = (container, axis, getAutoScrollerOptions = () => defaultAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  const startScrollingFrom = container[axis.size] * autoScrollerOptions.startFromPercentage;\n  const maxScrollValueAt = container[axis.size] * autoScrollerOptions.maxScrollAtPercentage;\n  const thresholds = {\n    startScrollingFrom,\n    maxScrollValueAt\n  };\n  return thresholds;\n};\n\nvar getPercentage = ({\n  startOfRange,\n  endOfRange,\n  current\n}) => {\n  const range = endOfRange - startOfRange;\n  if (range === 0) {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Detected distance range of 0 in the fluid auto scroller\n      This is unexpected and would cause a divide by 0 issue.\n      Not allowing an auto scroll\n    `) : void 0;\n    return 0;\n  }\n  const currentInRange = current - startOfRange;\n  const percentage = currentInRange / range;\n  return percentage;\n};\n\nvar minScroll = 1;\n\nvar getValueFromDistance = (distanceToEdge, thresholds, getAutoScrollerOptions = () => defaultAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  if (distanceToEdge > thresholds.startScrollingFrom) {\n    return 0;\n  }\n  if (distanceToEdge <= thresholds.maxScrollValueAt) {\n    return autoScrollerOptions.maxPixelScroll;\n  }\n  if (distanceToEdge === thresholds.startScrollingFrom) {\n    return minScroll;\n  }\n  const percentageFromMaxScrollValueAt = getPercentage({\n    startOfRange: thresholds.maxScrollValueAt,\n    endOfRange: thresholds.startScrollingFrom,\n    current: distanceToEdge\n  });\n  const percentageFromStartScrollingFrom = 1 - percentageFromMaxScrollValueAt;\n  const scroll = autoScrollerOptions.maxPixelScroll * autoScrollerOptions.ease(percentageFromStartScrollingFrom);\n  return Math.ceil(scroll);\n};\n\nvar dampenValueByTime = (proposedScroll, dragStartTime, getAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  const accelerateAt = autoScrollerOptions.durationDampening.accelerateAt;\n  const stopAt = autoScrollerOptions.durationDampening.stopDampeningAt;\n  const startOfRange = dragStartTime;\n  const endOfRange = stopAt;\n  const now = Date.now();\n  const runTime = now - startOfRange;\n  if (runTime >= stopAt) {\n    return proposedScroll;\n  }\n  if (runTime < accelerateAt) {\n    return minScroll;\n  }\n  const betweenAccelerateAtAndStopAtPercentage = getPercentage({\n    startOfRange: accelerateAt,\n    endOfRange,\n    current: runTime\n  });\n  const scroll = proposedScroll * autoScrollerOptions.ease(betweenAccelerateAtAndStopAtPercentage);\n  return Math.ceil(scroll);\n};\n\nvar getValue = ({\n  distanceToEdge,\n  thresholds,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const scroll = getValueFromDistance(distanceToEdge, thresholds, getAutoScrollerOptions);\n  if (scroll === 0) {\n    return 0;\n  }\n  if (!shouldUseTimeDampening) {\n    return scroll;\n  }\n  return Math.max(dampenValueByTime(scroll, dragStartTime, getAutoScrollerOptions), minScroll);\n};\n\nvar getScrollOnAxis = ({\n  container,\n  distanceToEdges,\n  dragStartTime,\n  axis,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const thresholds = getDistanceThresholds(container, axis, getAutoScrollerOptions);\n  const isCloserToEnd = distanceToEdges[axis.end] < distanceToEdges[axis.start];\n  if (isCloserToEnd) {\n    return getValue({\n      distanceToEdge: distanceToEdges[axis.end],\n      thresholds,\n      dragStartTime,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n  }\n  return -1 * getValue({\n    distanceToEdge: distanceToEdges[axis.start],\n    thresholds,\n    dragStartTime,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n};\n\nvar adjustForSizeLimits = ({\n  container,\n  subject,\n  proposedScroll\n}) => {\n  const isTooBigVertically = subject.height > container.height;\n  const isTooBigHorizontally = subject.width > container.width;\n  if (!isTooBigHorizontally && !isTooBigVertically) {\n    return proposedScroll;\n  }\n  if (isTooBigHorizontally && isTooBigVertically) {\n    return null;\n  }\n  return {\n    x: isTooBigHorizontally ? 0 : proposedScroll.x,\n    y: isTooBigVertically ? 0 : proposedScroll.y\n  };\n};\n\nconst clean = apply(value => value === 0 ? 0 : value);\nvar getScroll$1 = ({\n  dragStartTime,\n  container,\n  subject,\n  center,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const distanceToEdges = {\n    top: center.y - container.top,\n    right: container.right - center.x,\n    bottom: container.bottom - center.y,\n    left: center.x - container.left\n  };\n  const y = getScrollOnAxis({\n    container,\n    distanceToEdges,\n    dragStartTime,\n    axis: vertical,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  const x = getScrollOnAxis({\n    container,\n    distanceToEdges,\n    dragStartTime,\n    axis: horizontal,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  const required = clean({\n    x,\n    y\n  });\n  if (isEqual$1(required, origin)) {\n    return null;\n  }\n  const limited = adjustForSizeLimits({\n    container,\n    subject,\n    proposedScroll: required\n  });\n  if (!limited) {\n    return null;\n  }\n  return isEqual$1(limited, origin) ? null : limited;\n};\n\nconst smallestSigned = apply(value => {\n  if (value === 0) {\n    return 0;\n  }\n  return value > 0 ? 1 : -1;\n});\nconst getOverlap = (() => {\n  const getRemainder = (target, max) => {\n    if (target < 0) {\n      return target;\n    }\n    if (target > max) {\n      return target - max;\n    }\n    return 0;\n  };\n  return ({\n    current,\n    max,\n    change\n  }) => {\n    const targetScroll = add(current, change);\n    const overlap = {\n      x: getRemainder(targetScroll.x, max.x),\n      y: getRemainder(targetScroll.y, max.y)\n    };\n    if (isEqual$1(overlap, origin)) {\n      return null;\n    }\n    return overlap;\n  };\n})();\nconst canPartiallyScroll = ({\n  max: rawMax,\n  current,\n  change\n}) => {\n  const max = {\n    x: Math.max(current.x, rawMax.x),\n    y: Math.max(current.y, rawMax.y)\n  };\n  const smallestChange = smallestSigned(change);\n  const overlap = getOverlap({\n    max,\n    current,\n    change: smallestChange\n  });\n  if (!overlap) {\n    return true;\n  }\n  if (smallestChange.x !== 0 && overlap.x === 0) {\n    return true;\n  }\n  if (smallestChange.y !== 0 && overlap.y === 0) {\n    return true;\n  }\n  return false;\n};\nconst canScrollWindow = (viewport, change) => canPartiallyScroll({\n  current: viewport.scroll.current,\n  max: viewport.scroll.max,\n  change\n});\nconst getWindowOverlap = (viewport, change) => {\n  if (!canScrollWindow(viewport, change)) {\n    return null;\n  }\n  const max = viewport.scroll.max;\n  const current = viewport.scroll.current;\n  return getOverlap({\n    current,\n    max,\n    change\n  });\n};\nconst canScrollDroppable = (droppable, change) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return false;\n  }\n  return canPartiallyScroll({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change\n  });\n};\nconst getDroppableOverlap = (droppable, change) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return null;\n  }\n  if (!canScrollDroppable(droppable, change)) {\n    return null;\n  }\n  return getOverlap({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change\n  });\n};\n\nvar getWindowScrollChange = ({\n  viewport,\n  subject,\n  center,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const scroll = getScroll$1({\n    dragStartTime,\n    container: viewport.frame,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  return scroll && canScrollWindow(viewport, scroll) ? scroll : null;\n};\n\nvar getDroppableScrollChange = ({\n  droppable,\n  subject,\n  center,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return null;\n  }\n  const scroll = getScroll$1({\n    dragStartTime,\n    container: frame.pageMarginBox,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  return scroll && canScrollDroppable(droppable, scroll) ? scroll : null;\n};\n\nvar scroll = ({\n  state,\n  dragStartTime,\n  shouldUseTimeDampening,\n  scrollWindow,\n  scrollDroppable,\n  getAutoScrollerOptions\n}) => {\n  const center = state.current.page.borderBoxCenter;\n  const draggable = state.dimensions.draggables[state.critical.draggable.id];\n  const subject = draggable.page.marginBox;\n  if (state.isWindowScrollAllowed) {\n    const viewport = state.viewport;\n    const change = getWindowScrollChange({\n      dragStartTime,\n      viewport,\n      subject,\n      center,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n    if (change) {\n      scrollWindow(change);\n      return;\n    }\n  }\n  const droppable = getBestScrollableDroppable({\n    center,\n    destination: whatIsDraggedOver(state.impact),\n    droppables: state.dimensions.droppables\n  });\n  if (!droppable) {\n    return;\n  }\n  const change = getDroppableScrollChange({\n    dragStartTime,\n    droppable,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  if (change) {\n    scrollDroppable(droppable.descriptor.id, change);\n  }\n};\n\nvar createFluidScroller = ({\n  scrollWindow,\n  scrollDroppable,\n  getAutoScrollerOptions = () => defaultAutoScrollerOptions\n}) => {\n  const scheduleWindowScroll = rafSchd(scrollWindow);\n  const scheduleDroppableScroll = rafSchd(scrollDroppable);\n  let dragging = null;\n  const tryScroll = state => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fluid scroll if not dragging') : invariant() : void 0;\n    const {\n      shouldUseTimeDampening,\n      dragStartTime\n    } = dragging;\n    scroll({\n      state,\n      scrollWindow: scheduleWindowScroll,\n      scrollDroppable: scheduleDroppableScroll,\n      dragStartTime,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n  };\n  const start$1 = state => {\n    start();\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start auto scrolling when already started') : invariant() : void 0;\n    const dragStartTime = Date.now();\n    let wasScrollNeeded = false;\n    const fakeScrollCallback = () => {\n      wasScrollNeeded = true;\n    };\n    scroll({\n      state,\n      dragStartTime: 0,\n      shouldUseTimeDampening: false,\n      scrollWindow: fakeScrollCallback,\n      scrollDroppable: fakeScrollCallback,\n      getAutoScrollerOptions\n    });\n    dragging = {\n      dragStartTime,\n      shouldUseTimeDampening: wasScrollNeeded\n    };\n    finish();\n    if (wasScrollNeeded) {\n      tryScroll(state);\n    }\n  };\n  const stop = () => {\n    if (!dragging) {\n      return;\n    }\n    scheduleWindowScroll.cancel();\n    scheduleDroppableScroll.cancel();\n    dragging = null;\n  };\n  return {\n    start: start$1,\n    stop,\n    scroll: tryScroll\n  };\n};\n\nvar createJumpScroller = ({\n  move,\n  scrollDroppable,\n  scrollWindow\n}) => {\n  const moveByOffset = (state, offset) => {\n    const client = add(state.current.client.selection, offset);\n    move({\n      client\n    });\n  };\n  const scrollDroppableAsMuchAsItCan = (droppable, change) => {\n    if (!canScrollDroppable(droppable, change)) {\n      return change;\n    }\n    const overlap = getDroppableOverlap(droppable, change);\n    if (!overlap) {\n      scrollDroppable(droppable.descriptor.id, change);\n      return null;\n    }\n    const whatTheDroppableCanScroll = subtract(change, overlap);\n    scrollDroppable(droppable.descriptor.id, whatTheDroppableCanScroll);\n    const remainder = subtract(change, whatTheDroppableCanScroll);\n    return remainder;\n  };\n  const scrollWindowAsMuchAsItCan = (isWindowScrollAllowed, viewport, change) => {\n    if (!isWindowScrollAllowed) {\n      return change;\n    }\n    if (!canScrollWindow(viewport, change)) {\n      return change;\n    }\n    const overlap = getWindowOverlap(viewport, change);\n    if (!overlap) {\n      scrollWindow(change);\n      return null;\n    }\n    const whatTheWindowCanScroll = subtract(change, overlap);\n    scrollWindow(whatTheWindowCanScroll);\n    const remainder = subtract(change, whatTheWindowCanScroll);\n    return remainder;\n  };\n  const jumpScroller = state => {\n    const request = state.scrollJumpRequest;\n    if (!request) {\n      return;\n    }\n    const destination = whatIsDraggedOver(state.impact);\n    !destination ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot perform a jump scroll when there is no destination') : invariant() : void 0;\n    const droppableRemainder = scrollDroppableAsMuchAsItCan(state.dimensions.droppables[destination], request);\n    if (!droppableRemainder) {\n      return;\n    }\n    const viewport = state.viewport;\n    const windowRemainder = scrollWindowAsMuchAsItCan(state.isWindowScrollAllowed, viewport, droppableRemainder);\n    if (!windowRemainder) {\n      return;\n    }\n    moveByOffset(state, windowRemainder);\n  };\n  return jumpScroller;\n};\n\nvar createAutoScroller = ({\n  scrollDroppable,\n  scrollWindow,\n  move,\n  getAutoScrollerOptions\n}) => {\n  const fluidScroller = createFluidScroller({\n    scrollWindow,\n    scrollDroppable,\n    getAutoScrollerOptions\n  });\n  const jumpScroll = createJumpScroller({\n    move,\n    scrollWindow,\n    scrollDroppable\n  });\n  const scroll = state => {\n    const autoScrollerOptions = getAutoScrollerOptions();\n    if (autoScrollerOptions.disabled || state.phase !== 'DRAGGING') {\n      return;\n    }\n    if (state.movementMode === 'FLUID') {\n      fluidScroller.scroll(state);\n      return;\n    }\n    if (!state.scrollJumpRequest) {\n      return;\n    }\n    jumpScroll(state);\n  };\n  const scroller = {\n    scroll,\n    start: fluidScroller.start,\n    stop: fluidScroller.stop\n  };\n  return scroller;\n};\n\nconst prefix = 'data-rfd';\nconst dragHandle = (() => {\n  const base = `${prefix}-drag-handle`;\n  return {\n    base,\n    draggableId: `${base}-draggable-id`,\n    contextId: `${base}-context-id`\n  };\n})();\nconst draggable = (() => {\n  const base = `${prefix}-draggable`;\n  return {\n    base,\n    contextId: `${base}-context-id`,\n    id: `${base}-id`\n  };\n})();\nconst droppable = (() => {\n  const base = `${prefix}-droppable`;\n  return {\n    base,\n    contextId: `${base}-context-id`,\n    id: `${base}-id`\n  };\n})();\nconst scrollContainer = {\n  contextId: `${prefix}-scroll-container-context-id`\n};\n\nconst makeGetSelector = context => attribute => `[${attribute}=\"${context}\"]`;\nconst getStyles = (rules, property) => rules.map(rule => {\n  const value = rule.styles[property];\n  if (!value) {\n    return '';\n  }\n  return `${rule.selector} { ${value} }`;\n}).join(' ');\nconst noPointerEvents = 'pointer-events: none;';\nvar getStyles$1 = contextId => {\n  const getSelector = makeGetSelector(contextId);\n  const dragHandle$1 = (() => {\n    const grabCursor = `\n      cursor: -webkit-grab;\n      cursor: grab;\n    `;\n    return {\n      selector: getSelector(dragHandle.contextId),\n      styles: {\n        always: `\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        `,\n        resting: grabCursor,\n        dragging: noPointerEvents,\n        dropAnimating: grabCursor\n      }\n    };\n  })();\n  const draggable$1 = (() => {\n    const transition = `\n      transition: ${transitions.outOfTheWay};\n    `;\n    return {\n      selector: getSelector(draggable.contextId),\n      styles: {\n        dragging: transition,\n        dropAnimating: transition,\n        userCancel: transition\n      }\n    };\n  })();\n  const droppable$1 = {\n    selector: getSelector(droppable.contextId),\n    styles: {\n      always: `overflow-anchor: none;`\n    }\n  };\n  const body = {\n    selector: 'body',\n    styles: {\n      dragging: `\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      `\n    }\n  };\n  const rules = [draggable$1, dragHandle$1, droppable$1, body];\n  return {\n    always: getStyles(rules, 'always'),\n    resting: getStyles(rules, 'resting'),\n    dragging: getStyles(rules, 'dragging'),\n    dropAnimating: getStyles(rules, 'dropAnimating'),\n    userCancel: getStyles(rules, 'userCancel')\n  };\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;\n\nconst getHead = () => {\n  const head = document.querySelector('head');\n  !head ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find the head to append a style to') : invariant() : void 0;\n  return head;\n};\nconst createStyleEl = nonce => {\n  const el = document.createElement('style');\n  if (nonce) {\n    el.setAttribute('nonce', nonce);\n  }\n  el.type = 'text/css';\n  return el;\n};\nfunction useStyleMarshal(contextId, nonce) {\n  const styles = useMemo(() => getStyles$1(contextId), [contextId]);\n  const alwaysRef = useRef(null);\n  const dynamicRef = useRef(null);\n  const setDynamicStyle = useCallback(memoizeOne(proposed => {\n    const el = dynamicRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant() : void 0;\n    el.textContent = proposed;\n  }), []);\n  const setAlwaysStyle = useCallback(proposed => {\n    const el = alwaysRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant() : void 0;\n    el.textContent = proposed;\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    !(!alwaysRef.current && !dynamicRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'style elements already mounted') : invariant() : void 0;\n    const always = createStyleEl(nonce);\n    const dynamic = createStyleEl(nonce);\n    alwaysRef.current = always;\n    dynamicRef.current = dynamic;\n    always.setAttribute(`${prefix}-always`, contextId);\n    dynamic.setAttribute(`${prefix}-dynamic`, contextId);\n    getHead().appendChild(always);\n    getHead().appendChild(dynamic);\n    setAlwaysStyle(styles.always);\n    setDynamicStyle(styles.resting);\n    return () => {\n      const remove = ref => {\n        const current = ref.current;\n        !current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot unmount ref as it is not set') : invariant() : void 0;\n        getHead().removeChild(current);\n        ref.current = null;\n      };\n      remove(alwaysRef);\n      remove(dynamicRef);\n    };\n  }, [nonce, setAlwaysStyle, setDynamicStyle, styles.always, styles.resting, contextId]);\n  const dragging = useCallback(() => setDynamicStyle(styles.dragging), [setDynamicStyle, styles.dragging]);\n  const dropping = useCallback(reason => {\n    if (reason === 'DROP') {\n      setDynamicStyle(styles.dropAnimating);\n      return;\n    }\n    setDynamicStyle(styles.userCancel);\n  }, [setDynamicStyle, styles.dropAnimating, styles.userCancel]);\n  const resting = useCallback(() => {\n    if (!dynamicRef.current) {\n      return;\n    }\n    setDynamicStyle(styles.resting);\n  }, [setDynamicStyle, styles.resting]);\n  const marshal = useMemo(() => ({\n    dragging,\n    dropping,\n    resting\n  }), [dragging, dropping, resting]);\n  return marshal;\n}\n\nfunction querySelectorAll(parentNode, selector) {\n  return Array.from(parentNode.querySelectorAll(selector));\n}\n\nvar getWindowFromEl = el => {\n  if (el && el.ownerDocument && el.ownerDocument.defaultView) {\n    return el.ownerDocument.defaultView;\n  }\n  return window;\n};\n\nfunction isHtmlElement(el) {\n  return el instanceof getWindowFromEl(el).HTMLElement;\n}\n\nfunction findDragHandle(contextId, draggableId) {\n  const selector = `[${dragHandle.contextId}=\"${contextId}\"]`;\n  const possible = querySelectorAll(document, selector);\n  if (!possible.length) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find any drag handles in the context \"${contextId}\"`) : void 0;\n    return null;\n  }\n  const handle = possible.find(el => {\n    return el.getAttribute(dragHandle.draggableId) === draggableId;\n  });\n  if (!handle) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find drag handle with id \"${draggableId}\" as no handle with a matching id was found`) : void 0;\n    return null;\n  }\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle needs to be a HTMLElement') : void 0;\n    return null;\n  }\n  return handle;\n}\n\nfunction useFocusMarshal(contextId) {\n  const entriesRef = useRef({});\n  const recordRef = useRef(null);\n  const restoreFocusFrameRef = useRef(null);\n  const isMountedRef = useRef(false);\n  const register = useCallback(function register(id, focus) {\n    const entry = {\n      id,\n      focus\n    };\n    entriesRef.current[id] = entry;\n    return function unregister() {\n      const entries = entriesRef.current;\n      const current = entries[id];\n      if (current !== entry) {\n        delete entries[id];\n      }\n    };\n  }, []);\n  const tryGiveFocus = useCallback(function tryGiveFocus(tryGiveFocusTo) {\n    const handle = findDragHandle(contextId, tryGiveFocusTo);\n    if (handle && handle !== document.activeElement) {\n      handle.focus();\n    }\n  }, [contextId]);\n  const tryShiftRecord = useCallback(function tryShiftRecord(previous, redirectTo) {\n    if (recordRef.current === previous) {\n      recordRef.current = redirectTo;\n    }\n  }, []);\n  const tryRestoreFocusRecorded = useCallback(function tryRestoreFocusRecorded() {\n    if (restoreFocusFrameRef.current) {\n      return;\n    }\n    if (!isMountedRef.current) {\n      return;\n    }\n    restoreFocusFrameRef.current = requestAnimationFrame(() => {\n      restoreFocusFrameRef.current = null;\n      const record = recordRef.current;\n      if (record) {\n        tryGiveFocus(record);\n      }\n    });\n  }, [tryGiveFocus]);\n  const tryRecordFocus = useCallback(function tryRecordFocus(id) {\n    recordRef.current = null;\n    const focused = document.activeElement;\n    if (!focused) {\n      return;\n    }\n    if (focused.getAttribute(dragHandle.draggableId) !== id) {\n      return;\n    }\n    recordRef.current = id;\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    isMountedRef.current = true;\n    return function clearFrameOnUnmount() {\n      isMountedRef.current = false;\n      const frameId = restoreFocusFrameRef.current;\n      if (frameId) {\n        cancelAnimationFrame(frameId);\n      }\n    };\n  }, []);\n  const marshal = useMemo(() => ({\n    register,\n    tryRecordFocus,\n    tryRestoreFocusRecorded,\n    tryShiftRecord\n  }), [register, tryRecordFocus, tryRestoreFocusRecorded, tryShiftRecord]);\n  return marshal;\n}\n\nfunction createRegistry() {\n  const entries = {\n    draggables: {},\n    droppables: {}\n  };\n  const subscribers = [];\n  function subscribe(cb) {\n    subscribers.push(cb);\n    return function unsubscribe() {\n      const index = subscribers.indexOf(cb);\n      if (index === -1) {\n        return;\n      }\n      subscribers.splice(index, 1);\n    };\n  }\n  function notify(event) {\n    if (subscribers.length) {\n      subscribers.forEach(cb => cb(event));\n    }\n  }\n  function findDraggableById(id) {\n    return entries.draggables[id] || null;\n  }\n  function getDraggableById(id) {\n    const entry = findDraggableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find draggable entry with id [${id}]`) : invariant() : void 0;\n    return entry;\n  }\n  const draggableAPI = {\n    register: entry => {\n      entries.draggables[entry.descriptor.id] = entry;\n      notify({\n        type: 'ADDITION',\n        value: entry\n      });\n    },\n    update: (entry, last) => {\n      const current = entries.draggables[last.descriptor.id];\n      if (!current) {\n        return;\n      }\n      if (current.uniqueId !== entry.uniqueId) {\n        return;\n      }\n      delete entries.draggables[last.descriptor.id];\n      entries.draggables[entry.descriptor.id] = entry;\n    },\n    unregister: entry => {\n      const draggableId = entry.descriptor.id;\n      const current = findDraggableById(draggableId);\n      if (!current) {\n        return;\n      }\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n      delete entries.draggables[draggableId];\n      if (entries.droppables[entry.descriptor.droppableId]) {\n        notify({\n          type: 'REMOVAL',\n          value: entry\n        });\n      }\n    },\n    getById: getDraggableById,\n    findById: findDraggableById,\n    exists: id => Boolean(findDraggableById(id)),\n    getAllByType: type => Object.values(entries.draggables).filter(entry => entry.descriptor.type === type)\n  };\n  function findDroppableById(id) {\n    return entries.droppables[id] || null;\n  }\n  function getDroppableById(id) {\n    const entry = findDroppableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find droppable entry with id [${id}]`) : invariant() : void 0;\n    return entry;\n  }\n  const droppableAPI = {\n    register: entry => {\n      entries.droppables[entry.descriptor.id] = entry;\n    },\n    unregister: entry => {\n      const current = findDroppableById(entry.descriptor.id);\n      if (!current) {\n        return;\n      }\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n      delete entries.droppables[entry.descriptor.id];\n    },\n    getById: getDroppableById,\n    findById: findDroppableById,\n    exists: id => Boolean(findDroppableById(id)),\n    getAllByType: type => Object.values(entries.droppables).filter(entry => entry.descriptor.type === type)\n  };\n  function clean() {\n    entries.draggables = {};\n    entries.droppables = {};\n    subscribers.length = 0;\n  }\n  return {\n    draggable: draggableAPI,\n    droppable: droppableAPI,\n    subscribe,\n    clean\n  };\n}\n\nfunction useRegistry() {\n  const registry = useMemo(createRegistry, []);\n  useEffect(() => {\n    return function unmount() {\n      registry.clean();\n    };\n  }, [registry]);\n  return registry;\n}\n\nvar StoreContext = React.createContext(null);\n\nvar getBodyElement = () => {\n  const body = document.body;\n  !body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.body') : invariant() : void 0;\n  return body;\n};\n\nconst visuallyHidden = {\n  position: 'absolute',\n  width: '1px',\n  height: '1px',\n  margin: '-1px',\n  border: '0',\n  padding: '0',\n  overflow: 'hidden',\n  clip: 'rect(0 0 0 0)',\n  'clip-path': 'inset(100%)'\n};\n\nconst getId = contextId => `rfd-announcement-${contextId}`;\nfunction useAnnouncer(contextId) {\n  const id = useMemo(() => getId(contextId), [contextId]);\n  const ref = useRef(null);\n  useEffect(function setup() {\n    const el = document.createElement('div');\n    ref.current = el;\n    el.id = id;\n    el.setAttribute('aria-live', 'assertive');\n    el.setAttribute('aria-atomic', 'true');\n    _extends(el.style, visuallyHidden);\n    getBodyElement().appendChild(el);\n    return function cleanup() {\n      setTimeout(function remove() {\n        const body = getBodyElement();\n        if (body.contains(el)) {\n          body.removeChild(el);\n        }\n        if (el === ref.current) {\n          ref.current = null;\n        }\n      });\n    };\n  }, [id]);\n  const announce = useCallback(message => {\n    const el = ref.current;\n    if (el) {\n      el.textContent = message;\n      return;\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      A screen reader message was trying to be announced but it was unable to do so.\n      This can occur if you unmount your <DragDropContext /> in your onDragEnd.\n      Consider calling provided.announce() before the unmount so that the instruction will\n      not be lost for users relying on a screen reader.\n\n      Message not passed to screen reader:\n\n      \"${message}\"\n    `) : void 0;\n  }, []);\n  return announce;\n}\n\nconst defaults = {\n  separator: '::'\n};\nfunction useUniqueId(prefix, options = defaults) {\n  const id = React.useId();\n  return useMemo(() => `${prefix}${options.separator}${id}`, [options.separator, prefix, id]);\n}\n\nfunction getElementId({\n  contextId,\n  uniqueId\n}) {\n  return `rfd-hidden-text-${contextId}-${uniqueId}`;\n}\nfunction useHiddenTextElement({\n  contextId,\n  text\n}) {\n  const uniqueId = useUniqueId('hidden-text', {\n    separator: '-'\n  });\n  const id = useMemo(() => getElementId({\n    contextId,\n    uniqueId\n  }), [uniqueId, contextId]);\n  useEffect(function mount() {\n    const el = document.createElement('div');\n    el.id = id;\n    el.textContent = text;\n    el.style.display = 'none';\n    getBodyElement().appendChild(el);\n    return function unmount() {\n      const body = getBodyElement();\n      if (body.contains(el)) {\n        body.removeChild(el);\n      }\n    };\n  }, [id, text]);\n  return id;\n}\n\nvar AppContext = React.createContext(null);\n\nvar peerDependencies = {\n\treact: \"^18.0.0 || ^19.0.0\"};\n\nconst semver = /(\\d+)\\.(\\d+)\\.(\\d+)/;\nconst getVersion = value => {\n  const result = semver.exec(value);\n  !(result != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Unable to parse React version ${value}`) : invariant() : void 0;\n  const major = Number(result[1]);\n  const minor = Number(result[2]);\n  const patch = Number(result[3]);\n  return {\n    major,\n    minor,\n    patch,\n    raw: value\n  };\n};\nconst isSatisfied = (expected, actual) => {\n  if (actual.major > expected.major) {\n    return true;\n  }\n  if (actual.major < expected.major) {\n    return false;\n  }\n  if (actual.minor > expected.minor) {\n    return true;\n  }\n  if (actual.minor < expected.minor) {\n    return false;\n  }\n  return actual.patch >= expected.patch;\n};\nvar checkReactVersion = (peerDepValue, actualValue) => {\n  const peerDep = getVersion(peerDepValue);\n  const actual = getVersion(actualValue);\n  if (isSatisfied(peerDep, actual)) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    React version: [${actual.raw}]\n    does not satisfy expected peer dependency version: [${peerDep.raw}]\n\n    This can result in run time bugs, and even fatal crashes\n  `) : void 0;\n};\n\nconst suffix = `\n  We expect a html5 doctype: <!doctype html>\n  This is to ensure consistent browser layout and measurement\n\n  More information: https://github.com/hello-pangea/dnd/blob/main/docs/guides/doctype.md\n`;\nvar checkDoctype = doc => {\n  const doctype = doc.doctype;\n  if (!doctype) {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      No <!doctype html> found.\n\n      ${suffix}\n    `) : void 0;\n    return;\n  }\n  if (doctype.name.toLowerCase() !== 'html') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Unexpected <!doctype> found: (${doctype.name})\n\n      ${suffix}\n    `) : void 0;\n  }\n  if (doctype.publicId !== '') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Unexpected <!doctype> publicId found: (${doctype.publicId})\n      A html5 doctype does not have a publicId\n\n      ${suffix}\n    `) : void 0;\n  }\n};\n\nfunction useDev(useHook) {\n  if (process.env.NODE_ENV !== 'production') {\n    useHook();\n  }\n}\n\nfunction useDevSetupWarning(fn, inputs) {\n  useDev(() => {\n    useEffect(() => {\n      try {\n        fn();\n      } catch (e) {\n        error(`\n          A setup problem was encountered.\n\n          > ${e.message}\n        `);\n      }\n    }, inputs);\n  });\n}\n\nfunction useStartupValidation() {\n  useDevSetupWarning(() => {\n    checkReactVersion(peerDependencies.react, React.version);\n    checkDoctype(document);\n  }, []);\n}\n\nfunction usePrevious(current) {\n  const ref = useRef(current);\n  useEffect(() => {\n    ref.current = current;\n  });\n  return ref;\n}\n\nfunction create() {\n  let lock = null;\n  function isClaimed() {\n    return Boolean(lock);\n  }\n  function isActive(value) {\n    return value === lock;\n  }\n  function claim(abandon) {\n    !!lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot claim lock as it is already claimed') : invariant() : void 0;\n    const newLock = {\n      abandon\n    };\n    lock = newLock;\n    return newLock;\n  }\n  function release() {\n    !lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot release lock when there is no lock') : invariant() : void 0;\n    lock = null;\n  }\n  function tryAbandon() {\n    if (lock) {\n      lock.abandon();\n      release();\n    }\n  }\n  return {\n    isClaimed,\n    isActive,\n    claim,\n    release,\n    tryAbandon\n  };\n}\n\nfunction isDragging(state) {\n  if (state.phase === 'IDLE' || state.phase === 'DROP_ANIMATING') {\n    return false;\n  }\n  return state.isDragging;\n}\n\nconst tab = 9;\nconst enter = 13;\nconst escape = 27;\nconst space = 32;\nconst pageUp = 33;\nconst pageDown = 34;\nconst end = 35;\nconst home = 36;\nconst arrowLeft = 37;\nconst arrowUp = 38;\nconst arrowRight = 39;\nconst arrowDown = 40;\n\nconst preventedKeys = {\n  [enter]: true,\n  [tab]: true\n};\nvar preventStandardKeyEvents = event => {\n  if (preventedKeys[event.keyCode]) {\n    event.preventDefault();\n  }\n};\n\nconst supportedEventName = (() => {\n  const base = 'visibilitychange';\n  if (typeof document === 'undefined') {\n    return base;\n  }\n  const candidates = [base, `ms${base}`, `webkit${base}`, `moz${base}`, `o${base}`];\n  const supported = candidates.find(eventName => `on${eventName}` in document);\n  return supported || base;\n})();\n\nconst primaryButton = 0;\nconst sloppyClickThreshold = 5;\nfunction isSloppyClickThresholdExceeded(original, current) {\n  return Math.abs(current.x - original.x) >= sloppyClickThreshold || Math.abs(current.y - original.y) >= sloppyClickThreshold;\n}\nconst idle$1 = {\n  type: 'IDLE'\n};\nfunction getCaptureBindings({\n  cancel,\n  completed,\n  getPhase,\n  setPhase\n}) {\n  return [{\n    eventName: 'mousemove',\n    fn: event => {\n      const {\n        button,\n        clientX,\n        clientY\n      } = event;\n      if (button !== primaryButton) {\n        return;\n      }\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      const phase = getPhase();\n      if (phase.type === 'DRAGGING') {\n        event.preventDefault();\n        phase.actions.move(point);\n        return;\n      }\n      !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot be IDLE') : invariant() : void 0;\n      const pending = phase.point;\n      if (!isSloppyClickThresholdExceeded(pending, point)) {\n        return;\n      }\n      event.preventDefault();\n      const actions = phase.actions.fluidLift(point);\n      setPhase({\n        type: 'DRAGGING',\n        actions\n      });\n    }\n  }, {\n    eventName: 'mouseup',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: event => {\n      if (getPhase().type === 'DRAGGING') {\n        event.preventDefault();\n      }\n      cancel();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type === 'PENDING') {\n        cancel();\n        return;\n      }\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: () => {\n      if (getPhase().type === 'PENDING') {\n        cancel();\n      }\n    }\n  }, {\n    eventName: 'webkitmouseforcedown',\n    fn: event => {\n      const phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase') : invariant() : void 0;\n      if (phase.actions.shouldRespectForcePress()) {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useMouseSensor(api) {\n  const phaseRef = useRef(idle$1);\n  const unbindEventsRef = useRef(noop$2);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'mousedown',\n    fn: function onMouseDown(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      if (event.button !== primaryButton) {\n        return;\n      }\n      if (event.ctrlKey || event.metaKey || event.shiftKey || event.altKey) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const actions = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!actions) {\n        return;\n      }\n      event.preventDefault();\n      const point = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      unbindEventsRef.current();\n      startPendingDrag(actions, point);\n    }\n  }), [api]);\n  const preventForcePressBinding = useMemo(() => ({\n    eventName: 'webkitmouseforcewillbegin',\n    fn: event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      const id = api.findClosestDraggableId(event);\n      if (!id) {\n        return;\n      }\n      const options = api.findOptionsForDraggable(id);\n      if (!options) {\n        return;\n      }\n      if (options.shouldRespectForcePress) {\n        return;\n      }\n      if (!api.canGetLock(id)) {\n        return;\n      }\n      event.preventDefault();\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function listenForCapture() {\n    const options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [preventForcePressBinding, startCaptureBinding], options);\n  }, [preventForcePressBinding, startCaptureBinding]);\n  const stop = useCallback(() => {\n    const current = phaseRef.current;\n    if (current.type === 'IDLE') {\n      return;\n    }\n    phaseRef.current = idle$1;\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture]);\n  const cancel = useCallback(() => {\n    const phase = phaseRef.current;\n    stop();\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  const bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    const bindings = getCaptureBindings({\n      cancel,\n      completed: stop,\n      getPhase: () => phaseRef.current,\n      setPhase: phase => {\n        phaseRef.current = phase;\n      }\n    });\n    unbindEventsRef.current = bindEvents(window, bindings, options);\n  }, [cancel, stop]);\n  const startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(phaseRef.current.type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant() : void 0;\n    phaseRef.current = {\n      type: 'PENDING',\n      point,\n      actions\n    };\n    bindCapturingEvents();\n  }, [bindCapturingEvents]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nfunction noop$1() {}\nconst scrollJumpKeys = {\n  [pageDown]: true,\n  [pageUp]: true,\n  [home]: true,\n  [end]: true\n};\nfunction getDraggingBindings(actions, stop) {\n  function cancel() {\n    stop();\n    actions.cancel();\n  }\n  function drop() {\n    stop();\n    actions.drop();\n  }\n  return [{\n    eventName: 'keydown',\n    fn: event => {\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n      if (event.keyCode === space) {\n        event.preventDefault();\n        drop();\n        return;\n      }\n      if (event.keyCode === arrowDown) {\n        event.preventDefault();\n        actions.moveDown();\n        return;\n      }\n      if (event.keyCode === arrowUp) {\n        event.preventDefault();\n        actions.moveUp();\n        return;\n      }\n      if (event.keyCode === arrowRight) {\n        event.preventDefault();\n        actions.moveRight();\n        return;\n      }\n      if (event.keyCode === arrowLeft) {\n        event.preventDefault();\n        actions.moveLeft();\n        return;\n      }\n      if (scrollJumpKeys[event.keyCode]) {\n        event.preventDefault();\n        return;\n      }\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: cancel\n  }, {\n    eventName: 'mouseup',\n    fn: cancel\n  }, {\n    eventName: 'click',\n    fn: cancel\n  }, {\n    eventName: 'touchstart',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'wheel',\n    fn: cancel,\n    options: {\n      passive: true\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useKeyboardSensor(api) {\n  const unbindEventsRef = useRef(noop$1);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'keydown',\n    fn: function onKeyDown(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      if (event.keyCode !== space) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const preDrag = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!preDrag) {\n        return;\n      }\n      event.preventDefault();\n      let isCapturing = true;\n      const actions = preDrag.snapLift();\n      unbindEventsRef.current();\n      function stop() {\n        !isCapturing ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop capturing a keyboard drag when not capturing') : invariant() : void 0;\n        isCapturing = false;\n        unbindEventsRef.current();\n        listenForCapture();\n      }\n      unbindEventsRef.current = bindEvents(window, getDraggingBindings(actions, stop), {\n        capture: true,\n        passive: false\n      });\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function tryStartCapture() {\n    const options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nconst idle = {\n  type: 'IDLE'\n};\nconst timeForLongPress = 120;\nconst forcePressThreshold = 0.15;\nfunction getWindowBindings({\n  cancel,\n  getPhase\n}) {\n  return [{\n    eventName: 'orientationchange',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'contextmenu',\n    fn: event => {\n      event.preventDefault();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: event => {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      if (event.keyCode === escape) {\n        event.preventDefault();\n      }\n      cancel();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction getHandleBindings({\n  cancel,\n  completed,\n  getPhase\n}) {\n  return [{\n    eventName: 'touchmove',\n    options: {\n      capture: false\n    },\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      phase.hasMoved = true;\n      const {\n        clientX,\n        clientY\n      } = event.touches[0];\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      event.preventDefault();\n      phase.actions.move(point);\n    }\n  }, {\n    eventName: 'touchend',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'touchcancel',\n    fn: event => {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      cancel();\n    }\n  }, {\n    eventName: 'touchforcechange',\n    fn: event => {\n      const phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n      const touch = event.touches[0];\n      if (!touch) {\n        return;\n      }\n      const isForcePress = touch.force >= forcePressThreshold;\n      if (!isForcePress) {\n        return;\n      }\n      const shouldRespect = phase.actions.shouldRespectForcePress();\n      if (phase.type === 'PENDING') {\n        if (shouldRespect) {\n          cancel();\n        }\n        return;\n      }\n      if (shouldRespect) {\n        if (phase.hasMoved) {\n          event.preventDefault();\n          return;\n        }\n        cancel();\n        return;\n      }\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useTouchSensor(api) {\n  const phaseRef = useRef(idle);\n  const unbindEventsRef = useRef(noop$2);\n  const getPhase = useCallback(function getPhase() {\n    return phaseRef.current;\n  }, []);\n  const setPhase = useCallback(function setPhase(phase) {\n    phaseRef.current = phase;\n  }, []);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'touchstart',\n    fn: function onTouchStart(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const actions = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!actions) {\n        return;\n      }\n      const touch = event.touches[0];\n      const {\n        clientX,\n        clientY\n      } = touch;\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      unbindEventsRef.current();\n      startPendingDrag(actions, point);\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function listenForCapture() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  const stop = useCallback(() => {\n    const current = phaseRef.current;\n    if (current.type === 'IDLE') {\n      return;\n    }\n    if (current.type === 'PENDING') {\n      clearTimeout(current.longPressTimerId);\n    }\n    setPhase(idle);\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture, setPhase]);\n  const cancel = useCallback(() => {\n    const phase = phaseRef.current;\n    stop();\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  const bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    const args = {\n      cancel,\n      completed: stop,\n      getPhase\n    };\n    const unbindTarget = bindEvents(window, getHandleBindings(args), options);\n    const unbindWindow = bindEvents(window, getWindowBindings(args), options);\n    unbindEventsRef.current = function unbindAll() {\n      unbindTarget();\n      unbindWindow();\n    };\n  }, [cancel, getPhase, stop]);\n  const startDragging = useCallback(function startDragging() {\n    const phase = getPhase();\n    !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot start dragging from phase ${phase.type}`) : invariant() : void 0;\n    const actions = phase.actions.fluidLift(phase.point);\n    setPhase({\n      type: 'DRAGGING',\n      actions,\n      hasMoved: false\n    });\n  }, [getPhase, setPhase]);\n  const startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(getPhase().type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant() : void 0;\n    const longPressTimerId = setTimeout(startDragging, timeForLongPress);\n    setPhase({\n      type: 'PENDING',\n      point,\n      actions,\n      longPressTimerId\n    });\n    bindCapturingEvents();\n  }, [bindCapturingEvents, getPhase, setPhase, startDragging]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n      const phase = getPhase();\n      if (phase.type === 'PENDING') {\n        clearTimeout(phase.longPressTimerId);\n        setPhase(idle);\n      }\n    };\n  }, [getPhase, listenForCapture, setPhase]);\n  useIsomorphicLayoutEffect(function webkitHack() {\n    const unbind = bindEvents(window, [{\n      eventName: 'touchmove',\n      fn: () => {},\n      options: {\n        capture: false,\n        passive: false\n      }\n    }]);\n    return unbind;\n  }, []);\n}\n\nfunction useValidateSensorHooks(sensorHooks) {\n  useDev(() => {\n    const previousRef = usePrevious(sensorHooks);\n    useDevSetupWarning(() => {\n      !(previousRef.current.length === sensorHooks.length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot change the amount of sensor hooks after mounting') : invariant(false) : void 0;\n    });\n  });\n}\n\nconst interactiveTagNames = ['input', 'button', 'textarea', 'select', 'option', 'optgroup', 'video', 'audio'];\nfunction isAnInteractiveElement(parent, current) {\n  if (current == null) {\n    return false;\n  }\n  const hasAnInteractiveTag = interactiveTagNames.includes(current.tagName.toLowerCase());\n  if (hasAnInteractiveTag) {\n    return true;\n  }\n  const attribute = current.getAttribute('contenteditable');\n  if (attribute === 'true' || attribute === '') {\n    return true;\n  }\n  if (current === parent) {\n    return false;\n  }\n  return isAnInteractiveElement(parent, current.parentElement);\n}\nfunction isEventInInteractiveElement(draggable, event) {\n  const target = event.target;\n  if (!isHtmlElement(target)) {\n    return false;\n  }\n  return isAnInteractiveElement(draggable, target);\n}\n\nvar getBorderBoxCenterPosition = el => getRect(el.getBoundingClientRect()).center;\n\nfunction isElement(el) {\n  return el instanceof getWindowFromEl(el).Element;\n}\n\nconst supportedMatchesName = (() => {\n  const base = 'matches';\n  if (typeof document === 'undefined') {\n    return base;\n  }\n  const candidates = [base, 'msMatchesSelector', 'webkitMatchesSelector'];\n  const value = candidates.find(name => name in Element.prototype);\n  return value || base;\n})();\nfunction closestPonyfill(el, selector) {\n  if (el == null) {\n    return null;\n  }\n  if (el[supportedMatchesName](selector)) {\n    return el;\n  }\n  return closestPonyfill(el.parentElement, selector);\n}\nfunction closest(el, selector) {\n  if (el.closest) {\n    return el.closest(selector);\n  }\n  return closestPonyfill(el, selector);\n}\n\nfunction getSelector(contextId) {\n  return `[${dragHandle.contextId}=\"${contextId}\"]`;\n}\nfunction findClosestDragHandleFromEvent(contextId, event) {\n  const target = event.target;\n  if (!isElement(target)) {\n    process.env.NODE_ENV !== \"production\" ? warning('event.target must be a Element') : void 0;\n    return null;\n  }\n  const selector = getSelector(contextId);\n  const handle = closest(target, selector);\n  if (!handle) {\n    return null;\n  }\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle must be a HTMLElement') : void 0;\n    return null;\n  }\n  return handle;\n}\nfunction tryGetClosestDraggableIdFromEvent(contextId, event) {\n  const handle = findClosestDragHandleFromEvent(contextId, event);\n  if (!handle) {\n    return null;\n  }\n  return handle.getAttribute(dragHandle.draggableId);\n}\n\nfunction findDraggable(contextId, draggableId) {\n  const selector = `[${draggable.contextId}=\"${contextId}\"]`;\n  const possible = querySelectorAll(document, selector);\n  const draggable$1 = possible.find(el => {\n    return el.getAttribute(draggable.id) === draggableId;\n  });\n  if (!draggable$1) {\n    return null;\n  }\n  if (!isHtmlElement(draggable$1)) {\n    process.env.NODE_ENV !== \"production\" ? warning('Draggable element is not a HTMLElement') : void 0;\n    return null;\n  }\n  return draggable$1;\n}\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction isActive({\n  expected,\n  phase,\n  isLockActive,\n  shouldWarn\n}) {\n  if (!isLockActive()) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Cannot perform action.\n        The sensor no longer has an action lock.\n\n        Tips:\n\n        - Throw away your action handlers when forceStop() is called\n        - Check actions.isActive() if you really need to\n      `) : void 0;\n    }\n    return false;\n  }\n  if (expected !== phase) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Cannot perform action.\n        The actions you used belong to an outdated phase\n\n        Current phase: ${expected}\n        You called an action from outdated phase: ${phase}\n\n        Tips:\n\n        - Do not use preDragActions actions after calling preDragActions.lift()\n      `) : void 0;\n    }\n    return false;\n  }\n  return true;\n}\nfunction canStart({\n  lockAPI,\n  store,\n  registry,\n  draggableId\n}) {\n  if (lockAPI.isClaimed()) {\n    return false;\n  }\n  const entry = registry.draggable.findById(draggableId);\n  if (!entry) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find draggable with id: ${draggableId}`) : void 0;\n    return false;\n  }\n  if (!entry.options.isEnabled) {\n    return false;\n  }\n  if (!canStartDrag(store.getState(), draggableId)) {\n    return false;\n  }\n  return true;\n}\nfunction tryStart({\n  lockAPI,\n  contextId,\n  store,\n  registry,\n  draggableId,\n  forceSensorStop,\n  sourceEvent\n}) {\n  const shouldStart = canStart({\n    lockAPI,\n    store,\n    registry,\n    draggableId\n  });\n  if (!shouldStart) {\n    return null;\n  }\n  const entry = registry.draggable.getById(draggableId);\n  const el = findDraggable(contextId, entry.descriptor.id);\n  if (!el) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find draggable element with id: ${draggableId}`) : void 0;\n    return null;\n  }\n  if (sourceEvent && !entry.options.canDragInteractiveElements && isEventInInteractiveElement(el, sourceEvent)) {\n    return null;\n  }\n  const lock = lockAPI.claim(forceSensorStop || noop$2);\n  let phase = 'PRE_DRAG';\n  function getShouldRespectForcePress() {\n    return entry.options.shouldRespectForcePress;\n  }\n  function isLockActive() {\n    return lockAPI.isActive(lock);\n  }\n  function tryDispatch(expected, getAction) {\n    if (isActive({\n      expected,\n      phase,\n      isLockActive,\n      shouldWarn: true\n    })) {\n      store.dispatch(getAction());\n    }\n  }\n  const tryDispatchWhenDragging = tryDispatch.bind(null, 'DRAGGING');\n  function lift(args) {\n    function completed() {\n      lockAPI.release();\n      phase = 'COMPLETED';\n    }\n    if (phase !== 'PRE_DRAG') {\n      completed();\n      process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot lift in phase ${phase}`) : invariant() ;\n    }\n    store.dispatch(lift$1(args.liftActionArgs));\n    phase = 'DRAGGING';\n    function finish(reason, options = {\n      shouldBlockNextClick: false\n    }) {\n      args.cleanup();\n      if (options.shouldBlockNextClick) {\n        const unbind = bindEvents(window, [{\n          eventName: 'click',\n          fn: preventDefault,\n          options: {\n            once: true,\n            passive: false,\n            capture: true\n          }\n        }]);\n        setTimeout(unbind);\n      }\n      completed();\n      store.dispatch(drop({\n        reason\n      }));\n    }\n    return {\n      isActive: () => isActive({\n        expected: 'DRAGGING',\n        phase,\n        isLockActive,\n        shouldWarn: false\n      }),\n      shouldRespectForcePress: getShouldRespectForcePress,\n      drop: options => finish('DROP', options),\n      cancel: options => finish('CANCEL', options),\n      ...args.actions\n    };\n  }\n  function fluidLift(clientSelection) {\n    const move$1 = rafSchd(client => {\n      tryDispatchWhenDragging(() => move({\n        client\n      }));\n    });\n    const api = lift({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection,\n        movementMode: 'FLUID'\n      },\n      cleanup: () => move$1.cancel(),\n      actions: {\n        move: move$1\n      }\n    });\n    return {\n      ...api,\n      move: move$1\n    };\n  }\n  function snapLift() {\n    const actions = {\n      moveUp: () => tryDispatchWhenDragging(moveUp),\n      moveRight: () => tryDispatchWhenDragging(moveRight),\n      moveDown: () => tryDispatchWhenDragging(moveDown),\n      moveLeft: () => tryDispatchWhenDragging(moveLeft)\n    };\n    return lift({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection: getBorderBoxCenterPosition(el),\n        movementMode: 'SNAP'\n      },\n      cleanup: noop$2,\n      actions\n    });\n  }\n  function abortPreDrag() {\n    const shouldRelease = isActive({\n      expected: 'PRE_DRAG',\n      phase,\n      isLockActive,\n      shouldWarn: true\n    });\n    if (shouldRelease) {\n      lockAPI.release();\n    }\n  }\n  const preDrag = {\n    isActive: () => isActive({\n      expected: 'PRE_DRAG',\n      phase,\n      isLockActive,\n      shouldWarn: false\n    }),\n    shouldRespectForcePress: getShouldRespectForcePress,\n    fluidLift,\n    snapLift,\n    abort: abortPreDrag\n  };\n  return preDrag;\n}\nconst defaultSensors = [useMouseSensor, useKeyboardSensor, useTouchSensor];\nfunction useSensorMarshal({\n  contextId,\n  store,\n  registry,\n  customSensors,\n  enableDefaultSensors\n}) {\n  const useSensors = [...(enableDefaultSensors ? defaultSensors : []), ...(customSensors || [])];\n  const lockAPI = useState(() => create())[0];\n  const tryAbandonLock = useCallback(function tryAbandonLock(previous, current) {\n    if (isDragging(previous) && !isDragging(current)) {\n      lockAPI.tryAbandon();\n    }\n  }, [lockAPI]);\n  useIsomorphicLayoutEffect(function listenToStore() {\n    let previous = store.getState();\n    const unsubscribe = store.subscribe(() => {\n      const current = store.getState();\n      tryAbandonLock(previous, current);\n      previous = current;\n    });\n    return unsubscribe;\n  }, [lockAPI, store, tryAbandonLock]);\n  useIsomorphicLayoutEffect(() => {\n    return lockAPI.tryAbandon;\n  }, [lockAPI.tryAbandon]);\n  const canGetLock = useCallback(draggableId => {\n    return canStart({\n      lockAPI,\n      registry,\n      store,\n      draggableId\n    });\n  }, [lockAPI, registry, store]);\n  const tryGetLock = useCallback((draggableId, forceStop, options) => tryStart({\n    lockAPI,\n    registry,\n    contextId,\n    store,\n    draggableId,\n    forceSensorStop: forceStop || null,\n    sourceEvent: options && options.sourceEvent ? options.sourceEvent : null\n  }), [contextId, lockAPI, registry, store]);\n  const findClosestDraggableId = useCallback(event => tryGetClosestDraggableIdFromEvent(contextId, event), [contextId]);\n  const findOptionsForDraggable = useCallback(id => {\n    const entry = registry.draggable.findById(id);\n    return entry ? entry.options : null;\n  }, [registry.draggable]);\n  const tryReleaseLock = useCallback(function tryReleaseLock() {\n    if (!lockAPI.isClaimed()) {\n      return;\n    }\n    lockAPI.tryAbandon();\n    if (store.getState().phase !== 'IDLE') {\n      store.dispatch(flush());\n    }\n  }, [lockAPI, store]);\n  const isLockClaimed = useCallback(() => lockAPI.isClaimed(), [lockAPI]);\n  const api = useMemo(() => ({\n    canGetLock,\n    tryGetLock,\n    findClosestDraggableId,\n    findOptionsForDraggable,\n    tryReleaseLock,\n    isLockClaimed\n  }), [canGetLock, tryGetLock, findClosestDraggableId, findOptionsForDraggable, tryReleaseLock, isLockClaimed]);\n  useValidateSensorHooks(useSensors);\n  for (let i = 0; i < useSensors.length; i++) {\n    useSensors[i](api);\n  }\n}\n\nconst createResponders = props => ({\n  onBeforeCapture: t => {\n    const onBeforeCapureCallback = () => {\n      if (props.onBeforeCapture) {\n        props.onBeforeCapture(t);\n      }\n    };\n    flushSync(onBeforeCapureCallback);\n  },\n  onBeforeDragStart: props.onBeforeDragStart,\n  onDragStart: props.onDragStart,\n  onDragEnd: props.onDragEnd,\n  onDragUpdate: props.onDragUpdate\n});\nconst createAutoScrollerOptions = props => ({\n  ...defaultAutoScrollerOptions,\n  ...props.autoScrollerOptions,\n  durationDampening: {\n    ...defaultAutoScrollerOptions.durationDampening,\n    ...props.autoScrollerOptions\n  }\n});\nfunction getStore(lazyRef) {\n  !lazyRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find store from lazy ref') : invariant() : void 0;\n  return lazyRef.current;\n}\nfunction App(props) {\n  const {\n    contextId,\n    setCallbacks,\n    sensors,\n    nonce,\n    dragHandleUsageInstructions\n  } = props;\n  const lazyStoreRef = useRef(null);\n  useStartupValidation();\n  const lastPropsRef = usePrevious(props);\n  const getResponders = useCallback(() => {\n    return createResponders(lastPropsRef.current);\n  }, [lastPropsRef]);\n  const getAutoScrollerOptions = useCallback(() => {\n    return createAutoScrollerOptions(lastPropsRef.current);\n  }, [lastPropsRef]);\n  const announce = useAnnouncer(contextId);\n  const dragHandleUsageInstructionsId = useHiddenTextElement({\n    contextId,\n    text: dragHandleUsageInstructions\n  });\n  const styleMarshal = useStyleMarshal(contextId, nonce);\n  const lazyDispatch = useCallback(action => {\n    getStore(lazyStoreRef).dispatch(action);\n  }, []);\n  const marshalCallbacks = useMemo(() => bindActionCreators({\n    publishWhileDragging,\n    updateDroppableScroll,\n    updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled,\n    collectionStarting\n  }, lazyDispatch), [lazyDispatch]);\n  const registry = useRegistry();\n  const dimensionMarshal = useMemo(() => {\n    return createDimensionMarshal(registry, marshalCallbacks);\n  }, [registry, marshalCallbacks]);\n  const autoScroller = useMemo(() => createAutoScroller({\n    scrollWindow,\n    scrollDroppable: dimensionMarshal.scrollDroppable,\n    getAutoScrollerOptions,\n    ...bindActionCreators({\n      move\n    }, lazyDispatch)\n  }), [dimensionMarshal.scrollDroppable, lazyDispatch, getAutoScrollerOptions]);\n  const focusMarshal = useFocusMarshal(contextId);\n  const store = useMemo(() => createStore({\n    announce,\n    autoScroller,\n    dimensionMarshal,\n    focusMarshal,\n    getResponders,\n    styleMarshal\n  }), [announce, autoScroller, dimensionMarshal, focusMarshal, getResponders, styleMarshal]);\n  if (process.env.NODE_ENV !== 'production') {\n    if (lazyStoreRef.current && lazyStoreRef.current !== store) {\n      process.env.NODE_ENV !== \"production\" ? warning('unexpected store change') : void 0;\n    }\n  }\n  lazyStoreRef.current = store;\n  const tryResetStore = useCallback(() => {\n    const current = getStore(lazyStoreRef);\n    const state = current.getState();\n    if (state.phase !== 'IDLE') {\n      current.dispatch(flush());\n    }\n  }, []);\n  const isDragging = useCallback(() => {\n    const state = getStore(lazyStoreRef).getState();\n    if (state.phase === 'DROP_ANIMATING') {\n      return true;\n    }\n    if (state.phase === 'IDLE') {\n      return false;\n    }\n    return state.isDragging;\n  }, []);\n  const appCallbacks = useMemo(() => ({\n    isDragging,\n    tryAbort: tryResetStore\n  }), [isDragging, tryResetStore]);\n  setCallbacks(appCallbacks);\n  const getCanLift = useCallback(id => canStartDrag(getStore(lazyStoreRef).getState(), id), []);\n  const getIsMovementAllowed = useCallback(() => isMovementAllowed(getStore(lazyStoreRef).getState()), []);\n  const appContext = useMemo(() => ({\n    marshal: dimensionMarshal,\n    focus: focusMarshal,\n    contextId,\n    canLift: getCanLift,\n    isMovementAllowed: getIsMovementAllowed,\n    dragHandleUsageInstructionsId,\n    registry\n  }), [contextId, dimensionMarshal, dragHandleUsageInstructionsId, focusMarshal, getCanLift, getIsMovementAllowed, registry]);\n  useSensorMarshal({\n    contextId,\n    store,\n    registry,\n    customSensors: sensors || null,\n    enableDefaultSensors: props.enableDefaultSensors !== false\n  });\n  useEffect(() => {\n    return tryResetStore;\n  }, [tryResetStore]);\n  return React.createElement(AppContext.Provider, {\n    value: appContext\n  }, React.createElement(Provider, {\n    context: StoreContext,\n    store: store\n  }, props.children));\n}\n\nfunction useUniqueContextId() {\n  return React.useId();\n}\n\nfunction DragDropContext(props) {\n  const contextId = useUniqueContextId();\n  const dragHandleUsageInstructions = props.dragHandleUsageInstructions || preset.dragHandleUsageInstructions;\n  return React.createElement(ErrorBoundary, null, setCallbacks => React.createElement(App, {\n    nonce: props.nonce,\n    contextId: contextId,\n    setCallbacks: setCallbacks,\n    dragHandleUsageInstructions: dragHandleUsageInstructions,\n    enableDefaultSensors: props.enableDefaultSensors,\n    sensors: props.sensors,\n    onBeforeCapture: props.onBeforeCapture,\n    onBeforeDragStart: props.onBeforeDragStart,\n    onDragStart: props.onDragStart,\n    onDragUpdate: props.onDragUpdate,\n    onDragEnd: props.onDragEnd,\n    autoScrollerOptions: props.autoScrollerOptions\n  }, props.children));\n}\n\nconst zIndexOptions = {\n  dragging: 5000,\n  dropAnimating: 4500\n};\nconst getDraggingTransition = (shouldAnimateDragMovement, dropping) => {\n  if (dropping) {\n    return transitions.drop(dropping.duration);\n  }\n  if (shouldAnimateDragMovement) {\n    return transitions.snap;\n  }\n  return transitions.fluid;\n};\nconst getDraggingOpacity = (isCombining, isDropAnimating) => {\n  if (!isCombining) {\n    return undefined;\n  }\n  return isDropAnimating ? combine.opacity.drop : combine.opacity.combining;\n};\nconst getShouldDraggingAnimate = dragging => {\n  if (dragging.forceShouldAnimate != null) {\n    return dragging.forceShouldAnimate;\n  }\n  return dragging.mode === 'SNAP';\n};\nfunction getDraggingStyle(dragging) {\n  const dimension = dragging.dimension;\n  const box = dimension.client;\n  const {\n    offset,\n    combineWith,\n    dropping\n  } = dragging;\n  const isCombining = Boolean(combineWith);\n  const shouldAnimate = getShouldDraggingAnimate(dragging);\n  const isDropAnimating = Boolean(dropping);\n  const transform = isDropAnimating ? transforms.drop(offset, isCombining) : transforms.moveTo(offset);\n  const style = {\n    position: 'fixed',\n    top: box.marginBox.top,\n    left: box.marginBox.left,\n    boxSizing: 'border-box',\n    width: box.borderBox.width,\n    height: box.borderBox.height,\n    transition: getDraggingTransition(shouldAnimate, dropping),\n    transform,\n    opacity: getDraggingOpacity(isCombining, isDropAnimating),\n    zIndex: isDropAnimating ? zIndexOptions.dropAnimating : zIndexOptions.dragging,\n    pointerEvents: 'none'\n  };\n  return style;\n}\nfunction getSecondaryStyle(secondary) {\n  return {\n    transform: transforms.moveTo(secondary.offset),\n    transition: secondary.shouldAnimateDisplacement ? undefined : 'none'\n  };\n}\nfunction getStyle$1(mapped) {\n  return mapped.type === 'DRAGGING' ? getDraggingStyle(mapped) : getSecondaryStyle(mapped);\n}\n\nfunction getDimension$1(descriptor, el, windowScroll = origin) {\n  const computedStyles = window.getComputedStyle(el);\n  const borderBox = el.getBoundingClientRect();\n  const client = calculateBox(borderBox, computedStyles);\n  const page = withScroll(client, windowScroll);\n  const placeholder = {\n    client,\n    tagName: el.tagName.toLowerCase(),\n    display: computedStyles.display\n  };\n  const displaceBy = {\n    x: client.marginBox.width,\n    y: client.marginBox.height\n  };\n  const dimension = {\n    descriptor,\n    placeholder,\n    displaceBy,\n    client,\n    page\n  };\n  return dimension;\n}\n\nfunction useDraggablePublisher(args) {\n  const uniqueId = useUniqueId('draggable');\n  const {\n    descriptor,\n    registry,\n    getDraggableRef,\n    canDragInteractiveElements,\n    shouldRespectForcePress,\n    isEnabled\n  } = args;\n  const options = useMemo(() => ({\n    canDragInteractiveElements,\n    shouldRespectForcePress,\n    isEnabled\n  }), [canDragInteractiveElements, isEnabled, shouldRespectForcePress]);\n  const getDimension = useCallback(windowScroll => {\n    const el = getDraggableRef();\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get dimension when no ref is set') : invariant() : void 0;\n    return getDimension$1(descriptor, el, windowScroll);\n  }, [descriptor, getDraggableRef]);\n  const entry = useMemo(() => ({\n    uniqueId,\n    descriptor,\n    options,\n    getDimension\n  }), [descriptor, getDimension, options, uniqueId]);\n  const publishedRef = useRef(entry);\n  const isFirstPublishRef = useRef(true);\n  useIsomorphicLayoutEffect(() => {\n    registry.draggable.register(publishedRef.current);\n    return () => registry.draggable.unregister(publishedRef.current);\n  }, [registry.draggable]);\n  useIsomorphicLayoutEffect(() => {\n    if (isFirstPublishRef.current) {\n      isFirstPublishRef.current = false;\n      return;\n    }\n    const last = publishedRef.current;\n    publishedRef.current = entry;\n    registry.draggable.update(entry, last);\n  }, [entry, registry.draggable]);\n}\n\nvar DroppableContext = React.createContext(null);\n\nfunction checkIsValidInnerRef(el) {\n  !(el && isHtmlElement(el)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `\n    provided.innerRef has not been provided with a HTMLElement.\n\n    You can find a guide on using the innerRef callback functions at:\n    https://github.com/hello-pangea/dnd/blob/main/docs/guides/using-inner-ref.md\n  `) : invariant() : void 0;\n}\n\nfunction useValidation$1(props, contextId, getRef) {\n  useDevSetupWarning(() => {\n    function prefix(id) {\n      return `Draggable[id: ${id}]: `;\n    }\n    const id = props.draggableId;\n    !id ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable requires a draggableId') : invariant(false) : void 0;\n    !(typeof id === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Draggable requires a [string] draggableId.\n      Provided: [type: ${typeof id}] (value: ${id})`) : invariant(false) : void 0;\n    !Number.isInteger(props.index) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${prefix(id)} requires an integer index prop`) : invariant(false) : void 0;\n    if (props.mapped.type === 'DRAGGING') {\n      return;\n    }\n    checkIsValidInnerRef(getRef());\n    if (props.isEnabled) {\n      !findDragHandle(contextId, id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${prefix(id)} Unable to find drag handle`) : invariant(false) : void 0;\n    }\n  });\n}\nfunction useClonePropValidation(isClone) {\n  useDev(() => {\n    const initialRef = useRef(isClone);\n    useDevSetupWarning(() => {\n      !(isClone === initialRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable isClone prop value changed during component life') : invariant(false) : void 0;\n    }, [isClone]);\n  });\n}\n\nfunction useRequiredContext(Context) {\n  const result = useContext(Context);\n  !result ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find required context') : invariant() : void 0;\n  return result;\n}\n\nfunction preventHtml5Dnd(event) {\n  event.preventDefault();\n}\nconst Draggable = props => {\n  const ref = useRef(null);\n  const setRef = useCallback((el = null) => {\n    ref.current = el;\n  }, []);\n  const getRef = useCallback(() => ref.current, []);\n  const {\n    contextId,\n    dragHandleUsageInstructionsId,\n    registry\n  } = useRequiredContext(AppContext);\n  const {\n    type,\n    droppableId\n  } = useRequiredContext(DroppableContext);\n  const descriptor = useMemo(() => ({\n    id: props.draggableId,\n    index: props.index,\n    type,\n    droppableId\n  }), [props.draggableId, props.index, type, droppableId]);\n  const {\n    children,\n    draggableId,\n    isEnabled,\n    shouldRespectForcePress,\n    canDragInteractiveElements,\n    isClone,\n    mapped,\n    dropAnimationFinished: dropAnimationFinishedAction\n  } = props;\n  useValidation$1(props, contextId, getRef);\n  useClonePropValidation(isClone);\n  if (!isClone) {\n    const forPublisher = useMemo(() => ({\n      descriptor,\n      registry,\n      getDraggableRef: getRef,\n      canDragInteractiveElements,\n      shouldRespectForcePress,\n      isEnabled\n    }), [descriptor, registry, getRef, canDragInteractiveElements, shouldRespectForcePress, isEnabled]);\n    useDraggablePublisher(forPublisher);\n  }\n  const dragHandleProps = useMemo(() => isEnabled ? {\n    tabIndex: 0,\n    role: 'button',\n    'aria-describedby': dragHandleUsageInstructionsId,\n    'data-rfd-drag-handle-draggable-id': draggableId,\n    'data-rfd-drag-handle-context-id': contextId,\n    draggable: false,\n    onDragStart: preventHtml5Dnd\n  } : null, [contextId, dragHandleUsageInstructionsId, draggableId, isEnabled]);\n  const onMoveEnd = useCallback(event => {\n    if (mapped.type !== 'DRAGGING') {\n      return;\n    }\n    if (!mapped.dropping) {\n      return;\n    }\n    if (event.propertyName !== 'transform') {\n      return;\n    }\n    flushSync(dropAnimationFinishedAction);\n  }, [dropAnimationFinishedAction, mapped]);\n  const provided = useMemo(() => {\n    const style = getStyle$1(mapped);\n    const onTransitionEnd = mapped.type === 'DRAGGING' && mapped.dropping ? onMoveEnd : undefined;\n    const result = {\n      innerRef: setRef,\n      draggableProps: {\n        'data-rfd-draggable-context-id': contextId,\n        'data-rfd-draggable-id': draggableId,\n        style,\n        onTransitionEnd\n      },\n      dragHandleProps\n    };\n    return result;\n  }, [contextId, dragHandleProps, draggableId, mapped, onMoveEnd, setRef]);\n  const rubric = useMemo(() => ({\n    draggableId: descriptor.id,\n    type: descriptor.type,\n    source: {\n      index: descriptor.index,\n      droppableId: descriptor.droppableId\n    }\n  }), [descriptor.droppableId, descriptor.id, descriptor.index, descriptor.type]);\n  return React.createElement(React.Fragment, null, children(provided, mapped.snapshot, rubric));\n};\n\nvar isStrictEqual = (a, b) => a === b;\n\nvar whatIsDraggedOverFromResult = result => {\n  const {\n    combine,\n    destination\n  } = result;\n  if (destination) {\n    return destination.droppableId;\n  }\n  if (combine) {\n    return combine.droppableId;\n  }\n  return null;\n};\n\nconst getCombineWithFromResult = result => {\n  return result.combine ? result.combine.draggableId : null;\n};\nconst getCombineWithFromImpact = impact => {\n  return impact.at && impact.at.type === 'COMBINE' ? impact.at.combine.draggableId : null;\n};\nfunction getDraggableSelector() {\n  const memoizedOffset = memoizeOne((x, y) => ({\n    x,\n    y\n  }));\n  const getMemoizedSnapshot = memoizeOne((mode, isClone, draggingOver = null, combineWith = null, dropping = null) => ({\n    isDragging: true,\n    isClone,\n    isDropAnimating: Boolean(dropping),\n    dropAnimation: dropping,\n    mode,\n    draggingOver,\n    combineWith,\n    combineTargetFor: null\n  }));\n  const getMemoizedProps = memoizeOne((offset, mode, dimension, isClone, draggingOver = null, combineWith = null, forceShouldAnimate = null) => ({\n    mapped: {\n      type: 'DRAGGING',\n      dropping: null,\n      draggingOver,\n      combineWith,\n      mode,\n      offset,\n      dimension,\n      forceShouldAnimate,\n      snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, null)\n    }\n  }));\n  const selector = (state, ownProps) => {\n    if (isDragging(state)) {\n      if (state.critical.draggable.id !== ownProps.draggableId) {\n        return null;\n      }\n      const offset = state.current.client.offset;\n      const dimension = state.dimensions.draggables[ownProps.draggableId];\n      const draggingOver = whatIsDraggedOver(state.impact);\n      const combineWith = getCombineWithFromImpact(state.impact);\n      const forceShouldAnimate = state.forceShouldAnimate;\n      return getMemoizedProps(memoizedOffset(offset.x, offset.y), state.movementMode, dimension, ownProps.isClone, draggingOver, combineWith, forceShouldAnimate);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (completed.result.draggableId !== ownProps.draggableId) {\n        return null;\n      }\n      const isClone = ownProps.isClone;\n      const dimension = state.dimensions.draggables[ownProps.draggableId];\n      const result = completed.result;\n      const mode = result.mode;\n      const draggingOver = whatIsDraggedOverFromResult(result);\n      const combineWith = getCombineWithFromResult(result);\n      const duration = state.dropDuration;\n      const dropping = {\n        duration,\n        curve: curves.drop,\n        moveTo: state.newHomeClientOffset,\n        opacity: combineWith ? combine.opacity.drop : null,\n        scale: combineWith ? combine.scale.drop : null\n      };\n      return {\n        mapped: {\n          type: 'DRAGGING',\n          offset: state.newHomeClientOffset,\n          dimension,\n          dropping,\n          draggingOver,\n          combineWith,\n          mode,\n          forceShouldAnimate: null,\n          snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, dropping)\n        }\n      };\n    }\n    return null;\n  };\n  return selector;\n}\nfunction getSecondarySnapshot(combineTargetFor = null) {\n  return {\n    isDragging: false,\n    isDropAnimating: false,\n    isClone: false,\n    dropAnimation: null,\n    mode: null,\n    draggingOver: null,\n    combineTargetFor,\n    combineWith: null\n  };\n}\nconst atRest = {\n  mapped: {\n    type: 'SECONDARY',\n    offset: origin,\n    combineTargetFor: null,\n    shouldAnimateDisplacement: true,\n    snapshot: getSecondarySnapshot(null)\n  }\n};\nfunction getSecondarySelector() {\n  const memoizedOffset = memoizeOne((x, y) => ({\n    x,\n    y\n  }));\n  const getMemoizedSnapshot = memoizeOne(getSecondarySnapshot);\n  const getMemoizedProps = memoizeOne((offset, combineTargetFor = null, shouldAnimateDisplacement) => ({\n    mapped: {\n      type: 'SECONDARY',\n      offset,\n      combineTargetFor,\n      shouldAnimateDisplacement,\n      snapshot: getMemoizedSnapshot(combineTargetFor)\n    }\n  }));\n  const getFallback = combineTargetFor => {\n    return combineTargetFor ? getMemoizedProps(origin, combineTargetFor, true) : null;\n  };\n  const getProps = (ownId, draggingId, impact, afterCritical) => {\n    const visualDisplacement = impact.displaced.visible[ownId];\n    const isAfterCriticalInVirtualList = Boolean(afterCritical.inVirtualList && afterCritical.effected[ownId]);\n    const combine = tryGetCombine(impact);\n    const combineTargetFor = combine && combine.draggableId === ownId ? draggingId : null;\n    if (!visualDisplacement) {\n      if (!isAfterCriticalInVirtualList) {\n        return getFallback(combineTargetFor);\n      }\n      if (impact.displaced.invisible[ownId]) {\n        return null;\n      }\n      const change = negate(afterCritical.displacedBy.point);\n      const offset = memoizedOffset(change.x, change.y);\n      return getMemoizedProps(offset, combineTargetFor, true);\n    }\n    if (isAfterCriticalInVirtualList) {\n      return getFallback(combineTargetFor);\n    }\n    const displaceBy = impact.displacedBy.point;\n    const offset = memoizedOffset(displaceBy.x, displaceBy.y);\n    return getMemoizedProps(offset, combineTargetFor, visualDisplacement.shouldAnimate);\n  };\n  const selector = (state, ownProps) => {\n    if (isDragging(state)) {\n      if (state.critical.draggable.id === ownProps.draggableId) {\n        return null;\n      }\n      return getProps(ownProps.draggableId, state.critical.draggable.id, state.impact, state.afterCritical);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (completed.result.draggableId === ownProps.draggableId) {\n        return null;\n      }\n      return getProps(ownProps.draggableId, completed.result.draggableId, completed.impact, completed.afterCritical);\n    }\n    return null;\n  };\n  return selector;\n}\nconst makeMapStateToProps$1 = () => {\n  const draggingSelector = getDraggableSelector();\n  const secondarySelector = getSecondarySelector();\n  const selector = (state, ownProps) => draggingSelector(state, ownProps) || secondarySelector(state, ownProps) || atRest;\n  return selector;\n};\nconst mapDispatchToProps$1 = {\n  dropAnimationFinished: dropAnimationFinished\n};\nconst ConnectedDraggable = connect(makeMapStateToProps$1, mapDispatchToProps$1, null, {\n  context: StoreContext,\n  areStatePropsEqual: isStrictEqual\n})(Draggable);\n\nfunction PrivateDraggable(props) {\n  const droppableContext = useRequiredContext(DroppableContext);\n  const isUsingCloneFor = droppableContext.isUsingCloneFor;\n  if (isUsingCloneFor === props.draggableId && !props.isClone) {\n    return null;\n  }\n  return React.createElement(ConnectedDraggable, props);\n}\nfunction PublicDraggable(props) {\n  const isEnabled = typeof props.isDragDisabled === 'boolean' ? !props.isDragDisabled : true;\n  const canDragInteractiveElements = Boolean(props.disableInteractiveElementBlocking);\n  const shouldRespectForcePress = Boolean(props.shouldRespectForcePress);\n  return React.createElement(PrivateDraggable, _extends({}, props, {\n    isClone: false,\n    isEnabled: isEnabled,\n    canDragInteractiveElements: canDragInteractiveElements,\n    shouldRespectForcePress: shouldRespectForcePress\n  }));\n}\n\nconst isEqual = base => value => base === value;\nconst isScroll = isEqual('scroll');\nconst isAuto = isEqual('auto');\nconst isVisible = isEqual('visible');\nconst isEither = (overflow, fn) => fn(overflow.overflowX) || fn(overflow.overflowY);\nconst isBoth = (overflow, fn) => fn(overflow.overflowX) && fn(overflow.overflowY);\nconst isElementScrollable = el => {\n  const style = window.getComputedStyle(el);\n  const overflow = {\n    overflowX: style.overflowX,\n    overflowY: style.overflowY\n  };\n  return isEither(overflow, isScroll) || isEither(overflow, isAuto);\n};\nconst isBodyScrollable = () => {\n  if (process.env.NODE_ENV === 'production') {\n    return false;\n  }\n  const body = getBodyElement();\n  const html = document.documentElement;\n  !html ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  if (!isElementScrollable(body)) {\n    return false;\n  }\n  const htmlStyle = window.getComputedStyle(html);\n  const htmlOverflow = {\n    overflowX: htmlStyle.overflowX,\n    overflowY: htmlStyle.overflowY\n  };\n  if (isBoth(htmlOverflow, isVisible)) {\n    return false;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    We have detected that your <body> element might be a scroll container.\n    We have found no reliable way of detecting whether the <body> element is a scroll container.\n    Under most circumstances a <body> scroll bar will be on the <html> element (document.documentElement)\n\n    Because we cannot determine if the <body> is a scroll container, and generally it is not one,\n    we will be treating the <body> as *not* a scroll container\n\n    More information: https://github.com/hello-pangea/dnd/blob/main/docs/guides/how-we-detect-scroll-containers.md\n  `) : void 0;\n  return false;\n};\nconst getClosestScrollable = el => {\n  if (el == null) {\n    return null;\n  }\n  if (el === document.body) {\n    return isBodyScrollable() ? el : null;\n  }\n  if (el === document.documentElement) {\n    return null;\n  }\n  if (!isElementScrollable(el)) {\n    return getClosestScrollable(el.parentElement);\n  }\n  return el;\n};\n\nvar checkForNestedScrollContainers = scrollable => {\n  if (!scrollable) {\n    return;\n  }\n  const anotherScrollParent = getClosestScrollable(scrollable.parentElement);\n  if (!anotherScrollParent) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    Droppable: unsupported nested scroll container detected.\n    A Droppable can only have one scroll parent (which can be itself)\n    Nested scroll containers are currently not supported.\n\n    We hope to support nested scroll containers soon: https://github.com/atlassian/react-beautiful-dnd/issues/131\n  `) : void 0;\n};\n\nvar getScroll = el => ({\n  x: el.scrollLeft,\n  y: el.scrollTop\n});\n\nconst getIsFixed = el => {\n  if (!el) {\n    return false;\n  }\n  const style = window.getComputedStyle(el);\n  if (style.position === 'fixed') {\n    return true;\n  }\n  return getIsFixed(el.parentElement);\n};\nvar getEnv = start => {\n  const closestScrollable = getClosestScrollable(start);\n  const isFixedOnPage = getIsFixed(start);\n  return {\n    closestScrollable,\n    isFixedOnPage\n  };\n};\n\nvar getDroppableDimension = ({\n  descriptor,\n  isEnabled,\n  isCombineEnabled,\n  isFixedOnPage,\n  direction,\n  client,\n  page,\n  closest\n}) => {\n  const frame = (() => {\n    if (!closest) {\n      return null;\n    }\n    const {\n      scrollSize,\n      client: frameClient\n    } = closest;\n    const maxScroll = getMaxScroll({\n      scrollHeight: scrollSize.scrollHeight,\n      scrollWidth: scrollSize.scrollWidth,\n      height: frameClient.paddingBox.height,\n      width: frameClient.paddingBox.width\n    });\n    return {\n      pageMarginBox: closest.page.marginBox,\n      frameClient,\n      scrollSize,\n      shouldClipSubject: closest.shouldClipSubject,\n      scroll: {\n        initial: closest.scroll,\n        current: closest.scroll,\n        max: maxScroll,\n        diff: {\n          value: origin,\n          displacement: origin\n        }\n      }\n    };\n  })();\n  const axis = direction === 'vertical' ? vertical : horizontal;\n  const subject = getSubject({\n    page,\n    withPlaceholder: null,\n    axis,\n    frame\n  });\n  const dimension = {\n    descriptor,\n    isCombineEnabled,\n    isFixedOnPage,\n    axis,\n    isEnabled,\n    client,\n    page,\n    frame,\n    subject\n  };\n  return dimension;\n};\n\nconst getClient = (targetRef, closestScrollable) => {\n  const base = getBox(targetRef);\n  if (!closestScrollable) {\n    return base;\n  }\n  if (targetRef !== closestScrollable) {\n    return base;\n  }\n  const top = base.paddingBox.top - closestScrollable.scrollTop;\n  const left = base.paddingBox.left - closestScrollable.scrollLeft;\n  const bottom = top + closestScrollable.scrollHeight;\n  const right = left + closestScrollable.scrollWidth;\n  const paddingBox = {\n    top,\n    right,\n    bottom,\n    left\n  };\n  const borderBox = expand(paddingBox, base.border);\n  const client = createBox({\n    borderBox,\n    margin: base.margin,\n    border: base.border,\n    padding: base.padding\n  });\n  return client;\n};\nvar getDimension = ({\n  ref,\n  descriptor,\n  env,\n  windowScroll,\n  direction,\n  isDropDisabled,\n  isCombineEnabled,\n  shouldClipSubject\n}) => {\n  const closestScrollable = env.closestScrollable;\n  const client = getClient(ref, closestScrollable);\n  const page = withScroll(client, windowScroll);\n  const closest = (() => {\n    if (!closestScrollable) {\n      return null;\n    }\n    const frameClient = getBox(closestScrollable);\n    const scrollSize = {\n      scrollHeight: closestScrollable.scrollHeight,\n      scrollWidth: closestScrollable.scrollWidth\n    };\n    return {\n      client: frameClient,\n      page: withScroll(frameClient, windowScroll),\n      scroll: getScroll(closestScrollable),\n      scrollSize,\n      shouldClipSubject\n    };\n  })();\n  const dimension = getDroppableDimension({\n    descriptor,\n    isEnabled: !isDropDisabled,\n    isCombineEnabled,\n    isFixedOnPage: env.isFixedOnPage,\n    direction,\n    client,\n    page,\n    closest\n  });\n  return dimension;\n};\n\nconst immediate = {\n  passive: false\n};\nconst delayed = {\n  passive: true\n};\nvar getListenerOptions = options => options.shouldPublishImmediately ? immediate : delayed;\n\nconst getClosestScrollableFromDrag = dragging => dragging && dragging.env.closestScrollable || null;\nfunction useDroppablePublisher(args) {\n  const whileDraggingRef = useRef(null);\n  const appContext = useRequiredContext(AppContext);\n  const uniqueId = useUniqueId('droppable');\n  const {\n    registry,\n    marshal\n  } = appContext;\n  const previousRef = usePrevious(args);\n  const descriptor = useMemo(() => ({\n    id: args.droppableId,\n    type: args.type,\n    mode: args.mode\n  }), [args.droppableId, args.mode, args.type]);\n  const publishedDescriptorRef = useRef(descriptor);\n  const memoizedUpdateScroll = useMemo(() => memoizeOne((x, y) => {\n    !whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only update scroll when dragging') : invariant() : void 0;\n    const scroll = {\n      x,\n      y\n    };\n    marshal.updateDroppableScroll(descriptor.id, scroll);\n  }), [descriptor.id, marshal]);\n  const getClosestScroll = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    if (!dragging || !dragging.env.closestScrollable) {\n      return origin;\n    }\n    return getScroll(dragging.env.closestScrollable);\n  }, []);\n  const updateScroll = useCallback(() => {\n    const scroll = getClosestScroll();\n    memoizedUpdateScroll(scroll.x, scroll.y);\n  }, [getClosestScroll, memoizedUpdateScroll]);\n  const scheduleScrollUpdate = useMemo(() => rafSchd(updateScroll), [updateScroll]);\n  const onClosestScroll = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find scroll options while scrolling') : invariant() : void 0;\n    const options = dragging.scrollOptions;\n    if (options.shouldPublishImmediately) {\n      updateScroll();\n      return;\n    }\n    scheduleScrollUpdate();\n  }, [scheduleScrollUpdate, updateScroll]);\n  const getDimensionAndWatchScroll = useCallback((windowScroll, options) => {\n    !!whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect a droppable while a drag is occurring') : invariant() : void 0;\n    const previous = previousRef.current;\n    const ref = previous.getDroppableRef();\n    !ref ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect without a droppable ref') : invariant() : void 0;\n    const env = getEnv(ref);\n    const dragging = {\n      ref,\n      descriptor,\n      env,\n      scrollOptions: options\n    };\n    whileDraggingRef.current = dragging;\n    const dimension = getDimension({\n      ref,\n      descriptor,\n      env,\n      windowScroll,\n      direction: previous.direction,\n      isDropDisabled: previous.isDropDisabled,\n      isCombineEnabled: previous.isCombineEnabled,\n      shouldClipSubject: !previous.ignoreContainerClipping\n    });\n    const scrollable = env.closestScrollable;\n    if (scrollable) {\n      scrollable.setAttribute(scrollContainer.contextId, appContext.contextId);\n      scrollable.addEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n      if (process.env.NODE_ENV !== 'production') {\n        checkForNestedScrollContainers(scrollable);\n      }\n    }\n    return dimension;\n  }, [appContext.contextId, descriptor, onClosestScroll, previousRef]);\n  const getScrollWhileDragging = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only recollect Droppable client for Droppables that have a scroll container') : invariant() : void 0;\n    return getScroll(closest);\n  }, []);\n  const dragStopped = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop drag when no active drag') : invariant() : void 0;\n    const closest = getClosestScrollableFromDrag(dragging);\n    whileDraggingRef.current = null;\n    if (!closest) {\n      return;\n    }\n    scheduleScrollUpdate.cancel();\n    closest.removeAttribute(scrollContainer.contextId);\n    closest.removeEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n  }, [onClosestScroll, scheduleScrollUpdate]);\n  const scroll = useCallback(change => {\n    const dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll when there is no drag') : invariant() : void 0;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !closest ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll a droppable with no closest scrollable') : invariant() : void 0;\n    closest.scrollTop += change.y;\n    closest.scrollLeft += change.x;\n  }, []);\n  const callbacks = useMemo(() => {\n    return {\n      getDimensionAndWatchScroll,\n      getScrollWhileDragging,\n      dragStopped,\n      scroll\n    };\n  }, [dragStopped, getDimensionAndWatchScroll, getScrollWhileDragging, scroll]);\n  const entry = useMemo(() => ({\n    uniqueId,\n    descriptor,\n    callbacks\n  }), [callbacks, descriptor, uniqueId]);\n  useIsomorphicLayoutEffect(() => {\n    publishedDescriptorRef.current = entry.descriptor;\n    registry.droppable.register(entry);\n    return () => {\n      if (whileDraggingRef.current) {\n        process.env.NODE_ENV !== \"production\" ? warning('Unsupported: changing the droppableId or type of a Droppable during a drag') : void 0;\n        dragStopped();\n      }\n      registry.droppable.unregister(entry);\n    };\n  }, [callbacks, descriptor, dragStopped, entry, marshal, registry.droppable]);\n  useIsomorphicLayoutEffect(() => {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n    marshal.updateDroppableIsEnabled(publishedDescriptorRef.current.id, !args.isDropDisabled);\n  }, [args.isDropDisabled, marshal]);\n  useIsomorphicLayoutEffect(() => {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n    marshal.updateDroppableIsCombineEnabled(publishedDescriptorRef.current.id, args.isCombineEnabled);\n  }, [args.isCombineEnabled, marshal]);\n}\n\nfunction noop() {}\nconst empty = {\n  width: 0,\n  height: 0,\n  margin: noSpacing\n};\nconst getSize = ({\n  isAnimatingOpenOnMount,\n  placeholder,\n  animate\n}) => {\n  if (isAnimatingOpenOnMount) {\n    return empty;\n  }\n  if (animate === 'close') {\n    return empty;\n  }\n  return {\n    height: placeholder.client.borderBox.height,\n    width: placeholder.client.borderBox.width,\n    margin: placeholder.client.margin\n  };\n};\nconst getStyle = ({\n  isAnimatingOpenOnMount,\n  placeholder,\n  animate\n}) => {\n  const size = getSize({\n    isAnimatingOpenOnMount,\n    placeholder,\n    animate\n  });\n  return {\n    display: placeholder.display,\n    boxSizing: 'border-box',\n    width: size.width,\n    height: size.height,\n    marginTop: size.margin.top,\n    marginRight: size.margin.right,\n    marginBottom: size.margin.bottom,\n    marginLeft: size.margin.left,\n    flexShrink: '0',\n    flexGrow: '0',\n    pointerEvents: 'none',\n    transition: animate !== 'none' ? transitions.placeholder : null\n  };\n};\nconst Placeholder = props => {\n  const animateOpenTimerRef = useRef(null);\n  const tryClearAnimateOpenTimer = useCallback(() => {\n    if (!animateOpenTimerRef.current) {\n      return;\n    }\n    clearTimeout(animateOpenTimerRef.current);\n    animateOpenTimerRef.current = null;\n  }, []);\n  const {\n    animate,\n    onTransitionEnd,\n    onClose,\n    contextId\n  } = props;\n  const [isAnimatingOpenOnMount, setIsAnimatingOpenOnMount] = useState(props.animate === 'open');\n  useEffect(() => {\n    if (!isAnimatingOpenOnMount) {\n      return noop;\n    }\n    if (animate !== 'open') {\n      tryClearAnimateOpenTimer();\n      setIsAnimatingOpenOnMount(false);\n      return noop;\n    }\n    if (animateOpenTimerRef.current) {\n      return noop;\n    }\n    animateOpenTimerRef.current = setTimeout(() => {\n      animateOpenTimerRef.current = null;\n      setIsAnimatingOpenOnMount(false);\n    });\n    return tryClearAnimateOpenTimer;\n  }, [animate, isAnimatingOpenOnMount, tryClearAnimateOpenTimer]);\n  const onSizeChangeEnd = useCallback(event => {\n    if (event.propertyName !== 'height') {\n      return;\n    }\n    onTransitionEnd();\n    if (animate === 'close') {\n      onClose();\n    }\n  }, [animate, onClose, onTransitionEnd]);\n  const style = getStyle({\n    isAnimatingOpenOnMount,\n    animate: props.animate,\n    placeholder: props.placeholder\n  });\n  return React.createElement(props.placeholder.tagName, {\n    style,\n    'data-rfd-placeholder-context-id': contextId,\n    onTransitionEnd: onSizeChangeEnd,\n    ref: props.innerRef\n  });\n};\nvar Placeholder$1 = React.memo(Placeholder);\n\nfunction isBoolean(value) {\n  return typeof value === 'boolean';\n}\nfunction runChecks(args, checks) {\n  checks.forEach(check => check(args));\n}\nconst shared = [function required({\n  props\n}) {\n  !props.droppableId ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A Droppable requires a droppableId prop') : invariant() : void 0;\n  !(typeof props.droppableId === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `A Droppable requires a [string] droppableId. Provided: [${typeof props.droppableId}]`) : invariant() : void 0;\n}, function boolean({\n  props\n}) {\n  !isBoolean(props.isDropDisabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isDropDisabled must be a boolean') : invariant() : void 0;\n  !isBoolean(props.isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isCombineEnabled must be a boolean') : invariant() : void 0;\n  !isBoolean(props.ignoreContainerClipping) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'ignoreContainerClipping must be a boolean') : invariant() : void 0;\n}, function ref({\n  getDroppableRef\n}) {\n  checkIsValidInnerRef(getDroppableRef());\n}];\nconst standard = [function placeholder({\n  props,\n  getPlaceholderRef\n}) {\n  if (!props.placeholder) {\n    return;\n  }\n  const ref = getPlaceholderRef();\n  if (ref) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n      Droppable setup issue [droppableId: \"${props.droppableId}\"]:\n      DroppableProvided > placeholder could not be found.\n\n      Please be sure to add the {provided.placeholder} React Node as a child of your Droppable.\n      More information: https://github.com/hello-pangea/dnd/blob/main/docs/api/droppable.md\n    `) : void 0;\n}];\nconst virtual = [function hasClone({\n  props\n}) {\n  !props.renderClone ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must provide a clone render function (renderClone) for virtual lists') : invariant() : void 0;\n}, function hasNoPlaceholder({\n  getPlaceholderRef\n}) {\n  !!getPlaceholderRef() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected virtual list to not have a placeholder') : invariant() : void 0;\n}];\nfunction useValidation(args) {\n  useDevSetupWarning(() => {\n    runChecks(args, shared);\n    if (args.props.mode === 'standard') {\n      runChecks(args, standard);\n    }\n    if (args.props.mode === 'virtual') {\n      runChecks(args, virtual);\n    }\n  });\n}\n\nclass AnimateInOut extends React.PureComponent {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      isVisible: Boolean(this.props.on),\n      data: this.props.on,\n      animate: this.props.shouldAnimate && this.props.on ? 'open' : 'none'\n    };\n    this.onClose = () => {\n      if (this.state.animate !== 'close') {\n        return;\n      }\n      this.setState({\n        isVisible: false\n      });\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (!props.shouldAnimate) {\n      return {\n        isVisible: Boolean(props.on),\n        data: props.on,\n        animate: 'none'\n      };\n    }\n    if (props.on) {\n      return {\n        isVisible: true,\n        data: props.on,\n        animate: 'open'\n      };\n    }\n    if (state.isVisible) {\n      return {\n        isVisible: true,\n        data: state.data,\n        animate: 'close'\n      };\n    }\n    return {\n      isVisible: false,\n      animate: 'close',\n      data: null\n    };\n  }\n  render() {\n    if (!this.state.isVisible) {\n      return null;\n    }\n    const provided = {\n      onClose: this.onClose,\n      data: this.state.data,\n      animate: this.state.animate\n    };\n    return this.props.children(provided);\n  }\n}\n\nconst Droppable = props => {\n  const appContext = useContext(AppContext);\n  !appContext ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find app context') : invariant() : void 0;\n  const {\n    contextId,\n    isMovementAllowed\n  } = appContext;\n  const droppableRef = useRef(null);\n  const placeholderRef = useRef(null);\n  const {\n    children,\n    droppableId,\n    type,\n    mode,\n    direction,\n    ignoreContainerClipping,\n    isDropDisabled,\n    isCombineEnabled,\n    snapshot,\n    useClone,\n    updateViewportMaxScroll,\n    getContainerForClone\n  } = props;\n  const getDroppableRef = useCallback(() => droppableRef.current, []);\n  const setDroppableRef = useCallback((value = null) => {\n    droppableRef.current = value;\n  }, []);\n  const getPlaceholderRef = useCallback(() => placeholderRef.current, []);\n  const setPlaceholderRef = useCallback((value = null) => {\n    placeholderRef.current = value;\n  }, []);\n  useValidation({\n    props,\n    getDroppableRef,\n    getPlaceholderRef\n  });\n  const onPlaceholderTransitionEnd = useCallback(() => {\n    if (isMovementAllowed()) {\n      updateViewportMaxScroll({\n        maxScroll: getMaxWindowScroll()\n      });\n    }\n  }, [isMovementAllowed, updateViewportMaxScroll]);\n  useDroppablePublisher({\n    droppableId,\n    type,\n    mode,\n    direction,\n    isDropDisabled,\n    isCombineEnabled,\n    ignoreContainerClipping,\n    getDroppableRef\n  });\n  const placeholder = useMemo(() => React.createElement(AnimateInOut, {\n    on: props.placeholder,\n    shouldAnimate: props.shouldAnimatePlaceholder\n  }, ({\n    onClose,\n    data,\n    animate\n  }) => React.createElement(Placeholder$1, {\n    placeholder: data,\n    onClose: onClose,\n    innerRef: setPlaceholderRef,\n    animate: animate,\n    contextId: contextId,\n    onTransitionEnd: onPlaceholderTransitionEnd\n  })), [contextId, onPlaceholderTransitionEnd, props.placeholder, props.shouldAnimatePlaceholder, setPlaceholderRef]);\n  const provided = useMemo(() => ({\n    innerRef: setDroppableRef,\n    placeholder,\n    droppableProps: {\n      'data-rfd-droppable-id': droppableId,\n      'data-rfd-droppable-context-id': contextId\n    }\n  }), [contextId, droppableId, placeholder, setDroppableRef]);\n  const isUsingCloneFor = useClone ? useClone.dragging.draggableId : null;\n  const droppableContext = useMemo(() => ({\n    droppableId,\n    type,\n    isUsingCloneFor\n  }), [droppableId, isUsingCloneFor, type]);\n  function getClone() {\n    if (!useClone) {\n      return null;\n    }\n    const {\n      dragging,\n      render\n    } = useClone;\n    const node = React.createElement(PrivateDraggable, {\n      draggableId: dragging.draggableId,\n      index: dragging.source.index,\n      isClone: true,\n      isEnabled: true,\n      shouldRespectForcePress: false,\n      canDragInteractiveElements: true\n    }, (draggableProvided, draggableSnapshot) => render(draggableProvided, draggableSnapshot, dragging));\n    return ReactDOM.createPortal(node, getContainerForClone());\n  }\n  return React.createElement(DroppableContext.Provider, {\n    value: droppableContext\n  }, children(provided, snapshot), getClone());\n};\n\nfunction getBody() {\n  !document.body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'document.body is not ready') : invariant() : void 0;\n  return document.body;\n}\nconst defaultProps = {\n  mode: 'standard',\n  type: 'DEFAULT',\n  direction: 'vertical',\n  isDropDisabled: false,\n  isCombineEnabled: false,\n  ignoreContainerClipping: false,\n  renderClone: null,\n  getContainerForClone: getBody\n};\nconst attachDefaultPropsToOwnProps = ownProps => {\n  let mergedProps = {\n    ...ownProps\n  };\n  let defaultPropKey;\n  for (defaultPropKey in defaultProps) {\n    if (ownProps[defaultPropKey] === undefined) {\n      mergedProps = {\n        ...mergedProps,\n        [defaultPropKey]: defaultProps[defaultPropKey]\n      };\n    }\n  }\n  return mergedProps;\n};\nconst isMatchingType = (type, critical) => type === critical.droppable.type;\nconst getDraggable = (critical, dimensions) => dimensions.draggables[critical.draggable.id];\nconst makeMapStateToProps = () => {\n  const idleWithAnimation = {\n    placeholder: null,\n    shouldAnimatePlaceholder: true,\n    snapshot: {\n      isDraggingOver: false,\n      draggingOverWith: null,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: false\n    },\n    useClone: null\n  };\n  const idleWithoutAnimation = {\n    ...idleWithAnimation,\n    shouldAnimatePlaceholder: false\n  };\n  const getDraggableRubric = memoizeOne(descriptor => ({\n    draggableId: descriptor.id,\n    type: descriptor.type,\n    source: {\n      index: descriptor.index,\n      droppableId: descriptor.droppableId\n    }\n  }));\n  const getMapProps = memoizeOne((id, isEnabled, isDraggingOverForConsumer, isDraggingOverForImpact, dragging, renderClone) => {\n    const draggableId = dragging.descriptor.id;\n    const isHome = dragging.descriptor.droppableId === id;\n    if (isHome) {\n      const useClone = renderClone ? {\n        render: renderClone,\n        dragging: getDraggableRubric(dragging.descriptor)\n      } : null;\n      const snapshot = {\n        isDraggingOver: isDraggingOverForConsumer,\n        draggingOverWith: isDraggingOverForConsumer ? draggableId : null,\n        draggingFromThisWith: draggableId,\n        isUsingPlaceholder: true\n      };\n      return {\n        placeholder: dragging.placeholder,\n        shouldAnimatePlaceholder: false,\n        snapshot,\n        useClone\n      };\n    }\n    if (!isEnabled) {\n      return idleWithoutAnimation;\n    }\n    if (!isDraggingOverForImpact) {\n      return idleWithAnimation;\n    }\n    const snapshot = {\n      isDraggingOver: isDraggingOverForConsumer,\n      draggingOverWith: draggableId,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: true\n    };\n    return {\n      placeholder: dragging.placeholder,\n      shouldAnimatePlaceholder: true,\n      snapshot,\n      useClone: null\n    };\n  });\n  const selector = (state, ownProps) => {\n    const ownPropsWithDefaultProps = attachDefaultPropsToOwnProps(ownProps);\n    const id = ownPropsWithDefaultProps.droppableId;\n    const type = ownPropsWithDefaultProps.type;\n    const isEnabled = !ownPropsWithDefaultProps.isDropDisabled;\n    const renderClone = ownPropsWithDefaultProps.renderClone;\n    if (isDragging(state)) {\n      const critical = state.critical;\n      if (!isMatchingType(type, critical)) {\n        return idleWithoutAnimation;\n      }\n      const dragging = getDraggable(critical, state.dimensions);\n      const isDraggingOver = whatIsDraggedOver(state.impact) === id;\n      return getMapProps(id, isEnabled, isDraggingOver, isDraggingOver, dragging, renderClone);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n      const dragging = getDraggable(completed.critical, state.dimensions);\n      return getMapProps(id, isEnabled, whatIsDraggedOverFromResult(completed.result) === id, whatIsDraggedOver(completed.impact) === id, dragging, renderClone);\n    }\n    if (state.phase === 'IDLE' && state.completed && !state.shouldFlush) {\n      const completed = state.completed;\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n      const wasOver = whatIsDraggedOver(completed.impact) === id;\n      const wasCombining = Boolean(completed.impact.at && completed.impact.at.type === 'COMBINE');\n      const isHome = completed.critical.droppable.id === id;\n      if (wasOver) {\n        return wasCombining ? idleWithAnimation : idleWithoutAnimation;\n      }\n      if (isHome) {\n        return idleWithAnimation;\n      }\n      return idleWithoutAnimation;\n    }\n    return idleWithoutAnimation;\n  };\n  return selector;\n};\nconst mapDispatchToProps = {\n  updateViewportMaxScroll: updateViewportMaxScroll\n};\nconst ConnectedDroppable = connect(makeMapStateToProps, mapDispatchToProps, (stateProps, dispatchProps, ownProps) => {\n  return {\n    ...attachDefaultPropsToOwnProps(ownProps),\n    ...stateProps,\n    ...dispatchProps\n  };\n}, {\n  context: StoreContext,\n  areStatePropsEqual: isStrictEqual\n})(Droppable);\n\nexport { DragDropContext, PublicDraggable as Draggable, ConnectedDroppable as Droppable, useKeyboardSensor, useMouseSensor, useTouchSensor };\n", "var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n", "import invariant from 'tiny-invariant';\n\nvar getRect = function getRect(_ref) {\n  var top = _ref.top,\n      right = _ref.right,\n      bottom = _ref.bottom,\n      left = _ref.left;\n  var width = right - left;\n  var height = bottom - top;\n  var rect = {\n    top: top,\n    right: right,\n    bottom: bottom,\n    left: left,\n    width: width,\n    height: height,\n    x: left,\n    y: top,\n    center: {\n      x: (right + left) / 2,\n      y: (bottom + top) / 2\n    }\n  };\n  return rect;\n};\nvar expand = function expand(target, expandBy) {\n  return {\n    top: target.top - expandBy.top,\n    left: target.left - expandBy.left,\n    bottom: target.bottom + expandBy.bottom,\n    right: target.right + expandBy.right\n  };\n};\nvar shrink = function shrink(target, shrinkBy) {\n  return {\n    top: target.top + shrinkBy.top,\n    left: target.left + shrinkBy.left,\n    bottom: target.bottom - shrinkBy.bottom,\n    right: target.right - shrinkBy.right\n  };\n};\n\nvar shift = function shift(target, shiftBy) {\n  return {\n    top: target.top + shiftBy.y,\n    left: target.left + shiftBy.x,\n    bottom: target.bottom + shiftBy.y,\n    right: target.right + shiftBy.x\n  };\n};\n\nvar noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nvar createBox = function createBox(_ref2) {\n  var borderBox = _ref2.borderBox,\n      _ref2$margin = _ref2.margin,\n      margin = _ref2$margin === void 0 ? noSpacing : _ref2$margin,\n      _ref2$border = _ref2.border,\n      border = _ref2$border === void 0 ? noSpacing : _ref2$border,\n      _ref2$padding = _ref2.padding,\n      padding = _ref2$padding === void 0 ? noSpacing : _ref2$padding;\n  var marginBox = getRect(expand(borderBox, margin));\n  var paddingBox = getRect(shrink(borderBox, border));\n  var contentBox = getRect(shrink(paddingBox, padding));\n  return {\n    marginBox: marginBox,\n    borderBox: getRect(borderBox),\n    paddingBox: paddingBox,\n    contentBox: contentBox,\n    margin: margin,\n    border: border,\n    padding: padding\n  };\n};\n\nvar parse = function parse(raw) {\n  var value = raw.slice(0, -2);\n  var suffix = raw.slice(-2);\n\n  if (suffix !== 'px') {\n    return 0;\n  }\n\n  var result = Number(value);\n  !!isNaN(result) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Could not parse value [raw: \" + raw + \", without suffix: \" + value + \"]\") : invariant(false) : void 0;\n  return result;\n};\n\nvar getWindowScroll = function getWindowScroll() {\n  return {\n    x: window.pageXOffset,\n    y: window.pageYOffset\n  };\n};\n\nvar offset = function offset(original, change) {\n  var borderBox = original.borderBox,\n      border = original.border,\n      margin = original.margin,\n      padding = original.padding;\n  var shifted = shift(borderBox, change);\n  return createBox({\n    borderBox: shifted,\n    border: border,\n    margin: margin,\n    padding: padding\n  });\n};\nvar withScroll = function withScroll(original, scroll) {\n  if (scroll === void 0) {\n    scroll = getWindowScroll();\n  }\n\n  return offset(original, scroll);\n};\nvar calculateBox = function calculateBox(borderBox, styles) {\n  var margin = {\n    top: parse(styles.marginTop),\n    right: parse(styles.marginRight),\n    bottom: parse(styles.marginBottom),\n    left: parse(styles.marginLeft)\n  };\n  var padding = {\n    top: parse(styles.paddingTop),\n    right: parse(styles.paddingRight),\n    bottom: parse(styles.paddingBottom),\n    left: parse(styles.paddingLeft)\n  };\n  var border = {\n    top: parse(styles.borderTopWidth),\n    right: parse(styles.borderRightWidth),\n    bottom: parse(styles.borderBottomWidth),\n    left: parse(styles.borderLeftWidth)\n  };\n  return createBox({\n    borderBox: borderBox,\n    margin: margin,\n    padding: padding,\n    border: border\n  });\n};\nvar getBox = function getBox(el) {\n  var borderBox = el.getBoundingClientRect();\n  var styles = window.getComputedStyle(el);\n  return calculateBox(borderBox, styles);\n};\n\nexport { calculateBox, createBox, expand, getBox, getRect, offset, shrink, withScroll };\n", "var rafSchd = function rafSchd(fn) {\n  var lastArgs = [];\n  var frameId = null;\n\n  var wrapperFn = function wrapperFn() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    lastArgs = args;\n\n    if (frameId) {\n      return;\n    }\n\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      fn.apply(void 0, lastArgs);\n    });\n  };\n\n  wrapperFn.cancel = function () {\n    if (!frameId) {\n      return;\n    }\n\n    cancelAnimationFrame(frameId);\n    frameId = null;\n  };\n\n  return wrapperFn;\n};\n\nexport default rafSchd;\n", "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,mBAAgF;AAChF,uBAAoC;;;ACDpC,IAAI,eAAe;AACnB,IAAI,SAAS;AACb,SAAS,UAAU,WAAW,SAAS;AACnC,MAAI,WAAW;AACX;AAAA,EACJ;AACA,MAAI,cAAc;AACd,UAAM,IAAI,MAAM,MAAM;AAAA,EAC1B;AACA,MAAI,WAAW,OAAO,YAAY,aAAa,QAAQ,IAAI;AAC3D,MAAI,QAAQ,WAAW,GAAG,OAAO,QAAQ,IAAI,EAAE,OAAO,QAAQ,IAAI;AAClE,QAAM,IAAI,MAAM,KAAK;AACzB;;;ACVA,IAAI,UAAU,SAASA,SAAQ,MAAM;AACnC,MAAI,MAAM,KAAK,KACX,QAAQ,KAAK,OACb,SAAS,KAAK,QACd,OAAO,KAAK;AAChB,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,SAAS;AACtB,MAAI,OAAO;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,IACH,GAAG;AAAA,IACH,QAAQ;AAAA,MACN,IAAI,QAAQ,QAAQ;AAAA,MACpB,IAAI,SAAS,OAAO;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,SAAS,SAASC,QAAO,QAAQ,UAAU;AAC7C,SAAO;AAAA,IACL,KAAK,OAAO,MAAM,SAAS;AAAA,IAC3B,MAAM,OAAO,OAAO,SAAS;AAAA,IAC7B,QAAQ,OAAO,SAAS,SAAS;AAAA,IACjC,OAAO,OAAO,QAAQ,SAAS;AAAA,EACjC;AACF;AACA,IAAI,SAAS,SAASC,QAAO,QAAQ,UAAU;AAC7C,SAAO;AAAA,IACL,KAAK,OAAO,MAAM,SAAS;AAAA,IAC3B,MAAM,OAAO,OAAO,SAAS;AAAA,IAC7B,QAAQ,OAAO,SAAS,SAAS;AAAA,IACjC,OAAO,OAAO,QAAQ,SAAS;AAAA,EACjC;AACF;AAEA,IAAI,QAAQ,SAASC,OAAM,QAAQ,SAAS;AAC1C,SAAO;AAAA,IACL,KAAK,OAAO,MAAM,QAAQ;AAAA,IAC1B,MAAM,OAAO,OAAO,QAAQ;AAAA,IAC5B,QAAQ,OAAO,SAAS,QAAQ;AAAA,IAChC,OAAO,OAAO,QAAQ,QAAQ;AAAA,EAChC;AACF;AAEA,IAAI,YAAY;AAAA,EACd,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AACA,IAAI,YAAY,SAASC,WAAU,OAAO;AACxC,MAAI,YAAY,MAAM,WAClB,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,YAAY,cAC/C,eAAe,MAAM,QACrB,SAAS,iBAAiB,SAAS,YAAY,cAC/C,gBAAgB,MAAM,SACtB,UAAU,kBAAkB,SAAS,YAAY;AACrD,MAAI,YAAY,QAAQ,OAAO,WAAW,MAAM,CAAC;AACjD,MAAI,aAAa,QAAQ,OAAO,WAAW,MAAM,CAAC;AAClD,MAAI,aAAa,QAAQ,OAAO,YAAY,OAAO,CAAC;AACpD,SAAO;AAAA,IACL;AAAA,IACA,WAAW,QAAQ,SAAS;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,QAAQ,SAASC,OAAM,KAAK;AAC9B,MAAI,QAAQ,IAAI,MAAM,GAAG,EAAE;AAC3B,MAAIC,UAAS,IAAI,MAAM,EAAE;AAEzB,MAAIA,YAAW,MAAM;AACnB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,OAAO,KAAK;AACzB,GAAC,CAAC,MAAM,MAAM,IAAI,OAAwC,UAAU,OAAO,iCAAiC,MAAM,uBAAuB,QAAQ,GAAG,IAAI,UAAU,KAAK,IAAI;AAC3K,SAAO;AACT;AAEA,IAAI,kBAAkB,SAASC,mBAAkB;AAC/C,SAAO;AAAA,IACL,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,EACZ;AACF;AAEA,IAAI,SAAS,SAASC,QAAO,UAAU,QAAQ;AAC7C,MAAI,YAAY,SAAS,WACrB,SAAS,SAAS,QAClB,SAAS,SAAS,QAClB,UAAU,SAAS;AACvB,MAAI,UAAU,MAAM,WAAW,MAAM;AACrC,SAAO,UAAU;AAAA,IACf,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,IAAI,aAAa,SAASC,YAAW,UAAUC,SAAQ;AACrD,MAAIA,YAAW,QAAQ;AACrB,IAAAA,UAAS,gBAAgB;AAAA,EAC3B;AAEA,SAAO,OAAO,UAAUA,OAAM;AAChC;AACA,IAAI,eAAe,SAASC,cAAa,WAAW,QAAQ;AAC1D,MAAI,SAAS;AAAA,IACX,KAAK,MAAM,OAAO,SAAS;AAAA,IAC3B,OAAO,MAAM,OAAO,WAAW;AAAA,IAC/B,QAAQ,MAAM,OAAO,YAAY;AAAA,IACjC,MAAM,MAAM,OAAO,UAAU;AAAA,EAC/B;AACA,MAAI,UAAU;AAAA,IACZ,KAAK,MAAM,OAAO,UAAU;AAAA,IAC5B,OAAO,MAAM,OAAO,YAAY;AAAA,IAChC,QAAQ,MAAM,OAAO,aAAa;AAAA,IAClC,MAAM,MAAM,OAAO,WAAW;AAAA,EAChC;AACA,MAAI,SAAS;AAAA,IACX,KAAK,MAAM,OAAO,cAAc;AAAA,IAChC,OAAO,MAAM,OAAO,gBAAgB;AAAA,IACpC,QAAQ,MAAM,OAAO,iBAAiB;AAAA,IACtC,MAAM,MAAM,OAAO,eAAe;AAAA,EACpC;AACA,SAAO,UAAU;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,IAAI,SAAS,SAASC,QAAO,IAAI;AAC/B,MAAI,YAAY,GAAG,sBAAsB;AACzC,MAAI,SAAS,OAAO,iBAAiB,EAAE;AACvC,SAAO,aAAa,WAAW,MAAM;AACvC;;;ACrJA,IAAI,UAAU,SAASC,SAAQ,IAAI;AACjC,MAAI,WAAW,CAAC;AAChB,MAAI,UAAU;AAEd,MAAI,YAAY,SAASC,aAAY;AACnC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,eAAW;AAEX,QAAI,SAAS;AACX;AAAA,IACF;AAEA,cAAU,sBAAsB,WAAY;AAC1C,gBAAU;AACV,SAAG,MAAM,QAAQ,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACH;AAEA,YAAU,SAAS,WAAY;AAC7B,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AAEA,yBAAqB,OAAO;AAC5B,cAAU;AAAA,EACZ;AAEA,SAAO;AACT;AAEA,IAAO,uBAAQ;;;ACjCf,SAAS,WAAW;AAClB,SAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,IAAI,UAAU,CAAC;AACnB,eAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAChE;AACA,WAAO;AAAA,EACT,GAAG,SAAS,MAAM,MAAM,SAAS;AACnC;;;AJAA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,UAAU,WAAS,MAAM,QAAQ,eAAe,GAAG,EAAE,QAAQ,qBAAqB,EAAE,EAAE,KAAK;AACjG,IAAM,gBAAgB,aAAW,QAAQ;AAAA;AAAA;AAAA,MAGnC,QAAQ,OAAO,CAAC;AAAA;AAAA;AAAA,CAGrB;AACD,IAAM,sBAAsB,aAAW,CAAC,cAAc,OAAO,GAAG,wDAAwD,oBAAoB,iBAAiB;AAC7J,IAAM,iBAAiB;AACvB,SAAS,IAAI,MAAM,SAAS;AAC1B,MAAI,gBAAgB;AAClB;AAAA,EACF;AACA,MAAI,OAAO,WAAW,eAAe,OAAO,cAAc,GAAG;AAC3D;AAAA,EACF;AACA,UAAQ,IAAI,EAAE,GAAG,oBAAoB,OAAO,CAAC;AAC/C;AACA,IAAM,UAAU,IAAI,KAAK,MAAM,MAAM;AACrC,IAAM,QAAQ,IAAI,KAAK,MAAM,OAAO;AAEpC,SAAS,SAAS;AAAC;AAEnB,SAAS,WAAWC,SAAQ,aAAa;AACvC,SAAO;AAAA,IACL,GAAGA;AAAA,IACH,GAAG;AAAA,EACL;AACF;AACA,SAAS,WAAW,IAAI,UAAU,eAAe;AAC/C,QAAM,aAAa,SAAS,IAAI,aAAW;AACzC,UAAM,UAAU,WAAW,eAAe,QAAQ,OAAO;AACzD,OAAG,iBAAiB,QAAQ,WAAW,QAAQ,IAAI,OAAO;AAC1D,WAAO,SAAS,SAAS;AACvB,SAAG,oBAAoB,QAAQ,WAAW,QAAQ,IAAI,OAAO;AAAA,IAC/D;AAAA,EACF,CAAC;AACD,SAAO,SAAS,YAAY;AAC1B,eAAW,QAAQ,YAAU;AAC3B,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAEA,IAAMC,gBAAe;AACrB,IAAM,WAAW;AACjB,IAAM,eAAN,cAA2B,MAAM;AAAC;AAClC,aAAa,UAAU,WAAW,SAAS,WAAW;AACpD,SAAO,KAAK;AACd;AACA,SAASC,WAAU,WAAW,SAAS;AACrC,MAAID,eAAc;AAChB,UAAM,IAAI,aAAa,QAAQ;AAAA,EACjC,OAAO;AACL,UAAM,IAAI,aAAa,GAAG,QAAQ,KAAK,WAAW,EAAE,EAAE;AAAA,EACxD;AACF;AAEA,IAAM,gBAAN,cAA4B,aAAAE,QAAM,UAAU;AAAA,EAC1C,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,gBAAgB,WAAS;AAC5B,YAAM,YAAY,KAAK,aAAa;AACpC,UAAI,UAAU,WAAW,GAAG;AAC1B,kBAAU,SAAS;AACnB,eAAwC,QAAQ;AAAA;AAAA;AAAA,OAGjD,IAAI;AAAA,MACL;AACA,YAAM,MAAM,MAAM;AAClB,UAAI,eAAe,cAAc;AAC/B,cAAM,eAAe;AACrB,YAAI,MAAuC;AACzC,gBAAM,IAAI,OAAO;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AACA,SAAK,eAAe,MAAM;AACxB,UAAI,CAAC,KAAK,WAAW;AACnB,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACnE;AACA,aAAO,KAAK;AAAA,IACd;AACA,SAAK,eAAe,eAAa;AAC/B,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,SAAS,WAAW,QAAQ,CAAC;AAAA,MAChC,WAAW;AAAA,MACX,IAAI,KAAK;AAAA,IACX,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,kBAAkB,KAAK;AACrB,QAAI,eAAe,cAAc;AAC/B,UAAI,MAAuC;AACzC,cAAM,IAAI,OAAO;AAAA,MACnB;AACA,WAAK,SAAS,CAAC,CAAC;AAChB;AAAA,IACF;AACA,UAAM;AAAA,EACR;AAAA,EACA,uBAAuB;AACrB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,SAAS;AACP,WAAO,KAAK,MAAM,SAAS,KAAK,YAAY;AAAA,EAC9C;AACF;AAEA,IAAM,8BAA8B;AAAA;AAAA;AAAA;AAAA;AAKpC,IAAM,WAAW,WAAS,QAAQ;AAClC,IAAM,cAAc,CAAAC,WAAS;AAAA,wCACW,SAASA,OAAM,OAAO,KAAK,CAAC;AAAA;AAEpE,IAAM,eAAe,CAAC,QAAQ,gBAAgB;AAC5C,QAAM,eAAe,OAAO,gBAAgB,YAAY;AACxD,QAAM,gBAAgB,SAAS,OAAO,KAAK;AAC3C,QAAM,cAAc,SAAS,YAAY,KAAK;AAC9C,MAAI,cAAc;AAChB,WAAO;AAAA,8CACmC,aAAa;AAAA,oBACvC,WAAW;AAAA;AAAA,EAE7B;AACA,SAAO;AAAA,4CACmC,aAAa;AAAA,cAC3C,OAAO,WAAW;AAAA,cAClB,YAAY,WAAW;AAAA,kBACnB,WAAW;AAAA;AAE7B;AACA,IAAM,cAAc,CAAC,IAAI,QAAQC,aAAY;AAC3C,QAAM,aAAa,OAAO,gBAAgBA,SAAQ;AAClD,MAAI,YAAY;AACd,WAAO;AAAA,iBACM,EAAE;AAAA,+BACYA,SAAQ,WAAW;AAAA,EAChD;AACA,SAAO;AAAA,iBACQ,EAAE;AAAA,gBACH,OAAO,WAAW;AAAA,+BACHA,SAAQ,WAAW;AAAA,gBAClCA,SAAQ,WAAW;AAAA;AAEnC;AACA,IAAM,eAAe,CAAAC,YAAU;AAC7B,QAAM,WAAWA,QAAO;AACxB,MAAI,UAAU;AACZ,WAAO,aAAaA,QAAO,QAAQ,QAAQ;AAAA,EAC7C;AACA,QAAMD,WAAUC,QAAO;AACvB,MAAID,UAAS;AACX,WAAO,YAAYC,QAAO,aAAaA,QAAO,QAAQD,QAAO;AAAA,EAC/D;AACA,SAAO;AACT;AACA,IAAM,kBAAkB,YAAU;AAAA;AAAA,OAE3B,SAAS,OAAO,KAAK,CAAC;AAAA;AAE7B,IAAM,YAAY,YAAU;AAC1B,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO;AAAA;AAAA,QAEH,gBAAgB,OAAO,MAAM,CAAC;AAAA;AAAA,EAEpC;AACA,QAAM,WAAW,OAAO;AACxB,QAAMA,WAAU,OAAO;AACvB,MAAI,UAAU;AACZ,WAAO;AAAA;AAAA,QAEH,aAAa,OAAO,QAAQ,QAAQ,CAAC;AAAA;AAAA,EAE3C;AACA,MAAIA,UAAS;AACX,WAAO;AAAA;AAAA,QAEH,YAAY,OAAO,aAAa,OAAO,QAAQA,QAAO,CAAC;AAAA;AAAA,EAE7D;AACA,SAAO;AAAA;AAAA,MAEH,gBAAgB,OAAO,MAAM,CAAC;AAAA;AAEpC;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,UAAU,OAAO,QAAQ;AAChC,MAAI,UAAU,QAAQ;AACpB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,MAAM,GAAG;AAC/C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,eAAe,WAAW,YAAY;AAC7C,MAAI,UAAU,WAAW,WAAW,QAAQ;AAC1C,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,CAAC,UAAU,UAAU,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG;AAC3C,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,WAAW,QAAQ;AAClC,QAAM,cAAU,uBAAS,OAAO;AAAA,IAC9B;AAAA,IACA,QAAQ,UAAU;AAAA,EACpB,EAAE,EAAE,CAAC;AACL,QAAM,iBAAa,qBAAO,IAAI;AAC9B,QAAM,gBAAY,qBAAO,OAAO;AAChC,QAAM,WAAW,WAAW,WAAW,QAAQ,UAAU,UAAU,QAAQ,UAAU,eAAe,QAAQ,UAAU,QAAQ,MAAM,CAAC;AACrI,QAAM,QAAQ,WAAW,UAAU,UAAU;AAAA,IAC3C;AAAA,IACA,QAAQ,UAAU;AAAA,EACpB;AACA,8BAAU,MAAM;AACd,eAAW,UAAU;AACrB,cAAU,UAAU;AAAA,EACtB,GAAG,CAAC,KAAK,CAAC;AACV,SAAO,MAAM;AACf;AACA,SAAS,YAAY,UAAU,QAAQ;AACrC,SAAO,QAAQ,MAAM,UAAU,MAAM;AACvC;AAEA,IAAM,SAAS;AAAA,EACb,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,MAAM,CAAC,QAAQ,YAAY;AAAA,EAC/B,GAAG,OAAO,IAAI,OAAO;AAAA,EACrB,GAAG,OAAO,IAAI,OAAO;AACvB;AACA,IAAM,WAAW,CAAC,QAAQ,YAAY;AAAA,EACpC,GAAG,OAAO,IAAI,OAAO;AAAA,EACrB,GAAG,OAAO,IAAI,OAAO;AACvB;AACA,IAAM,YAAY,CAAC,QAAQ,WAAW,OAAO,MAAM,OAAO,KAAK,OAAO,MAAM,OAAO;AACnF,IAAM,SAAS,YAAU;AAAA,EACvB,GAAG,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI;AAAA,EAC9B,GAAG,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI;AAChC;AACA,IAAM,QAAQ,CAAC,MAAM,OAAO,aAAa,MAAM;AAC7C,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AACA,IAAM,WAAW,CAAC,QAAQ,WAAW,KAAK,MAAM,OAAO,IAAI,OAAO,MAAM,KAAK,OAAO,IAAI,OAAO,MAAM,CAAC;AACtG,IAAM,YAAY,CAAC,QAAQ,WAAW,KAAK,IAAI,GAAG,OAAO,IAAI,WAAS,SAAS,QAAQ,KAAK,CAAC,CAAC;AAC9F,IAAM,QAAQ,QAAM,YAAU;AAAA,EAC5B,GAAG,GAAG,MAAM,CAAC;AAAA,EACb,GAAG,GAAG,MAAM,CAAC;AACf;AAEA,IAAI,cAAc,CAAC,OAAO,YAAY;AACpC,QAAM,SAAS,QAAQ;AAAA,IACrB,KAAK,KAAK,IAAI,QAAQ,KAAK,MAAM,GAAG;AAAA,IACpC,OAAO,KAAK,IAAI,QAAQ,OAAO,MAAM,KAAK;AAAA,IAC1C,QAAQ,KAAK,IAAI,QAAQ,QAAQ,MAAM,MAAM;AAAA,IAC7C,MAAM,KAAK,IAAI,QAAQ,MAAM,MAAM,IAAI;AAAA,EACzC,CAAC;AACD,MAAI,OAAO,SAAS,KAAK,OAAO,UAAU,GAAG;AAC3C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAM,mBAAmB,CAAC,SAAS,WAAW;AAAA,EAC5C,KAAK,QAAQ,MAAM,MAAM;AAAA,EACzB,MAAM,QAAQ,OAAO,MAAM;AAAA,EAC3B,QAAQ,QAAQ,SAAS,MAAM;AAAA,EAC/B,OAAO,QAAQ,QAAQ,MAAM;AAC/B;AACA,IAAM,aAAa,aAAW,CAAC;AAAA,EAC7B,GAAG,QAAQ;AAAA,EACX,GAAG,QAAQ;AACb,GAAG;AAAA,EACD,GAAG,QAAQ;AAAA,EACX,GAAG,QAAQ;AACb,GAAG;AAAA,EACD,GAAG,QAAQ;AAAA,EACX,GAAG,QAAQ;AACb,GAAG;AAAA,EACD,GAAG,QAAQ;AAAA,EACX,GAAG,QAAQ;AACb,CAAC;AACD,IAAME,aAAY;AAAA,EAChB,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AAEA,IAAM,WAAW,CAAC,QAAQ,UAAU;AAClC,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,QAAQ,MAAM,OAAO,KAAK,YAAY;AAChE;AACA,IAAM,WAAW,CAAC,QAAQ,MAAM,oBAAoB;AAClD,MAAI,mBAAmB,gBAAgB,aAAa;AAClD,WAAO;AAAA,MACL,GAAG;AAAA,MACH,CAAC,KAAK,GAAG,GAAG,OAAO,KAAK,GAAG,IAAI,gBAAgB,YAAY,KAAK,IAAI;AAAA,IACtE;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,OAAO,CAAC,QAAQ,UAAU;AAC9B,MAAI,SAAS,MAAM,mBAAmB;AACpC,WAAO,YAAY,MAAM,eAAe,MAAM;AAAA,EAChD;AACA,SAAO,QAAQ,MAAM;AACvB;AACA,IAAI,aAAa,CAAC;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,WAAW,SAAS,KAAK,WAAW,KAAK;AAC/C,QAAM,YAAY,SAAS,UAAU,MAAM,eAAe;AAC1D,QAAM,UAAU,KAAK,WAAW,KAAK;AACrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACV;AACF;AAEA,IAAI,kBAAkB,CAACC,YAAW,cAAc;AAC9C,GAACA,WAAU,QAAQ,OAAwCN,WAAU,IAAIA,WAAU,IAAI;AACvF,QAAM,aAAaM,WAAU;AAC7B,QAAM,aAAa,SAAS,WAAW,WAAW,OAAO,OAAO;AAChE,QAAM,qBAAqB,OAAO,UAAU;AAC5C,QAAM,QAAQ;AAAA,IACZ,GAAG;AAAA,IACH,QAAQ;AAAA,MACN,SAAS,WAAW,OAAO;AAAA,MAC3B,SAAS;AAAA,MACT,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,MAChB;AAAA,MACA,KAAK,WAAW,OAAO;AAAA,IACzB;AAAA,EACF;AACA,QAAM,UAAU,WAAW;AAAA,IACzB,MAAMA,WAAU,QAAQ;AAAA,IACxB,iBAAiBA,WAAU,QAAQ;AAAA,IACnC,MAAMA,WAAU;AAAA,IAChB;AAAA,EACF,CAAC;AACD,QAAM,SAAS;AAAA,IACb,GAAGA;AAAA,IACH;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,WAAW,UAAUC,WAAU,gBAAgB;AACtD,MAAI,QAAQ;AACZ,WAAS,YAAY,SAAS;AAC5B,QAAI,SAAS,MAAM,aAAa,QAAQA,SAAQ,SAAS,MAAM,QAAQ,GAAG;AACxE,aAAO,MAAM;AAAA,IACf;AACA,UAAM,aAAa,SAAS,MAAM,MAAM,OAAO;AAC/C,YAAQ;AAAA,MACN;AAAA,MACA,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AACA,WAAS,QAAQ,SAAS,QAAQ;AAChC,YAAQ;AAAA,EACV;AACA,SAAO;AACT;AAEA,IAAM,iBAAiB,WAAW,gBAAc,WAAW,OAAO,CAAC,UAAU,YAAY;AACvF,WAAS,QAAQ,WAAW,EAAE,IAAI;AAClC,SAAO;AACT,GAAG,CAAC,CAAC,CAAC;AACN,IAAM,iBAAiB,WAAW,gBAAc,WAAW,OAAO,CAAC,UAAU,YAAY;AACvF,WAAS,QAAQ,WAAW,EAAE,IAAI;AAClC,SAAO;AACT,GAAG,CAAC,CAAC,CAAC;AACN,IAAM,kBAAkB,WAAW,gBAAc,OAAO,OAAO,UAAU,CAAC;AAC1E,IAAM,kBAAkB,WAAW,gBAAc,OAAO,OAAO,UAAU,CAAC;AAE1E,IAAI,+BAA+B,WAAW,CAAC,aAAa,eAAe;AACzE,QAAM,SAAS,gBAAgB,UAAU,EAAE,OAAO,CAAAC,eAAa,gBAAgBA,WAAU,WAAW,WAAW,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,QAAQ,EAAE,WAAW,KAAK;AACvK,SAAO;AACT,CAAC;AAED,SAAS,kBAAkB,QAAQ;AACjC,MAAI,OAAO,MAAM,OAAO,GAAG,SAAS,WAAW;AAC7C,WAAO,OAAO,GAAG;AAAA,EACnB;AACA,SAAO;AACT;AACA,SAAS,cAAc,QAAQ;AAC7B,MAAI,OAAO,MAAM,OAAO,GAAG,SAAS,WAAW;AAC7C,WAAO,OAAO,GAAG;AAAA,EACnB;AACA,SAAO;AACT;AAEA,IAAI,0BAA0B,WAAW,CAAC,QAAQ,SAAS,KAAK,OAAO,UAAQ,KAAK,WAAW,OAAO,OAAO,WAAW,EAAE,CAAC;AAE3H,IAAI,oBAAoB,CAAC;AAAA,EACvB;AAAA,EACA,WAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,YAAY,kBAAkB;AACjC,WAAO;AAAA,EACT;AACA,QAAM,WAAW,kBAAkB,cAAc;AACjD,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,WAAS,UAAU,QAAQ;AACzB,UAAM,KAAK;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,QACP,aAAa;AAAA,QACb,aAAa,YAAY,WAAW;AAAA,MACtC;AAAA,IACF;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,QAAM,MAAM,eAAe,UAAU;AACrC,QAAM,YAAY,IAAI,SAAS,IAAI,CAAC,IAAI;AACxC,MAAI,iBAAiB;AACnB,WAAO,YAAY,UAAU,SAAS,IAAI;AAAA,EAC5C;AACA,QAAM,mBAAmB,wBAAwBA,YAAW,iBAAiB;AAC7E,MAAI,CAAC,WAAW;AACd,QAAI,CAAC,iBAAiB,QAAQ;AAC5B,aAAO;AAAA,IACT;AACA,UAAM,OAAO,iBAAiB,iBAAiB,SAAS,CAAC;AACzD,WAAO,UAAU,KAAK,WAAW,EAAE;AAAA,EACrC;AACA,QAAM,iBAAiB,iBAAiB,UAAU,OAAK,EAAE,WAAW,OAAO,SAAS;AACpF,IAAE,mBAAmB,MAAM,OAAwCR,WAAU,OAAO,sCAAsC,IAAIA,WAAU,IAAI;AAC5I,QAAM,gBAAgB,iBAAiB;AACvC,MAAI,gBAAgB,GAAG;AACrB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,iBAAiB,aAAa;AAC7C,SAAO,UAAU,OAAO,WAAW,EAAE;AACvC;AAEA,IAAI,WAAW,CAACQ,YAAW,gBAAgBA,WAAU,WAAW,gBAAgB,YAAY,WAAW;AAEvG,IAAM,gBAAgB;AAAA,EACpB,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAM,cAAc;AAAA,EAClB,WAAW,CAAC;AAAA,EACZ,SAAS,CAAC;AAAA,EACV,KAAK,CAAC;AACR;AACA,IAAM,WAAW;AAAA,EACf,WAAW;AAAA,EACX,aAAa;AAAA,EACb,IAAI;AACN;AAEA,IAAI,WAAW,CAAC,YAAY,eAAe,WAAS,cAAc,SAAS,SAAS;AAEpF,IAAI,iCAAiC,WAAS;AAC5C,QAAM,mBAAmB,SAAS,MAAM,KAAK,MAAM,MAAM;AACzD,QAAM,qBAAqB,SAAS,MAAM,MAAM,MAAM,KAAK;AAC3D,SAAO,aAAW;AAChB,UAAM,cAAc,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM,KAAK,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;AAC7J,QAAI,aAAa;AACf,aAAO;AAAA,IACT;AACA,UAAM,+BAA+B,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM;AACrG,UAAM,iCAAiC,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;AAC3G,UAAM,uBAAuB,gCAAgC;AAC7D,QAAI,sBAAsB;AACxB,aAAO;AAAA,IACT;AACA,UAAM,qBAAqB,QAAQ,MAAM,MAAM,OAAO,QAAQ,SAAS,MAAM;AAC7E,UAAM,uBAAuB,QAAQ,OAAO,MAAM,QAAQ,QAAQ,QAAQ,MAAM;AAChF,UAAM,0BAA0B,sBAAsB;AACtD,QAAI,yBAAyB;AAC3B,aAAO;AAAA,IACT;AACA,UAAM,0BAA0B,sBAAsB,kCAAkC,wBAAwB;AAChH,WAAO;AAAA,EACT;AACF;AAEA,IAAI,+BAA+B,WAAS;AAC1C,QAAM,mBAAmB,SAAS,MAAM,KAAK,MAAM,MAAM;AACzD,QAAM,qBAAqB,SAAS,MAAM,MAAM,MAAM,KAAK;AAC3D,SAAO,aAAW;AAChB,UAAM,cAAc,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM,KAAK,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;AAC7J,WAAO;AAAA,EACT;AACF;AAEA,IAAM,WAAW;AAAA,EACf,WAAW;AAAA,EACX,MAAM;AAAA,EACN,eAAe;AAAA,EACf,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AACjB;AACA,IAAM,aAAa;AAAA,EACjB,WAAW;AAAA,EACX,MAAM;AAAA,EACN,eAAe;AAAA,EACf,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AACjB;AAEA,IAAI,qCAAqC,UAAQ,WAAS;AACxD,QAAM,mBAAmB,SAAS,MAAM,KAAK,MAAM,MAAM;AACzD,QAAM,qBAAqB,SAAS,MAAM,MAAM,MAAM,KAAK;AAC3D,SAAO,aAAW;AAChB,QAAI,SAAS,UAAU;AACrB,aAAO,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM;AAAA,IACzE;AACA,WAAO,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;AAAA,EAC7E;AACF;AAEA,IAAM,wBAAwB,CAAC,QAAQ,gBAAgB;AACrD,QAAM,eAAe,YAAY,QAAQ,YAAY,MAAM,OAAO,KAAK,eAAe;AACtF,SAAO,iBAAiB,QAAQ,YAAY;AAC9C;AACA,IAAM,uBAAuB,CAAC,QAAQ,aAAa,4BAA4B;AAC7E,MAAI,CAAC,YAAY,QAAQ,QAAQ;AAC/B,WAAO;AAAA,EACT;AACA,SAAO,wBAAwB,YAAY,QAAQ,MAAM,EAAE,MAAM;AACnE;AACA,IAAM,sBAAsB,CAAC,QAAQ,UAAU,4BAA4B,wBAAwB,QAAQ,EAAE,MAAM;AACnH,IAAM,cAAc,CAAC;AAAA,EACnB,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,2BAAAC;AAAA,EACA;AACF,MAAM;AACJ,QAAM,kBAAkBA,6BAA4B,sBAAsB,eAAe,WAAW,IAAI;AACxG,SAAO,qBAAqB,iBAAiB,aAAa,uBAAuB,KAAK,oBAAoB,iBAAiB,UAAU,uBAAuB;AAC9J;AACA,IAAM,qBAAqB,UAAQ,YAAY;AAAA,EAC7C,GAAG;AAAA,EACH,yBAAyB;AAC3B,CAAC;AACD,IAAM,mBAAmB,UAAQ,YAAY;AAAA,EAC3C,GAAG;AAAA,EACH,yBAAyB;AAC3B,CAAC;AACD,IAAM,yBAAyB,UAAQ,YAAY;AAAA,EACjD,GAAG;AAAA,EACH,yBAAyB,mCAAmC,KAAK,YAAY,IAAI;AACnF,CAAC;AAED,IAAM,mBAAmB,CAAC,IAAI,MAAM,uBAAuB;AACzD,MAAI,OAAO,uBAAuB,WAAW;AAC3C,WAAO;AAAA,EACT;AACA,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,UAAU,EAAE,GAAG;AACjB,WAAO;AAAA,EACT;AACA,QAAM,WAAW,QAAQ,EAAE;AAC3B,SAAO,WAAW,SAAS,gBAAgB;AAC7C;AACA,SAAS,UAAUD,YAAW,aAAa;AACzC,QAAM,YAAYA,WAAU,KAAK;AACjC,QAAM,WAAW;AAAA,IACf,KAAK,YAAY,MAAM;AAAA,IACvB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM,YAAY,MAAM;AAAA,EAC1B;AACA,SAAO,QAAQ,OAAO,WAAW,QAAQ,CAAC;AAC5C;AACA,SAAS,sBAAsB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,cAAc,OAAO,SAASE,SAAQ,QAAQF,YAAW;AAC9D,UAAM,SAAS,UAAUA,YAAW,WAAW;AAC/C,UAAM,KAAKA,WAAU,WAAW;AAChC,WAAO,IAAI,KAAK,EAAE;AAClB,UAAMG,aAAY,mBAAmB;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA,2BAA2B;AAAA,IAC7B,CAAC;AACD,QAAI,CAACA,YAAW;AACd,aAAO,UAAUH,WAAU,WAAW,EAAE,IAAI;AAC5C,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,iBAAiB,IAAI,MAAM,kBAAkB;AACnE,UAAM,eAAe;AAAA,MACnB,aAAa;AAAA,MACb;AAAA,IACF;AACA,WAAO,QAAQ,EAAE,IAAI;AACrB,WAAO;AAAA,EACT,GAAG;AAAA,IACD,KAAK,CAAC;AAAA,IACN,SAAS,CAAC;AAAA,IACV,WAAW,CAAC;AAAA,EACd,CAAC;AACH;AAEA,SAAS,mBAAmB,YAAY,SAAS;AAC/C,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,WAAW,WAAW,SAAS,CAAC,EAAE,WAAW;AACrE,SAAO,QAAQ,aAAa,kBAAkB,kBAAkB;AAClE;AACA,SAAS,QAAQ;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,WAAW,mBAAmB,mBAAmB;AAAA,IACrD;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL,WAAW;AAAA,IACX;AAAA,IACA,IAAI;AAAA,MACF,MAAM;AAAA,MACN,aAAa;AAAA,QACX,aAAa,YAAY,WAAW;AAAA,QACpC,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,uBAAuB;AAAA,EAC9B,WAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,aAAa,SAASA,YAAW,WAAW;AAClD,MAAI,SAAS,MAAM;AACjB,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,QAAQ,kBAAkB,KAAK,UAAQ,KAAK,WAAW,UAAU,KAAK;AAC5E,MAAI,CAAC,OAAO;AACV,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,kBAAkB,wBAAwBA,YAAW,iBAAiB;AAC5E,QAAM,YAAY,kBAAkB,QAAQ,KAAK;AACjD,QAAM,WAAW,gBAAgB,MAAM,SAAS;AAChD,QAAM,YAAY,sBAAsB;AAAA,IACtC,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,SAAS;AAAA,IACnB;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,IAAI;AAAA,MACF,MAAM;AAAA,MACN,aAAa;AAAA,QACX,aAAa,YAAY,WAAW;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB,aAAa,eAAe;AACzD,SAAO,QAAQ,cAAc,SAAS,WAAW,CAAC;AACpD;AAEA,IAAI,cAAc,CAAC;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAAL;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,YAAY,kBAAkB;AACjC,WAAO;AAAA,EACT;AACA,QAAM,YAAYA,SAAQ;AAC1B,QAAM,cAAc,WAAW,SAAS;AACxC,QAAM,mBAAmB,YAAY,WAAW;AAChD,QAAM,mCAAmC,sBAAsB,WAAW,aAAa;AACvF,MAAI,kCAAkC;AACpC,QAAI,iBAAiB;AACnB,aAAO;AAAA,IACT;AACA,WAAO,mBAAmB;AAAA,EAC5B;AACA,MAAI,iBAAiB;AACnB,WAAO,mBAAmB;AAAA,EAC5B;AACA,SAAO;AACT;AAEA,IAAI,cAAc,CAAC;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,kBAAkB,QAAQ;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,eAAe,SAAS;AAC9B,QAAM,gBAAgB,kBAAkB,eAAe,IAAI,eAAe;AAC1E,QAAM,aAAa,kBAAkB,CAAC,EAAE,WAAW;AACnD,QAAM,YAAY,kBAAkB,kBAAkB,SAAS,CAAC,EAAE,WAAW;AAC7E,QAAM,aAAa,eAAe,YAAY,YAAY;AAC1D,MAAI,gBAAgB,YAAY;AAC9B,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,YAAY;AAC9B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAI,kBAAkB,CAAC;AAAA,EACrB;AAAA,EACA;AAAA,EACA,WAAAK;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,QAAQ,eAAe;AAC7B,GAAC,QAAQ,OAAwCR,WAAU,OAAO,2DAA2D,IAAIA,WAAU,IAAI;AAC/I,MAAI,MAAM,SAAS,WAAW;AAC5B,UAAMY,YAAW,YAAY;AAAA,MAC3B;AAAA,MACA;AAAA,MACA,UAAU,MAAM;AAAA,MAChB;AAAA,IACF,CAAC;AACD,QAAIA,aAAY,MAAM;AACpB,aAAO;AAAA,IACT;AACA,WAAO,uBAAuB;AAAA,MAC5B,WAAAJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,eAAe;AAAA,MACrB,aAAa,eAAe;AAAA,MAC5B,OAAOI;AAAA,IACT,CAAC;AAAA,EACH;AACA,QAAM,WAAW,YAAY;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,WAAW,eAAe;AAAA,IAC1B;AAAA,IACA,SAAS,MAAM;AAAA,IACf;AAAA,EACF,CAAC;AACD,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT;AACA,SAAO,uBAAuB;AAAA,IAC5B,WAAAJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,eAAe;AAAA,IACrB,aAAa,eAAe;AAAA,IAC5B,OAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,8BAA8B,CAAC;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,cAAc,QAAQ,UAAU,QAAQ,WAAW,KAAK,UAAU,UAAU,WAAW,CAAC;AAC9F,MAAI,sBAAsB,aAAa,aAAa,GAAG;AACrD,WAAO,cAAc,SAAS,OAAO,YAAY,KAAK;AAAA,EACxD;AACA,SAAO,cAAc,YAAY,QAAQ;AAC3C;AAEA,IAAI,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAML,WAAU,cAAc,MAAM;AACpC,GAACA,WAAU,OAAwCH,WAAU,IAAIA,WAAU,IAAI;AAC/E,QAAM,cAAcG,SAAQ;AAC5B,QAAM,SAAS,WAAW,WAAW,EAAE,KAAK,UAAU;AACtD,QAAM,aAAa,4BAA4B;AAAA,IAC7C,WAAW,OAAO;AAAA,IAClB;AAAA,IACA;AAAA,IACA,aAAa,OAAO;AAAA,EACtB,CAAC;AACD,SAAO,IAAI,QAAQ,UAAU;AAC/B;AAEA,IAAM,qCAAqC,CAAC,MAAM,QAAQ,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI,IAAI;AAC9G,IAAM,mCAAmC,CAAC,MAAM,QAAQ,IAAI,OAAO,KAAK,GAAG,IAAI,IAAI,UAAU,KAAK,IAAI,IAAI;AAC1G,IAAM,8BAA8B,CAAC,MAAM,QAAQ,aAAa,OAAO,KAAK,cAAc,IAAI,SAAS,OAAO,KAAK,cAAc,IAAI,SAAS,UAAU,KAAK,aAAa,IAAI;AAC9K,IAAM,UAAU,CAAC;AAAA,EACf;AAAA,EACA;AAAA,EACA;AACF,MAAM,MAAM,KAAK,MAAM,eAAe,UAAU,KAAK,GAAG,IAAI,mCAAmC,MAAM,QAAQ,GAAG,4BAA4B,MAAM,eAAe,WAAW,QAAQ,CAAC;AACrL,IAAM,WAAW,CAAC;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AACF,MAAM,MAAM,KAAK,MAAM,eAAe,UAAU,KAAK,KAAK,IAAI,iCAAiC,MAAM,QAAQ,GAAG,4BAA4B,MAAM,eAAe,WAAW,QAAQ,CAAC;AACrL,IAAM,cAAc,CAAC;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AACF,MAAM,MAAM,KAAK,MAAM,SAAS,WAAW,KAAK,KAAK,IAAI,mCAAmC,MAAM,QAAQ,GAAG,4BAA4B,MAAM,SAAS,YAAY,QAAQ,CAAC;AAE7K,IAAI,iBAAiB,CAAC;AAAA,EACpB;AAAA,EACA,WAAAK;AAAA,EACA;AAAA,EACA,WAAAF;AAAA,EACA;AACF,MAAM;AACJ,QAAM,oBAAoB,6BAA6BA,WAAU,WAAW,IAAI,UAAU;AAC1F,QAAM,gBAAgBE,WAAU;AAChC,QAAM,OAAOF,WAAU;AACvB,MAAI,CAAC,kBAAkB,QAAQ;AAC7B,WAAO,YAAY;AAAA,MACjB;AAAA,MACA,UAAUA,WAAU;AAAA,MACpB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,eAAe,UAAU,IAAI,CAAC;AACpC,MAAI,cAAc;AAChB,UAAMO,WAAU,WAAW,YAAY;AACvC,QAAI,sBAAsB,cAAc,aAAa,GAAG;AACtD,aAAO,SAAS;AAAA,QACd;AAAA,QACA,gBAAgBA,SAAQ;AAAA,QACxB,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,UAAM,mBAAmB,OAAOA,SAAQ,MAAM,YAAY,KAAK;AAC/D,WAAO,SAAS;AAAA,MACd;AAAA,MACA,gBAAgB;AAAA,MAChB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,QAAM,OAAO,kBAAkB,kBAAkB,SAAS,CAAC;AAC3D,MAAI,KAAK,WAAW,OAAOL,WAAU,WAAW,IAAI;AAClD,WAAO,cAAc,UAAU;AAAA,EACjC;AACA,MAAI,sBAAsB,KAAK,WAAW,IAAI,aAAa,GAAG;AAC5D,UAAM,OAAO,OAAO,KAAK,MAAM,OAAO,cAAc,YAAY,KAAK,CAAC;AACtE,WAAO,QAAQ;AAAA,MACb;AAAA,MACA,gBAAgB;AAAA,MAChB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,gBAAgB,KAAK;AAAA,IACrB,UAAU;AAAA,EACZ,CAAC;AACH;AAEA,IAAI,4BAA4B,CAACF,YAAW,UAAU;AACpD,QAAM,QAAQA,WAAU;AACxB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,IAAI,OAAO,MAAM,OAAO,KAAK,YAAY;AAClD;AAEA,IAAM,wCAAwC,CAAC;AAAA,EAC7C;AAAA,EACA,WAAAE;AAAA,EACA,WAAAF;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,WAAWE,WAAU,KAAK,UAAU;AAC1C,QAAM,KAAK,OAAO;AAClB,MAAI,CAACF,YAAW;AACd,WAAO;AAAA,EACT;AACA,MAAI,CAAC,IAAI;AACP,WAAO;AAAA,EACT;AACA,MAAI,GAAG,SAAS,WAAW;AACzB,WAAO,eAAe;AAAA,MACpB;AAAA,MACA,WAAAE;AAAA,MACA;AAAA,MACA,WAAAF;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,cAAc;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,IAAI,mCAAmC,UAAQ;AAC7C,QAAM,sBAAsB,sCAAsC,IAAI;AACtE,QAAMA,aAAY,KAAK;AACvB,QAAM,mBAAmBA,aAAY,0BAA0BA,YAAW,mBAAmB,IAAI;AACjG,SAAO;AACT;AAEA,IAAI,iBAAiB,CAAC,UAAU,cAAc;AAC5C,QAAM,OAAO,SAAS,WAAW,SAAS,OAAO,OAAO;AACxD,QAAM,eAAe,OAAO,IAAI;AAChC,QAAM,QAAQ,QAAQ;AAAA,IACpB,KAAK,UAAU;AAAA,IACf,QAAQ,UAAU,IAAI,SAAS,MAAM;AAAA,IACrC,MAAM,UAAU;AAAA,IAChB,OAAO,UAAU,IAAI,SAAS,MAAM;AAAA,EACtC,CAAC;AACD,QAAM,UAAU;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,MACN,SAAS,SAAS,OAAO;AAAA,MACzB,KAAK,SAAS,OAAO;AAAA,MACrB,SAAS;AAAA,MACT,MAAM;AAAA,QACJ,OAAO;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,KAAK,YAAY;AACxC,SAAO,IAAI,IAAI,QAAM,WAAW,EAAE,CAAC;AACrC;AACA,SAAS,cAAc,IAAI,QAAQ;AACjC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,eAAe,OAAO,CAAC,EAAE,QAAQ,EAAE;AACzC,QAAI,cAAc;AAChB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,mBAAmB,eAAe,UAAU,IAAI,SAAS,OAAO,SAAS,eAAe,CAAC;AAC/F,QAAM,oBAAoB,YAAY,QAAQ,gBAAgB,aAAa,IAAI,YAAY,MAAM,OAAO,SAAS,eAAe,CAAC,IAAI;AACrI,QAAM,OAAO,OAAO;AACpB,QAAM,qBAAqB,sBAAsB;AAAA,IAC/C,eAAe,gBAAgB,KAAK,KAAK,UAAU;AAAA,IACnD;AAAA,IACA,aAAa,OAAO;AAAA,IACpB,UAAU,iBAAiB;AAAA,IAC3B;AAAA,IACA,oBAAoB;AAAA,EACtB,CAAC;AACD,QAAMQ,uBAAsB,sBAAsB;AAAA,IAChD,eAAe,gBAAgB,KAAK,KAAK,UAAU;AAAA,IACnD,aAAa;AAAA,IACb,aAAa,OAAO;AAAA,IACpB,UAAU,SAAS;AAAA,IACnB;AAAA,IACA,oBAAoB;AAAA,EACtB,CAAC;AACD,QAAM,YAAY,CAAC;AACnB,QAAM,UAAU,CAAC;AACjB,QAAM,SAAS,CAAC,MAAM,oBAAoBA,oBAAmB;AAC7D,OAAK,IAAI,QAAQ,QAAM;AACrB,UAAM,eAAe,cAAc,IAAI,MAAM;AAC7C,QAAI,cAAc;AAChB,cAAQ,EAAE,IAAI;AACd;AAAA,IACF;AACA,cAAU,EAAE,IAAI;AAAA,EAClB,CAAC;AACD,QAAM,YAAY;AAAA,IAChB,GAAG;AAAA,IACH,WAAW;AAAA,MACT,KAAK,KAAK;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,2BAA2B,CAAC,UAAU,UAAU,IAAI,SAAS,OAAO,KAAK,cAAc,KAAK;AAEhG,IAAI,mCAAmC,CAAC;AAAA,EACtC;AAAA,EACA,WAAAN;AAAA,EACA;AACF,MAAM;AACJ,QAAM,0BAA0B,yBAAyB,UAAU,mBAAmB;AACtF,QAAMO,UAAS,SAAS,yBAAyBP,WAAU,KAAK,UAAU,MAAM;AAChF,SAAO,IAAIA,WAAU,OAAO,UAAU,QAAQO,OAAM;AACtD;AAEA,IAAI,gCAAgC,CAAC;AAAA,EACnC,WAAAP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,2BAAAC;AAAA,EACA,iBAAiB;AACnB,MAAM;AACJ,QAAM,eAAe,SAAS,wBAAwBD,WAAU,KAAK,UAAU,MAAM;AACrF,QAAM,UAAU,iBAAiBA,WAAU,KAAK,WAAW,YAAY;AACvE,QAAM,OAAO;AAAA,IACX,QAAQ;AAAA,IACR;AAAA,IACA,2BAAAC;AAAA,IACA;AAAA,EACF;AACA,SAAO,iBAAiB,uBAAuB,IAAI,IAAI,iBAAiB,IAAI;AAC9E;AAEA,IAAI,kBAAkB,CAAC;AAAA,EACrB;AAAA,EACA,WAAAD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,YAAY,WAAW;AAC1B,WAAO;AAAA,EACT;AACA,QAAM,oBAAoB,6BAA6B,YAAY,WAAW,IAAI,UAAU;AAC5F,QAAM,eAAe,SAASA,YAAW,WAAW;AACpD,QAAM,SAAS,kBAAkB;AAAA,IAC/B;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,KAAK,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,QAAM,sBAAsB,iCAAiC;AAAA,IAC3D;AAAA,IACA,WAAAA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,yBAAyB,8BAA8B;AAAA,IAC3D,WAAAA;AAAA,IACA;AAAA,IACA,wBAAwB;AAAA,IACxB,UAAU,SAAS;AAAA,IACnB,2BAA2B;AAAA,IAC3B,gBAAgB;AAAA,EAClB,CAAC;AACD,MAAI,wBAAwB;AAC1B,UAAM,kBAAkB,iCAAiC;AAAA,MACvD;AAAA,MACA,WAAAA;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,IACrB;AAAA,EACF;AACA,QAAMQ,YAAW,SAAS,qBAAqB,2BAA2B;AAC1E,QAAM,WAAW,sBAAsB;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiBA;AAAA,EACnB,CAAC;AACD,SAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,mBAAmBA;AAAA,EACrB;AACF;AAEA,IAAM,iBAAiB,CAAAV,eAAa;AAClC,QAAM,OAAOA,WAAU,QAAQ;AAC/B,GAAC,OAAO,OAAwCN,WAAU,OAAO,wCAAwC,IAAIA,WAAU,IAAI;AAC3H,SAAO;AACT;AACA,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,SAAS,OAAO,QAAQ;AAC9B,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,QAAM,OAAO,OAAO;AACpB,QAAM,yBAAyB,SAAS,OAAO,KAAK,KAAK,GAAG,OAAO,KAAK,GAAG,CAAC;AAC5E,QAAM,aAAa,gBAAgB,UAAU,EAAE,OAAO,CAAAM,eAAaA,eAAc,MAAM,EAAE,OAAO,CAAAA,eAAaA,WAAU,SAAS,EAAE,OAAO,CAAAA,eAAa,QAAQA,WAAU,QAAQ,MAAM,CAAC,EAAE,OAAO,CAAAA,eAAa,+BAA+B,SAAS,KAAK,EAAE,eAAeA,UAAS,CAAC,CAAC,EAAE,OAAO,CAAAA,eAAa;AAC1S,UAAM,iBAAiB,eAAeA,UAAS;AAC/C,QAAI,iBAAiB;AACnB,aAAO,OAAO,KAAK,YAAY,IAAI,eAAe,KAAK,YAAY;AAAA,IACrE;AACA,WAAO,eAAe,KAAK,cAAc,IAAI,OAAO,KAAK,cAAc;AAAA,EACzE,CAAC,EAAE,OAAO,CAAAA,eAAa;AACrB,UAAM,iBAAiB,eAAeA,UAAS;AAC/C,UAAM,8BAA8B,SAAS,eAAe,KAAK,KAAK,GAAG,eAAe,KAAK,GAAG,CAAC;AACjG,WAAO,uBAAuB,eAAe,KAAK,KAAK,CAAC,KAAK,uBAAuB,eAAe,KAAK,GAAG,CAAC,KAAK,4BAA4B,OAAO,KAAK,KAAK,CAAC,KAAK,4BAA4B,OAAO,KAAK,GAAG,CAAC;AAAA,EAClN,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;AAChB,UAAM,QAAQ,eAAe,CAAC,EAAE,KAAK,cAAc;AACnD,UAAM,SAAS,eAAe,CAAC,EAAE,KAAK,cAAc;AACpD,QAAI,iBAAiB;AACnB,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO,SAAS;AAAA,EAClB,CAAC,EAAE,OAAO,CAACA,YAAW,OAAO,UAAU,eAAeA,UAAS,EAAE,KAAK,cAAc,MAAM,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,cAAc,CAAC;AACvI,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO;AAAA,EACT;AACA,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO,WAAW,CAAC;AAAA,EACrB;AACA,QAAM,WAAW,WAAW,OAAO,CAAAA,eAAa;AAC9C,UAAM,oBAAoB,SAAS,eAAeA,UAAS,EAAE,KAAK,KAAK,GAAG,eAAeA,UAAS,EAAE,KAAK,GAAG,CAAC;AAC7G,WAAO,kBAAkB,oBAAoB,KAAK,IAAI,CAAC;AAAA,EACzD,CAAC;AACD,MAAI,SAAS,WAAW,GAAG;AACzB,WAAO,SAAS,CAAC;AAAA,EACnB;AACA,MAAI,SAAS,SAAS,GAAG;AACvB,WAAO,SAAS,KAAK,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,KAAK,KAAK,IAAI,eAAe,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;AAAA,EACjG;AACA,SAAO,WAAW,KAAK,CAAC,GAAG,MAAM;AAC/B,UAAM,QAAQ,UAAU,qBAAqB,WAAW,eAAe,CAAC,CAAC,CAAC;AAC1E,UAAM,SAAS,UAAU,qBAAqB,WAAW,eAAe,CAAC,CAAC,CAAC;AAC3E,QAAI,UAAU,QAAQ;AACpB,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO,eAAe,CAAC,EAAE,KAAK,KAAK,IAAI,eAAe,CAAC,EAAE,KAAK,KAAK;AAAA,EACrE,CAAC,EAAE,CAAC;AACN;AAEA,IAAM,gCAAgC,CAACE,YAAW,kBAAkB;AAClE,QAAM,WAAWA,WAAU,KAAK,UAAU;AAC1C,SAAO,sBAAsBA,WAAU,WAAW,IAAI,aAAa,IAAI,SAAS,UAAU,cAAc,YAAY,KAAK,IAAI;AAC/H;AACA,IAAM,0BAA0B,CAACA,YAAW,kBAAkB;AAC5D,QAAM,WAAWA,WAAU,KAAK;AAChC,SAAO,sBAAsBA,WAAU,WAAW,IAAI,aAAa,IAAI,iBAAiB,UAAU,OAAO,cAAc,YAAY,KAAK,CAAC,IAAI;AAC/I;AAEA,IAAI,sBAAsB,CAAC;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,SAAS,kBAAkB,OAAO,CAAAA,eAAa,iBAAiB;AAAA,IACpE,QAAQ,wBAAwBA,YAAW,aAAa;AAAA,IACxD;AAAA,IACA,UAAU,SAAS;AAAA,IACnB,2BAA2B;AAAA,EAC7B,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;AACjB,UAAM,cAAc,SAAS,qBAAqB,0BAA0B,aAAa,8BAA8B,GAAG,aAAa,CAAC,CAAC;AACzI,UAAM,cAAc,SAAS,qBAAqB,0BAA0B,aAAa,8BAA8B,GAAG,aAAa,CAAC,CAAC;AACzI,QAAI,cAAc,aAAa;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,cAAc,aAAa;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,EAAE,WAAW,QAAQ,EAAE,WAAW;AAAA,EAC3C,CAAC;AACD,SAAO,OAAO,CAAC,KAAK;AACtB;AAEA,IAAI,iBAAiB,WAAW,SAASS,gBAAe,MAAM,YAAY;AACxE,QAAM,eAAe,WAAW,KAAK,IAAI;AACzC,SAAO;AAAA,IACL,OAAO;AAAA,IACP,OAAO,MAAM,KAAK,MAAM,YAAY;AAAA,EACtC;AACF,CAAC;AAED,IAAM,kCAAkC,CAACX,YAAW,iBAAiB,eAAe;AAClF,QAAM,OAAOA,WAAU;AACvB,MAAIA,WAAU,WAAW,SAAS,WAAW;AAC3C,WAAO,MAAM,KAAK,MAAM,gBAAgB,KAAK,IAAI,CAAC;AAAA,EACpD;AACA,QAAM,iBAAiBA,WAAU,QAAQ,KAAK,WAAW,KAAK,IAAI;AAClE,QAAM,kBAAkB,6BAA6BA,WAAU,WAAW,IAAI,UAAU;AACxF,QAAM,YAAY,gBAAgB,OAAO,CAAC,KAAK,cAAc,MAAM,UAAU,OAAO,UAAU,KAAK,IAAI,GAAG,CAAC;AAC3G,QAAM,gBAAgB,YAAY,gBAAgB,KAAK,IAAI;AAC3D,QAAM,gBAAgB,gBAAgB;AACtC,MAAI,iBAAiB,GAAG;AACtB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,KAAK,MAAM,aAAa;AACvC;AACA,IAAM,gBAAgB,CAAC,OAAO,SAAS;AAAA,EACrC,GAAG;AAAA,EACH,QAAQ;AAAA,IACN,GAAG,MAAM;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAM,iBAAiB,CAACA,YAAWE,YAAW,eAAe;AAC3D,QAAM,QAAQF,WAAU;AACxB,GAAC,CAAC,SAASE,YAAWF,UAAS,IAAI,OAAwCN,WAAU,OAAO,+CAA+C,IAAIA,WAAU,IAAI;AAC7J,GAAC,CAACM,WAAU,QAAQ,kBAAkB,OAAwCN,WAAU,OAAO,kEAAkE,IAAIA,WAAU,IAAI;AACnL,QAAM,kBAAkB,eAAeM,WAAU,MAAME,WAAU,UAAU,EAAE;AAC7E,QAAM,iBAAiB,gCAAgCF,YAAW,iBAAiB,UAAU;AAC7F,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,mBAAmBA,WAAU,QAAQA,WAAU,MAAM,OAAO,MAAM;AAAA,EACpE;AACA,MAAI,CAAC,OAAO;AACV,UAAMY,WAAU,WAAW;AAAA,MACzB,MAAMZ,WAAU,QAAQ;AAAA,MACxB,iBAAiB;AAAA,MACjB,MAAMA,WAAU;AAAA,MAChB,OAAOA,WAAU;AAAA,IACnB,CAAC;AACD,WAAO;AAAA,MACL,GAAGA;AAAA,MACH,SAAAY;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,iBAAiB,IAAI,MAAM,OAAO,KAAK,cAAc,IAAI,MAAM,OAAO;AACxF,QAAM,WAAW,cAAc,OAAO,SAAS;AAC/C,QAAM,UAAU,WAAW;AAAA,IACzB,MAAMZ,WAAU,QAAQ;AAAA,IACxB,iBAAiB;AAAA,IACjB,MAAMA,WAAU;AAAA,IAChB,OAAO;AAAA,EACT,CAAC;AACD,SAAO;AAAA,IACL,GAAGA;AAAA,IACH;AAAA,IACA,OAAO;AAAA,EACT;AACF;AACA,IAAM,oBAAoB,CAAAA,eAAa;AACrC,QAAM,QAAQA,WAAU,QAAQ;AAChC,GAAC,QAAQ,OAAwCN,WAAU,OAAO,4DAA4D,IAAIA,WAAU,IAAI;AAChJ,QAAM,QAAQM,WAAU;AACxB,MAAI,CAAC,OAAO;AACV,UAAMY,WAAU,WAAW;AAAA,MACzB,MAAMZ,WAAU,QAAQ;AAAA,MACxB,MAAMA,WAAU;AAAA,MAChB,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB,CAAC;AACD,WAAO;AAAA,MACL,GAAGA;AAAA,MACH,SAAAY;AAAA,IACF;AAAA,EACF;AACA,QAAM,eAAe,MAAM;AAC3B,GAAC,eAAe,OAAwClB,WAAU,OAAO,sFAAsF,IAAIA,WAAU,IAAI;AACjL,QAAM,WAAW,cAAc,OAAO,YAAY;AAClD,QAAM,UAAU,WAAW;AAAA,IACzB,MAAMM,WAAU,QAAQ;AAAA,IACxB,MAAMA,WAAU;AAAA,IAChB,OAAO;AAAA,IACP,iBAAiB;AAAA,EACnB,CAAC;AACD,SAAO;AAAA,IACL,GAAGA;AAAA,IACH;AAAA,IACA,OAAO;AAAA,EACT;AACF;AAEA,IAAI,qBAAqB,CAAC;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAAE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,gBAAgB;AACnB,QAAI,kBAAkB,QAAQ;AAC5B,aAAO;AAAA,IACT;AACA,UAAM,WAAW;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,IAAI;AAAA,QACF,MAAM;AAAA,QACN,aAAa;AAAA,UACX,aAAa,YAAY,WAAW;AAAA,UACpC,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,UAAM,8BAA8B,iCAAiC;AAAA,MACnE,QAAQ;AAAA,MACR,WAAAA;AAAA,MACA,WAAW;AAAA,MACX;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,kBAAkB,SAASA,YAAW,WAAW,IAAI,cAAc,eAAe,aAAaA,YAAW,UAAU;AAC1H,UAAM,yBAAyB,8BAA8B;AAAA,MAC3D,WAAAA;AAAA,MACA,aAAa;AAAA,MACb,wBAAwB;AAAA,MACxB,UAAU,SAAS;AAAA,MACnB,2BAA2B;AAAA,MAC3B,gBAAgB;AAAA,IAClB,CAAC;AACD,WAAO,yBAAyB,WAAW;AAAA,EAC7C;AACA,QAAM,sBAAsB,QAAQ,4BAA4B,YAAY,KAAK,IAAI,KAAK,eAAe,KAAK,UAAU,OAAO,YAAY,KAAK,IAAI,CAAC;AACrJ,QAAM,iBAAiB,MAAM;AAC3B,UAAM,aAAa,eAAe,WAAW;AAC7C,QAAI,eAAe,WAAW,OAAOA,WAAU,WAAW,IAAI;AAC5D,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB;AACvB,aAAO;AAAA,IACT;AACA,WAAO,aAAa;AAAA,EACtB,GAAG;AACH,QAAM,cAAc,eAAe,YAAY,MAAMA,WAAU,UAAU;AACzE,SAAO,uBAAuB;AAAA,IAC5B,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA;AAAA,EACA,WAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,cAAc,0BAA0B;AAAA,IAC5C;AAAA,IACA,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,QAAM,oBAAoB,6BAA6B,YAAY,WAAW,IAAI,UAAU;AAC5F,QAAM,iBAAiB,oBAAoB;AAAA,IACzC,qBAAqB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,SAAS,mBAAmB;AAAA,IAChC;AAAA,IACA;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,QAAM,sBAAsB,iCAAiC;AAAA,IAC3D;AAAA,IACA,WAAAA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,kBAAkB,iCAAiC;AAAA,IACvD;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,mBAAmB;AAAA,EACrB;AACF;AAEA,IAAI,oBAAoB,YAAU;AAChC,QAAM,KAAK,OAAO;AAClB,MAAI,CAAC,IAAI;AACP,WAAO;AAAA,EACT;AACA,MAAI,GAAG,SAAS,WAAW;AACzB,WAAO,GAAG,YAAY;AAAA,EACxB;AACA,SAAO,GAAG,QAAQ;AACpB;AAEA,IAAM,qBAAqB,CAAC,QAAQ,eAAe;AACjD,QAAM,KAAK,kBAAkB,MAAM;AACnC,SAAO,KAAK,WAAW,EAAE,IAAI;AAC/B;AACA,IAAI,kBAAkB,CAAC;AAAA,EACrB;AAAA,EACA;AACF,MAAM;AACJ,QAAM,iBAAiB,mBAAmB,MAAM,QAAQ,MAAM,WAAW,UAAU;AACnF,QAAM,4BAA4B,QAAQ,cAAc;AACxD,QAAMW,QAAO,MAAM,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AACpE,QAAM,SAAS,kBAAkBA;AACjC,QAAM,YAAY,OAAO,KAAK;AAC9B,QAAM,qBAAqB,cAAc,eAAe,SAAS,aAAa,SAAS,gBAAgB,cAAc,iBAAiB,SAAS,eAAe,SAAS;AACvK,MAAI,sBAAsB,CAAC,2BAA2B;AACpD,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,SAAS,eAAe,SAAS;AACzD,QAAMX,aAAY,MAAM,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AACzE,QAAM,8BAA8B,MAAM,QAAQ,KAAK;AACvD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,MAAM;AACV,SAAO,qBAAqB,gBAAgB;AAAA,IAC1C;AAAA,IACA;AAAA,IACA,WAAAA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,yBAAyB,MAAM,QAAQ,OAAO;AAAA,IAC9C,gBAAgB,MAAM;AAAA,IACtB,eAAe,MAAM;AAAA,EACvB,CAAC,IAAI,cAAc;AAAA,IACjB;AAAA,IACA;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,eAAe,MAAM;AAAA,EACvB,CAAC;AACH;AAEA,SAAS,kBAAkB,OAAO;AAChC,SAAO,MAAM,UAAU,cAAc,MAAM,UAAU;AACvD;AAEA,SAAS,kBAAkB,OAAO;AAChC,QAAM,mBAAmB,SAAS,MAAM,KAAK,MAAM,MAAM;AACzD,QAAM,qBAAqB,SAAS,MAAM,MAAM,MAAM,KAAK;AAC3D,SAAO,SAAS,IAAI,OAAO;AACzB,WAAO,iBAAiB,MAAM,CAAC,KAAK,mBAAmB,MAAM,CAAC;AAAA,EAChE;AACF;AAEA,SAAS,cAAc,OAAO,QAAQ;AACpC,SAAO,MAAM,OAAO,OAAO,SAAS,MAAM,QAAQ,OAAO,QAAQ,MAAM,MAAM,OAAO,UAAU,MAAM,SAAS,OAAO;AACtH;AACA,SAAS,gBAAgB;AAAA,EACvB;AAAA,EACA,WAAAA;AAAA,EACA;AACF,GAAG;AACD,QAAM,cAAcA,WAAU,KAAK,UAAU;AAC7C,QAAM,SAAS,WAAW,IAAI,eAAa;AACzC,UAAM,OAAO,UAAU;AACvB,UAAM,SAAS,MAAM,UAAU,KAAK,MAAM,cAAc,OAAO,KAAK,IAAI,GAAG,UAAU,KAAK,UAAU,OAAO,KAAK,aAAa,CAAC;AAC9H,WAAO;AAAA,MACL,IAAI,UAAU,WAAW;AAAA,MACzB,UAAU,SAAS,aAAa,MAAM;AAAA,IACxC;AAAA,EACF,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ;AACzC,SAAO,OAAO,CAAC,IAAI,OAAO,CAAC,EAAE,KAAK;AACpC;AACA,SAAS,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAAA;AAAA,EACA;AACF,GAAG;AACD,QAAM,aAAa,gBAAgB,UAAU,EAAE,OAAO,UAAQ;AAC5D,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO;AAAA,IACT;AACA,UAAM,SAAS,KAAK,QAAQ;AAC5B,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,QAAI,CAAC,cAAc,eAAe,MAAM,GAAG;AACzC,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,MAAM,EAAE,cAAc,MAAM,GAAG;AACnD,aAAO;AAAA,IACT;AACA,UAAM,OAAO,KAAK;AAClB,UAAM,cAAc,OAAO,OAAO,KAAK,aAAa;AACpD,UAAM,iBAAiB,cAAc,KAAK,cAAc;AACxD,UAAM,eAAe,cAAc,KAAK,YAAY;AACpD,UAAM,cAAc,SAAS,OAAO,KAAK,cAAc,GAAG,OAAO,KAAK,YAAY,CAAC;AACnF,UAAM,mBAAmB,YAAY,cAAc;AACnD,UAAM,iBAAiB,YAAY,YAAY;AAC/C,QAAI,CAAC,oBAAoB,CAAC,gBAAgB;AACxC,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB;AACpB,aAAO,iBAAiB;AAAA,IAC1B;AACA,WAAO,eAAe;AAAA,EACxB,CAAC;AACD,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO;AAAA,EACT;AACA,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO,WAAW,CAAC,EAAE,WAAW;AAAA,EAClC;AACA,SAAO,gBAAgB;AAAA,IACrB;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAM,uBAAuB,CAAC,MAAM,UAAU,QAAQ,iBAAiB,MAAM,KAAK,CAAC;AAEnF,IAAI,sBAAsB,CAACF,YAAW,SAAS;AAC7C,QAAM,QAAQA,WAAU;AACxB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,qBAAqB,MAAM,MAAM,OAAO,KAAK,KAAK;AAC3D;AAEA,SAAS,eAAe;AAAA,EACtB;AAAA,EACA;AACF,GAAG;AACD,SAAO,QAAQ,UAAU,QAAQ,EAAE,KAAK,UAAU,UAAU,EAAE,CAAC;AACjE;AAEA,SAAS,QAAQ;AAAA,EACf,WAAAE;AAAA,EACA,SAAAK;AAAA,EACA;AACF,GAAG;AACD,MAAI,CAACA,UAAS;AACZ,WAAO;AAAA,EACT;AACA,MAAI,CAAC,YAAY;AACf,WAAOA,SAAQ,WAAW;AAAA,EAC5B;AACA,MAAIA,SAAQ,WAAW,QAAQL,WAAU,WAAW,OAAO;AACzD,WAAOK,SAAQ,WAAW,QAAQ;AAAA,EACpC;AACA,SAAOA,SAAQ,WAAW;AAC5B;AACA,IAAI,mBAAmB,CAAC;AAAA,EACtB,kCAAkC;AAAA,EAClC,WAAAL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,OAAO,YAAY;AACzB,QAAM,cAAc,eAAe,YAAY,MAAMA,WAAU,UAAU;AACzE,QAAM,eAAe,YAAY;AACjC,QAAM,cAAc,WAAW,KAAK,KAAK;AACzC,QAAM,YAAY,WAAW,KAAK,GAAG;AACrC,QAAM,kBAAkB,wBAAwBA,YAAW,iBAAiB;AAC5E,QAAMK,WAAU,gBAAgB,KAAK,WAAS;AAC5C,UAAM,KAAK,MAAM,WAAW;AAC5B,UAAM,cAAc,MAAM,KAAK,UAAU,OAAO,KAAK,IAAI;AACzD,UAAM,0BAA0B,sBAAsB,IAAI,aAAa;AACvE,UAAM,cAAc,eAAe;AAAA,MACjC,WAAW;AAAA,MACX;AAAA,IACF,CAAC;AACD,QAAI,yBAAyB;AAC3B,UAAI,aAAa;AACf,eAAO,aAAa;AAAA,MACtB;AACA,aAAO,cAAc,cAAc;AAAA,IACrC;AACA,QAAI,aAAa;AACf,aAAO,aAAa,cAAc;AAAA,IACpC;AACA,WAAO,cAAc;AAAA,EACvB,CAAC,KAAK;AACN,QAAM,WAAW,QAAQ;AAAA,IACvB,WAAAL;AAAA,IACA,SAAAK;AAAA,IACA,YAAY,SAASL,YAAW,WAAW;AAAA,EAC7C,CAAC;AACD,SAAO,uBAAuB;AAAA,IAC5B,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAM,0BAA0B;AAChC,IAAI,mBAAmB,CAAC;AAAA,EACtB,WAAAA;AAAA,EACA,kCAAkC;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,YAAY,kBAAkB;AACjC,WAAO;AAAA,EACT;AACA,QAAM,OAAO,YAAY;AACzB,QAAM,cAAc,eAAe,YAAY,MAAMA,WAAU,UAAU;AACzE,QAAM,eAAe,YAAY;AACjC,QAAM,cAAc,WAAW,KAAK,KAAK;AACzC,QAAM,YAAY,WAAW,KAAK,GAAG;AACrC,QAAM,kBAAkB,wBAAwBA,YAAW,iBAAiB;AAC5E,QAAM,cAAc,gBAAgB,KAAK,WAAS;AAChD,UAAM,KAAK,MAAM,WAAW;AAC5B,UAAM,YAAY,MAAM,KAAK;AAC7B,UAAM,YAAY,UAAU,KAAK,IAAI;AACrC,UAAM,YAAY,YAAY;AAC9B,UAAM,0BAA0B,sBAAsB,IAAI,aAAa;AACvE,UAAM,cAAc,eAAe;AAAA,MACjC,WAAW,eAAe;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,QAAI,yBAAyB;AAC3B,UAAI,aAAa;AACf,eAAO,YAAY,UAAU,KAAK,KAAK,IAAI,aAAa,YAAY,UAAU,KAAK,GAAG,IAAI;AAAA,MAC5F;AACA,aAAO,cAAc,UAAU,KAAK,KAAK,IAAI,eAAe,aAAa,cAAc,UAAU,KAAK,GAAG,IAAI,eAAe;AAAA,IAC9H;AACA,QAAI,aAAa;AACf,aAAO,YAAY,UAAU,KAAK,KAAK,IAAI,eAAe,aAAa,YAAY,UAAU,KAAK,GAAG,IAAI,eAAe;AAAA,IAC1H;AACA,WAAO,cAAc,UAAU,KAAK,KAAK,IAAI,aAAa,cAAc,UAAU,KAAK,GAAG,IAAI;AAAA,EAChG,CAAC;AACD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,QAAM,SAAS;AAAA,IACb;AAAA,IACA,WAAW,eAAe;AAAA,IAC1B,IAAI;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,QACP,aAAa,YAAY,WAAW;AAAA,QACpC,aAAa,YAAY,WAAW;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA,WAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,gBAAgB,qBAAqBA,WAAU,KAAK,WAAW,UAAU;AAC/E,QAAM,gBAAgB,iBAAiB;AAAA,IACrC;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACT;AACA,QAAM,cAAc,WAAW,aAAa;AAC5C,QAAM,oBAAoB,6BAA6B,YAAY,WAAW,IAAI,UAAU;AAC5F,QAAM,mCAAmC,oBAAoB,aAAa,aAAa;AACvF,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,KAAK,iBAAiB;AAAA,IACrB;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,eAAe;AAAA,IACrB;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,oBAAoB,CAAC,YAAY,aAAa;AAAA,EAChD,GAAG;AAAA,EACH,CAAC,QAAQ,WAAW,EAAE,GAAG;AAC3B;AAEA,IAAM,yBAAyB,CAAC;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,OAAO,kBAAkB,cAAc;AAC7C,QAAM,MAAM,kBAAkB,MAAM;AACpC,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,EACT;AACA,QAAM,gBAAgB,WAAW,IAAI;AACrC,MAAI,CAAC,cAAc,QAAQ,iBAAiB;AAC1C,WAAO;AAAA,EACT;AACA,QAAM,UAAU,kBAAkB,aAAa;AAC/C,SAAO,kBAAkB,YAAY,OAAO;AAC9C;AACA,IAAI,wBAAwB,CAAC;AAAA,EAC3B,WAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,UAAU,uBAAuB;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,SAAS,kBAAkB,MAAM;AACvC,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,QAAMF,aAAY,WAAW,MAAM;AACnC,MAAI,SAASE,YAAWF,UAAS,GAAG;AAClC,WAAO;AAAA,EACT;AACA,MAAIA,WAAU,QAAQ,iBAAiB;AACrC,WAAO;AAAA,EACT;AACA,QAAM,UAAU,eAAeA,YAAWE,YAAW,UAAU;AAC/D,SAAO,kBAAkB,SAAS,OAAO;AAC3C;AAEA,IAAI,SAAS,CAAC;AAAA,EACZ;AAAA,EACA,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,QAAQ;AAAA,EACR;AACF,MAAM;AACJ,QAAM,WAAW,kBAAkB,MAAM;AACzC,QAAM,aAAa,oBAAoB,MAAM;AAC7C,QAAM,kBAAkB,yBAAyB,MAAM,QAAQ,OAAO;AACtE,QAAMO,UAAS,SAAS,iBAAiB,MAAM,QAAQ,OAAO,SAAS;AACvE,QAAM,SAAS;AAAA,IACb,QAAAA;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB,IAAI,MAAM,QAAQ,OAAO,iBAAiBA,OAAM;AAAA,EACnE;AACA,QAAM,OAAO;AAAA,IACX,WAAW,IAAI,OAAO,WAAW,SAAS,OAAO,OAAO;AAAA,IACxD,iBAAiB,IAAI,OAAO,iBAAiB,SAAS,OAAO,OAAO;AAAA,IACpE,QAAQ,IAAI,OAAO,QAAQ,SAAS,OAAO,KAAK,KAAK;AAAA,EACvD;AACA,QAAM,UAAU;AAAA,IACd;AAAA,IACA;AAAA,EACF;AACA,MAAI,MAAM,UAAU,cAAc;AAChC,WAAO;AAAA,MACL,GAAG;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAMP,aAAY,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AACnE,QAAM,YAAY,gBAAgB,cAAc;AAAA,IAC9C,YAAY,KAAK;AAAA,IACjB,WAAAA;AAAA,IACA,YAAY,WAAW;AAAA,IACvB,YAAY,WAAW;AAAA,IACvB,gBAAgB,MAAM;AAAA,IACtB;AAAA,IACA,eAAe,MAAM;AAAA,EACvB,CAAC;AACD,QAAM,0BAA0B,sBAAsB;AAAA,IACpD,WAAAA;AAAA,IACA,QAAQ;AAAA,IACR,gBAAgB,MAAM;AAAA,IACtB,YAAY,WAAW;AAAA,IACvB,YAAY,WAAW;AAAA,EACzB,CAAC;AACD,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,IACH;AAAA,IACA,YAAY;AAAA,MACV,YAAY,WAAW;AAAA,MACvB,YAAY;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,mBAAmB,qBAAqB;AAAA,IACxC,oBAAoB,oBAAoB,QAAQ;AAAA,EAClD;AACA,SAAO;AACT;AAEA,SAAS,cAAc,KAAK,YAAY;AACtC,SAAO,IAAI,IAAI,QAAM,WAAW,EAAE,CAAC;AACrC;AACA,IAAI,YAAY,CAAC;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,OAAO,OAAO;AACpB,QAAM,gBAAgB,cAAc,KAAK,KAAK,UAAU;AACxD,QAAM,YAAY,sBAAsB;AAAA,IACtC;AAAA,IACA;AAAA,IACA,aAAa,OAAO;AAAA,IACpB,UAAU,SAAS;AAAA,IACnB;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,EACF;AACF;AAEA,IAAI,2BAA2B,CAAC;AAAA,EAC9B;AAAA,EACA,WAAAA;AAAA,EACA,WAAAF;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,sBAAsB,iCAAiC;AAAA,IAC3D;AAAA,IACA,WAAAE;AAAA,IACA;AAAA,IACA,WAAAF;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO,iCAAiC;AAAA,IACtC;AAAA,IACA,WAAAE;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,cAAc,CAAC;AAAA,EACjB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU;AACZ,MAAM;AACJ,IAAE,MAAM,iBAAiB,UAAU,OAAwCR,WAAU,IAAIA,WAAU,IAAI;AACvG,QAAM,uBAAuB,MAAM;AACnC,QAAM,WAAW,kBAAkB,MAAM;AACzC,QAAM,aAAa,oBAAoB,MAAM;AAC7C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAMQ,aAAY,WAAW,MAAM,SAAS,UAAU,EAAE;AACxD,QAAM,SAAS,kBAAkB,oBAAoB;AACrD,GAAC,SAAS,OAAwCR,WAAU,OAAO,kDAAkD,IAAIA,WAAU,IAAI;AACvI,QAAM,cAAc,WAAW,MAAM;AACrC,QAAM,SAAS,UAAU;AAAA,IACvB,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,kBAAkB,yBAAyB;AAAA,IAC/C;AAAA,IACA,WAAAQ;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,eAAe,MAAM;AAAA,EACvB,CAAC;AACD,SAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,kBAAkB,iBAAe;AAAA,EACnC,OAAO,WAAW;AAAA,EAClB,aAAa,WAAW;AAC1B;AAEA,IAAI,gBAAgB,CAAC;AAAA,EACnB,WAAAA;AAAA,EACA,MAAAW;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,cAAc,eAAeA,MAAK,MAAMX,WAAU,UAAU;AAClE,QAAM,aAAa,6BAA6BW,MAAK,WAAW,IAAI,UAAU;AAC9E,QAAM,WAAW,WAAW,QAAQX,UAAS;AAC7C,IAAE,aAAa,MAAM,OAAwCR,WAAU,OAAO,2CAA2C,IAAIA,WAAU,IAAI;AAC3I,QAAM,gBAAgB,WAAW,MAAM,WAAW,CAAC;AACnD,QAAM,WAAW,cAAc,OAAO,CAAC,UAAU,SAAS;AACxD,aAAS,KAAK,WAAW,EAAE,IAAI;AAC/B,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,QAAM,gBAAgB;AAAA,IACpB,eAAemB,MAAK,WAAW,SAAS;AAAA,IACxC;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAY,sBAAsB;AAAA,IACtC;AAAA,IACA,aAAaA;AAAA,IACb;AAAA,IACA,MAAM;AAAA,IACN,UAAU,SAAS;AAAA,IACnB,oBAAoB;AAAA,EACtB,CAAC;AACD,QAAM,SAAS;AAAA,IACb;AAAA,IACA;AAAA,IACA,IAAI;AAAA,MACF,MAAM;AAAA,MACN,aAAa,gBAAgBX,WAAU,UAAU;AAAA,IACnD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,oBAAoB,CAAC,YAAY,aAAa;AAAA,EAChD,YAAY,WAAW;AAAA,EACvB,YAAY,kBAAkB,WAAW,YAAY,OAAO;AAC9D;AAEA,IAAM,QAAQ,SAAO;AACnB,MAAI,MAAuC;AACzC;AACE;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,SAAS,SAAO;AACpB,MAAI,MAAuC;AACzC;AACE;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAI,kBAAkB,CAAC;AAAA,EACrB,WAAAA;AAAA,EACA,QAAQ;AAAA,EACR;AACF,MAAM;AACJ,QAAM,SAAS,OAAOA,WAAU,QAAQ,QAAQ;AAChD,QAAM,OAAO,WAAW,QAAQ,mBAAmB;AACnD,QAAM,QAAQ;AAAA,IACZ,GAAGA;AAAA,IACH,aAAa;AAAA,MACX,GAAGA,WAAU;AAAA,MACb;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,WAAW,CAAAF,eAAa;AAC1B,QAAM,QAAQA,WAAU;AACxB,GAAC,QAAQ,OAAwCN,WAAU,OAAO,oCAAoC,IAAIA,WAAU,IAAI;AACxH,SAAO;AACT;AAEA,IAAI,kCAAkC,CAAC;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,qBAAqB,SAAS,OAAO,KAAK;AAChD,SAAO,UAAU,IAAI,CAAAQ,eAAa;AAChC,UAAM,cAAcA,WAAU,WAAW;AACzC,UAAM,WAAW,kBAAkB,WAAW;AAC9C,UAAM,QAAQ,SAAS,QAAQ;AAC/B,UAAM,wBAAwB,MAAM,OAAO,KAAK;AAChD,UAAM,cAAc,IAAI,oBAAoB,qBAAqB;AACjE,UAAM,QAAQ,gBAAgB;AAAA,MAC5B,WAAAA;AAAA,MACA,QAAQ;AAAA,MACR,qBAAqB,SAAS,OAAO;AAAA,IACvC,CAAC;AACD,WAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAI,gCAAgC,CAAC;AAAA,EACnC;AAAA,EACA;AACF,MAAM;AACJ,QAAM;AACN,QAAM,mBAAmB,UAAU,SAAS,IAAI,CAAAJ,YAAU;AACxD,UAAM,WAAW,MAAM,WAAW,WAAWA,QAAO,WAAW;AAC/D,UAAM,WAAW,gBAAgB,UAAUA,QAAO,MAAM;AACxD,WAAO;AAAA,EACT,CAAC;AACD,QAAM,aAAa;AAAA,IACjB,GAAG,MAAM,WAAW;AAAA,IACpB,GAAG,eAAe,gBAAgB;AAAA,EACpC;AACA,QAAM,mBAAmB,eAAe,gCAAgC;AAAA,IACtE,WAAW,UAAU;AAAA,IACrB,mBAAmB;AAAA,IACnB,UAAU,MAAM;AAAA,EAClB,CAAC,CAAC;AACF,QAAM,aAAa;AAAA,IACjB,GAAG,MAAM,WAAW;AAAA,IACpB,GAAG;AAAA,EACL;AACA,YAAU,SAAS,QAAQ,QAAM;AAC/B,WAAO,WAAW,EAAE;AAAA,EACtB,CAAC;AACD,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAY,kBAAkB,MAAM,MAAM;AAChD,QAAM,UAAU,YAAY,WAAW,WAAW,SAAS,IAAI;AAC/D,QAAMI,aAAY,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AACnE,QAAMW,QAAO,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AAC9D,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR;AAAA,EACF,IAAI,cAAc;AAAA,IAChB,WAAAX;AAAA,IACA,MAAAW;AAAA,IACA;AAAA,IACA,UAAU,MAAM;AAAA,EAClB,CAAC;AACD,QAAM,iBAAiB,WAAW,QAAQ,mBAAmB,MAAM,SAAS;AAC5E,QAAM,SAAS,cAAc;AAAA,IAC3B,YAAY,MAAM,QAAQ,KAAK;AAAA,IAC/B,WAAW,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AAAA,IAC5D,YAAY,WAAW;AAAA,IACvB,YAAY,WAAW;AAAA,IACvB;AAAA,IACA,UAAU,MAAM;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACP,QAAM,gBAAgB;AAAA,IACpB,GAAG;AAAA,IACH,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,EACtB;AACA,MAAI,MAAM,UAAU,cAAc;AAChC,WAAO;AAAA,EACT;AACA,QAAMC,eAAc;AAAA,IAClB,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ,MAAM;AAAA,IACd,WAAW;AAAA,EACb;AACA,SAAOA;AACT;AAEA,IAAM,aAAa,WAAS,MAAM,iBAAiB;AACnD,IAAM,sBAAsB,CAAC,OAAO,SAAS,sBAAsB;AACjE,QAAM,aAAa,kBAAkB,MAAM,YAAY,OAAO;AAC9D,MAAI,CAAC,WAAW,KAAK,KAAK,mBAAmB;AAC3C,WAAO,OAAO;AAAA,MACZ;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,YAAY;AAAA,IACjB;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,SAAS,wBAAwB,OAAO;AACtC,MAAI,MAAM,cAAc,MAAM,iBAAiB,QAAQ;AACrD,WAAO;AAAA,MACL,GAAG;AAAA,MACH,mBAAmB;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,SAAS;AAAA,EACb,OAAO;AAAA,EACP,WAAW;AAAA,EACX,aAAa;AACf;AACA,IAAI,UAAU,CAAC,QAAQ,QAAQ,WAAW;AACxC,MAAI,OAAO,SAAS,SAAS;AAC3B,WAAO;AAAA,MACL,GAAG;AAAA,MACH,aAAa;AAAA,IACf;AAAA,EACF;AACA,MAAI,OAAO,SAAS,mBAAmB;AACrC,MAAE,MAAM,UAAU,UAAU,OAAwCpB,WAAU,OAAO,8CAA8C,IAAIA,WAAU,IAAI;AACrJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,UAAMQ,aAAY,WAAW,WAAW,SAAS,UAAU,EAAE;AAC7D,UAAMW,QAAO,WAAW,WAAW,SAAS,UAAU,EAAE;AACxD,UAAM,SAAS;AAAA,MACb,WAAW;AAAA,MACX,iBAAiBX,WAAU,OAAO,UAAU;AAAA,MAC5C,QAAQ;AAAA,IACV;AACA,UAAM,UAAU;AAAA,MACd;AAAA,MACA,MAAM;AAAA,QACJ,WAAW,IAAI,OAAO,WAAW,SAAS,OAAO,OAAO;AAAA,QACxD,iBAAiB,IAAI,OAAO,WAAW,SAAS,OAAO,OAAO;AAAA,QAC9D,QAAQ,IAAI,OAAO,WAAW,SAAS,OAAO,KAAK,KAAK;AAAA,MAC1D;AAAA,IACF;AACA,UAAM,wBAAwB,gBAAgB,WAAW,UAAU,EAAE,MAAM,UAAQ,CAAC,KAAK,aAAa;AACtG,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,cAAc;AAAA,MAChB,WAAAA;AAAA,MACA,MAAAW;AAAA,MACA,YAAY,WAAW;AAAA,MACvB;AAAA,IACF,CAAC;AACD,UAAM,SAAS;AAAA,MACb,OAAO;AAAA,MACP,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,uBAAuB;AACzC,QAAI,MAAM,UAAU,gBAAgB,MAAM,UAAU,gBAAgB;AAClE,aAAO;AAAA,IACT;AACA,MAAE,MAAM,UAAU,cAAc,OAAwCnB,WAAU,OAAO,sCAAsC,MAAM,KAAK,EAAE,IAAIA,WAAU,IAAI;AAC9J,UAAM,SAAS;AAAA,MACb,GAAG;AAAA,MACH,OAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,0BAA0B;AAC5C,MAAE,MAAM,UAAU,gBAAgB,MAAM,UAAU,kBAAkB,OAAwCA,WAAU,OAAO,cAAc,OAAO,IAAI,sBAAsB,MAAM,KAAK,EAAE,IAAIA,WAAU,IAAI;AAC3M,WAAO,8BAA8B;AAAA,MACnC;AAAA,MACA,WAAW,OAAO;AAAA,IACpB,CAAC;AAAA,EACH;AACA,MAAI,OAAO,SAAS,QAAQ;AAC1B,QAAI,MAAM,UAAU,gBAAgB;AAClC,aAAO;AAAA,IACT;AACA,KAAC,kBAAkB,KAAK,IAAI,OAAwCA,WAAU,OAAO,GAAG,OAAO,IAAI,2BAA2B,MAAM,KAAK,EAAE,IAAIA,WAAU,IAAI;AAC7J,UAAM;AAAA,MACJ,QAAQ;AAAA,IACV,IAAI,OAAO;AACX,QAAI,UAAU,iBAAiB,MAAM,QAAQ,OAAO,SAAS,GAAG;AAC9D,aAAO;AAAA,IACT;AACA,WAAO,OAAO;AAAA,MACZ;AAAA,MACA;AAAA,MACA,QAAQ,WAAW,KAAK,IAAI,MAAM,SAAS;AAAA,IAC7C,CAAC;AAAA,EACH;AACA,MAAI,OAAO,SAAS,2BAA2B;AAC7C,QAAI,MAAM,UAAU,gBAAgB;AAClC,aAAO,wBAAwB,KAAK;AAAA,IACtC;AACA,QAAI,MAAM,UAAU,cAAc;AAChC,aAAO,wBAAwB,KAAK;AAAA,IACtC;AACA,KAAC,kBAAkB,KAAK,IAAI,OAAwCA,WAAU,OAAO,GAAG,OAAO,IAAI,2BAA2B,MAAM,KAAK,EAAE,IAAIA,WAAU,IAAI;AAC7J,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,UAAM,SAAS,MAAM,WAAW,WAAW,EAAE;AAC7C,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,UAAM,WAAW,gBAAgB,QAAQ,SAAS;AAClD,WAAO,oBAAoB,OAAO,UAAU,KAAK;AAAA,EACnD;AACA,MAAI,OAAO,SAAS,+BAA+B;AACjD,QAAI,MAAM,UAAU,gBAAgB;AAClC,aAAO;AAAA,IACT;AACA,KAAC,kBAAkB,KAAK,IAAI,OAAwCA,WAAU,OAAO,8CAA8C,MAAM,KAAK,EAAE,IAAIA,WAAU,IAAI;AAClK,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,UAAM,SAAS,MAAM,WAAW,WAAW,EAAE;AAC7C,KAAC,SAAS,OAAwCA,WAAU,OAAO,6BAA6B,EAAE,+BAA+B,IAAIA,WAAU,IAAI;AACnJ,MAAE,OAAO,cAAc,aAAa,OAAwCA,WAAU,OAAO,wCAAwC,OAAO,SAAS,CAAC;AAAA,0BAChI,OAAO,OAAO,SAAS,CAAC,EAAE,IAAIA,WAAU,IAAI;AAClE,UAAM,UAAU;AAAA,MACd,GAAG;AAAA,MACH;AAAA,IACF;AACA,WAAO,oBAAoB,OAAO,SAAS,IAAI;AAAA,EACjD;AACA,MAAI,OAAO,SAAS,uCAAuC;AACzD,QAAI,MAAM,UAAU,gBAAgB;AAClC,aAAO;AAAA,IACT;AACA,KAAC,kBAAkB,KAAK,IAAI,OAAwCA,WAAU,OAAO,8CAA8C,MAAM,KAAK,EAAE,IAAIA,WAAU,IAAI;AAClK,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,UAAM,SAAS,MAAM,WAAW,WAAW,EAAE;AAC7C,KAAC,SAAS,OAAwCA,WAAU,OAAO,6BAA6B,EAAE,wCAAwC,IAAIA,WAAU,IAAI;AAC5J,MAAE,OAAO,qBAAqB,oBAAoB,OAAwCA,WAAU,OAAO,+CAA+C,OAAO,gBAAgB,CAAC;AAAA,0BAC5J,OAAO,OAAO,gBAAgB,CAAC,EAAE,IAAIA,WAAU,IAAI;AACzE,UAAM,UAAU;AAAA,MACd,GAAG;AAAA,MACH;AAAA,IACF;AACA,WAAO,oBAAoB,OAAO,SAAS,IAAI;AAAA,EACjD;AACA,MAAI,OAAO,SAAS,yBAAyB;AAC3C,QAAI,MAAM,UAAU,kBAAkB,MAAM,UAAU,kBAAkB;AACtE,aAAO;AAAA,IACT;AACA,KAAC,kBAAkB,KAAK,IAAI,OAAwCA,WAAU,OAAO,kCAAkC,MAAM,KAAK,EAAE,IAAIA,WAAU,IAAI;AACtJ,KAAC,MAAM,wBAAwB,OAAwCA,WAAU,OAAO,6DAA6D,IAAIA,WAAU,IAAI;AACvK,UAAM,YAAY,OAAO,QAAQ;AACjC,QAAI,UAAU,MAAM,SAAS,OAAO,SAAS,SAAS,GAAG;AACvD,aAAO,wBAAwB,KAAK;AAAA,IACtC;AACA,UAAM,WAAW,eAAe,MAAM,UAAU,SAAS;AACzD,QAAI,WAAW,KAAK,GAAG;AACrB,aAAO,YAAY;AAAA,QACjB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,OAAO;AAAA,MACZ;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI,OAAO,SAAS,8BAA8B;AAChD,QAAI,CAAC,kBAAkB,KAAK,GAAG;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,YAAY,OAAO,QAAQ;AACjC,QAAI,UAAU,WAAW,MAAM,SAAS,OAAO,GAAG,GAAG;AACnD,aAAO;AAAA,IACT;AACA,UAAMqB,iBAAgB;AAAA,MACpB,GAAG,MAAM;AAAA,MACT,QAAQ;AAAA,QACN,GAAG,MAAM,SAAS;AAAA,QAClB,KAAK;AAAA,MACP;AAAA,IACF;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,UAAUA;AAAA,IACZ;AAAA,EACF;AACA,MAAI,OAAO,SAAS,aAAa,OAAO,SAAS,eAAe,OAAO,SAAS,eAAe,OAAO,SAAS,cAAc;AAC3H,QAAI,MAAM,UAAU,gBAAgB,MAAM,UAAU,gBAAgB;AAClE,aAAO;AAAA,IACT;AACA,MAAE,MAAM,UAAU,cAAc,OAAwCrB,WAAU,OAAO,GAAG,OAAO,IAAI,uCAAuC,IAAIA,WAAU,IAAI;AAChK,UAAM,SAAS,gBAAgB;AAAA,MAC7B;AAAA,MACA,MAAM,OAAO;AAAA,IACf,CAAC;AACD,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,WAAO,OAAO;AAAA,MACZ;AAAA,MACA,QAAQ,OAAO;AAAA,MACf,iBAAiB,OAAO;AAAA,MACxB,mBAAmB,OAAO;AAAA,IAC5B,CAAC;AAAA,EACH;AACA,MAAI,OAAO,SAAS,gBAAgB;AAClC,UAAM,SAAS,OAAO,QAAQ;AAC9B,MAAE,MAAM,UAAU,gBAAgB,OAAwCA,WAAU,OAAO,qEAAqE,IAAIA,WAAU,IAAI;AAClL,UAAM,WAAW;AAAA,MACf,GAAG;AAAA,MACH,OAAO;AAAA,MACP,WAAW;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,gBAAgB;AAClC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,OAAO;AACX,MAAE,MAAM,UAAU,cAAc,MAAM,UAAU,kBAAkB,OAAwCA,WAAU,OAAO,kCAAkC,MAAM,KAAK,EAAE,IAAIA,WAAU,IAAI;AAC5L,UAAM,SAAS;AAAA,MACb,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,iBAAiB;AACnC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,OAAO;AACX,WAAO;AAAA,MACL,OAAO;AAAA,MACP;AAAA,MACA,aAAa;AAAA,IACf;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,MAAM,QAAQ,WAAW;AAChC,SAAO,kBAAkB,UAAU,UAAU,UAAU,OAAO,SAAS;AACzE;AACA,IAAM,uBAAuB,WAAS;AAAA,EACpC,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,SAAS,WAAS;AAAA,EACtB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,iBAAiB,WAAS;AAAA,EAC9B,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,uBAAuB,WAAS;AAAA,EACpC,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,qBAAqB,OAAO;AAAA,EAChC,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,wBAAwB,WAAS;AAAA,EACrC,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,2BAA2B,WAAS;AAAA,EACxC,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,kCAAkC,WAAS;AAAA,EAC/C,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,OAAO,WAAS;AAAA,EACpB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,qBAAqB,WAAS;AAAA,EAClC,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,0BAA0B,WAAS;AAAA,EACvC,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,SAAS,OAAO;AAAA,EACpB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,WAAW,OAAO;AAAA,EACtB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,YAAY,OAAO;AAAA,EACvB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,WAAW,OAAO;AAAA,EACtB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,QAAQ,OAAO;AAAA,EACnB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,cAAc,WAAS;AAAA,EAC3B,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,eAAe,WAAS;AAAA,EAC5B,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,OAAO,WAAS;AAAA,EACpB,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,cAAc,WAAS;AAAA,EAC3B,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,wBAAwB,OAAO;AAAA,EACnC,MAAM;AAAA,EACN,SAAS;AACX;AAEA,SAAS,aAAa,mBAAmB;AACvC,MAAI,kBAAkB,UAAU,GAAG;AACjC;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,IAAI,OAAK,EAAE,WAAW,KAAK;AAC7D,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,UAAU,QAAQ,CAAC;AACzB,UAAM,WAAW,QAAQ,IAAI,CAAC;AAC9B,QAAI,YAAY,WAAW,GAAG;AAC5B,aAAO,OAAO,IAAI;AAAA,IACpB;AAAA,EACF;AACA,MAAI,CAAC,OAAO,KAAK,MAAM,EAAE,QAAQ;AAC/B;AAAA,EACF;AACA,QAAM,YAAY,QAAQ,IAAI,WAAS;AACrC,UAAM,WAAW,QAAQ,OAAO,KAAK,CAAC;AACtC,WAAO,WAAW,MAAM,KAAK,MAAM,GAAG,KAAK;AAAA,EAC7C,CAAC,EAAE,KAAK,IAAI;AACZ,SAAwC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK5C,SAAS;AAAA,GACZ,IAAI;AACP;AACA,SAAS,mBAAmB,UAAU,YAAY;AAChD,MAAI,MAAuC;AACzC,UAAM,oBAAoB,6BAA6B,SAAS,UAAU,IAAI,WAAW,UAAU;AACnG,iBAAa,iBAAiB;AAAA,EAChC;AACF;AAEA,IAAI,OAAO,aAAW,CAAC;AAAA,EACrB;AAAA,EACA;AACF,MAAM,UAAQ,YAAU;AACtB,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,SAAK,MAAM;AACX;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO;AACX,QAAM,UAAU,SAAS;AACzB,MAAI,QAAQ,UAAU,kBAAkB;AACtC,aAAS,aAAa;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB,CAAC,CAAC;AAAA,EACJ;AACA,IAAE,SAAS,EAAE,UAAU,UAAU,OAAwCA,WAAU,OAAO,kCAAkC,IAAIA,WAAU,IAAI;AAC9I,WAAS,MAAM,CAAC;AAChB,WAAS,qBAAqB;AAAA,IAC5B,aAAa;AAAA,IACb;AAAA,EACF,CAAC,CAAC;AACF,QAAM,gBAAgB;AAAA,IACpB,0BAA0B,iBAAiB;AAAA,EAC7C;AACA,QAAM,UAAU;AAAA,IACd,aAAa;AAAA,IACb;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,QAAQ,gBAAgB,OAAO;AACnC,qBAAmB,UAAU,UAAU;AACvC,WAAS,eAAe;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACJ;AAEA,IAAI,QAAQ,aAAW,MAAM,UAAQ,YAAU;AAC7C,MAAI,MAAM,QAAQ,iBAAiB,GAAG;AACpC,YAAQ,SAAS;AAAA,EACnB;AACA,MAAI,MAAM,QAAQ,cAAc,GAAG;AACjC,YAAQ,SAAS,OAAO,QAAQ,UAAU,OAAO,MAAM;AAAA,EACzD;AACA,MAAI,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,eAAe,GAAG;AAC5D,YAAQ,QAAQ;AAAA,EAClB;AACA,OAAK,MAAM;AACb;AAEA,IAAM,SAAS;AAAA,EACb,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAM,UAAU;AAAA,EACd,SAAS;AAAA,IACP,MAAM;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,EACR;AACF;AACA,IAAM,UAAU;AAAA,EACd,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AACf;AACA,IAAM,oBAAoB,GAAG,QAAQ,WAAW,KAAK,OAAO,WAAW;AACvE,IAAM,cAAc;AAAA,EAClB,OAAO,WAAW,iBAAiB;AAAA,EACnC,MAAM,aAAa,iBAAiB,aAAa,iBAAiB;AAAA,EAClE,MAAM,cAAY;AAChB,UAAM,SAAS,GAAG,QAAQ,KAAK,OAAO,IAAI;AAC1C,WAAO,aAAa,MAAM,aAAa,MAAM;AAAA,EAC/C;AAAA,EACA,aAAa,aAAa,iBAAiB;AAAA,EAC3C,aAAa,UAAU,iBAAiB,WAAW,iBAAiB,YAAY,iBAAiB;AACnG;AACA,IAAM,SAAS,CAAAe,YAAU,UAAUA,SAAQ,MAAM,IAAI,SAAY,aAAaA,QAAO,CAAC,OAAOA,QAAO,CAAC;AACrG,IAAM,aAAa;AAAA,EACjB;AAAA,EACA,MAAM,CAACA,SAAQ,gBAAgB;AAC7B,UAAM,YAAY,OAAOA,OAAM;AAC/B,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AACA,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AACA,WAAO,GAAG,SAAS,UAAU,QAAQ,MAAM,IAAI;AAAA,EACjD;AACF;AAEA,IAAM;AAAA,EACJ;AAAA,EACA;AACF,IAAI;AACJ,IAAM,gBAAgB,cAAc;AACpC,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB;AAC3B,IAAI,kBAAkB,CAAC;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,aAAa,SAAS,SAAS,WAAW;AAChD,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AACA,MAAI,cAAc,uBAAuB;AACvC,WAAO;AAAA,EACT;AACA,QAAM,aAAa,aAAa;AAChC,QAAM,WAAW,cAAc,gBAAgB;AAC/C,QAAM,eAAe,WAAW,WAAW,WAAW,qBAAqB;AAC3E,SAAO,OAAO,aAAa,QAAQ,CAAC,CAAC;AACvC;AAEA,IAAI,yBAAyB,CAAC;AAAA,EAC5B;AAAA,EACA,WAAAP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,cAAc,kBAAkB,MAAM;AAC5C,QAAM,cAAc,cAAc,WAAW,WAAW,IAAI;AAC5D,QAAMW,QAAO,WAAWX,WAAU,WAAW,WAAW;AACxD,QAAM,kBAAkB,yBAAyB;AAAA,IAC/C;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,eAAeW;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,QAAMJ,UAAS,SAAS,iBAAiBP,WAAU,OAAO,UAAU,MAAM;AAC1E,SAAOO;AACT;AAEA,IAAI,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAAI;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,WAAW,MAAM,WAAW,QAAQ;AACvC,UAAM,uBAAuB,UAAU;AAAA,MACrC;AAAA,MACA,QAAQ;AAAA,MACR,aAAaA;AAAA,MACb;AAAA,MACA,oBAAoB;AAAA,IACtB,CAAC;AACD,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,wBAAwB;AAAA,IAC1B;AAAA,EACF;AACA,MAAI,WAAW,GAAG,SAAS,WAAW;AACpC,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,wBAAwB;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,kBAAkB;AAAA,IACtB,GAAG;AAAA,IACH,WAAW;AAAA,EACb;AACA,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,wBAAwB;AAAA,EAC1B;AACF;AAEA,IAAM,iBAAiB,CAAC;AAAA,EACtB;AAAA,EACA;AACF,MAAM,UAAQ,YAAU;AACtB,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,SAAK,MAAM;AACX;AAAA,EACF;AACA,QAAM,QAAQ,SAAS;AACvB,QAAM,SAAS,OAAO,QAAQ;AAC9B,MAAI,MAAM,UAAU,cAAc;AAChC,aAAS,YAAY;AAAA,MACnB;AAAA,IACF,CAAC,CAAC;AACF;AAAA,EACF;AACA,MAAI,MAAM,UAAU,QAAQ;AAC1B;AAAA,EACF;AACA,QAAM,mBAAmB,MAAM,UAAU,kBAAkB,MAAM;AACjE,GAAC,CAAC,mBAAmB,OAAwCnB,WAAU,OAAO,6DAA6D,IAAIA,WAAU,IAAI;AAC7J,IAAE,MAAM,UAAU,cAAc,MAAM,UAAU,kBAAkB,OAAwCA,WAAU,OAAO,yBAAyB,MAAM,KAAK,EAAE,IAAIA,WAAU,IAAI;AACnL,QAAM,WAAW,MAAM;AACvB,QAAM,aAAa,MAAM;AACzB,QAAMQ,aAAY,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AACnE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc;AAAA,IAChB;AAAA,IACA,YAAY,MAAM;AAAA,IAClB,eAAe,MAAM;AAAA,IACrB,cAAc,MAAM;AAAA,IACpB,MAAM,MAAM,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AAAA,IAC7D,UAAU,MAAM;AAAA,IAChB,YAAY,MAAM,WAAW;AAAA,EAC/B,CAAC;AACD,QAAM,cAAc,yBAAyB,kBAAkB,MAAM,IAAI;AACzE,QAAML,WAAU,yBAAyB,cAAc,MAAM,IAAI;AACjE,QAAM,SAAS;AAAA,IACb,OAAO,SAAS,UAAU;AAAA,IAC1B,aAAa,SAAS,UAAU;AAAA,EAClC;AACA,QAAM,SAAS;AAAA,IACb,aAAaK,WAAU,WAAW;AAAA,IAClC,MAAMA,WAAU,WAAW;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,MAAM,MAAM;AAAA,IACZ;AAAA,IACA,SAAAL;AAAA,EACF;AACA,QAAM,sBAAsB,uBAAuB;AAAA,IACjD;AAAA,IACA,WAAAK;AAAA,IACA;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,eAAe,MAAM;AAAA,EACvB,CAAC;AACD,QAAM,YAAY;AAAA,IAChB,UAAU,MAAM;AAAA,IAChB,eAAe,MAAM;AAAA,IACrB;AAAA,IACA;AAAA,EACF;AACA,QAAM,sBAAsB,CAAC,UAAU,MAAM,QAAQ,OAAO,QAAQ,mBAAmB,KAAK,QAAQ,OAAO,OAAO;AAClH,MAAI,CAAC,qBAAqB;AACxB,aAAS,aAAa;AAAA,MACpB;AAAA,IACF,CAAC,CAAC;AACF;AAAA,EACF;AACA,QAAM,eAAe,gBAAgB;AAAA,IACnC,SAAS,MAAM,QAAQ,OAAO;AAAA,IAC9B,aAAa;AAAA,IACb;AAAA,EACF,CAAC;AACD,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,WAAS,YAAY,IAAI,CAAC;AAC5B;AAEA,IAAIc,mBAAkB,OAAO;AAAA,EAC3B,GAAG,OAAO;AAAA,EACV,GAAG,OAAO;AACZ;AAEA,SAAS,uBAAuBlB,SAAQ;AACtC,SAAO;AAAA,IACL,WAAW;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,IAAI,WAAS;AACX,UAAI,MAAM,WAAW,UAAU,MAAM,WAAW,OAAO,UAAU;AAC/D;AAAA,MACF;AACA,MAAAA,QAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,kBAAkB;AAAA,EACzB;AACF,GAAG;AACD,WAAS,eAAe;AACtB,mBAAekB,iBAAgB,CAAC;AAAA,EAClC;AACA,QAAM,YAAY,qBAAQ,YAAY;AACtC,QAAM,UAAU,uBAAuB,SAAS;AAChD,MAAI,SAAS;AACb,WAASC,YAAW;AAClB,WAAO,WAAW;AAAA,EACpB;AACA,WAASrB,SAAQ;AACf,KAAC,CAACqB,UAAS,IAAI,OAAwCvB,WAAU,OAAO,kDAAkD,IAAIA,WAAU,IAAI;AAC5I,aAAS,WAAW,QAAQ,CAAC,OAAO,CAAC;AAAA,EACvC;AACA,WAAS,OAAO;AACd,KAACuB,UAAS,IAAI,OAAwCvB,WAAU,OAAO,6CAA6C,IAAIA,WAAU,IAAI;AACtI,cAAU,OAAO;AACjB,WAAO;AACP,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL,OAAAE;AAAA,IACA;AAAA,IACA,UAAAqB;AAAA,EACF;AACF;AAEA,IAAM,eAAe,YAAU,MAAM,QAAQ,eAAe,KAAK,MAAM,QAAQ,cAAc,KAAK,MAAM,QAAQ,OAAO;AACvH,IAAM,iBAAiB,WAAS;AAC9B,QAAM,WAAW,kBAAkB;AAAA,IACjC,gBAAgB,eAAa;AAC3B,YAAM,SAAS,mBAAmB;AAAA,QAChC;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAAA,EACF,CAAC;AACD,SAAO,UAAQ,YAAU;AACvB,QAAI,CAAC,SAAS,SAAS,KAAK,MAAM,QAAQ,iBAAiB,GAAG;AAC5D,eAAS,MAAM;AAAA,IACjB;AACA,QAAI,SAAS,SAAS,KAAK,aAAa,MAAM,GAAG;AAC/C,eAAS,KAAK;AAAA,IAChB;AACA,SAAK,MAAM;AAAA,EACb;AACF;AAEA,IAAI,sBAAsB,cAAY;AACpC,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,QAAM,YAAY,WAAW,MAAM;AACjC,gBAAY;AAAA,EACd,CAAC;AACD,QAAM,SAAS,aAAW;AACxB,QAAI,WAAW;AACb,aAAwC,QAAQ,6DAA6D,IAAI;AACjH;AAAA,IACF;AACA,QAAI,WAAW;AACb,aAAwC,QAAQ;AAAA;AAAA;AAAA,OAG/C,IAAI;AACL;AAAA,IACF;AACA,gBAAY;AACZ,aAAS,OAAO;AAChB,iBAAa,SAAS;AAAA,EACxB;AACA,SAAO,YAAY,MAAM;AACzB,SAAO;AACT;AAEA,IAAI,kBAAkB,MAAM;AAC1B,QAAM,UAAU,CAAC;AACjB,QAAMC,WAAU,aAAW;AACzB,UAAM,QAAQ,QAAQ,UAAU,UAAQ,KAAK,YAAY,OAAO;AAChE,MAAE,UAAU,MAAM,OAAwCxB,WAAU,OAAO,sBAAsB,IAAIA,WAAU,IAAI;AACnH,UAAM,CAAC,KAAK,IAAI,QAAQ,OAAO,OAAO,CAAC;AACvC,UAAM,SAAS;AAAA,EACjB;AACA,QAAMyB,OAAM,QAAM;AAChB,UAAM,UAAU,WAAW,MAAMD,SAAQ,OAAO,CAAC;AACjD,UAAM,QAAQ;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,IACZ;AACA,YAAQ,KAAK,KAAK;AAAA,EACpB;AACA,QAAME,SAAQ,MAAM;AAClB,QAAI,CAAC,QAAQ,QAAQ;AACnB;AAAA,IACF;AACA,UAAM,UAAU,CAAC,GAAG,OAAO;AAC3B,YAAQ,SAAS;AACjB,YAAQ,QAAQ,WAAS;AACvB,mBAAa,MAAM,OAAO;AAC1B,YAAM,SAAS;AAAA,IACjB,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL,KAAAD;AAAA,IACA,OAAAC;AAAA,EACF;AACF;AAEA,IAAM,oBAAoB,CAAC,OAAO,WAAW;AAC3C,MAAI,SAAS,QAAQ,UAAU,MAAM;AACnC,WAAO;AAAA,EACT;AACA,MAAI,SAAS,QAAQ,UAAU,MAAM;AACnC,WAAO;AAAA,EACT;AACA,SAAO,MAAM,gBAAgB,OAAO,eAAe,MAAM,UAAU,OAAO;AAC5E;AACA,IAAM,iBAAiB,CAAC,OAAO,WAAW;AACxC,MAAI,SAAS,QAAQ,UAAU,MAAM;AACnC,WAAO;AAAA,EACT;AACA,MAAI,SAAS,QAAQ,UAAU,MAAM;AACnC,WAAO;AAAA,EACT;AACA,SAAO,MAAM,gBAAgB,OAAO,eAAe,MAAM,gBAAgB,OAAO;AAClF;AACA,IAAM,kBAAkB,CAAC,OAAO,WAAW;AACzC,MAAI,UAAU,QAAQ;AACpB,WAAO;AAAA,EACT;AACA,QAAM,mBAAmB,MAAM,UAAU,OAAO,OAAO,UAAU,MAAM,MAAM,UAAU,gBAAgB,OAAO,UAAU,eAAe,MAAM,UAAU,SAAS,OAAO,UAAU,QAAQ,MAAM,UAAU,UAAU,OAAO,UAAU;AACpO,QAAM,mBAAmB,MAAM,UAAU,OAAO,OAAO,UAAU,MAAM,MAAM,UAAU,SAAS,OAAO,UAAU;AACjH,SAAO,oBAAoB;AAC7B;AAEA,IAAM,cAAc,CAAC,KAAK,OAAO;AAC/B,QAAM;AACN,KAAG;AACH,SAAO;AACT;AACA,IAAM,eAAe,CAAC,UAAU,UAAU;AAAA,EACxC,aAAa,SAAS,UAAU;AAAA,EAChC,MAAM,SAAS,UAAU;AAAA,EACzB,QAAQ;AAAA,IACN,aAAa,SAAS,UAAU;AAAA,IAChC,OAAO,SAAS,UAAU;AAAA,EAC5B;AAAA,EACA;AACF;AACA,SAAS,QAAQ,WAAW,MAAM,UAAU,mBAAmB;AAC7D,MAAI,CAAC,WAAW;AACd,aAAS,kBAAkB,IAAI,CAAC;AAChC;AAAA,EACF;AACA,QAAM,aAAa,oBAAoB,QAAQ;AAC/C,QAAM,WAAW;AAAA,IACf,UAAU;AAAA,EACZ;AACA,YAAU,MAAM,QAAQ;AACxB,MAAI,CAAC,WAAW,UAAU,GAAG;AAC3B,aAAS,kBAAkB,IAAI,CAAC;AAAA,EAClC;AACF;AACA,IAAI,eAAe,CAAC,eAAe,aAAa;AAC9C,QAAM,eAAe,gBAAgB;AACrC,MAAI,WAAW;AACf,QAAM,gBAAgB,CAAC,aAAa,SAAS;AAC3C,KAAC,CAAC,WAAW,OAAwC1B,WAAU,OAAO,wEAAwE,IAAIA,WAAU,IAAI;AAChK,gBAAY,mBAAmB,MAAM;AACnC,YAAM,KAAK,cAAc,EAAE;AAC3B,UAAI,IAAI;AACN,cAAM,SAAS;AAAA,UACb;AAAA,UACA;AAAA,QACF;AACA,WAAG,MAAM;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,cAAc,CAAC,UAAU,SAAS;AACtC,KAAC,CAAC,WAAW,OAAwCA,WAAU,OAAO,0EAA0E,IAAIA,WAAU,IAAI;AAClK,gBAAY,qBAAqB,MAAM;AACrC,YAAM,KAAK,cAAc,EAAE;AAC3B,UAAI,IAAI;AACN,WAAG,aAAa,UAAU,IAAI,CAAC;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAME,SAAQ,CAAC,UAAU,SAAS;AAChC,KAAC,CAAC,WAAW,OAAwCF,WAAU,OAAO,0EAA0E,IAAIA,WAAU,IAAI;AAClK,UAAM,OAAO,aAAa,UAAU,IAAI;AACxC,eAAW;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,cAAc,KAAK;AAAA,MACnB,aAAa;AAAA,IACf;AACA,iBAAa,IAAI,MAAM;AACrB,kBAAY,eAAe,MAAM,QAAQ,cAAc,EAAE,aAAa,MAAM,UAAU,OAAO,WAAW,CAAC;AAAA,IAC3G,CAAC;AAAA,EACH;AACA,QAAMI,UAAS,CAAC,UAAU,WAAW;AACnC,UAAM,WAAW,kBAAkB,MAAM;AACzC,UAAMD,WAAU,cAAc,MAAM;AACpC,KAAC,WAAW,OAAwCH,WAAU,OAAO,6DAA6D,IAAIA,WAAU,IAAI;AACpJ,UAAM,qBAAqB,CAAC,gBAAgB,UAAU,SAAS,YAAY;AAC3E,QAAI,oBAAoB;AACtB,eAAS,eAAe;AAAA,IAC1B;AACA,UAAM,qBAAqB,CAAC,kBAAkB,SAAS,cAAc,QAAQ;AAC7E,QAAI,oBAAoB;AACtB,eAAS,eAAe;AAAA,IAC1B;AACA,UAAM,qBAAqB,CAAC,eAAe,SAAS,aAAaG,QAAO;AACxE,QAAI,oBAAoB;AACtB,eAAS,cAAcA;AAAA,IACzB;AACA,QAAI,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,oBAAoB;AACrE;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX,GAAG,aAAa,UAAU,SAAS,IAAI;AAAA,MACvC,SAAAA;AAAA,MACA,aAAa;AAAA,IACf;AACA,iBAAa,IAAI,MAAM;AACrB,kBAAY,gBAAgB,MAAM,QAAQ,cAAc,EAAE,cAAc,MAAM,UAAU,OAAO,YAAY,CAAC;AAAA,IAC9G,CAAC;AAAA,EACH;AACA,QAAMuB,SAAQ,MAAM;AAClB,KAAC,WAAW,OAAwC1B,WAAU,OAAO,0CAA0C,IAAIA,WAAU,IAAI;AACjI,iBAAa,MAAM;AAAA,EACrB;AACA,QAAM2B,QAAO,YAAU;AACrB,KAAC,WAAW,OAAwC3B,WAAU,OAAO,6DAA6D,IAAIA,WAAU,IAAI;AACpJ,eAAW;AACX,gBAAY,aAAa,MAAM,QAAQ,cAAc,EAAE,WAAW,QAAQ,UAAU,OAAO,SAAS,CAAC;AAAA,EACvG;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,UAAM,SAAS;AAAA,MACb,GAAG,aAAa,SAAS,cAAc,SAAS,IAAI;AAAA,MACpD,SAAS;AAAA,MACT,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AACA,IAAA2B,MAAK,MAAM;AAAA,EACb;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAAzB;AAAA,IACA,QAAAE;AAAA,IACA,OAAAsB;AAAA,IACA,MAAAC;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,aAAa,CAAC,eAAe,aAAa;AAC5C,QAAM,YAAY,aAAa,eAAe,QAAQ;AACtD,SAAO,WAAS,UAAQ,YAAU;AAChC,QAAI,MAAM,QAAQ,wBAAwB,GAAG;AAC3C,gBAAU,cAAc,OAAO,QAAQ,aAAa,OAAO,QAAQ,YAAY;AAC/E;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,iBAAiB,GAAG;AACpC,YAAM,WAAW,OAAO,QAAQ;AAChC,gBAAU,YAAY,UAAU,OAAO,QAAQ,YAAY;AAC3D,WAAK,MAAM;AACX,gBAAU,MAAM,UAAU,OAAO,QAAQ,YAAY;AACrD;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,eAAe,GAAG;AAClC,YAAM,SAAS,OAAO,QAAQ,UAAU;AACxC,gBAAU,MAAM;AAChB,WAAK,MAAM;AACX,gBAAU,KAAK,MAAM;AACrB;AAAA,IACF;AACA,SAAK,MAAM;AACX,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,gBAAU,MAAM;AAChB;AAAA,IACF;AACA,UAAM,QAAQ,MAAM,SAAS;AAC7B,QAAI,MAAM,UAAU,YAAY;AAC9B,gBAAU,OAAO,MAAM,UAAU,MAAM,MAAM;AAAA,IAC/C;AAAA,EACF;AACF;AAEA,IAAM,gCAAgC,WAAS,UAAQ,YAAU;AAC/D,MAAI,CAAC,MAAM,QAAQ,yBAAyB,GAAG;AAC7C,SAAK,MAAM;AACX;AAAA,EACF;AACA,QAAM,QAAQ,MAAM,SAAS;AAC7B,IAAE,MAAM,UAAU,oBAAoB,OAAwC3B,WAAU,OAAO,0DAA0D,IAAIA,WAAU,IAAI;AAC3K,QAAM,SAAS,aAAa;AAAA,IAC1B,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC;AACJ;AAEA,IAAM,uCAAuC,WAAS;AACpD,MAAI,SAAS;AACb,MAAI,UAAU;AACd,WAAS,QAAQ;AACf,QAAI,SAAS;AACX,2BAAqB,OAAO;AAC5B,gBAAU;AAAA,IACZ;AACA,QAAI,QAAQ;AACV,aAAO;AACP,eAAS;AAAA,IACX;AAAA,EACF;AACA,SAAO,UAAQ,YAAU;AACvB,QAAI,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,eAAe,KAAK,MAAM,QAAQ,yBAAyB,GAAG;AACxG,YAAM;AAAA,IACR;AACA,SAAK,MAAM;AACX,QAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAClC;AAAA,IACF;AACA,UAAM,UAAU;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,MACA,IAAI,SAAS,qBAAqB;AAChC,cAAM,QAAQ,MAAM,SAAS;AAC7B,YAAI,MAAM,UAAU,kBAAkB;AACpC,gBAAM,SAAS,sBAAsB,CAAC;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AACA,cAAU,sBAAsB,MAAM;AACpC,gBAAU;AACV,eAAS,WAAW,QAAQ,CAAC,OAAO,CAAC;AAAA,IACvC,CAAC;AAAA,EACH;AACF;AAEA,IAAI,0BAA0B,aAAW,MAAM,UAAQ,YAAU;AAC/D,MAAI,MAAM,QAAQ,eAAe,KAAK,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,cAAc,GAAG;AAC7F,YAAQ,eAAe;AAAA,EACzB;AACA,OAAK,MAAM;AACb;AAEA,IAAI,QAAQ,aAAW;AACrB,MAAI,aAAa;AACjB,SAAO,MAAM,UAAQ,YAAU;AAC7B,QAAI,MAAM,QAAQ,iBAAiB,GAAG;AACpC,mBAAa;AACb,cAAQ,eAAe,OAAO,QAAQ,SAAS,UAAU,EAAE;AAC3D,WAAK,MAAM;AACX,cAAQ,wBAAwB;AAChC;AAAA,IACF;AACA,SAAK,MAAM;AACX,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,mBAAa;AACb,cAAQ,wBAAwB;AAChC;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,eAAe,GAAG;AAClC,mBAAa;AACb,YAAM,SAAS,OAAO,QAAQ,UAAU;AACxC,UAAI,OAAO,SAAS;AAClB,gBAAQ,eAAe,OAAO,aAAa,OAAO,QAAQ,WAAW;AAAA,MACvE;AACA,cAAQ,wBAAwB;AAAA,IAClC;AAAA,EACF;AACF;AAEA,IAAM,aAAa,YAAU,MAAM,QAAQ,eAAe,KAAK,MAAM,QAAQ,cAAc,KAAK,MAAM,QAAQ,OAAO;AACrH,IAAI,aAAa,kBAAgB,WAAS,UAAQ,YAAU;AAC1D,MAAI,WAAW,MAAM,GAAG;AACtB,iBAAa,KAAK;AAClB,SAAK,MAAM;AACX;AAAA,EACF;AACA,MAAI,MAAM,QAAQ,iBAAiB,GAAG;AACpC,SAAK,MAAM;AACX,UAAM,QAAQ,MAAM,SAAS;AAC7B,MAAE,MAAM,UAAU,cAAc,OAAwCA,WAAU,OAAO,qDAAqD,IAAIA,WAAU,IAAI;AAChK,iBAAa,MAAM,KAAK;AACxB;AAAA,EACF;AACA,OAAK,MAAM;AACX,eAAa,OAAO,MAAM,SAAS,CAAC;AACtC;AAEA,IAAM,cAAc,WAAS,UAAQ,YAAU;AAC7C,OAAK,MAAM;AACX,MAAI,CAAC,MAAM,QAAQ,wBAAwB,GAAG;AAC5C;AAAA,EACF;AACA,QAAM,kBAAkB,MAAM,SAAS;AACvC,MAAI,gBAAgB,UAAU,gBAAgB;AAC5C;AAAA,EACF;AACA,MAAI,gBAAgB,WAAW;AAC7B;AAAA,EACF;AACA,QAAM,SAAS,KAAK;AAAA,IAClB,QAAQ,gBAAgB;AAAA,EAC1B,CAAC,CAAC;AACJ;AAEA,IAAM,mBAA4D,OAAO,WAAW,eAAe,OAAO,uCAAuC,OAAO,qCAAqC;AAAA,EAC3L,MAAM;AACR,CAAC,IAAI;AACL,IAAI4B,eAAc,CAAC;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM,YAAc,SAAS,iBAAiB,gBAAgB,MAAM,YAAY,GAAG,wBAAwB,gBAAgB,GAAG,KAAK,gBAAgB,GAAG,gBAAgB,+BAA+B,sCAAsC,aAAa,WAAW,YAAY,GAAG,gBAAgB,MAAM,YAAY,GAAG,WAAW,eAAe,QAAQ,CAAC,CAAC,CAAC;AAE5V,IAAM,UAAU,OAAO;AAAA,EACrB,WAAW,CAAC;AAAA,EACZ,UAAU,CAAC;AAAA,EACX,UAAU,CAAC;AACb;AACA,SAAS,gBAAgB;AAAA,EACvB;AAAA,EACA;AACF,GAAG;AACD,MAAI,UAAU,QAAQ;AACtB,MAAI,UAAU;AACd,QAAM,UAAU,MAAM;AACpB,QAAI,SAAS;AACX;AAAA,IACF;AACA,cAAU,mBAAmB;AAC7B,cAAU,sBAAsB,MAAM;AACpC,gBAAU;AACV,YAAM;AACN,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,QAAQ,OAAO,KAAK,SAAS,EAAE,IAAI,QAAM,SAAS,UAAU,QAAQ,EAAE,EAAE,aAAa,MAAM,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,QAAQ,EAAE,WAAW,KAAK;AAC1J,YAAM,UAAU,OAAO,KAAK,QAAQ,EAAE,IAAI,QAAM;AAC9C,cAAM,QAAQ,SAAS,UAAU,QAAQ,EAAE;AAC3C,cAAMC,UAAS,MAAM,UAAU,uBAAuB;AACtD,eAAO;AAAA,UACL,aAAa;AAAA,UACb,QAAAA;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU,OAAO,KAAK,QAAQ;AAAA,QAC9B,UAAU;AAAA,MACZ;AACA,gBAAU,QAAQ;AAClB,aAAO;AACP,gBAAU,QAAQ,MAAM;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,QAAMJ,OAAM,WAAS;AACnB,UAAM,KAAK,MAAM,WAAW;AAC5B,YAAQ,UAAU,EAAE,IAAI;AACxB,YAAQ,SAAS,MAAM,WAAW,WAAW,IAAI;AACjD,QAAI,QAAQ,SAAS,EAAE,GAAG;AACxB,aAAO,QAAQ,SAAS,EAAE;AAAA,IAC5B;AACA,YAAQ;AAAA,EACV;AACA,QAAM,SAAS,WAAS;AACtB,UAAM,aAAa,MAAM;AACzB,YAAQ,SAAS,WAAW,EAAE,IAAI;AAClC,YAAQ,SAAS,WAAW,WAAW,IAAI;AAC3C,QAAI,QAAQ,UAAU,WAAW,EAAE,GAAG;AACpC,aAAO,QAAQ,UAAU,WAAW,EAAE;AAAA,IACxC;AACA,YAAQ;AAAA,EACV;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,yBAAqB,OAAO;AAC5B,cAAU;AACV,cAAU,QAAQ;AAAA,EACpB;AACA,SAAO;AAAA,IACL,KAAAA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,eAAe,CAAC;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,YAAY,SAAS;AAAA,IACzB,GAAG;AAAA,IACH,GAAG;AAAA,EACL,GAAG;AAAA,IACD,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AACD,QAAM,oBAAoB;AAAA,IACxB,GAAG,KAAK,IAAI,GAAG,UAAU,CAAC;AAAA,IAC1B,GAAG,KAAK,IAAI,GAAG,UAAU,CAAC;AAAA,EAC5B;AACA,SAAO;AACT;AAEA,IAAI,qBAAqB,MAAM;AAC7B,QAAM,MAAM,SAAS;AACrB,GAAC,MAAM,OAAwCzB,WAAU,OAAO,sCAAsC,IAAIA,WAAU,IAAI;AACxH,SAAO;AACT;AAEA,IAAI,qBAAqB,MAAM;AAC7B,QAAM,MAAM,mBAAmB;AAC/B,QAAM,YAAY,aAAa;AAAA,IAC7B,cAAc,IAAI;AAAA,IAClB,aAAa,IAAI;AAAA,IACjB,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,EACd,CAAC;AACD,SAAO;AACT;AAEA,IAAI,cAAc,MAAM;AACtB,QAAM6B,UAASP,iBAAgB;AAC/B,QAAM,YAAY,mBAAmB;AACrC,QAAM,MAAMO,QAAO;AACnB,QAAM,OAAOA,QAAO;AACpB,QAAM,MAAM,mBAAmB;AAC/B,QAAM,QAAQ,IAAI;AAClB,QAAM,SAAS,IAAI;AACnB,QAAM,QAAQ,OAAO;AACrB,QAAM,SAAS,MAAM;AACrB,QAAM,QAAQ,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,WAAW;AAAA,IACf;AAAA,IACA,QAAQ;AAAA,MACN,SAASA;AAAA,MACT,SAASA;AAAA,MACT,KAAK;AAAA,MACL,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,oBAAoB,CAAC;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM;AACN,QAAM,WAAW,YAAY;AAC7B,QAAM,eAAe,SAAS,OAAO;AACrC,QAAMV,QAAO,SAAS;AACtB,QAAM,aAAa,SAAS,UAAU,aAAaA,MAAK,IAAI,EAAE,IAAI,WAAS,MAAM,UAAU,2BAA2B,cAAc,aAAa,CAAC;AAClJ,QAAM,aAAa,SAAS,UAAU,aAAa,SAAS,UAAU,IAAI,EAAE,IAAI,WAAS,MAAM,aAAa,YAAY,CAAC;AACzH,QAAM,aAAa;AAAA,IACjB,YAAY,eAAe,UAAU;AAAA,IACrC,YAAY,eAAe,UAAU;AAAA,EACvC;AACA,SAAO;AACP,QAAM,SAAS;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,oBAAoB,UAAU,UAAU,OAAO;AACtD,MAAI,MAAM,WAAW,OAAO,SAAS,IAAI;AACvC,WAAO;AAAA,EACT;AACA,MAAI,MAAM,WAAW,SAAS,SAAS,MAAM;AAC3C,WAAO;AAAA,EACT;AACA,QAAMA,QAAO,SAAS,UAAU,QAAQ,MAAM,WAAW,WAAW;AACpE,MAAIA,MAAK,WAAW,SAAS,WAAW;AACtC,WAAwC,QAAQ;AAAA,6DACS,MAAM,WAAW,EAAE;AAAA;AAAA;AAAA;AAAA,KAI3E,IAAI;AACL,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAI,yBAAyB,CAAC,UAAU,cAAc;AACpD,MAAI,aAAa;AACjB,QAAM,YAAY,gBAAgB;AAAA,IAChC,WAAW;AAAA,MACT,SAAS,UAAU;AAAA,MACnB,oBAAoB,UAAU;AAAA,IAChC;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAMW,4BAA2B,CAAC,IAAI,cAAc;AAClD,KAAC,SAAS,UAAU,OAAO,EAAE,IAAI,OAAwC9B,WAAU,OAAO,8CAA8C,EAAE,0BAA0B,IAAIA,WAAU,IAAI;AACtL,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,cAAU,yBAAyB;AAAA,MACjC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM+B,mCAAkC,CAAC,IAAI,qBAAqB;AAChE,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,KAAC,SAAS,UAAU,OAAO,EAAE,IAAI,OAAwC/B,WAAU,OAAO,oDAAoD,EAAE,0BAA0B,IAAIA,WAAU,IAAI;AAC5L,cAAU,gCAAgC;AAAA,MACxC;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAMgC,yBAAwB,CAAC,IAAI,cAAc;AAC/C,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,KAAC,SAAS,UAAU,OAAO,EAAE,IAAI,OAAwChC,WAAU,OAAO,yCAAyC,EAAE,0BAA0B,IAAIA,WAAU,IAAI;AACjL,cAAU,sBAAsB;AAAA,MAC9B;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAMiC,mBAAkB,CAAC,IAAI,WAAW;AACtC,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,aAAS,UAAU,QAAQ,EAAE,EAAE,UAAU,OAAO,MAAM;AAAA,EACxD;AACA,QAAM,iBAAiB,MAAM;AAC3B,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,cAAU,KAAK;AACf,UAAMd,QAAO,WAAW,SAAS;AACjC,aAAS,UAAU,aAAaA,MAAK,IAAI,EAAE,QAAQ,WAAS,MAAM,UAAU,YAAY,CAAC;AACzF,eAAW,YAAY;AACvB,iBAAa;AAAA,EACf;AACA,QAAM,aAAa,WAAS;AAC1B,KAAC,aAAa,OAAwCnB,WAAU,OAAO,0DAA0D,IAAIA,WAAU,IAAI;AACnJ,UAAM,WAAW,WAAW,SAAS;AACrC,QAAI,MAAM,SAAS,YAAY;AAC7B,UAAI,oBAAoB,UAAU,UAAU,MAAM,KAAK,GAAG;AACxD,kBAAU,IAAI,MAAM,KAAK;AAAA,MAC3B;AAAA,IACF;AACA,QAAI,MAAM,SAAS,WAAW;AAC5B,UAAI,oBAAoB,UAAU,UAAU,MAAM,KAAK,GAAG;AACxD,kBAAU,OAAO,MAAM,KAAK;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AACA,QAAM,kBAAkB,aAAW;AACjC,KAAC,CAAC,aAAa,OAAwCA,WAAU,OAAO,6EAA6E,IAAIA,WAAU,IAAI;AACvK,UAAM,QAAQ,SAAS,UAAU,QAAQ,QAAQ,WAAW;AAC5D,UAAMmB,QAAO,SAAS,UAAU,QAAQ,MAAM,WAAW,WAAW;AACpE,UAAM,WAAW;AAAA,MACf,WAAW,MAAM;AAAA,MACjB,WAAWA,MAAK;AAAA,IAClB;AACA,UAAM,cAAc,SAAS,UAAU,UAAU;AACjD,iBAAa;AAAA,MACX;AAAA,MACA;AAAA,IACF;AACA,WAAO,kBAAkB;AAAA,MACvB;AAAA,MACA;AAAA,MACA,eAAe,QAAQ;AAAA,IACzB,CAAC;AAAA,EACH;AACA,QAAM,UAAU;AAAA,IACd,0BAAAW;AAAA,IACA,iCAAAC;AAAA,IACA,iBAAAE;AAAA,IACA,uBAAAD;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,eAAe,CAAC,OAAO,OAAO;AAChC,MAAI,MAAM,UAAU,QAAQ;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,MAAM,UAAU,kBAAkB;AACpC,WAAO;AAAA,EACT;AACA,MAAI,MAAM,UAAU,OAAO,gBAAgB,IAAI;AAC7C,WAAO;AAAA,EACT;AACA,SAAO,MAAM,UAAU,OAAO,WAAW;AAC3C;AAEA,IAAI,eAAe,YAAU;AAC3B,SAAO,SAAS,OAAO,GAAG,OAAO,CAAC;AACpC;AAEA,IAAM,0BAA0B,WAAW,gBAAc,gBAAgB,UAAU,EAAE,OAAO,CAAA1B,eAAa;AACvG,MAAI,CAACA,WAAU,WAAW;AACxB,WAAO;AAAA,EACT;AACA,MAAI,CAACA,WAAU,OAAO;AACpB,WAAO;AAAA,EACT;AACA,SAAO;AACT,CAAC,CAAC;AACF,IAAM,6BAA6B,CAAC,QAAQ,eAAe;AACzD,QAAM,QAAQ,wBAAwB,UAAU,EAAE,KAAK,CAAAA,eAAa;AAClE,KAACA,WAAU,QAAQ,OAAwCN,WAAU,OAAO,gBAAgB,IAAIA,WAAU,IAAI;AAC9G,WAAO,kBAAkBM,WAAU,MAAM,aAAa,EAAE,MAAM;AAAA,EAChE,CAAC,KAAK;AACN,SAAO;AACT;AACA,IAAI,6BAA6B,CAAC;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,aAAa;AACf,UAAM4B,aAAY,WAAW,WAAW;AACxC,QAAI,CAACA,WAAU,OAAO;AACpB,aAAO;AAAA,IACT;AACA,WAAOA;AAAA,EACT;AACA,QAAM,YAAY,2BAA2B,QAAQ,UAAU;AAC/D,SAAO;AACT;AAEA,IAAM,6BAA6B;AAAA,EACjC,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,EAChB,MAAM,gBAAc,cAAc;AAAA,EAClC,mBAAmB;AAAA,IACjB,iBAAiB;AAAA,IACjB,cAAc;AAAA,EAChB;AAAA,EACA,UAAU;AACZ;AAEA,IAAI,wBAAwB,CAAC,WAAW,MAAM,yBAAyB,MAAM,+BAA+B;AAC1G,QAAM,sBAAsB,uBAAuB;AACnD,QAAM,qBAAqB,UAAU,KAAK,IAAI,IAAI,oBAAoB;AACtE,QAAM,mBAAmB,UAAU,KAAK,IAAI,IAAI,oBAAoB;AACpE,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,gBAAgB,CAAC;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,QAAQ,aAAa;AAC3B,MAAI,UAAU,GAAG;AACf,WAAwC,QAAQ;AAAA;AAAA;AAAA;AAAA,KAI/C,IAAI;AACL,WAAO;AAAA,EACT;AACA,QAAM,iBAAiB,UAAU;AACjC,QAAM,aAAa,iBAAiB;AACpC,SAAO;AACT;AAEA,IAAI,YAAY;AAEhB,IAAI,uBAAuB,CAAC,gBAAgB,YAAY,yBAAyB,MAAM,+BAA+B;AACpH,QAAM,sBAAsB,uBAAuB;AACnD,MAAI,iBAAiB,WAAW,oBAAoB;AAClD,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,WAAW,kBAAkB;AACjD,WAAO,oBAAoB;AAAA,EAC7B;AACA,MAAI,mBAAmB,WAAW,oBAAoB;AACpD,WAAO;AAAA,EACT;AACA,QAAM,iCAAiC,cAAc;AAAA,IACnD,cAAc,WAAW;AAAA,IACzB,YAAY,WAAW;AAAA,IACvB,SAAS;AAAA,EACX,CAAC;AACD,QAAM,mCAAmC,IAAI;AAC7C,QAAML,UAAS,oBAAoB,iBAAiB,oBAAoB,KAAK,gCAAgC;AAC7G,SAAO,KAAK,KAAKA,OAAM;AACzB;AAEA,IAAI,oBAAoB,CAAC,gBAAgB,eAAe,2BAA2B;AACjF,QAAM,sBAAsB,uBAAuB;AACnD,QAAM,eAAe,oBAAoB,kBAAkB;AAC3D,QAAM,SAAS,oBAAoB,kBAAkB;AACrD,QAAM,eAAe;AACrB,QAAM,aAAa;AACnB,QAAM,MAAM,KAAK,IAAI;AACrB,QAAM,UAAU,MAAM;AACtB,MAAI,WAAW,QAAQ;AACrB,WAAO;AAAA,EACT;AACA,MAAI,UAAU,cAAc;AAC1B,WAAO;AAAA,EACT;AACA,QAAM,yCAAyC,cAAc;AAAA,IAC3D,cAAc;AAAA,IACd;AAAA,IACA,SAAS;AAAA,EACX,CAAC;AACD,QAAMA,UAAS,iBAAiB,oBAAoB,KAAK,sCAAsC;AAC/F,SAAO,KAAK,KAAKA,OAAM;AACzB;AAEA,IAAI,WAAW,CAAC;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAMA,UAAS,qBAAqB,gBAAgB,YAAY,sBAAsB;AACtF,MAAIA,YAAW,GAAG;AAChB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,wBAAwB;AAC3B,WAAOA;AAAA,EACT;AACA,SAAO,KAAK,IAAI,kBAAkBA,SAAQ,eAAe,sBAAsB,GAAG,SAAS;AAC7F;AAEA,IAAI,kBAAkB,CAAC;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,aAAa,sBAAsB,WAAW,MAAM,sBAAsB;AAChF,QAAM,gBAAgB,gBAAgB,KAAK,GAAG,IAAI,gBAAgB,KAAK,KAAK;AAC5E,MAAI,eAAe;AACjB,WAAO,SAAS;AAAA,MACd,gBAAgB,gBAAgB,KAAK,GAAG;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,KAAK,SAAS;AAAA,IACnB,gBAAgB,gBAAgB,KAAK,KAAK;AAAA,IAC1C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,sBAAsB,CAAC;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,qBAAqB,QAAQ,SAAS,UAAU;AACtD,QAAM,uBAAuB,QAAQ,QAAQ,UAAU;AACvD,MAAI,CAAC,wBAAwB,CAAC,oBAAoB;AAChD,WAAO;AAAA,EACT;AACA,MAAI,wBAAwB,oBAAoB;AAC9C,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,uBAAuB,IAAI,eAAe;AAAA,IAC7C,GAAG,qBAAqB,IAAI,eAAe;AAAA,EAC7C;AACF;AAEA,IAAM,QAAQ,MAAM,WAAS,UAAU,IAAI,IAAI,KAAK;AACpD,IAAI,cAAc,CAAC;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,kBAAkB;AAAA,IACtB,KAAK,OAAO,IAAI,UAAU;AAAA,IAC1B,OAAO,UAAU,QAAQ,OAAO;AAAA,IAChC,QAAQ,UAAU,SAAS,OAAO;AAAA,IAClC,MAAM,OAAO,IAAI,UAAU;AAAA,EAC7B;AACA,QAAM,IAAI,gBAAgB;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,IAAI,gBAAgB;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAMM,YAAW,MAAM;AAAA,IACrB;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,UAAUA,WAAU,MAAM,GAAG;AAC/B,WAAO;AAAA,EACT;AACA,QAAM,UAAU,oBAAoB;AAAA,IAClC;AAAA,IACA;AAAA,IACA,gBAAgBA;AAAA,EAClB,CAAC;AACD,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,SAAO,UAAU,SAAS,MAAM,IAAI,OAAO;AAC7C;AAEA,IAAM,iBAAiB,MAAM,WAAS;AACpC,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,IAAI,IAAI;AACzB,CAAC;AACD,IAAM,aAAc,uBAAM;AACxB,QAAM,eAAe,CAAC,QAAQ,QAAQ;AACpC,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK;AAChB,aAAO,SAAS;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AACA,SAAO,CAAC;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,UAAM,eAAe,IAAI,SAAS,MAAM;AACxC,UAAM,UAAU;AAAA,MACd,GAAG,aAAa,aAAa,GAAG,IAAI,CAAC;AAAA,MACrC,GAAG,aAAa,aAAa,GAAG,IAAI,CAAC;AAAA,IACvC;AACA,QAAI,UAAU,SAAS,MAAM,GAAG;AAC9B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF,GAAG;AACH,IAAM,qBAAqB,CAAC;AAAA,EAC1B,KAAK;AAAA,EACL;AAAA,EACA;AACF,MAAM;AACJ,QAAM,MAAM;AAAA,IACV,GAAG,KAAK,IAAI,QAAQ,GAAG,OAAO,CAAC;AAAA,IAC/B,GAAG,KAAK,IAAI,QAAQ,GAAG,OAAO,CAAC;AAAA,EACjC;AACA,QAAM,iBAAiB,eAAe,MAAM;AAC5C,QAAM,UAAU,WAAW;AAAA,IACzB;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,MAAI,eAAe,MAAM,KAAK,QAAQ,MAAM,GAAG;AAC7C,WAAO;AAAA,EACT;AACA,MAAI,eAAe,MAAM,KAAK,QAAQ,MAAM,GAAG;AAC7C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,kBAAkB,CAAC,UAAU,WAAW,mBAAmB;AAAA,EAC/D,SAAS,SAAS,OAAO;AAAA,EACzB,KAAK,SAAS,OAAO;AAAA,EACrB;AACF,CAAC;AACD,IAAM,mBAAmB,CAAC,UAAU,WAAW;AAC7C,MAAI,CAAC,gBAAgB,UAAU,MAAM,GAAG;AACtC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,SAAS,OAAO;AAC5B,QAAM,UAAU,SAAS,OAAO;AAChC,SAAO,WAAW;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,IAAM,qBAAqB,CAAC7B,YAAW,WAAW;AAChD,QAAM,QAAQA,WAAU;AACxB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,mBAAmB;AAAA,IACxB,SAAS,MAAM,OAAO;AAAA,IACtB,KAAK,MAAM,OAAO;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AACA,IAAM,sBAAsB,CAACA,YAAW,WAAW;AACjD,QAAM,QAAQA,WAAU;AACxB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,MAAI,CAAC,mBAAmBA,YAAW,MAAM,GAAG;AAC1C,WAAO;AAAA,EACT;AACA,SAAO,WAAW;AAAA,IAChB,SAAS,MAAM,OAAO;AAAA,IACtB,KAAK,MAAM,OAAO;AAAA,IAClB;AAAA,EACF,CAAC;AACH;AAEA,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAMuB,UAAS,YAAY;AAAA,IACzB;AAAA,IACA,WAAW,SAAS;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAOA,WAAU,gBAAgB,UAAUA,OAAM,IAAIA,UAAS;AAChE;AAEA,IAAI,2BAA2B,CAAC;AAAA,EAC9B,WAAAvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,QAAQA,WAAU;AACxB,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,QAAMuB,UAAS,YAAY;AAAA,IACzB;AAAA,IACA,WAAW,MAAM;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAOA,WAAU,mBAAmBvB,YAAWuB,OAAM,IAAIA,UAAS;AACpE;AAEA,IAAI,SAAS,CAAC;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAAO;AAAA,EACA,iBAAAH;AAAA,EACA;AACF,MAAM;AACJ,QAAM,SAAS,MAAM,QAAQ,KAAK;AAClC,QAAMzB,aAAY,MAAM,WAAW,WAAW,MAAM,SAAS,UAAU,EAAE;AACzE,QAAM,UAAUA,WAAU,KAAK;AAC/B,MAAI,MAAM,uBAAuB;AAC/B,UAAM,WAAW,MAAM;AACvB,UAAM6B,UAAS,sBAAsB;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAIA,SAAQ;AACV,MAAAD,cAAaC,OAAM;AACnB;AAAA,IACF;AAAA,EACF;AACA,QAAM/B,aAAY,2BAA2B;AAAA,IAC3C;AAAA,IACA,aAAa,kBAAkB,MAAM,MAAM;AAAA,IAC3C,YAAY,MAAM,WAAW;AAAA,EAC/B,CAAC;AACD,MAAI,CAACA,YAAW;AACd;AAAA,EACF;AACA,QAAM,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,QAAQ;AACV,IAAA2B,iBAAgB3B,WAAU,WAAW,IAAI,MAAM;AAAA,EACjD;AACF;AAEA,IAAI,sBAAsB,CAAC;AAAA,EACzB,cAAA8B;AAAA,EACA,iBAAAH;AAAA,EACA,yBAAyB,MAAM;AACjC,MAAM;AACJ,QAAM,uBAAuB,qBAAQG,aAAY;AACjD,QAAM,0BAA0B,qBAAQH,gBAAe;AACvD,MAAI,WAAW;AACf,QAAM,YAAY,WAAS;AACzB,KAAC,WAAW,OAAwCjC,WAAU,OAAO,qCAAqC,IAAIA,WAAU,IAAI;AAC5H,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO;AAAA,MACL;AAAA,MACA,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,UAAU,WAAS;AACvB,UAAM;AACN,KAAC,CAAC,WAAW,OAAwCA,WAAU,OAAO,kDAAkD,IAAIA,WAAU,IAAI;AAC1I,UAAM,gBAAgB,KAAK,IAAI;AAC/B,QAAI,kBAAkB;AACtB,UAAM,qBAAqB,MAAM;AAC/B,wBAAkB;AAAA,IACpB;AACA,WAAO;AAAA,MACL;AAAA,MACA,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB;AAAA,IACF,CAAC;AACD,eAAW;AAAA,MACT;AAAA,MACA,wBAAwB;AAAA,IAC1B;AACA,WAAO;AACP,QAAI,iBAAiB;AACnB,gBAAU,KAAK;AAAA,IACjB;AAAA,EACF;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,yBAAqB,OAAO;AAC5B,4BAAwB,OAAO;AAC/B,eAAW;AAAA,EACb;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP;AAAA,IACA,QAAQ;AAAA,EACV;AACF;AAEA,IAAI,qBAAqB,CAAC;AAAA,EACxB,MAAAsC;AAAA,EACA,iBAAAL;AAAA,EACA,cAAAG;AACF,MAAM;AACJ,QAAM,eAAe,CAAC,OAAOrB,YAAW;AACtC,UAAM,SAAS,IAAI,MAAM,QAAQ,OAAO,WAAWA,OAAM;AACzD,IAAAuB,MAAK;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,+BAA+B,CAAChC,YAAW,WAAW;AAC1D,QAAI,CAAC,mBAAmBA,YAAW,MAAM,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,UAAM,UAAU,oBAAoBA,YAAW,MAAM;AACrD,QAAI,CAAC,SAAS;AACZ,MAAA2B,iBAAgB3B,WAAU,WAAW,IAAI,MAAM;AAC/C,aAAO;AAAA,IACT;AACA,UAAM,4BAA4B,SAAS,QAAQ,OAAO;AAC1D,IAAA2B,iBAAgB3B,WAAU,WAAW,IAAI,yBAAyB;AAClE,UAAM,YAAY,SAAS,QAAQ,yBAAyB;AAC5D,WAAO;AAAA,EACT;AACA,QAAM,4BAA4B,CAAC,uBAAuB,UAAU,WAAW;AAC7E,QAAI,CAAC,uBAAuB;AAC1B,aAAO;AAAA,IACT;AACA,QAAI,CAAC,gBAAgB,UAAU,MAAM,GAAG;AACtC,aAAO;AAAA,IACT;AACA,UAAM,UAAU,iBAAiB,UAAU,MAAM;AACjD,QAAI,CAAC,SAAS;AACZ,MAAA8B,cAAa,MAAM;AACnB,aAAO;AAAA,IACT;AACA,UAAM,yBAAyB,SAAS,QAAQ,OAAO;AACvD,IAAAA,cAAa,sBAAsB;AACnC,UAAM,YAAY,SAAS,QAAQ,sBAAsB;AACzD,WAAO;AAAA,EACT;AACA,QAAM,eAAe,WAAS;AAC5B,UAAM,UAAU,MAAM;AACtB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,cAAc,kBAAkB,MAAM,MAAM;AAClD,KAAC,cAAc,OAAwCpC,WAAU,OAAO,2DAA2D,IAAIA,WAAU,IAAI;AACrJ,UAAM,qBAAqB,6BAA6B,MAAM,WAAW,WAAW,WAAW,GAAG,OAAO;AACzG,QAAI,CAAC,oBAAoB;AACvB;AAAA,IACF;AACA,UAAM,WAAW,MAAM;AACvB,UAAM,kBAAkB,0BAA0B,MAAM,uBAAuB,UAAU,kBAAkB;AAC3G,QAAI,CAAC,iBAAiB;AACpB;AAAA,IACF;AACA,iBAAa,OAAO,eAAe;AAAA,EACrC;AACA,SAAO;AACT;AAEA,IAAI,qBAAqB,CAAC;AAAA,EACxB,iBAAAiC;AAAA,EACA,cAAAG;AAAA,EACA,MAAAE;AAAA,EACA;AACF,MAAM;AACJ,QAAM,gBAAgB,oBAAoB;AAAA,IACxC,cAAAF;AAAA,IACA,iBAAAH;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,aAAa,mBAAmB;AAAA,IACpC,MAAAK;AAAA,IACA,cAAAF;AAAA,IACA,iBAAAH;AAAA,EACF,CAAC;AACD,QAAMJ,UAAS,WAAS;AACtB,UAAM,sBAAsB,uBAAuB;AACnD,QAAI,oBAAoB,YAAY,MAAM,UAAU,YAAY;AAC9D;AAAA,IACF;AACA,QAAI,MAAM,iBAAiB,SAAS;AAClC,oBAAc,OAAO,KAAK;AAC1B;AAAA,IACF;AACA,QAAI,CAAC,MAAM,mBAAmB;AAC5B;AAAA,IACF;AACA,eAAW,KAAK;AAAA,EAClB;AACA,QAAM,WAAW;AAAA,IACf,QAAAA;AAAA,IACA,OAAO,cAAc;AAAA,IACrB,MAAM,cAAc;AAAA,EACtB;AACA,SAAO;AACT;AAEA,IAAMU,UAAS;AACf,IAAM,cAAc,MAAM;AACxB,QAAM,OAAO,GAAGA,OAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA,aAAa,GAAG,IAAI;AAAA,IACpB,WAAW,GAAG,IAAI;AAAA,EACpB;AACF,GAAG;AACH,IAAM,aAAa,MAAM;AACvB,QAAM,OAAO,GAAGA,OAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA,WAAW,GAAG,IAAI;AAAA,IAClB,IAAI,GAAG,IAAI;AAAA,EACb;AACF,GAAG;AACH,IAAM,aAAa,MAAM;AACvB,QAAM,OAAO,GAAGA,OAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA,WAAW,GAAG,IAAI;AAAA,IAClB,IAAI,GAAG,IAAI;AAAA,EACb;AACF,GAAG;AACH,IAAM,kBAAkB;AAAA,EACtB,WAAW,GAAGA,OAAM;AACtB;AAEA,IAAM,kBAAkB,aAAW,eAAa,IAAI,SAAS,KAAK,OAAO;AACzE,IAAM,YAAY,CAAC,OAAO,aAAa,MAAM,IAAI,UAAQ;AACvD,QAAM,QAAQ,KAAK,OAAO,QAAQ;AAClC,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,GAAG,KAAK,QAAQ,MAAM,KAAK;AACpC,CAAC,EAAE,KAAK,GAAG;AACX,IAAM,kBAAkB;AACxB,IAAI,cAAc,eAAa;AAC7B,QAAMC,eAAc,gBAAgB,SAAS;AAC7C,QAAM,gBAAgB,MAAM;AAC1B,UAAM,aAAa;AAAA;AAAA;AAAA;AAInB,WAAO;AAAA,MACL,UAAUA,aAAY,WAAW,SAAS;AAAA,MAC1C,QAAQ;AAAA,QACN,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,QAKR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF,GAAG;AACH,QAAM,eAAe,MAAM;AACzB,UAAM,aAAa;AAAA,oBACH,YAAY,WAAW;AAAA;AAEvC,WAAO;AAAA,MACL,UAAUA,aAAY,UAAU,SAAS;AAAA,MACzC,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,eAAe;AAAA,QACf,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,GAAG;AACH,QAAM,cAAc;AAAA,IAClB,UAAUA,aAAY,UAAU,SAAS;AAAA,IACzC,QAAQ;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AACA,QAAM,OAAO;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,MACN,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASZ;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,aAAa,cAAc,aAAa,IAAI;AAC3D,SAAO;AAAA,IACL,QAAQ,UAAU,OAAO,QAAQ;AAAA,IACjC,SAAS,UAAU,OAAO,SAAS;AAAA,IACnC,UAAU,UAAU,OAAO,UAAU;AAAA,IACrC,eAAe,UAAU,OAAO,eAAe;AAAA,IAC/C,YAAY,UAAU,OAAO,YAAY;AAAA,EAC3C;AACF;AAEA,IAAM,4BAA4B,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa,eAAe,OAAO,OAAO,SAAS,kBAAkB,cAAc,+BAAkB;AAEtL,IAAM,UAAU,MAAM;AACpB,QAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,GAAC,OAAO,OAAwCxC,WAAU,OAAO,2CAA2C,IAAIA,WAAU,IAAI;AAC9H,SAAO;AACT;AACA,IAAM,gBAAgB,WAAS;AAC7B,QAAM,KAAK,SAAS,cAAc,OAAO;AACzC,MAAI,OAAO;AACT,OAAG,aAAa,SAAS,KAAK;AAAA,EAChC;AACA,KAAG,OAAO;AACV,SAAO;AACT;AACA,SAAS,gBAAgB,WAAW,OAAO;AACzC,QAAM,SAAS,QAAQ,MAAM,YAAY,SAAS,GAAG,CAAC,SAAS,CAAC;AAChE,QAAM,gBAAY,qBAAO,IAAI;AAC7B,QAAM,iBAAa,qBAAO,IAAI;AAC9B,QAAM,kBAAkB,YAAY,WAAW,cAAY;AACzD,UAAM,KAAK,WAAW;AACtB,KAAC,KAAK,OAAwCA,WAAU,OAAO,mDAAmD,IAAIA,WAAU,IAAI;AACpI,OAAG,cAAc;AAAA,EACnB,CAAC,GAAG,CAAC,CAAC;AACN,QAAM,iBAAiB,YAAY,cAAY;AAC7C,UAAM,KAAK,UAAU;AACrB,KAAC,KAAK,OAAwCA,WAAU,OAAO,mDAAmD,IAAIA,WAAU,IAAI;AACpI,OAAG,cAAc;AAAA,EACnB,GAAG,CAAC,CAAC;AACL,4BAA0B,MAAM;AAC9B,MAAE,CAAC,UAAU,WAAW,CAAC,WAAW,WAAW,OAAwCA,WAAU,OAAO,gCAAgC,IAAIA,WAAU,IAAI;AAC1J,UAAM,SAAS,cAAc,KAAK;AAClC,UAAM,UAAU,cAAc,KAAK;AACnC,cAAU,UAAU;AACpB,eAAW,UAAU;AACrB,WAAO,aAAa,GAAGuC,OAAM,WAAW,SAAS;AACjD,YAAQ,aAAa,GAAGA,OAAM,YAAY,SAAS;AACnD,YAAQ,EAAE,YAAY,MAAM;AAC5B,YAAQ,EAAE,YAAY,OAAO;AAC7B,mBAAe,OAAO,MAAM;AAC5B,oBAAgB,OAAO,OAAO;AAC9B,WAAO,MAAM;AACX,YAAM,SAAS,CAAAE,SAAO;AACpB,cAAM,UAAUA,KAAI;AACpB,SAAC,UAAU,OAAwCzC,WAAU,OAAO,qCAAqC,IAAIA,WAAU,IAAI;AAC3H,gBAAQ,EAAE,YAAY,OAAO;AAC7B,QAAAyC,KAAI,UAAU;AAAA,MAChB;AACA,aAAO,SAAS;AAChB,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,OAAO,gBAAgB,iBAAiB,OAAO,QAAQ,OAAO,SAAS,SAAS,CAAC;AACrF,QAAM,WAAW,YAAY,MAAM,gBAAgB,OAAO,QAAQ,GAAG,CAAC,iBAAiB,OAAO,QAAQ,CAAC;AACvG,QAAM,WAAW,YAAY,YAAU;AACrC,QAAI,WAAW,QAAQ;AACrB,sBAAgB,OAAO,aAAa;AACpC;AAAA,IACF;AACA,oBAAgB,OAAO,UAAU;AAAA,EACnC,GAAG,CAAC,iBAAiB,OAAO,eAAe,OAAO,UAAU,CAAC;AAC7D,QAAM,UAAU,YAAY,MAAM;AAChC,QAAI,CAAC,WAAW,SAAS;AACvB;AAAA,IACF;AACA,oBAAgB,OAAO,OAAO;AAAA,EAChC,GAAG,CAAC,iBAAiB,OAAO,OAAO,CAAC;AACpC,QAAM,UAAU,QAAQ,OAAO;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,UAAU,UAAU,OAAO,CAAC;AACjC,SAAO;AACT;AAEA,SAAS,iBAAiB,YAAY,UAAU;AAC9C,SAAO,MAAM,KAAK,WAAW,iBAAiB,QAAQ,CAAC;AACzD;AAEA,IAAI,kBAAkB,QAAM;AAC1B,MAAI,MAAM,GAAG,iBAAiB,GAAG,cAAc,aAAa;AAC1D,WAAO,GAAG,cAAc;AAAA,EAC1B;AACA,SAAO;AACT;AAEA,SAAS,cAAc,IAAI;AACzB,SAAO,cAAc,gBAAgB,EAAE,EAAE;AAC3C;AAEA,SAAS,eAAe,WAAW,aAAa;AAC9C,QAAM,WAAW,IAAI,WAAW,SAAS,KAAK,SAAS;AACvD,QAAM,WAAW,iBAAiB,UAAU,QAAQ;AACpD,MAAI,CAAC,SAAS,QAAQ;AACpB,WAAwC,QAAQ,mDAAmD,SAAS,GAAG,IAAI;AACnH,WAAO;AAAA,EACT;AACA,QAAM,SAAS,SAAS,KAAK,QAAM;AACjC,WAAO,GAAG,aAAa,WAAW,WAAW,MAAM;AAAA,EACrD,CAAC;AACD,MAAI,CAAC,QAAQ;AACX,WAAwC,QAAQ,uCAAuC,WAAW,6CAA6C,IAAI;AACnJ,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,WAAwC,QAAQ,uCAAuC,IAAI;AAC3F,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,WAAW;AAClC,QAAM,iBAAa,qBAAO,CAAC,CAAC;AAC5B,QAAM,gBAAY,qBAAO,IAAI;AAC7B,QAAM,2BAAuB,qBAAO,IAAI;AACxC,QAAM,mBAAe,qBAAO,KAAK;AACjC,QAAM,WAAW,YAAY,SAASC,UAAS,IAAIC,QAAO;AACxD,UAAM,QAAQ;AAAA,MACZ;AAAA,MACA,OAAAA;AAAA,IACF;AACA,eAAW,QAAQ,EAAE,IAAI;AACzB,WAAO,SAAS,aAAa;AAC3B,YAAM,UAAU,WAAW;AAC3B,YAAM,UAAU,QAAQ,EAAE;AAC1B,UAAI,YAAY,OAAO;AACrB,eAAO,QAAQ,EAAE;AAAA,MACnB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,eAAe,YAAY,SAASC,cAAa,gBAAgB;AACrE,UAAM,SAAS,eAAe,WAAW,cAAc;AACvD,QAAI,UAAU,WAAW,SAAS,eAAe;AAC/C,aAAO,MAAM;AAAA,IACf;AAAA,EACF,GAAG,CAAC,SAAS,CAAC;AACd,QAAM,iBAAiB,YAAY,SAASC,gBAAe,UAAU,YAAY;AAC/E,QAAI,UAAU,YAAY,UAAU;AAClC,gBAAU,UAAU;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,0BAA0B,YAAY,SAASC,2BAA0B;AAC7E,QAAI,qBAAqB,SAAS;AAChC;AAAA,IACF;AACA,QAAI,CAAC,aAAa,SAAS;AACzB;AAAA,IACF;AACA,yBAAqB,UAAU,sBAAsB,MAAM;AACzD,2BAAqB,UAAU;AAC/B,YAAM,SAAS,UAAU;AACzB,UAAI,QAAQ;AACV,qBAAa,MAAM;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,iBAAiB,YAAY,SAASC,gBAAe,IAAI;AAC7D,cAAU,UAAU;AACpB,UAAM,UAAU,SAAS;AACzB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,QAAI,QAAQ,aAAa,WAAW,WAAW,MAAM,IAAI;AACvD;AAAA,IACF;AACA,cAAU,UAAU;AAAA,EACtB,GAAG,CAAC,CAAC;AACL,4BAA0B,MAAM;AAC9B,iBAAa,UAAU;AACvB,WAAO,SAAS,sBAAsB;AACpC,mBAAa,UAAU;AACvB,YAAM,UAAU,qBAAqB;AACrC,UAAI,SAAS;AACX,6BAAqB,OAAO;AAAA,MAC9B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,UAAU,QAAQ,OAAO;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,UAAU,gBAAgB,yBAAyB,cAAc,CAAC;AACvE,SAAO;AACT;AAEA,SAAS,iBAAiB;AACxB,QAAM,UAAU;AAAA,IACd,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,EACf;AACA,QAAM,cAAc,CAAC;AACrB,WAAS,UAAU,IAAI;AACrB,gBAAY,KAAK,EAAE;AACnB,WAAO,SAAS,cAAc;AAC5B,YAAM,QAAQ,YAAY,QAAQ,EAAE;AACpC,UAAI,UAAU,IAAI;AAChB;AAAA,MACF;AACA,kBAAY,OAAO,OAAO,CAAC;AAAA,IAC7B;AAAA,EACF;AACA,WAAS,OAAO,OAAO;AACrB,QAAI,YAAY,QAAQ;AACtB,kBAAY,QAAQ,QAAM,GAAG,KAAK,CAAC;AAAA,IACrC;AAAA,EACF;AACA,WAAS,kBAAkB,IAAI;AAC7B,WAAO,QAAQ,WAAW,EAAE,KAAK;AAAA,EACnC;AACA,WAAS,iBAAiB,IAAI;AAC5B,UAAM,QAAQ,kBAAkB,EAAE;AAClC,KAAC,QAAQ,OAAwC/C,WAAU,OAAO,wCAAwC,EAAE,GAAG,IAAIA,WAAU,IAAI;AACjI,WAAO;AAAA,EACT;AACA,QAAM,eAAe;AAAA,IACnB,UAAU,WAAS;AACjB,cAAQ,WAAW,MAAM,WAAW,EAAE,IAAI;AAC1C,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,QAAQ,CAAC,OAAO,SAAS;AACvB,YAAM,UAAU,QAAQ,WAAW,KAAK,WAAW,EAAE;AACrD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,UAAI,QAAQ,aAAa,MAAM,UAAU;AACvC;AAAA,MACF;AACA,aAAO,QAAQ,WAAW,KAAK,WAAW,EAAE;AAC5C,cAAQ,WAAW,MAAM,WAAW,EAAE,IAAI;AAAA,IAC5C;AAAA,IACA,YAAY,WAAS;AACnB,YAAM,cAAc,MAAM,WAAW;AACrC,YAAM,UAAU,kBAAkB,WAAW;AAC7C,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,UAAI,MAAM,aAAa,QAAQ,UAAU;AACvC;AAAA,MACF;AACA,aAAO,QAAQ,WAAW,WAAW;AACrC,UAAI,QAAQ,WAAW,MAAM,WAAW,WAAW,GAAG;AACpD,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,QAAQ,QAAM,QAAQ,kBAAkB,EAAE,CAAC;AAAA,IAC3C,cAAc,UAAQ,OAAO,OAAO,QAAQ,UAAU,EAAE,OAAO,WAAS,MAAM,WAAW,SAAS,IAAI;AAAA,EACxG;AACA,WAAS,kBAAkB,IAAI;AAC7B,WAAO,QAAQ,WAAW,EAAE,KAAK;AAAA,EACnC;AACA,WAAS,iBAAiB,IAAI;AAC5B,UAAM,QAAQ,kBAAkB,EAAE;AAClC,KAAC,QAAQ,OAAwCA,WAAU,OAAO,wCAAwC,EAAE,GAAG,IAAIA,WAAU,IAAI;AACjI,WAAO;AAAA,EACT;AACA,QAAM,eAAe;AAAA,IACnB,UAAU,WAAS;AACjB,cAAQ,WAAW,MAAM,WAAW,EAAE,IAAI;AAAA,IAC5C;AAAA,IACA,YAAY,WAAS;AACnB,YAAM,UAAU,kBAAkB,MAAM,WAAW,EAAE;AACrD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,UAAI,MAAM,aAAa,QAAQ,UAAU;AACvC;AAAA,MACF;AACA,aAAO,QAAQ,WAAW,MAAM,WAAW,EAAE;AAAA,IAC/C;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,QAAQ,QAAM,QAAQ,kBAAkB,EAAE,CAAC;AAAA,IAC3C,cAAc,UAAQ,OAAO,OAAO,QAAQ,UAAU,EAAE,OAAO,WAAS,MAAM,WAAW,SAAS,IAAI;AAAA,EACxG;AACA,WAASgD,SAAQ;AACf,YAAQ,aAAa,CAAC;AACtB,YAAQ,aAAa,CAAC;AACtB,gBAAY,SAAS;AAAA,EACvB;AACA,SAAO;AAAA,IACL,WAAW;AAAA,IACX,WAAW;AAAA,IACX;AAAA,IACA,OAAAA;AAAA,EACF;AACF;AAEA,SAAS,cAAc;AACrB,QAAM,WAAW,QAAQ,gBAAgB,CAAC,CAAC;AAC3C,8BAAU,MAAM;AACd,WAAO,SAAS,UAAU;AACxB,eAAS,MAAM;AAAA,IACjB;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AACb,SAAO;AACT;AAEA,IAAI,eAAe,aAAA/C,QAAM,cAAc,IAAI;AAE3C,IAAI,iBAAiB,MAAM;AACzB,QAAM,OAAO,SAAS;AACtB,GAAC,OAAO,OAAwCD,WAAU,OAAO,2BAA2B,IAAIA,WAAU,IAAI;AAC9G,SAAO;AACT;AAEA,IAAM,iBAAiB;AAAA,EACrB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,aAAa;AACf;AAEA,IAAM,QAAQ,eAAa,oBAAoB,SAAS;AACxD,SAAS,aAAa,WAAW;AAC/B,QAAM,KAAK,QAAQ,MAAM,MAAM,SAAS,GAAG,CAAC,SAAS,CAAC;AACtD,QAAMyC,WAAM,qBAAO,IAAI;AACvB,8BAAU,SAAS,QAAQ;AACzB,UAAM,KAAK,SAAS,cAAc,KAAK;AACvC,IAAAA,KAAI,UAAU;AACd,OAAG,KAAK;AACR,OAAG,aAAa,aAAa,WAAW;AACxC,OAAG,aAAa,eAAe,MAAM;AACrC,aAAS,GAAG,OAAO,cAAc;AACjC,mBAAe,EAAE,YAAY,EAAE;AAC/B,WAAO,SAAS,UAAU;AACxB,iBAAW,SAAS,SAAS;AAC3B,cAAM,OAAO,eAAe;AAC5B,YAAI,KAAK,SAAS,EAAE,GAAG;AACrB,eAAK,YAAY,EAAE;AAAA,QACrB;AACA,YAAI,OAAOA,KAAI,SAAS;AACtB,UAAAA,KAAI,UAAU;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,EAAE,CAAC;AACP,QAAM,WAAW,YAAY,aAAW;AACtC,UAAM,KAAKA,KAAI;AACf,QAAI,IAAI;AACN,SAAG,cAAc;AACjB;AAAA,IACF;AACA,WAAwC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAQ3C,OAAO;AAAA,KACX,IAAI;AAAA,EACP,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AAEA,IAAM,WAAW;AAAA,EACf,WAAW;AACb;AACA,SAAS,YAAYF,SAAQ,UAAU,UAAU;AAC/C,QAAM,KAAK,aAAAtC,QAAM,MAAM;AACvB,SAAO,QAAQ,MAAM,GAAGsC,OAAM,GAAG,QAAQ,SAAS,GAAG,EAAE,IAAI,CAAC,QAAQ,WAAWA,SAAQ,EAAE,CAAC;AAC5F;AAEA,SAAS,aAAa;AAAA,EACpB;AAAA,EACA;AACF,GAAG;AACD,SAAO,mBAAmB,SAAS,IAAI,QAAQ;AACjD;AACA,SAAS,qBAAqB;AAAA,EAC5B;AAAA,EACA;AACF,GAAG;AACD,QAAM,WAAW,YAAY,eAAe;AAAA,IAC1C,WAAW;AAAA,EACb,CAAC;AACD,QAAM,KAAK,QAAQ,MAAM,aAAa;AAAA,IACpC;AAAA,IACA;AAAA,EACF,CAAC,GAAG,CAAC,UAAU,SAAS,CAAC;AACzB,8BAAU,SAAS,QAAQ;AACzB,UAAM,KAAK,SAAS,cAAc,KAAK;AACvC,OAAG,KAAK;AACR,OAAG,cAAc;AACjB,OAAG,MAAM,UAAU;AACnB,mBAAe,EAAE,YAAY,EAAE;AAC/B,WAAO,SAAS,UAAU;AACxB,YAAM,OAAO,eAAe;AAC5B,UAAI,KAAK,SAAS,EAAE,GAAG;AACrB,aAAK,YAAY,EAAE;AAAA,MACrB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,IAAI,IAAI,CAAC;AACb,SAAO;AACT;AAEA,IAAI,aAAa,aAAAtC,QAAM,cAAc,IAAI;AAEzC,IAAI,mBAAmB;AAAA,EACtB,OAAO;AAAoB;AAE5B,IAAM,SAAS;AACf,IAAM,aAAa,WAAS;AAC1B,QAAM,SAAS,OAAO,KAAK,KAAK;AAChC,IAAE,UAAU,QAAQ,OAAwCD,WAAU,OAAO,iCAAiC,KAAK,EAAE,IAAIA,WAAU,IAAI;AACvI,QAAM,QAAQ,OAAO,OAAO,CAAC,CAAC;AAC9B,QAAM,QAAQ,OAAO,OAAO,CAAC,CAAC;AAC9B,QAAMiD,SAAQ,OAAO,OAAO,CAAC,CAAC;AAC9B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAAA;AAAA,IACA,KAAK;AAAA,EACP;AACF;AACA,IAAM,cAAc,CAAC,UAAU,WAAW;AACxC,MAAI,OAAO,QAAQ,SAAS,OAAO;AACjC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,QAAQ,SAAS,OAAO;AACjC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,QAAQ,SAAS,OAAO;AACjC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,QAAQ,SAAS,OAAO;AACjC,WAAO;AAAA,EACT;AACA,SAAO,OAAO,SAAS,SAAS;AAClC;AACA,IAAI,oBAAoB,CAAC,cAAc,gBAAgB;AACrD,QAAM,UAAU,WAAW,YAAY;AACvC,QAAM,SAAS,WAAW,WAAW;AACrC,MAAI,YAAY,SAAS,MAAM,GAAG;AAChC;AAAA,EACF;AACA,SAAwC,QAAQ;AAAA,sBAC5B,OAAO,GAAG;AAAA,0DAC0B,QAAQ,GAAG;AAAA;AAAA;AAAA,GAGlE,IAAI;AACP;AAEA,IAAM,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAMf,IAAI,eAAe,SAAO;AACxB,QAAM,UAAU,IAAI;AACpB,MAAI,CAAC,SAAS;AACZ,WAAwC,QAAQ;AAAA;AAAA;AAAA,QAG5C,MAAM;AAAA,KACT,IAAI;AACL;AAAA,EACF;AACA,MAAI,QAAQ,KAAK,YAAY,MAAM,QAAQ;AACzC,WAAwC,QAAQ;AAAA,sCACd,QAAQ,IAAI;AAAA;AAAA,QAE1C,MAAM;AAAA,KACT,IAAI;AAAA,EACP;AACA,MAAI,QAAQ,aAAa,IAAI;AAC3B,WAAwC,QAAQ;AAAA,+CACL,QAAQ,QAAQ;AAAA;AAAA;AAAA,QAGvD,MAAM;AAAA,KACT,IAAI;AAAA,EACP;AACF;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,MAAuC;AACzC,YAAQ;AAAA,EACV;AACF;AAEA,SAAS,mBAAmB,IAAI,QAAQ;AACtC,SAAO,MAAM;AACX,gCAAU,MAAM;AACd,UAAI;AACF,WAAG;AAAA,MACL,SAAS,GAAG;AACV,cAAM;AAAA;AAAA;AAAA,cAGA,EAAE,OAAO;AAAA,SACd;AAAA,MACH;AAAA,IACF,GAAG,MAAM;AAAA,EACX,CAAC;AACH;AAEA,SAAS,uBAAuB;AAC9B,qBAAmB,MAAM;AACvB,sBAAkB,iBAAiB,OAAO,aAAAhD,QAAM,OAAO;AACvD,iBAAa,QAAQ;AAAA,EACvB,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,YAAY,SAAS;AAC5B,QAAMwC,WAAM,qBAAO,OAAO;AAC1B,8BAAU,MAAM;AACd,IAAAA,KAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAOA;AACT;AAEA,SAAS,SAAS;AAChB,MAAI,OAAO;AACX,WAAS,YAAY;AACnB,WAAO,QAAQ,IAAI;AAAA,EACrB;AACA,WAASlB,UAAS,OAAO;AACvB,WAAO,UAAU;AAAA,EACnB;AACA,WAAS,MAAM,SAAS;AACtB,KAAC,CAAC,OAAO,OAAwCvB,WAAU,OAAO,4CAA4C,IAAIA,WAAU,IAAI;AAChI,UAAM,UAAU;AAAA,MACd;AAAA,IACF;AACA,WAAO;AACP,WAAO;AAAA,EACT;AACA,WAAS,UAAU;AACjB,KAAC,OAAO,OAAwCA,WAAU,OAAO,2CAA2C,IAAIA,WAAU,IAAI;AAC9H,WAAO;AAAA,EACT;AACA,WAAS,aAAa;AACpB,QAAI,MAAM;AACR,WAAK,QAAQ;AACb,cAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,UAAAuB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,OAAO;AACzB,MAAI,MAAM,UAAU,UAAU,MAAM,UAAU,kBAAkB;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,MAAM;AACf;AAEA,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,WAAW;AACjB,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,YAAY;AAClB,IAAM,UAAU;AAChB,IAAM,aAAa;AACnB,IAAM,YAAY;AAElB,IAAM,gBAAgB;AAAA,EACpB,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,GAAG,GAAG;AACT;AACA,IAAI,2BAA2B,WAAS;AACtC,MAAI,cAAc,MAAM,OAAO,GAAG;AAChC,UAAM,eAAe;AAAA,EACvB;AACF;AAEA,IAAM,sBAAsB,MAAM;AAChC,QAAM,OAAO;AACb,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC,MAAM,KAAK,IAAI,IAAI,SAAS,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE;AAChF,QAAM,YAAY,WAAW,KAAK,eAAa,KAAK,SAAS,MAAM,QAAQ;AAC3E,SAAO,aAAa;AACtB,GAAG;AAEH,IAAM,gBAAgB;AACtB,IAAM,uBAAuB;AAC7B,SAAS,+BAA+B,UAAU,SAAS;AACzD,SAAO,KAAK,IAAI,QAAQ,IAAI,SAAS,CAAC,KAAK,wBAAwB,KAAK,IAAI,QAAQ,IAAI,SAAS,CAAC,KAAK;AACzG;AACA,IAAM,SAAS;AAAA,EACb,MAAM;AACR;AACA,SAAS,mBAAmB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,CAAC;AAAA,IACN,WAAW;AAAA,IACX,IAAI,WAAS;AACX,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,WAAW,eAAe;AAC5B;AAAA,MACF;AACA,YAAM,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,YAAM,QAAQ,SAAS;AACvB,UAAI,MAAM,SAAS,YAAY;AAC7B,cAAM,eAAe;AACrB,cAAM,QAAQ,KAAK,KAAK;AACxB;AAAA,MACF;AACA,QAAE,MAAM,SAAS,aAAa,OAAwCvB,WAAU,OAAO,gBAAgB,IAAIA,WAAU,IAAI;AACzH,YAAM,UAAU,MAAM;AACtB,UAAI,CAAC,+BAA+B,SAAS,KAAK,GAAG;AACnD;AAAA,MACF;AACA,YAAM,eAAe;AACrB,YAAM,UAAU,MAAM,QAAQ,UAAU,KAAK;AAC7C,eAAS;AAAA,QACP,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,WAAS;AACX,YAAM,QAAQ,SAAS;AACvB,UAAI,MAAM,SAAS,YAAY;AAC7B,eAAO;AACP;AAAA,MACF;AACA,YAAM,eAAe;AACrB,YAAM,QAAQ,KAAK;AAAA,QACjB,sBAAsB;AAAA,MACxB,CAAC;AACD,gBAAU;AAAA,IACZ;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,WAAS;AACX,UAAI,SAAS,EAAE,SAAS,YAAY;AAClC,cAAM,eAAe;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,WAAS;AACX,YAAM,QAAQ,SAAS;AACvB,UAAI,MAAM,SAAS,WAAW;AAC5B,eAAO;AACP;AAAA,MACF;AACA,UAAI,MAAM,YAAY,QAAQ;AAC5B,cAAM,eAAe;AACrB,eAAO;AACP;AAAA,MACF;AACA,+BAAyB,KAAK;AAAA,IAChC;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,IAAI,MAAM;AACR,UAAI,SAAS,EAAE,SAAS,WAAW;AACjC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,WAAS;AACX,YAAM,QAAQ,SAAS;AACvB,QAAE,MAAM,SAAS,UAAU,OAAwCA,WAAU,OAAO,kBAAkB,IAAIA,WAAU,IAAI;AACxH,UAAI,MAAM,QAAQ,wBAAwB,GAAG;AAC3C,eAAO;AACP;AAAA,MACF;AACA,YAAM,eAAe;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,CAAC;AACH;AACA,SAAS,eAAe,KAAK;AAC3B,QAAM,eAAW,qBAAO,MAAM;AAC9B,QAAM,sBAAkB,qBAAO,MAAM;AACrC,QAAM,sBAAsB,QAAQ,OAAO;AAAA,IACzC,WAAW;AAAA,IACX,IAAI,SAAS,YAAY,OAAO;AAC9B,UAAI,MAAM,kBAAkB;AAC1B;AAAA,MACF;AACA,UAAI,MAAM,WAAW,eAAe;AAClC;AAAA,MACF;AACA,UAAI,MAAM,WAAW,MAAM,WAAW,MAAM,YAAY,MAAM,QAAQ;AACpE;AAAA,MACF;AACA,YAAM,cAAc,IAAI,uBAAuB,KAAK;AACpD,UAAI,CAAC,aAAa;AAChB;AAAA,MACF;AACA,YAAM,UAAU,IAAI,WAAW,aAAa,MAAM;AAAA,QAChD,aAAa;AAAA,MACf,CAAC;AACD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,YAAM,eAAe;AACrB,YAAM,QAAQ;AAAA,QACZ,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,MACX;AACA,sBAAgB,QAAQ;AACxB,uBAAiB,SAAS,KAAK;AAAA,IACjC;AAAA,EACF,IAAI,CAAC,GAAG,CAAC;AACT,QAAM,2BAA2B,QAAQ,OAAO;AAAA,IAC9C,WAAW;AAAA,IACX,IAAI,WAAS;AACX,UAAI,MAAM,kBAAkB;AAC1B;AAAA,MACF;AACA,YAAM,KAAK,IAAI,uBAAuB,KAAK;AAC3C,UAAI,CAAC,IAAI;AACP;AAAA,MACF;AACA,YAAM,UAAU,IAAI,wBAAwB,EAAE;AAC9C,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,UAAI,QAAQ,yBAAyB;AACnC;AAAA,MACF;AACA,UAAI,CAAC,IAAI,WAAW,EAAE,GAAG;AACvB;AAAA,MACF;AACA,YAAM,eAAe;AAAA,IACvB;AAAA,EACF,IAAI,CAAC,GAAG,CAAC;AACT,QAAM,mBAAmB,YAAY,SAASkD,oBAAmB;AAC/D,UAAM,UAAU;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,oBAAgB,UAAU,WAAW,QAAQ,CAAC,0BAA0B,mBAAmB,GAAG,OAAO;AAAA,EACvG,GAAG,CAAC,0BAA0B,mBAAmB,CAAC;AAClD,QAAM,OAAO,YAAY,MAAM;AAC7B,UAAM,UAAU,SAAS;AACzB,QAAI,QAAQ,SAAS,QAAQ;AAC3B;AAAA,IACF;AACA,aAAS,UAAU;AACnB,oBAAgB,QAAQ;AACxB,qBAAiB;AAAA,EACnB,GAAG,CAAC,gBAAgB,CAAC;AACrB,QAAM,SAAS,YAAY,MAAM;AAC/B,UAAM,QAAQ,SAAS;AACvB,SAAK;AACL,QAAI,MAAM,SAAS,YAAY;AAC7B,YAAM,QAAQ,OAAO;AAAA,QACnB,sBAAsB;AAAA,MACxB,CAAC;AAAA,IACH;AACA,QAAI,MAAM,SAAS,WAAW;AAC5B,YAAM,QAAQ,MAAM;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,QAAM,sBAAsB,YAAY,SAASC,uBAAsB;AACrE,UAAM,UAAU;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,UAAM,WAAW,mBAAmB;AAAA,MAClC;AAAA,MACA,WAAW;AAAA,MACX,UAAU,MAAM,SAAS;AAAA,MACzB,UAAU,WAAS;AACjB,iBAAS,UAAU;AAAA,MACrB;AAAA,IACF,CAAC;AACD,oBAAgB,UAAU,WAAW,QAAQ,UAAU,OAAO;AAAA,EAChE,GAAG,CAAC,QAAQ,IAAI,CAAC;AACjB,QAAM,mBAAmB,YAAY,SAASC,kBAAiB,SAAS,OAAO;AAC7E,MAAE,SAAS,QAAQ,SAAS,UAAU,OAAwCpD,WAAU,OAAO,4CAA4C,IAAIA,WAAU,IAAI;AAC7J,aAAS,UAAU;AAAA,MACjB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AACA,wBAAoB;AAAA,EACtB,GAAG,CAAC,mBAAmB,CAAC;AACxB,4BAA0B,SAAS,QAAQ;AACzC,qBAAiB;AACjB,WAAO,SAAS,UAAU;AACxB,sBAAgB,QAAQ;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,gBAAgB,CAAC;AACvB;AAEA,SAAS,SAAS;AAAC;AACnB,IAAM,iBAAiB;AAAA,EACrB,CAAC,QAAQ,GAAG;AAAA,EACZ,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,GAAG,GAAG;AACT;AACA,SAAS,oBAAoB,SAAS,MAAM;AAC1C,WAAS,SAAS;AAChB,SAAK;AACL,YAAQ,OAAO;AAAA,EACjB;AACA,WAAS2B,QAAO;AACd,SAAK;AACL,YAAQ,KAAK;AAAA,EACf;AACA,SAAO,CAAC;AAAA,IACN,WAAW;AAAA,IACX,IAAI,WAAS;AACX,UAAI,MAAM,YAAY,QAAQ;AAC5B,cAAM,eAAe;AACrB,eAAO;AACP;AAAA,MACF;AACA,UAAI,MAAM,YAAY,OAAO;AAC3B,cAAM,eAAe;AACrB,QAAAA,MAAK;AACL;AAAA,MACF;AACA,UAAI,MAAM,YAAY,WAAW;AAC/B,cAAM,eAAe;AACrB,gBAAQ,SAAS;AACjB;AAAA,MACF;AACA,UAAI,MAAM,YAAY,SAAS;AAC7B,cAAM,eAAe;AACrB,gBAAQ,OAAO;AACf;AAAA,MACF;AACA,UAAI,MAAM,YAAY,YAAY;AAChC,cAAM,eAAe;AACrB,gBAAQ,UAAU;AAClB;AAAA,MACF;AACA,UAAI,MAAM,YAAY,WAAW;AAC/B,cAAM,eAAe;AACrB,gBAAQ,SAAS;AACjB;AAAA,MACF;AACA,UAAI,eAAe,MAAM,OAAO,GAAG;AACjC,cAAM,eAAe;AACrB;AAAA,MACF;AACA,+BAAyB,KAAK;AAAA,IAChC;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,CAAC;AACH;AACA,SAAS,kBAAkB,KAAK;AAC9B,QAAM,sBAAkB,qBAAO,MAAM;AACrC,QAAM,sBAAsB,QAAQ,OAAO;AAAA,IACzC,WAAW;AAAA,IACX,IAAI,SAAS,UAAU,OAAO;AAC5B,UAAI,MAAM,kBAAkB;AAC1B;AAAA,MACF;AACA,UAAI,MAAM,YAAY,OAAO;AAC3B;AAAA,MACF;AACA,YAAM,cAAc,IAAI,uBAAuB,KAAK;AACpD,UAAI,CAAC,aAAa;AAChB;AAAA,MACF;AACA,YAAM,UAAU,IAAI,WAAW,aAAa,MAAM;AAAA,QAChD,aAAa;AAAA,MACf,CAAC;AACD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,YAAM,eAAe;AACrB,UAAI,cAAc;AAClB,YAAM,UAAU,QAAQ,SAAS;AACjC,sBAAgB,QAAQ;AACxB,eAAS,OAAO;AACd,SAAC,cAAc,OAAwC3B,WAAU,OAAO,0DAA0D,IAAIA,WAAU,IAAI;AACpJ,sBAAc;AACd,wBAAgB,QAAQ;AACxB,yBAAiB;AAAA,MACnB;AACA,sBAAgB,UAAU,WAAW,QAAQ,oBAAoB,SAAS,IAAI,GAAG;AAAA,QAC/E,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF,IAAI,CAAC,GAAG,CAAC;AACT,QAAM,mBAAmB,YAAY,SAAS,kBAAkB;AAC9D,UAAM,UAAU;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,oBAAgB,UAAU,WAAW,QAAQ,CAAC,mBAAmB,GAAG,OAAO;AAAA,EAC7E,GAAG,CAAC,mBAAmB,CAAC;AACxB,4BAA0B,SAAS,QAAQ;AACzC,qBAAiB;AACjB,WAAO,SAAS,UAAU;AACxB,sBAAgB,QAAQ;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,gBAAgB,CAAC;AACvB;AAEA,IAAM,OAAO;AAAA,EACX,MAAM;AACR;AACA,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAC5B,SAAS,kBAAkB;AAAA,EACzB;AAAA,EACA;AACF,GAAG;AACD,SAAO,CAAC;AAAA,IACN,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,WAAS;AACX,YAAM,eAAe;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,WAAS;AACX,UAAI,SAAS,EAAE,SAAS,YAAY;AAClC,eAAO;AACP;AAAA,MACF;AACA,UAAI,MAAM,YAAY,QAAQ;AAC5B,cAAM,eAAe;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,CAAC;AACH;AACA,SAAS,kBAAkB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,CAAC;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,IAAI,WAAS;AACX,YAAM,QAAQ,SAAS;AACvB,UAAI,MAAM,SAAS,YAAY;AAC7B,eAAO;AACP;AAAA,MACF;AACA,YAAM,WAAW;AACjB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM,QAAQ,CAAC;AACnB,YAAM,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,YAAM,eAAe;AACrB,YAAM,QAAQ,KAAK,KAAK;AAAA,IAC1B;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,WAAS;AACX,YAAM,QAAQ,SAAS;AACvB,UAAI,MAAM,SAAS,YAAY;AAC7B,eAAO;AACP;AAAA,MACF;AACA,YAAM,eAAe;AACrB,YAAM,QAAQ,KAAK;AAAA,QACjB,sBAAsB;AAAA,MACxB,CAAC;AACD,gBAAU;AAAA,IACZ;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,WAAS;AACX,UAAI,SAAS,EAAE,SAAS,YAAY;AAClC,eAAO;AACP;AAAA,MACF;AACA,YAAM,eAAe;AACrB,aAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI,WAAS;AACX,YAAM,QAAQ,SAAS;AACvB,QAAE,MAAM,SAAS,UAAU,OAAwCA,WAAU,IAAIA,WAAU,IAAI;AAC/F,YAAM,QAAQ,MAAM,QAAQ,CAAC;AAC7B,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,YAAM,eAAe,MAAM,SAAS;AACpC,UAAI,CAAC,cAAc;AACjB;AAAA,MACF;AACA,YAAM,gBAAgB,MAAM,QAAQ,wBAAwB;AAC5D,UAAI,MAAM,SAAS,WAAW;AAC5B,YAAI,eAAe;AACjB,iBAAO;AAAA,QACT;AACA;AAAA,MACF;AACA,UAAI,eAAe;AACjB,YAAI,MAAM,UAAU;AAClB,gBAAM,eAAe;AACrB;AAAA,QACF;AACA,eAAO;AACP;AAAA,MACF;AACA,YAAM,eAAe;AAAA,IACvB;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,IACX,IAAI;AAAA,EACN,CAAC;AACH;AACA,SAAS,eAAe,KAAK;AAC3B,QAAM,eAAW,qBAAO,IAAI;AAC5B,QAAM,sBAAkB,qBAAO,MAAM;AACrC,QAAM,WAAW,YAAY,SAASqD,YAAW;AAC/C,WAAO,SAAS;AAAA,EAClB,GAAG,CAAC,CAAC;AACL,QAAM,WAAW,YAAY,SAASC,UAAS,OAAO;AACpD,aAAS,UAAU;AAAA,EACrB,GAAG,CAAC,CAAC;AACL,QAAM,sBAAsB,QAAQ,OAAO;AAAA,IACzC,WAAW;AAAA,IACX,IAAI,SAAS,aAAa,OAAO;AAC/B,UAAI,MAAM,kBAAkB;AAC1B;AAAA,MACF;AACA,YAAM,cAAc,IAAI,uBAAuB,KAAK;AACpD,UAAI,CAAC,aAAa;AAChB;AAAA,MACF;AACA,YAAM,UAAU,IAAI,WAAW,aAAa,MAAM;AAAA,QAChD,aAAa;AAAA,MACf,CAAC;AACD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,YAAM,QAAQ,MAAM,QAAQ,CAAC;AAC7B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,sBAAgB,QAAQ;AACxB,uBAAiB,SAAS,KAAK;AAAA,IACjC;AAAA,EACF,IAAI,CAAC,GAAG,CAAC;AACT,QAAM,mBAAmB,YAAY,SAASJ,oBAAmB;AAC/D,UAAM,UAAU;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,oBAAgB,UAAU,WAAW,QAAQ,CAAC,mBAAmB,GAAG,OAAO;AAAA,EAC7E,GAAG,CAAC,mBAAmB,CAAC;AACxB,QAAM,OAAO,YAAY,MAAM;AAC7B,UAAM,UAAU,SAAS;AACzB,QAAI,QAAQ,SAAS,QAAQ;AAC3B;AAAA,IACF;AACA,QAAI,QAAQ,SAAS,WAAW;AAC9B,mBAAa,QAAQ,gBAAgB;AAAA,IACvC;AACA,aAAS,IAAI;AACb,oBAAgB,QAAQ;AACxB,qBAAiB;AAAA,EACnB,GAAG,CAAC,kBAAkB,QAAQ,CAAC;AAC/B,QAAM,SAAS,YAAY,MAAM;AAC/B,UAAM,QAAQ,SAAS;AACvB,SAAK;AACL,QAAI,MAAM,SAAS,YAAY;AAC7B,YAAM,QAAQ,OAAO;AAAA,QACnB,sBAAsB;AAAA,MACxB,CAAC;AAAA,IACH;AACA,QAAI,MAAM,SAAS,WAAW;AAC5B,YAAM,QAAQ,MAAM;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,QAAM,sBAAsB,YAAY,SAASC,uBAAsB;AACrE,UAAM,UAAU;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,UAAM,OAAO;AAAA,MACX;AAAA,MACA,WAAW;AAAA,MACX;AAAA,IACF;AACA,UAAM,eAAe,WAAW,QAAQ,kBAAkB,IAAI,GAAG,OAAO;AACxE,UAAM,eAAe,WAAW,QAAQ,kBAAkB,IAAI,GAAG,OAAO;AACxE,oBAAgB,UAAU,SAAS,YAAY;AAC7C,mBAAa;AACb,mBAAa;AAAA,IACf;AAAA,EACF,GAAG,CAAC,QAAQ,UAAU,IAAI,CAAC;AAC3B,QAAM,gBAAgB,YAAY,SAASI,iBAAgB;AACzD,UAAM,QAAQ,SAAS;AACvB,MAAE,MAAM,SAAS,aAAa,OAAwCvD,WAAU,OAAO,oCAAoC,MAAM,IAAI,EAAE,IAAIA,WAAU,IAAI;AACzJ,UAAM,UAAU,MAAM,QAAQ,UAAU,MAAM,KAAK;AACnD,aAAS;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,GAAG,CAAC,UAAU,QAAQ,CAAC;AACvB,QAAM,mBAAmB,YAAY,SAASoD,kBAAiB,SAAS,OAAO;AAC7E,MAAE,SAAS,EAAE,SAAS,UAAU,OAAwCpD,WAAU,OAAO,4CAA4C,IAAIA,WAAU,IAAI;AACvJ,UAAM,mBAAmB,WAAW,eAAe,gBAAgB;AACnE,aAAS;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,wBAAoB;AAAA,EACtB,GAAG,CAAC,qBAAqB,UAAU,UAAU,aAAa,CAAC;AAC3D,4BAA0B,SAAS,QAAQ;AACzC,qBAAiB;AACjB,WAAO,SAAS,UAAU;AACxB,sBAAgB,QAAQ;AACxB,YAAM,QAAQ,SAAS;AACvB,UAAI,MAAM,SAAS,WAAW;AAC5B,qBAAa,MAAM,gBAAgB;AACnC,iBAAS,IAAI;AAAA,MACf;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,kBAAkB,QAAQ,CAAC;AACzC,4BAA0B,SAAS,aAAa;AAC9C,UAAM,SAAS,WAAW,QAAQ,CAAC;AAAA,MACjC,WAAW;AAAA,MACX,IAAI,MAAM;AAAA,MAAC;AAAA,MACX,SAAS;AAAA,QACP,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF,CAAC,CAAC;AACF,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,uBAAuB,aAAa;AAC3C,SAAO,MAAM;AACX,UAAM,cAAc,YAAY,WAAW;AAC3C,uBAAmB,MAAM;AACvB,QAAE,YAAY,QAAQ,WAAW,YAAY,UAAU,OAAwCA,WAAU,OAAO,yDAAyD,IAAIA,WAAU,KAAK,IAAI;AAAA,IAClM,CAAC;AAAA,EACH,CAAC;AACH;AAEA,IAAM,sBAAsB,CAAC,SAAS,UAAU,YAAY,UAAU,UAAU,YAAY,SAAS,OAAO;AAC5G,SAAS,uBAAuB,QAAQ,SAAS;AAC/C,MAAI,WAAW,MAAM;AACnB,WAAO;AAAA,EACT;AACA,QAAM,sBAAsB,oBAAoB,SAAS,QAAQ,QAAQ,YAAY,CAAC;AACtF,MAAI,qBAAqB;AACvB,WAAO;AAAA,EACT;AACA,QAAM,YAAY,QAAQ,aAAa,iBAAiB;AACxD,MAAI,cAAc,UAAU,cAAc,IAAI;AAC5C,WAAO;AAAA,EACT;AACA,MAAI,YAAY,QAAQ;AACtB,WAAO;AAAA,EACT;AACA,SAAO,uBAAuB,QAAQ,QAAQ,aAAa;AAC7D;AACA,SAAS,4BAA4BQ,YAAW,OAAO;AACrD,QAAM,SAAS,MAAM;AACrB,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,uBAAuBA,YAAW,MAAM;AACjD;AAEA,IAAI,6BAA6B,QAAM,QAAQ,GAAG,sBAAsB,CAAC,EAAE;AAE3E,SAAS,UAAU,IAAI;AACrB,SAAO,cAAc,gBAAgB,EAAE,EAAE;AAC3C;AAEA,IAAM,wBAAwB,MAAM;AAClC,QAAM,OAAO;AACb,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC,MAAM,qBAAqB,uBAAuB;AACtE,QAAM,QAAQ,WAAW,KAAK,UAAQ,QAAQ,QAAQ,SAAS;AAC/D,SAAO,SAAS;AAClB,GAAG;AACH,SAAS,gBAAgB,IAAI,UAAU;AACrC,MAAI,MAAM,MAAM;AACd,WAAO;AAAA,EACT;AACA,MAAI,GAAG,oBAAoB,EAAE,QAAQ,GAAG;AACtC,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,GAAG,eAAe,QAAQ;AACnD;AACA,SAAS,QAAQ,IAAI,UAAU;AAC7B,MAAI,GAAG,SAAS;AACd,WAAO,GAAG,QAAQ,QAAQ;AAAA,EAC5B;AACA,SAAO,gBAAgB,IAAI,QAAQ;AACrC;AAEA,SAAS,YAAY,WAAW;AAC9B,SAAO,IAAI,WAAW,SAAS,KAAK,SAAS;AAC/C;AACA,SAAS,+BAA+B,WAAW,OAAO;AACxD,QAAM,SAAS,MAAM;AACrB,MAAI,CAAC,UAAU,MAAM,GAAG;AACtB,WAAwC,QAAQ,gCAAgC,IAAI;AACpF,WAAO;AAAA,EACT;AACA,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,SAAS,QAAQ,QAAQ,QAAQ;AACvC,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,WAAwC,QAAQ,mCAAmC,IAAI;AACvF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,kCAAkC,WAAW,OAAO;AAC3D,QAAM,SAAS,+BAA+B,WAAW,KAAK;AAC9D,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,SAAO,OAAO,aAAa,WAAW,WAAW;AACnD;AAEA,SAAS,cAAc,WAAW,aAAa;AAC7C,QAAM,WAAW,IAAI,UAAU,SAAS,KAAK,SAAS;AACtD,QAAM,WAAW,iBAAiB,UAAU,QAAQ;AACpD,QAAM,cAAc,SAAS,KAAK,QAAM;AACtC,WAAO,GAAG,aAAa,UAAU,EAAE,MAAM;AAAA,EAC3C,CAAC;AACD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,WAAW,GAAG;AAC/B,WAAwC,QAAQ,wCAAwC,IAAI;AAC5F,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,eAAe,OAAO;AAC7B,QAAM,eAAe;AACvB;AACA,SAAS,SAAS;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,CAAC,aAAa,GAAG;AACnB,QAAI,YAAY;AACd,aAAwC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAQ/C,IAAI;AAAA,IACP;AACA,WAAO;AAAA,EACT;AACA,MAAI,aAAa,OAAO;AACtB,QAAI,YAAY;AACd,aAAwC,QAAQ;AAAA;AAAA;AAAA;AAAA,yBAI7B,QAAQ;AAAA,oDACmB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,OAKlD,IAAI;AAAA,IACP;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,SAAS;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,QAAQ,UAAU,GAAG;AACvB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,SAAS,UAAU,SAAS,WAAW;AACrD,MAAI,CAAC,OAAO;AACV,WAAwC,QAAQ,qCAAqC,WAAW,EAAE,IAAI;AACtG,WAAO;AAAA,EACT;AACA,MAAI,CAAC,MAAM,QAAQ,WAAW;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,CAAC,aAAa,MAAM,SAAS,GAAG,WAAW,GAAG;AAChD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,SAAS;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,cAAc,SAAS;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,SAAS,UAAU,QAAQ,WAAW;AACpD,QAAM,KAAK,cAAc,WAAW,MAAM,WAAW,EAAE;AACvD,MAAI,CAAC,IAAI;AACP,WAAwC,QAAQ,6CAA6C,WAAW,EAAE,IAAI;AAC9G,WAAO;AAAA,EACT;AACA,MAAI,eAAe,CAAC,MAAM,QAAQ,8BAA8B,4BAA4B,IAAI,WAAW,GAAG;AAC5G,WAAO;AAAA,EACT;AACA,QAAM,OAAO,QAAQ,MAAM,mBAAmB,MAAM;AACpD,MAAI,QAAQ;AACZ,WAAS,6BAA6B;AACpC,WAAO,MAAM,QAAQ;AAAA,EACvB;AACA,WAAS,eAAe;AACtB,WAAO,QAAQ,SAAS,IAAI;AAAA,EAC9B;AACA,WAAS,YAAY,UAAU,WAAW;AACxC,QAAI,SAAS;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd,CAAC,GAAG;AACF,YAAM,SAAS,UAAU,CAAC;AAAA,IAC5B;AAAA,EACF;AACA,QAAM,0BAA0B,YAAY,KAAK,MAAM,UAAU;AACjE,WAASgD,MAAK,MAAM;AAClB,aAAS,YAAY;AACnB,cAAQ,QAAQ;AAChB,cAAQ;AAAA,IACV;AACA,QAAI,UAAU,YAAY;AACxB,gBAAU;AACV,aAAwCxD,WAAU,OAAO,wBAAwB,KAAK,EAAE,IAAIA,WAAU;AAAA,IACxG;AACA,UAAM,SAAS,OAAO,KAAK,cAAc,CAAC;AAC1C,YAAQ;AACR,aAASyD,QAAO,QAAQ,UAAU;AAAA,MAChC,sBAAsB;AAAA,IACxB,GAAG;AACD,WAAK,QAAQ;AACb,UAAI,QAAQ,sBAAsB;AAChC,cAAM,SAAS,WAAW,QAAQ,CAAC;AAAA,UACjC,WAAW;AAAA,UACX,IAAI;AAAA,UACJ,SAAS;AAAA,YACP,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS;AAAA,UACX;AAAA,QACF,CAAC,CAAC;AACF,mBAAW,MAAM;AAAA,MACnB;AACA,gBAAU;AACV,YAAM,SAAS,KAAK;AAAA,QAClB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AACA,WAAO;AAAA,MACL,UAAU,MAAM,SAAS;AAAA,QACvB,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,YAAY;AAAA,MACd,CAAC;AAAA,MACD,yBAAyB;AAAA,MACzB,MAAM,aAAWA,QAAO,QAAQ,OAAO;AAAA,MACvC,QAAQ,aAAWA,QAAO,UAAU,OAAO;AAAA,MAC3C,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AACA,WAAS,UAAU,iBAAiB;AAClC,UAAM,SAAS,qBAAQ,YAAU;AAC/B,8BAAwB,MAAM,KAAK;AAAA,QACjC;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,UAAM,MAAMD,MAAK;AAAA,MACf,gBAAgB;AAAA,QACd,IAAI;AAAA,QACJ;AAAA,QACA,cAAc;AAAA,MAChB;AAAA,MACA,SAAS,MAAM,OAAO,OAAO;AAAA,MAC7B,SAAS;AAAA,QACP,MAAM;AAAA,MACR;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL,GAAG;AAAA,MACH,MAAM;AAAA,IACR;AAAA,EACF;AACA,WAAS,WAAW;AAClB,UAAM,UAAU;AAAA,MACd,QAAQ,MAAM,wBAAwB,MAAM;AAAA,MAC5C,WAAW,MAAM,wBAAwB,SAAS;AAAA,MAClD,UAAU,MAAM,wBAAwB,QAAQ;AAAA,MAChD,UAAU,MAAM,wBAAwB,QAAQ;AAAA,IAClD;AACA,WAAOA,MAAK;AAAA,MACV,gBAAgB;AAAA,QACd,IAAI;AAAA,QACJ,iBAAiB,2BAA2B,EAAE;AAAA,QAC9C,cAAc;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,eAAe;AACtB,UAAM,gBAAgB,SAAS;AAAA,MAC7B,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AACD,QAAI,eAAe;AACjB,cAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AACA,QAAM,UAAU;AAAA,IACd,UAAU,MAAM,SAAS;AAAA,MACvB,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,IACD,yBAAyB;AAAA,IACzB;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,CAAC,gBAAgB,mBAAmB,cAAc;AACzE,SAAS,iBAAiB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,aAAa,CAAC,GAAI,uBAAuB,iBAAiB,CAAC,GAAI,GAAI,iBAAiB,CAAC,CAAE;AAC7F,QAAM,cAAU,uBAAS,MAAM,OAAO,CAAC,EAAE,CAAC;AAC1C,QAAM,iBAAiB,YAAY,SAASE,gBAAe,UAAU,SAAS;AAC5E,QAAI,WAAW,QAAQ,KAAK,CAAC,WAAW,OAAO,GAAG;AAChD,cAAQ,WAAW;AAAA,IACrB;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AACZ,4BAA0B,SAAS,gBAAgB;AACjD,QAAI,WAAW,MAAM,SAAS;AAC9B,UAAM,cAAc,MAAM,UAAU,MAAM;AACxC,YAAM,UAAU,MAAM,SAAS;AAC/B,qBAAe,UAAU,OAAO;AAChC,iBAAW;AAAA,IACb,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,SAAS,OAAO,cAAc,CAAC;AACnC,4BAA0B,MAAM;AAC9B,WAAO,QAAQ;AAAA,EACjB,GAAG,CAAC,QAAQ,UAAU,CAAC;AACvB,QAAM,aAAa,YAAY,iBAAe;AAC5C,WAAO,SAAS;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,SAAS,UAAU,KAAK,CAAC;AAC7B,QAAM,aAAa,YAAY,CAAC,aAAa,WAAW,YAAY,SAAS;AAAA,IAC3E;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,aAAa;AAAA,IAC9B,aAAa,WAAW,QAAQ,cAAc,QAAQ,cAAc;AAAA,EACtE,CAAC,GAAG,CAAC,WAAW,SAAS,UAAU,KAAK,CAAC;AACzC,QAAM,yBAAyB,YAAY,WAAS,kCAAkC,WAAW,KAAK,GAAG,CAAC,SAAS,CAAC;AACpH,QAAM,0BAA0B,YAAY,QAAM;AAChD,UAAM,QAAQ,SAAS,UAAU,SAAS,EAAE;AAC5C,WAAO,QAAQ,MAAM,UAAU;AAAA,EACjC,GAAG,CAAC,SAAS,SAAS,CAAC;AACvB,QAAM,iBAAiB,YAAY,SAASC,kBAAiB;AAC3D,QAAI,CAAC,QAAQ,UAAU,GAAG;AACxB;AAAA,IACF;AACA,YAAQ,WAAW;AACnB,QAAI,MAAM,SAAS,EAAE,UAAU,QAAQ;AACrC,YAAM,SAAS,MAAM,CAAC;AAAA,IACxB;AAAA,EACF,GAAG,CAAC,SAAS,KAAK,CAAC;AACnB,QAAM,gBAAgB,YAAY,MAAM,QAAQ,UAAU,GAAG,CAAC,OAAO,CAAC;AACtE,QAAM,MAAM,QAAQ,OAAO;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,YAAY,YAAY,wBAAwB,yBAAyB,gBAAgB,aAAa,CAAC;AAC5G,yBAAuB,UAAU;AACjC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,eAAW,CAAC,EAAE,GAAG;AAAA,EACnB;AACF;AAEA,IAAM,mBAAmB,YAAU;AAAA,EACjC,iBAAiB,OAAK;AACpB,UAAM,yBAAyB,MAAM;AACnC,UAAI,MAAM,iBAAiB;AACzB,cAAM,gBAAgB,CAAC;AAAA,MACzB;AAAA,IACF;AACA,oCAAU,sBAAsB;AAAA,EAClC;AAAA,EACA,mBAAmB,MAAM;AAAA,EACzB,aAAa,MAAM;AAAA,EACnB,WAAW,MAAM;AAAA,EACjB,cAAc,MAAM;AACtB;AACA,IAAM,4BAA4B,YAAU;AAAA,EAC1C,GAAG;AAAA,EACH,GAAG,MAAM;AAAA,EACT,mBAAmB;AAAA,IACjB,GAAG,2BAA2B;AAAA,IAC9B,GAAG,MAAM;AAAA,EACX;AACF;AACA,SAAS,SAAS,SAAS;AACzB,GAAC,QAAQ,UAAU,OAAwC3D,WAAU,OAAO,oCAAoC,IAAIA,WAAU,IAAI;AAClI,SAAO,QAAQ;AACjB;AACA,SAAS,IAAI,OAAO;AAClB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,6BAAA4D;AAAA,EACF,IAAI;AACJ,QAAM,mBAAe,qBAAO,IAAI;AAChC,uBAAqB;AACrB,QAAM,eAAe,YAAY,KAAK;AACtC,QAAM,gBAAgB,YAAY,MAAM;AACtC,WAAO,iBAAiB,aAAa,OAAO;AAAA,EAC9C,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,yBAAyB,YAAY,MAAM;AAC/C,WAAO,0BAA0B,aAAa,OAAO;AAAA,EACvD,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,WAAW,aAAa,SAAS;AACvC,QAAM,gCAAgC,qBAAqB;AAAA,IACzD;AAAA,IACA,MAAMA;AAAA,EACR,CAAC;AACD,QAAM,eAAe,gBAAgB,WAAW,KAAK;AACrD,QAAM,eAAe,YAAY,YAAU;AACzC,aAAS,YAAY,EAAE,SAAS,MAAM;AAAA,EACxC,GAAG,CAAC,CAAC;AACL,QAAM,mBAAmB,QAAQ,MAAM,mBAAmB;AAAA,IACxD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,YAAY,GAAG,CAAC,YAAY,CAAC;AAChC,QAAM,WAAW,YAAY;AAC7B,QAAM,mBAAmB,QAAQ,MAAM;AACrC,WAAO,uBAAuB,UAAU,gBAAgB;AAAA,EAC1D,GAAG,CAAC,UAAU,gBAAgB,CAAC;AAC/B,QAAM,eAAe,QAAQ,MAAM,mBAAmB;AAAA,IACpD;AAAA,IACA,iBAAiB,iBAAiB;AAAA,IAClC;AAAA,IACA,GAAG,mBAAmB;AAAA,MACpB;AAAA,IACF,GAAG,YAAY;AAAA,EACjB,CAAC,GAAG,CAAC,iBAAiB,iBAAiB,cAAc,sBAAsB,CAAC;AAC5E,QAAM,eAAe,gBAAgB,SAAS;AAC9C,QAAM,QAAQ,QAAQ,MAAMhC,aAAY;AAAA,IACtC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,GAAG,CAAC,UAAU,cAAc,kBAAkB,cAAc,eAAe,YAAY,CAAC;AACzF,MAAI,MAAuC;AACzC,QAAI,aAAa,WAAW,aAAa,YAAY,OAAO;AAC1D,aAAwC,QAAQ,yBAAyB,IAAI;AAAA,IAC/E;AAAA,EACF;AACA,eAAa,UAAU;AACvB,QAAM,gBAAgB,YAAY,MAAM;AACtC,UAAM,UAAU,SAAS,YAAY;AACrC,UAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAI,MAAM,UAAU,QAAQ;AAC1B,cAAQ,SAAS,MAAM,CAAC;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAMiC,cAAa,YAAY,MAAM;AACnC,UAAM,QAAQ,SAAS,YAAY,EAAE,SAAS;AAC9C,QAAI,MAAM,UAAU,kBAAkB;AACpC,aAAO;AAAA,IACT;AACA,QAAI,MAAM,UAAU,QAAQ;AAC1B,aAAO;AAAA,IACT;AACA,WAAO,MAAM;AAAA,EACf,GAAG,CAAC,CAAC;AACL,QAAM,eAAe,QAAQ,OAAO;AAAA,IAClC,YAAAA;AAAA,IACA,UAAU;AAAA,EACZ,IAAI,CAACA,aAAY,aAAa,CAAC;AAC/B,eAAa,YAAY;AACzB,QAAM,aAAa,YAAY,QAAM,aAAa,SAAS,YAAY,EAAE,SAAS,GAAG,EAAE,GAAG,CAAC,CAAC;AAC5F,QAAM,uBAAuB,YAAY,MAAM,kBAAkB,SAAS,YAAY,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC;AACvG,QAAM,aAAa,QAAQ,OAAO;AAAA,IAChC,SAAS;AAAA,IACT,OAAO;AAAA,IACP;AAAA,IACA,SAAS;AAAA,IACT,mBAAmB;AAAA,IACnB;AAAA,IACA;AAAA,EACF,IAAI,CAAC,WAAW,kBAAkB,+BAA+B,cAAc,YAAY,sBAAsB,QAAQ,CAAC;AAC1H,mBAAiB;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe,WAAW;AAAA,IAC1B,sBAAsB,MAAM,yBAAyB;AAAA,EACvD,CAAC;AACD,8BAAU,MAAM;AACd,WAAO;AAAA,EACT,GAAG,CAAC,aAAa,CAAC;AAClB,SAAO,aAAA5D,QAAM,cAAc,WAAW,UAAU;AAAA,IAC9C,OAAO;AAAA,EACT,GAAG,aAAAA,QAAM,cAAc,kBAAU;AAAA,IAC/B,SAAS;AAAA,IACT;AAAA,EACF,GAAG,MAAM,QAAQ,CAAC;AACpB;AAEA,SAAS,qBAAqB;AAC5B,SAAO,aAAAA,QAAM,MAAM;AACrB;AAEA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,YAAY,mBAAmB;AACrC,QAAM2D,+BAA8B,MAAM,+BAA+B,OAAO;AAChF,SAAO,aAAA3D,QAAM,cAAc,eAAe,MAAM,kBAAgB,aAAAA,QAAM,cAAc,KAAK;AAAA,IACvF,OAAO,MAAM;AAAA,IACb;AAAA,IACA;AAAA,IACA,6BAA6B2D;AAAA,IAC7B,sBAAsB,MAAM;AAAA,IAC5B,SAAS,MAAM;AAAA,IACf,iBAAiB,MAAM;AAAA,IACvB,mBAAmB,MAAM;AAAA,IACzB,aAAa,MAAM;AAAA,IACnB,cAAc,MAAM;AAAA,IACpB,WAAW,MAAM;AAAA,IACjB,qBAAqB,MAAM;AAAA,EAC7B,GAAG,MAAM,QAAQ,CAAC;AACpB;AAEA,IAAM,gBAAgB;AAAA,EACpB,UAAU;AAAA,EACV,eAAe;AACjB;AACA,IAAM,wBAAwB,CAAC,2BAA2B,aAAa;AACrE,MAAI,UAAU;AACZ,WAAO,YAAY,KAAK,SAAS,QAAQ;AAAA,EAC3C;AACA,MAAI,2BAA2B;AAC7B,WAAO,YAAY;AAAA,EACrB;AACA,SAAO,YAAY;AACrB;AACA,IAAM,qBAAqB,CAAC,aAAa,oBAAoB;AAC3D,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,SAAO,kBAAkB,QAAQ,QAAQ,OAAO,QAAQ,QAAQ;AAClE;AACA,IAAM,2BAA2B,cAAY;AAC3C,MAAI,SAAS,sBAAsB,MAAM;AACvC,WAAO,SAAS;AAAA,EAClB;AACA,SAAO,SAAS,SAAS;AAC3B;AACA,SAAS,iBAAiB,UAAU;AAClC,QAAM,YAAY,SAAS;AAC3B,QAAM,MAAM,UAAU;AACtB,QAAM;AAAA,IACJ,QAAA7C;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,cAAc,QAAQ,WAAW;AACvC,QAAM,gBAAgB,yBAAyB,QAAQ;AACvD,QAAM,kBAAkB,QAAQ,QAAQ;AACxC,QAAM,YAAY,kBAAkB,WAAW,KAAKA,SAAQ,WAAW,IAAI,WAAW,OAAOA,OAAM;AACnG,QAAM+C,SAAQ;AAAA,IACZ,UAAU;AAAA,IACV,KAAK,IAAI,UAAU;AAAA,IACnB,MAAM,IAAI,UAAU;AAAA,IACpB,WAAW;AAAA,IACX,OAAO,IAAI,UAAU;AAAA,IACrB,QAAQ,IAAI,UAAU;AAAA,IACtB,YAAY,sBAAsB,eAAe,QAAQ;AAAA,IACzD;AAAA,IACA,SAAS,mBAAmB,aAAa,eAAe;AAAA,IACxD,QAAQ,kBAAkB,cAAc,gBAAgB,cAAc;AAAA,IACtE,eAAe;AAAA,EACjB;AACA,SAAOA;AACT;AACA,SAAS,kBAAkB,WAAW;AACpC,SAAO;AAAA,IACL,WAAW,WAAW,OAAO,UAAU,MAAM;AAAA,IAC7C,YAAY,UAAU,4BAA4B,SAAY;AAAA,EAChE;AACF;AACA,SAAS,WAAW,QAAQ;AAC1B,SAAO,OAAO,SAAS,aAAa,iBAAiB,MAAM,IAAI,kBAAkB,MAAM;AACzF;AAEA,SAAS,eAAe,YAAY,IAAI,eAAe,QAAQ;AAC7D,QAAM,iBAAiB,OAAO,iBAAiB,EAAE;AACjD,QAAM,YAAY,GAAG,sBAAsB;AAC3C,QAAM,SAAS,aAAa,WAAW,cAAc;AACrD,QAAM,OAAO,WAAW,QAAQ,YAAY;AAC5C,QAAMC,eAAc;AAAA,IAClB;AAAA,IACA,SAAS,GAAG,QAAQ,YAAY;AAAA,IAChC,SAAS,eAAe;AAAA,EAC1B;AACA,QAAM,aAAa;AAAA,IACjB,GAAG,OAAO,UAAU;AAAA,IACpB,GAAG,OAAO,UAAU;AAAA,EACtB;AACA,QAAM,YAAY;AAAA,IAChB;AAAA,IACA,aAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,MAAM;AACnC,QAAM,WAAW,YAAY,WAAW;AACxC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,QAAQ,OAAO;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,4BAA4B,WAAW,uBAAuB,CAAC;AACpE,QAAMC,gBAAe,YAAY,kBAAgB;AAC/C,UAAM,KAAK,gBAAgB;AAC3B,KAAC,KAAK,OAAwChE,WAAU,OAAO,yCAAyC,IAAIA,WAAU,IAAI;AAC1H,WAAO,eAAe,YAAY,IAAI,YAAY;AAAA,EACpD,GAAG,CAAC,YAAY,eAAe,CAAC;AAChC,QAAM,QAAQ,QAAQ,OAAO;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAAgE;AAAA,EACF,IAAI,CAAC,YAAYA,eAAc,SAAS,QAAQ,CAAC;AACjD,QAAM,mBAAe,qBAAO,KAAK;AACjC,QAAM,wBAAoB,qBAAO,IAAI;AACrC,4BAA0B,MAAM;AAC9B,aAAS,UAAU,SAAS,aAAa,OAAO;AAChD,WAAO,MAAM,SAAS,UAAU,WAAW,aAAa,OAAO;AAAA,EACjE,GAAG,CAAC,SAAS,SAAS,CAAC;AACvB,4BAA0B,MAAM;AAC9B,QAAI,kBAAkB,SAAS;AAC7B,wBAAkB,UAAU;AAC5B;AAAA,IACF;AACA,UAAM,OAAO,aAAa;AAC1B,iBAAa,UAAU;AACvB,aAAS,UAAU,OAAO,OAAO,IAAI;AAAA,EACvC,GAAG,CAAC,OAAO,SAAS,SAAS,CAAC;AAChC;AAEA,IAAI,mBAAmB,aAAA/D,QAAM,cAAc,IAAI;AAE/C,SAAS,qBAAqB,IAAI;AAChC,IAAE,MAAM,cAAc,EAAE,KAAK,OAAwCD,WAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,GAKrF,IAAIA,WAAU,IAAI;AACrB;AAEA,SAAS,gBAAgB,OAAO,WAAW,QAAQ;AACjD,qBAAmB,MAAM;AACvB,aAASuC,QAAO0B,KAAI;AAClB,aAAO,iBAAiBA,GAAE;AAAA,IAC5B;AACA,UAAM,KAAK,MAAM;AACjB,KAAC,KAAK,OAAwCjE,WAAU,OAAO,kCAAkC,IAAIA,WAAU,KAAK,IAAI;AACxH,MAAE,OAAO,OAAO,YAAY,OAAwCA,WAAU,OAAO;AAAA,yBAChE,OAAO,EAAE,aAAa,EAAE,GAAG,IAAIA,WAAU,KAAK,IAAI;AACvE,KAAC,OAAO,UAAU,MAAM,KAAK,IAAI,OAAwCA,WAAU,OAAO,GAAGuC,QAAO,EAAE,CAAC,iCAAiC,IAAIvC,WAAU,KAAK,IAAI;AAC/J,QAAI,MAAM,OAAO,SAAS,YAAY;AACpC;AAAA,IACF;AACA,yBAAqB,OAAO,CAAC;AAC7B,QAAI,MAAM,WAAW;AACnB,OAAC,eAAe,WAAW,EAAE,IAAI,OAAwCA,WAAU,OAAO,GAAGuC,QAAO,EAAE,CAAC,6BAA6B,IAAIvC,WAAU,KAAK,IAAI;AAAA,IAC7J;AAAA,EACF,CAAC;AACH;AACA,SAAS,uBAAuB,SAAS;AACvC,SAAO,MAAM;AACX,UAAM,iBAAa,qBAAO,OAAO;AACjC,uBAAmB,MAAM;AACvB,QAAE,YAAY,WAAW,WAAW,OAAwCA,WAAU,OAAO,4DAA4D,IAAIA,WAAU,KAAK,IAAI;AAAA,IAClL,GAAG,CAAC,OAAO,CAAC;AAAA,EACd,CAAC;AACH;AAEA,SAAS,mBAAmB,SAAS;AACnC,QAAM,aAAS,yBAAW,OAAO;AACjC,GAAC,SAAS,OAAwCA,WAAU,OAAO,iCAAiC,IAAIA,WAAU,IAAI;AACtH,SAAO;AACT;AAEA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,eAAe;AACvB;AACA,IAAM,YAAY,WAAS;AACzB,QAAMyC,WAAM,qBAAO,IAAI;AACvB,QAAM,SAAS,YAAY,CAAC,KAAK,SAAS;AACxC,IAAAA,KAAI,UAAU;AAAA,EAChB,GAAG,CAAC,CAAC;AACL,QAAM,SAAS,YAAY,MAAMA,KAAI,SAAS,CAAC,CAAC;AAChD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB,UAAU;AACjC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB,gBAAgB;AACvC,QAAM,aAAa,QAAQ,OAAO;AAAA,IAChC,IAAI,MAAM;AAAA,IACV,OAAO,MAAM;AAAA,IACb;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM,aAAa,MAAM,OAAO,MAAM,WAAW,CAAC;AACvD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,uBAAuB;AAAA,EACzB,IAAI;AACJ,kBAAgB,OAAO,WAAW,MAAM;AACxC,yBAAuB,OAAO;AAC9B,MAAI,CAAC,SAAS;AACZ,UAAM,eAAe,QAAQ,OAAO;AAAA,MAClC;AAAA,MACA;AAAA,MACA,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,CAAC,YAAY,UAAU,QAAQ,4BAA4B,yBAAyB,SAAS,CAAC;AAClG,0BAAsB,YAAY;AAAA,EACpC;AACA,QAAM,kBAAkB,QAAQ,MAAM,YAAY;AAAA,IAChD,UAAU;AAAA,IACV,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,qCAAqC;AAAA,IACrC,mCAAmC;AAAA,IACnC,WAAW;AAAA,IACX,aAAa;AAAA,EACf,IAAI,MAAM,CAAC,WAAW,+BAA+B,aAAa,SAAS,CAAC;AAC5E,QAAM,YAAY,YAAY,WAAS;AACrC,QAAI,OAAO,SAAS,YAAY;AAC9B;AAAA,IACF;AACA,QAAI,CAAC,OAAO,UAAU;AACpB;AAAA,IACF;AACA,QAAI,MAAM,iBAAiB,aAAa;AACtC;AAAA,IACF;AACA,oCAAU,2BAA2B;AAAA,EACvC,GAAG,CAAC,6BAA6B,MAAM,CAAC;AACxC,QAAM,WAAW,QAAQ,MAAM;AAC7B,UAAMqB,SAAQ,WAAW,MAAM;AAC/B,UAAM,kBAAkB,OAAO,SAAS,cAAc,OAAO,WAAW,YAAY;AACpF,UAAM,SAAS;AAAA,MACb,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,iCAAiC;AAAA,QACjC,yBAAyB;AAAA,QACzB,OAAAA;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,iBAAiB,aAAa,QAAQ,WAAW,MAAM,CAAC;AACvE,QAAM,SAAS,QAAQ,OAAO;AAAA,IAC5B,aAAa,WAAW;AAAA,IACxB,MAAM,WAAW;AAAA,IACjB,QAAQ;AAAA,MACN,OAAO,WAAW;AAAA,MAClB,aAAa,WAAW;AAAA,IAC1B;AAAA,EACF,IAAI,CAAC,WAAW,aAAa,WAAW,IAAI,WAAW,OAAO,WAAW,IAAI,CAAC;AAC9E,SAAO,aAAA7D,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,SAAS,UAAU,OAAO,UAAU,MAAM,CAAC;AAC9F;AAEA,IAAI,gBAAgB,CAAC,GAAG,MAAM,MAAM;AAEpC,IAAI,8BAA8B,YAAU;AAC1C,QAAM;AAAA,IACJ,SAAAE;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,aAAa;AACf,WAAO,YAAY;AAAA,EACrB;AACA,MAAIA,UAAS;AACX,WAAOA,SAAQ;AAAA,EACjB;AACA,SAAO;AACT;AAEA,IAAM,2BAA2B,YAAU;AACzC,SAAO,OAAO,UAAU,OAAO,QAAQ,cAAc;AACvD;AACA,IAAM,2BAA2B,YAAU;AACzC,SAAO,OAAO,MAAM,OAAO,GAAG,SAAS,YAAY,OAAO,GAAG,QAAQ,cAAc;AACrF;AACA,SAAS,uBAAuB;AAC9B,QAAM,iBAAiB,WAAW,CAAC,GAAG,OAAO;AAAA,IAC3C;AAAA,IACA;AAAA,EACF,EAAE;AACF,QAAM,sBAAsB,WAAW,CAAC,MAAM,SAAS,eAAe,MAAM,cAAc,MAAM,WAAW,UAAU;AAAA,IACnH,YAAY;AAAA,IACZ;AAAA,IACA,iBAAiB,QAAQ,QAAQ;AAAA,IACjC,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,EACpB,EAAE;AACF,QAAM,mBAAmB,WAAW,CAACY,SAAQ,MAAM,WAAW,SAAS,eAAe,MAAM,cAAc,MAAM,qBAAqB,UAAU;AAAA,IAC7I,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,oBAAoB,MAAM,SAAS,cAAc,aAAa,IAAI;AAAA,IAC9E;AAAA,EACF,EAAE;AACF,QAAM,WAAW,CAAC,OAAO,aAAa;AACpC,QAAI,WAAW,KAAK,GAAG;AACrB,UAAI,MAAM,SAAS,UAAU,OAAO,SAAS,aAAa;AACxD,eAAO;AAAA,MACT;AACA,YAAMA,UAAS,MAAM,QAAQ,OAAO;AACpC,YAAM,YAAY,MAAM,WAAW,WAAW,SAAS,WAAW;AAClE,YAAM,eAAe,kBAAkB,MAAM,MAAM;AACnD,YAAM,cAAc,yBAAyB,MAAM,MAAM;AACzD,YAAM,qBAAqB,MAAM;AACjC,aAAO,iBAAiB,eAAeA,QAAO,GAAGA,QAAO,CAAC,GAAG,MAAM,cAAc,WAAW,SAAS,SAAS,cAAc,aAAa,kBAAkB;AAAA,IAC5J;AACA,QAAI,MAAM,UAAU,kBAAkB;AACpC,YAAM,YAAY,MAAM;AACxB,UAAI,UAAU,OAAO,gBAAgB,SAAS,aAAa;AACzD,eAAO;AAAA,MACT;AACA,YAAM,UAAU,SAAS;AACzB,YAAM,YAAY,MAAM,WAAW,WAAW,SAAS,WAAW;AAClE,YAAM,SAAS,UAAU;AACzB,YAAM,OAAO,OAAO;AACpB,YAAM,eAAe,4BAA4B,MAAM;AACvD,YAAM,cAAc,yBAAyB,MAAM;AACnD,YAAM,WAAW,MAAM;AACvB,YAAM,WAAW;AAAA,QACf;AAAA,QACA,OAAO,OAAO;AAAA,QACd,QAAQ,MAAM;AAAA,QACd,SAAS,cAAc,QAAQ,QAAQ,OAAO;AAAA,QAC9C,OAAO,cAAc,QAAQ,MAAM,OAAO;AAAA,MAC5C;AACA,aAAO;AAAA,QACL,QAAQ;AAAA,UACN,MAAM;AAAA,UACN,QAAQ,MAAM;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,oBAAoB;AAAA,UACpB,UAAU,oBAAoB,MAAM,SAAS,cAAc,aAAa,QAAQ;AAAA,QAClF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,mBAAmB,MAAM;AACrD,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,SAAS;AAAA,IACT,eAAe;AAAA,IACf,MAAM;AAAA,IACN,cAAc;AAAA,IACd;AAAA,IACA,aAAa;AAAA,EACf;AACF;AACA,IAAM,SAAS;AAAA,EACb,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,2BAA2B;AAAA,IAC3B,UAAU,qBAAqB,IAAI;AAAA,EACrC;AACF;AACA,SAAS,uBAAuB;AAC9B,QAAM,iBAAiB,WAAW,CAAC,GAAG,OAAO;AAAA,IAC3C;AAAA,IACA;AAAA,EACF,EAAE;AACF,QAAM,sBAAsB,WAAW,oBAAoB;AAC3D,QAAM,mBAAmB,WAAW,CAACA,SAAQ,mBAAmB,MAAM,+BAA+B;AAAA,IACnG,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,QAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,oBAAoB,gBAAgB;AAAA,IAChD;AAAA,EACF,EAAE;AACF,QAAM,cAAc,sBAAoB;AACtC,WAAO,mBAAmB,iBAAiB,QAAQ,kBAAkB,IAAI,IAAI;AAAA,EAC/E;AACA,QAAM,WAAW,CAAC,OAAO,YAAY,QAAQ,kBAAkB;AAC7D,UAAM,qBAAqB,OAAO,UAAU,QAAQ,KAAK;AACzD,UAAM,+BAA+B,QAAQ,cAAc,iBAAiB,cAAc,SAAS,KAAK,CAAC;AACzG,UAAMZ,WAAU,cAAc,MAAM;AACpC,UAAM,mBAAmBA,YAAWA,SAAQ,gBAAgB,QAAQ,aAAa;AACjF,QAAI,CAAC,oBAAoB;AACvB,UAAI,CAAC,8BAA8B;AACjC,eAAO,YAAY,gBAAgB;AAAA,MACrC;AACA,UAAI,OAAO,UAAU,UAAU,KAAK,GAAG;AACrC,eAAO;AAAA,MACT;AACA,YAAM,SAAS,OAAO,cAAc,YAAY,KAAK;AACrD,YAAMY,UAAS,eAAe,OAAO,GAAG,OAAO,CAAC;AAChD,aAAO,iBAAiBA,SAAQ,kBAAkB,IAAI;AAAA,IACxD;AACA,QAAI,8BAA8B;AAChC,aAAO,YAAY,gBAAgB;AAAA,IACrC;AACA,UAAM,aAAa,OAAO,YAAY;AACtC,UAAMA,UAAS,eAAe,WAAW,GAAG,WAAW,CAAC;AACxD,WAAO,iBAAiBA,SAAQ,kBAAkB,mBAAmB,aAAa;AAAA,EACpF;AACA,QAAM,WAAW,CAAC,OAAO,aAAa;AACpC,QAAI,WAAW,KAAK,GAAG;AACrB,UAAI,MAAM,SAAS,UAAU,OAAO,SAAS,aAAa;AACxD,eAAO;AAAA,MACT;AACA,aAAO,SAAS,SAAS,aAAa,MAAM,SAAS,UAAU,IAAI,MAAM,QAAQ,MAAM,aAAa;AAAA,IACtG;AACA,QAAI,MAAM,UAAU,kBAAkB;AACpC,YAAM,YAAY,MAAM;AACxB,UAAI,UAAU,OAAO,gBAAgB,SAAS,aAAa;AACzD,eAAO;AAAA,MACT;AACA,aAAO,SAAS,SAAS,aAAa,UAAU,OAAO,aAAa,UAAU,QAAQ,UAAU,aAAa;AAAA,IAC/G;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,wBAAwB,MAAM;AAClC,QAAM,mBAAmB,qBAAqB;AAC9C,QAAM,oBAAoB,qBAAqB;AAC/C,QAAM,WAAW,CAAC,OAAO,aAAa,iBAAiB,OAAO,QAAQ,KAAK,kBAAkB,OAAO,QAAQ,KAAK;AACjH,SAAO;AACT;AACA,IAAM,uBAAuB;AAAA,EAC3B;AACF;AACA,IAAM,qBAAqB,gBAAQ,uBAAuB,sBAAsB,MAAM;AAAA,EACpF,SAAS;AAAA,EACT,oBAAoB;AACtB,CAAC,EAAE,SAAS;AAEZ,SAAS,iBAAiB,OAAO;AAC/B,QAAM,mBAAmB,mBAAmB,gBAAgB;AAC5D,QAAM,kBAAkB,iBAAiB;AACzC,MAAI,oBAAoB,MAAM,eAAe,CAAC,MAAM,SAAS;AAC3D,WAAO;AAAA,EACT;AACA,SAAO,aAAAd,QAAM,cAAc,oBAAoB,KAAK;AACtD;AACA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,YAAY,OAAO,MAAM,mBAAmB,YAAY,CAAC,MAAM,iBAAiB;AACtF,QAAM,6BAA6B,QAAQ,MAAM,iCAAiC;AAClF,QAAM,0BAA0B,QAAQ,MAAM,uBAAuB;AACrE,SAAO,aAAAA,QAAM,cAAc,kBAAkB,SAAS,CAAC,GAAG,OAAO;AAAA,IAC/D,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACJ;AAEA,IAAM,UAAU,UAAQ,WAAS,SAAS;AAC1C,IAAM,WAAW,QAAQ,QAAQ;AACjC,IAAM,SAAS,QAAQ,MAAM;AAC7B,IAAM,YAAY,QAAQ,SAAS;AACnC,IAAM,WAAW,CAAC,UAAU,OAAO,GAAG,SAAS,SAAS,KAAK,GAAG,SAAS,SAAS;AAClF,IAAM,SAAS,CAAC,UAAU,OAAO,GAAG,SAAS,SAAS,KAAK,GAAG,SAAS,SAAS;AAChF,IAAM,sBAAsB,QAAM;AAChC,QAAM6D,SAAQ,OAAO,iBAAiB,EAAE;AACxC,QAAM,WAAW;AAAA,IACf,WAAWA,OAAM;AAAA,IACjB,WAAWA,OAAM;AAAA,EACnB;AACA,SAAO,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,MAAM;AAClE;AACA,IAAM,mBAAmB,MAAM;AAC7B,MAAI,OAAuC;AACzC,WAAO;AAAA,EACT;AACA,QAAM,OAAO,eAAe;AAC5B,QAAM,OAAO,SAAS;AACtB,GAAC,OAAO,OAAwC9D,WAAU,IAAIA,WAAU,IAAI;AAC5E,MAAI,CAAC,oBAAoB,IAAI,GAAG;AAC9B,WAAO;AAAA,EACT;AACA,QAAM,YAAY,OAAO,iBAAiB,IAAI;AAC9C,QAAM,eAAe;AAAA,IACnB,WAAW,UAAU;AAAA,IACrB,WAAW,UAAU;AAAA,EACvB;AACA,MAAI,OAAO,cAAc,SAAS,GAAG;AACnC,WAAO;AAAA,EACT;AACA,SAAwC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAS/C,IAAI;AACL,SAAO;AACT;AACA,IAAM,uBAAuB,QAAM;AACjC,MAAI,MAAM,MAAM;AACd,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,MAAM;AACxB,WAAO,iBAAiB,IAAI,KAAK;AAAA,EACnC;AACA,MAAI,OAAO,SAAS,iBAAiB;AACnC,WAAO;AAAA,EACT;AACA,MAAI,CAAC,oBAAoB,EAAE,GAAG;AAC5B,WAAO,qBAAqB,GAAG,aAAa;AAAA,EAC9C;AACA,SAAO;AACT;AAEA,IAAI,iCAAiC,gBAAc;AACjD,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AACA,QAAM,sBAAsB,qBAAqB,WAAW,aAAa;AACzE,MAAI,CAAC,qBAAqB;AACxB;AAAA,EACF;AACA,SAAwC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAM/C,IAAI;AACP;AAEA,IAAI,YAAY,SAAO;AAAA,EACrB,GAAG,GAAG;AAAA,EACN,GAAG,GAAG;AACR;AAEA,IAAM,aAAa,QAAM;AACvB,MAAI,CAAC,IAAI;AACP,WAAO;AAAA,EACT;AACA,QAAM8D,SAAQ,OAAO,iBAAiB,EAAE;AACxC,MAAIA,OAAM,aAAa,SAAS;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,WAAW,GAAG,aAAa;AACpC;AACA,IAAI,SAAS,CAAA5D,WAAS;AACpB,QAAM,oBAAoB,qBAAqBA,MAAK;AACpD,QAAM,gBAAgB,WAAWA,MAAK;AACtC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAAW;AACF,MAAM;AACJ,QAAM,SAAS,MAAM;AACnB,QAAI,CAACA,UAAS;AACZ,aAAO;AAAA,IACT;AACA,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,IACV,IAAIA;AACJ,UAAM,YAAY,aAAa;AAAA,MAC7B,cAAc,WAAW;AAAA,MACzB,aAAa,WAAW;AAAA,MACxB,QAAQ,YAAY,WAAW;AAAA,MAC/B,OAAO,YAAY,WAAW;AAAA,IAChC,CAAC;AACD,WAAO;AAAA,MACL,eAAeA,SAAQ,KAAK;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,mBAAmBA,SAAQ;AAAA,MAC3B,QAAQ;AAAA,QACN,SAASA,SAAQ;AAAA,QACjB,SAASA,SAAQ;AAAA,QACjB,KAAK;AAAA,QACL,MAAM;AAAA,UACJ,OAAO;AAAA,UACP,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AACH,QAAM,OAAO,cAAc,aAAa,WAAW;AACnD,QAAM,UAAU,WAAW;AAAA,IACzB;AAAA,IACA,iBAAiB;AAAA,IACjB;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,YAAY;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,YAAY,CAAC,WAAW,sBAAsB;AAClD,QAAM,OAAO,OAAO,SAAS;AAC7B,MAAI,CAAC,mBAAmB;AACtB,WAAO;AAAA,EACT;AACA,MAAI,cAAc,mBAAmB;AACnC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,KAAK,WAAW,MAAM,kBAAkB;AACpD,QAAM,OAAO,KAAK,WAAW,OAAO,kBAAkB;AACtD,QAAM,SAAS,MAAM,kBAAkB;AACvC,QAAM,QAAQ,OAAO,kBAAkB;AACvC,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAY,OAAO,YAAY,KAAK,MAAM;AAChD,QAAM,SAAS,UAAU;AAAA,IACvB;AAAA,IACA,QAAQ,KAAK;AAAA,IACb,QAAQ,KAAK;AAAA,IACb,SAAS,KAAK;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AACA,IAAI,eAAe,CAAC;AAAA,EAClB,KAAA4B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,oBAAoB,IAAI;AAC9B,QAAM,SAAS,UAAUA,MAAK,iBAAiB;AAC/C,QAAM,OAAO,WAAW,QAAQ,YAAY;AAC5C,QAAM5B,YAAW,MAAM;AACrB,QAAI,CAAC,mBAAmB;AACtB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,OAAO,iBAAiB;AAC5C,UAAM,aAAa;AAAA,MACjB,cAAc,kBAAkB;AAAA,MAChC,aAAa,kBAAkB;AAAA,IACjC;AACA,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,MAAM,WAAW,aAAa,YAAY;AAAA,MAC1C,QAAQ,UAAU,iBAAiB;AAAA,MACnC;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG;AACH,QAAM,YAAY,sBAAsB;AAAA,IACtC;AAAA,IACA,WAAW,CAAC;AAAA,IACZ;AAAA,IACA,eAAe,IAAI;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAAA;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,IAAM,YAAY;AAAA,EAChB,SAAS;AACX;AACA,IAAM,UAAU;AAAA,EACd,SAAS;AACX;AACA,IAAI,qBAAqB,aAAW,QAAQ,2BAA2B,YAAY;AAEnF,IAAM,+BAA+B,cAAY,YAAY,SAAS,IAAI,qBAAqB;AAC/F,SAAS,sBAAsB,MAAM;AACnC,QAAM,uBAAmB,qBAAO,IAAI;AACpC,QAAM,aAAa,mBAAmB,UAAU;AAChD,QAAM,WAAW,YAAY,WAAW;AACxC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,cAAc,YAAY,IAAI;AACpC,QAAM,aAAa,QAAQ,OAAO;AAAA,IAChC,IAAI,KAAK;AAAA,IACT,MAAM,KAAK;AAAA,IACX,MAAM,KAAK;AAAA,EACb,IAAI,CAAC,KAAK,aAAa,KAAK,MAAM,KAAK,IAAI,CAAC;AAC5C,QAAM,6BAAyB,qBAAO,UAAU;AAChD,QAAM,uBAAuB,QAAQ,MAAM,WAAW,CAAC,GAAG,MAAM;AAC9D,KAAC,iBAAiB,UAAU,OAAwCb,WAAU,OAAO,sCAAsC,IAAIA,WAAU,IAAI;AAC7I,UAAM6B,UAAS;AAAA,MACb;AAAA,MACA;AAAA,IACF;AACA,YAAQ,sBAAsB,WAAW,IAAIA,OAAM;AAAA,EACrD,CAAC,GAAG,CAAC,WAAW,IAAI,OAAO,CAAC;AAC5B,QAAM,mBAAmB,YAAY,MAAM;AACzC,UAAM,WAAW,iBAAiB;AAClC,QAAI,CAAC,YAAY,CAAC,SAAS,IAAI,mBAAmB;AAChD,aAAO;AAAA,IACT;AACA,WAAO,UAAU,SAAS,IAAI,iBAAiB;AAAA,EACjD,GAAG,CAAC,CAAC;AACL,QAAM,eAAe,YAAY,MAAM;AACrC,UAAMA,UAAS,iBAAiB;AAChC,yBAAqBA,QAAO,GAAGA,QAAO,CAAC;AAAA,EACzC,GAAG,CAAC,kBAAkB,oBAAoB,CAAC;AAC3C,QAAM,uBAAuB,QAAQ,MAAM,qBAAQ,YAAY,GAAG,CAAC,YAAY,CAAC;AAChF,QAAM,kBAAkB,YAAY,MAAM;AACxC,UAAM,WAAW,iBAAiB;AAClC,UAAMhB,WAAU,6BAA6B,QAAQ;AACrD,MAAE,YAAYA,YAAW,OAAwCb,WAAU,OAAO,+CAA+C,IAAIA,WAAU,IAAI;AACnJ,UAAM,UAAU,SAAS;AACzB,QAAI,QAAQ,0BAA0B;AACpC,mBAAa;AACb;AAAA,IACF;AACA,yBAAqB;AAAA,EACvB,GAAG,CAAC,sBAAsB,YAAY,CAAC;AACvC,QAAM,6BAA6B,YAAY,CAAC,cAAc,YAAY;AACxE,KAAC,CAAC,iBAAiB,UAAU,OAAwCA,WAAU,OAAO,sDAAsD,IAAIA,WAAU,IAAI;AAC9J,UAAM,WAAW,YAAY;AAC7B,UAAMyC,OAAM,SAAS,gBAAgB;AACrC,KAACA,OAAM,OAAwCzC,WAAU,OAAO,wCAAwC,IAAIA,WAAU,IAAI;AAC1H,UAAM,MAAM,OAAOyC,IAAG;AACtB,UAAM,WAAW;AAAA,MACf,KAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA,eAAe;AAAA,IACjB;AACA,qBAAiB,UAAU;AAC3B,UAAM,YAAY,aAAa;AAAA,MAC7B,KAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,SAAS;AAAA,MACpB,gBAAgB,SAAS;AAAA,MACzB,kBAAkB,SAAS;AAAA,MAC3B,mBAAmB,CAAC,SAAS;AAAA,IAC/B,CAAC;AACD,UAAM,aAAa,IAAI;AACvB,QAAI,YAAY;AACd,iBAAW,aAAa,gBAAgB,WAAW,WAAW,SAAS;AACvE,iBAAW,iBAAiB,UAAU,iBAAiB,mBAAmB,SAAS,aAAa,CAAC;AACjG,UAAI,MAAuC;AACzC,uCAA+B,UAAU;AAAA,MAC3C;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,WAAW,YAAY,iBAAiB,WAAW,CAAC;AACnE,QAAM,yBAAyB,YAAY,MAAM;AAC/C,UAAM,WAAW,iBAAiB;AAClC,UAAM5B,WAAU,6BAA6B,QAAQ;AACrD,MAAE,YAAYA,YAAW,OAAwCb,WAAU,OAAO,iFAAiF,IAAIA,WAAU,IAAI;AACrL,WAAO,UAAUa,QAAO;AAAA,EAC1B,GAAG,CAAC,CAAC;AACL,QAAM,cAAc,YAAY,MAAM;AACpC,UAAM,WAAW,iBAAiB;AAClC,KAAC,WAAW,OAAwCb,WAAU,OAAO,sCAAsC,IAAIA,WAAU,IAAI;AAC7H,UAAMa,WAAU,6BAA6B,QAAQ;AACrD,qBAAiB,UAAU;AAC3B,QAAI,CAACA,UAAS;AACZ;AAAA,IACF;AACA,yBAAqB,OAAO;AAC5B,IAAAA,SAAQ,gBAAgB,gBAAgB,SAAS;AACjD,IAAAA,SAAQ,oBAAoB,UAAU,iBAAiB,mBAAmB,SAAS,aAAa,CAAC;AAAA,EACnG,GAAG,CAAC,iBAAiB,oBAAoB,CAAC;AAC1C,QAAMgB,UAAS,YAAY,YAAU;AACnC,UAAM,WAAW,iBAAiB;AAClC,KAAC,WAAW,OAAwC7B,WAAU,OAAO,qCAAqC,IAAIA,WAAU,IAAI;AAC5H,UAAMa,WAAU,6BAA6B,QAAQ;AACrD,KAACA,WAAU,OAAwCb,WAAU,OAAO,sDAAsD,IAAIA,WAAU,IAAI;AAC5I,IAAAa,SAAQ,aAAa,OAAO;AAC5B,IAAAA,SAAQ,cAAc,OAAO;AAAA,EAC/B,GAAG,CAAC,CAAC;AACL,QAAM,YAAY,QAAQ,MAAM;AAC9B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAAgB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,aAAa,4BAA4B,wBAAwBA,OAAM,CAAC;AAC5E,QAAM,QAAQ,QAAQ,OAAO;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,WAAW,YAAY,QAAQ,CAAC;AACrC,4BAA0B,MAAM;AAC9B,2BAAuB,UAAU,MAAM;AACvC,aAAS,UAAU,SAAS,KAAK;AACjC,WAAO,MAAM;AACX,UAAI,iBAAiB,SAAS;AAC5B,eAAwC,QAAQ,4EAA4E,IAAI;AAChI,oBAAY;AAAA,MACd;AACA,eAAS,UAAU,WAAW,KAAK;AAAA,IACrC;AAAA,EACF,GAAG,CAAC,WAAW,YAAY,aAAa,OAAO,SAAS,SAAS,SAAS,CAAC;AAC3E,4BAA0B,MAAM;AAC9B,QAAI,CAAC,iBAAiB,SAAS;AAC7B;AAAA,IACF;AACA,YAAQ,yBAAyB,uBAAuB,QAAQ,IAAI,CAAC,KAAK,cAAc;AAAA,EAC1F,GAAG,CAAC,KAAK,gBAAgB,OAAO,CAAC;AACjC,4BAA0B,MAAM;AAC9B,QAAI,CAAC,iBAAiB,SAAS;AAC7B;AAAA,IACF;AACA,YAAQ,gCAAgC,uBAAuB,QAAQ,IAAI,KAAK,gBAAgB;AAAA,EAClG,GAAG,CAAC,KAAK,kBAAkB,OAAO,CAAC;AACrC;AAEA,SAAS,OAAO;AAAC;AACjB,IAAM,QAAQ;AAAA,EACZ,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQxB;AACV;AACA,IAAM,UAAU,CAAC;AAAA,EACf;AAAA,EACA,aAAA0D;AAAA,EACA;AACF,MAAM;AACJ,MAAI,wBAAwB;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,YAAY,SAAS;AACvB,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,QAAQA,aAAY,OAAO,UAAU;AAAA,IACrC,OAAOA,aAAY,OAAO,UAAU;AAAA,IACpC,QAAQA,aAAY,OAAO;AAAA,EAC7B;AACF;AACA,IAAM,WAAW,CAAC;AAAA,EAChB;AAAA,EACA,aAAAA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,OAAO,QAAQ;AAAA,IACnB;AAAA,IACA,aAAAA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL,SAASA,aAAY;AAAA,IACrB,WAAW;AAAA,IACX,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,IACb,WAAW,KAAK,OAAO;AAAA,IACvB,aAAa,KAAK,OAAO;AAAA,IACzB,cAAc,KAAK,OAAO;AAAA,IAC1B,YAAY,KAAK,OAAO;AAAA,IACxB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,eAAe;AAAA,IACf,YAAY,YAAY,SAAS,YAAY,cAAc;AAAA,EAC7D;AACF;AACA,IAAM,cAAc,WAAS;AAC3B,QAAM,0BAAsB,qBAAO,IAAI;AACvC,QAAM,2BAA2B,YAAY,MAAM;AACjD,QAAI,CAAC,oBAAoB,SAAS;AAChC;AAAA,IACF;AACA,iBAAa,oBAAoB,OAAO;AACxC,wBAAoB,UAAU;AAAA,EAChC,GAAG,CAAC,CAAC;AACL,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,wBAAwB,yBAAyB,QAAI,uBAAS,MAAM,YAAY,MAAM;AAC7F,8BAAU,MAAM;AACd,QAAI,CAAC,wBAAwB;AAC3B,aAAO;AAAA,IACT;AACA,QAAI,YAAY,QAAQ;AACtB,+BAAyB;AACzB,gCAA0B,KAAK;AAC/B,aAAO;AAAA,IACT;AACA,QAAI,oBAAoB,SAAS;AAC/B,aAAO;AAAA,IACT;AACA,wBAAoB,UAAU,WAAW,MAAM;AAC7C,0BAAoB,UAAU;AAC9B,gCAA0B,KAAK;AAAA,IACjC,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,SAAS,wBAAwB,wBAAwB,CAAC;AAC9D,QAAM,kBAAkB,YAAY,WAAS;AAC3C,QAAI,MAAM,iBAAiB,UAAU;AACnC;AAAA,IACF;AACA,oBAAgB;AAChB,QAAI,YAAY,SAAS;AACvB,cAAQ;AAAA,IACV;AAAA,EACF,GAAG,CAAC,SAAS,SAAS,eAAe,CAAC;AACtC,QAAMD,SAAQ,SAAS;AAAA,IACrB;AAAA,IACA,SAAS,MAAM;AAAA,IACf,aAAa,MAAM;AAAA,EACrB,CAAC;AACD,SAAO,aAAA7D,QAAM,cAAc,MAAM,YAAY,SAAS;AAAA,IACpD,OAAA6D;AAAA,IACA,mCAAmC;AAAA,IACnC,iBAAiB;AAAA,IACjB,KAAK,MAAM;AAAA,EACb,CAAC;AACH;AACA,IAAI,gBAAgB,aAAA7D,QAAM,KAAK,WAAW;AAE1C,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,UAAU;AAC1B;AACA,SAAS,UAAU,MAAM,QAAQ;AAC/B,SAAO,QAAQ,WAAS,MAAM,IAAI,CAAC;AACrC;AACA,IAAM,SAAS,CAAC,SAAS,SAAS;AAAA,EAChC;AACF,GAAG;AACD,GAAC,MAAM,cAAc,OAAwCD,WAAU,OAAO,yCAAyC,IAAIA,WAAU,IAAI;AACzI,IAAE,OAAO,MAAM,gBAAgB,YAAY,OAAwCA,WAAU,OAAO,2DAA2D,OAAO,MAAM,WAAW,GAAG,IAAIA,WAAU,IAAI;AAC9M,GAAG,SAAS,QAAQ;AAAA,EAClB;AACF,GAAG;AACD,GAAC,UAAU,MAAM,cAAc,IAAI,OAAwCA,WAAU,OAAO,kCAAkC,IAAIA,WAAU,IAAI;AAChJ,GAAC,UAAU,MAAM,gBAAgB,IAAI,OAAwCA,WAAU,OAAO,oCAAoC,IAAIA,WAAU,IAAI;AACpJ,GAAC,UAAU,MAAM,uBAAuB,IAAI,OAAwCA,WAAU,OAAO,2CAA2C,IAAIA,WAAU,IAAI;AACpK,GAAG,SAAS,IAAI;AAAA,EACd;AACF,GAAG;AACD,uBAAqB,gBAAgB,CAAC;AACxC,CAAC;AACD,IAAM,WAAW,CAAC,SAAS,YAAY;AAAA,EACrC;AAAA,EACA;AACF,GAAG;AACD,MAAI,CAAC,MAAM,aAAa;AACtB;AAAA,EACF;AACA,QAAMyC,OAAM,kBAAkB;AAC9B,MAAIA,MAAK;AACP;AAAA,EACF;AACA,SAAwC,QAAQ;AAAA,6CACL,MAAM,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,KAKzD,IAAI;AACT,CAAC;AACD,IAAM,UAAU,CAAC,SAAS,SAAS;AAAA,EACjC;AACF,GAAG;AACD,GAAC,MAAM,cAAc,OAAwCzC,WAAU,OAAO,sEAAsE,IAAIA,WAAU,IAAI;AACxK,GAAG,SAAS,iBAAiB;AAAA,EAC3B;AACF,GAAG;AACD,GAAC,CAAC,kBAAkB,IAAI,OAAwCA,WAAU,OAAO,iDAAiD,IAAIA,WAAU,IAAI;AACtJ,CAAC;AACD,SAAS,cAAc,MAAM;AAC3B,qBAAmB,MAAM;AACvB,cAAU,MAAM,MAAM;AACtB,QAAI,KAAK,MAAM,SAAS,YAAY;AAClC,gBAAU,MAAM,QAAQ;AAAA,IAC1B;AACA,QAAI,KAAK,MAAM,SAAS,WAAW;AACjC,gBAAU,MAAM,OAAO;AAAA,IACzB;AAAA,EACF,CAAC;AACH;AAEA,IAAM,eAAN,cAA2B,aAAAC,QAAM,cAAc;AAAA,EAC7C,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,SAAK,QAAQ;AAAA,MACX,WAAW,QAAQ,KAAK,MAAM,EAAE;AAAA,MAChC,MAAM,KAAK,MAAM;AAAA,MACjB,SAAS,KAAK,MAAM,iBAAiB,KAAK,MAAM,KAAK,SAAS;AAAA,IAChE;AACA,SAAK,UAAU,MAAM;AACnB,UAAI,KAAK,MAAM,YAAY,SAAS;AAClC;AAAA,MACF;AACA,WAAK,SAAS;AAAA,QACZ,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,yBAAyB,OAAO,OAAO;AAC5C,QAAI,CAAC,MAAM,eAAe;AACxB,aAAO;AAAA,QACL,WAAW,QAAQ,MAAM,EAAE;AAAA,QAC3B,MAAM,MAAM;AAAA,QACZ,SAAS;AAAA,MACX;AAAA,IACF;AACA,QAAI,MAAM,IAAI;AACZ,aAAO;AAAA,QACL,WAAW;AAAA,QACX,MAAM,MAAM;AAAA,QACZ,SAAS;AAAA,MACX;AAAA,IACF;AACA,QAAI,MAAM,WAAW;AACnB,aAAO;AAAA,QACL,WAAW;AAAA,QACX,MAAM,MAAM;AAAA,QACZ,SAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI,CAAC,KAAK,MAAM,WAAW;AACzB,aAAO;AAAA,IACT;AACA,UAAM,WAAW;AAAA,MACf,SAAS,KAAK;AAAA,MACd,MAAM,KAAK,MAAM;AAAA,MACjB,SAAS,KAAK,MAAM;AAAA,IACtB;AACA,WAAO,KAAK,MAAM,SAAS,QAAQ;AAAA,EACrC;AACF;AAEA,IAAM,YAAY,WAAS;AACzB,QAAM,iBAAa,yBAAW,UAAU;AACxC,GAAC,aAAa,OAAwCD,WAAU,OAAO,4BAA4B,IAAIA,WAAU,IAAI;AACrH,QAAM;AAAA,IACJ;AAAA,IACA,mBAAAkE;AAAA,EACF,IAAI;AACJ,QAAM,mBAAe,qBAAO,IAAI;AAChC,QAAM,qBAAiB,qBAAO,IAAI;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,yBAAAC;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,YAAY,MAAM,aAAa,SAAS,CAAC,CAAC;AAClE,QAAM,kBAAkB,YAAY,CAAC,QAAQ,SAAS;AACpD,iBAAa,UAAU;AAAA,EACzB,GAAG,CAAC,CAAC;AACL,QAAM,oBAAoB,YAAY,MAAM,eAAe,SAAS,CAAC,CAAC;AACtE,QAAM,oBAAoB,YAAY,CAAC,QAAQ,SAAS;AACtD,mBAAe,UAAU;AAAA,EAC3B,GAAG,CAAC,CAAC;AACL,gBAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,6BAA6B,YAAY,MAAM;AACnD,QAAID,mBAAkB,GAAG;AACvB,MAAAC,yBAAwB;AAAA,QACtB,WAAW,mBAAmB;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAACD,oBAAmBC,wBAAuB,CAAC;AAC/C,wBAAsB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAMJ,eAAc,QAAQ,MAAM,aAAA9D,QAAM,cAAc,cAAc;AAAA,IAClE,IAAI,MAAM;AAAA,IACV,eAAe,MAAM;AAAA,EACvB,GAAG,CAAC;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM,aAAAA,QAAM,cAAc,eAAe;AAAA,IACvC,aAAa;AAAA,IACb;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC,CAAC,GAAG,CAAC,WAAW,4BAA4B,MAAM,aAAa,MAAM,0BAA0B,iBAAiB,CAAC;AAClH,QAAM,WAAW,QAAQ,OAAO;AAAA,IAC9B,UAAU;AAAA,IACV,aAAA8D;AAAA,IACA,gBAAgB;AAAA,MACd,yBAAyB;AAAA,MACzB,iCAAiC;AAAA,IACnC;AAAA,EACF,IAAI,CAAC,WAAW,aAAaA,cAAa,eAAe,CAAC;AAC1D,QAAM,kBAAkB,WAAW,SAAS,SAAS,cAAc;AACnE,QAAM,mBAAmB,QAAQ,OAAO;AAAA,IACtC;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,aAAa,iBAAiB,IAAI,CAAC;AACxC,WAAS,WAAW;AAClB,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,aAAA9D,QAAM,cAAc,kBAAkB;AAAA,MACjD,aAAa,SAAS;AAAA,MACtB,OAAO,SAAS,OAAO;AAAA,MACvB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,yBAAyB;AAAA,MACzB,4BAA4B;AAAA,IAC9B,GAAG,CAAC,mBAAmB,sBAAsB,OAAO,mBAAmB,mBAAmB,QAAQ,CAAC;AACnG,WAAO,iBAAAmE,QAAS,aAAa,MAAM,qBAAqB,CAAC;AAAA,EAC3D;AACA,SAAO,aAAAnE,QAAM,cAAc,iBAAiB,UAAU;AAAA,IACpD,OAAO;AAAA,EACT,GAAG,SAAS,UAAU,QAAQ,GAAG,SAAS,CAAC;AAC7C;AAEA,SAAS,UAAU;AACjB,GAAC,SAAS,OAAO,OAAwCD,WAAU,OAAO,4BAA4B,IAAIA,WAAU,IAAI;AACxH,SAAO,SAAS;AAClB;AACA,IAAM,eAAe;AAAA,EACnB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,aAAa;AAAA,EACb,sBAAsB;AACxB;AACA,IAAM,+BAA+B,cAAY;AAC/C,MAAI,cAAc;AAAA,IAChB,GAAG;AAAA,EACL;AACA,MAAI;AACJ,OAAK,kBAAkB,cAAc;AACnC,QAAI,SAAS,cAAc,MAAM,QAAW;AAC1C,oBAAc;AAAA,QACZ,GAAG;AAAA,QACH,CAAC,cAAc,GAAG,aAAa,cAAc;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,CAAC,MAAM,aAAa,SAAS,SAAS,UAAU;AACvE,IAAM,eAAe,CAAC,UAAU,eAAe,WAAW,WAAW,SAAS,UAAU,EAAE;AAC1F,IAAM,sBAAsB,MAAM;AAChC,QAAM,oBAAoB;AAAA,IACxB,aAAa;AAAA,IACb,0BAA0B;AAAA,IAC1B,UAAU;AAAA,MACR,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,IACtB;AAAA,IACA,UAAU;AAAA,EACZ;AACA,QAAM,uBAAuB;AAAA,IAC3B,GAAG;AAAA,IACH,0BAA0B;AAAA,EAC5B;AACA,QAAM,qBAAqB,WAAW,iBAAe;AAAA,IACnD,aAAa,WAAW;AAAA,IACxB,MAAM,WAAW;AAAA,IACjB,QAAQ;AAAA,MACN,OAAO,WAAW;AAAA,MAClB,aAAa,WAAW;AAAA,IAC1B;AAAA,EACF,EAAE;AACF,QAAM,cAAc,WAAW,CAAC,IAAI,WAAW,2BAA2B,yBAAyB,UAAU,gBAAgB;AAC3H,UAAM,cAAc,SAAS,WAAW;AACxC,UAAM,SAAS,SAAS,WAAW,gBAAgB;AACnD,QAAI,QAAQ;AACV,YAAM,WAAW,cAAc;AAAA,QAC7B,QAAQ;AAAA,QACR,UAAU,mBAAmB,SAAS,UAAU;AAAA,MAClD,IAAI;AACJ,YAAMqE,YAAW;AAAA,QACf,gBAAgB;AAAA,QAChB,kBAAkB,4BAA4B,cAAc;AAAA,QAC5D,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,MACtB;AACA,aAAO;AAAA,QACL,aAAa,SAAS;AAAA,QACtB,0BAA0B;AAAA,QAC1B,UAAAA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AACA,QAAI,CAAC,yBAAyB;AAC5B,aAAO;AAAA,IACT;AACA,UAAM,WAAW;AAAA,MACf,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,IACtB;AACA,WAAO;AAAA,MACL,aAAa,SAAS;AAAA,MACtB,0BAA0B;AAAA,MAC1B;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF,CAAC;AACD,QAAM,WAAW,CAAC,OAAO,aAAa;AACpC,UAAM,2BAA2B,6BAA6B,QAAQ;AACtE,UAAM,KAAK,yBAAyB;AACpC,UAAM,OAAO,yBAAyB;AACtC,UAAM,YAAY,CAAC,yBAAyB;AAC5C,UAAM,cAAc,yBAAyB;AAC7C,QAAI,WAAW,KAAK,GAAG;AACrB,YAAM,WAAW,MAAM;AACvB,UAAI,CAAC,eAAe,MAAM,QAAQ,GAAG;AACnC,eAAO;AAAA,MACT;AACA,YAAM,WAAW,aAAa,UAAU,MAAM,UAAU;AACxD,YAAM,iBAAiB,kBAAkB,MAAM,MAAM,MAAM;AAC3D,aAAO,YAAY,IAAI,WAAW,gBAAgB,gBAAgB,UAAU,WAAW;AAAA,IACzF;AACA,QAAI,MAAM,UAAU,kBAAkB;AACpC,YAAM,YAAY,MAAM;AACxB,UAAI,CAAC,eAAe,MAAM,UAAU,QAAQ,GAAG;AAC7C,eAAO;AAAA,MACT;AACA,YAAM,WAAW,aAAa,UAAU,UAAU,MAAM,UAAU;AAClE,aAAO,YAAY,IAAI,WAAW,4BAA4B,UAAU,MAAM,MAAM,IAAI,kBAAkB,UAAU,MAAM,MAAM,IAAI,UAAU,WAAW;AAAA,IAC3J;AACA,QAAI,MAAM,UAAU,UAAU,MAAM,aAAa,CAAC,MAAM,aAAa;AACnE,YAAM,YAAY,MAAM;AACxB,UAAI,CAAC,eAAe,MAAM,UAAU,QAAQ,GAAG;AAC7C,eAAO;AAAA,MACT;AACA,YAAM,UAAU,kBAAkB,UAAU,MAAM,MAAM;AACxD,YAAM,eAAe,QAAQ,UAAU,OAAO,MAAM,UAAU,OAAO,GAAG,SAAS,SAAS;AAC1F,YAAM,SAAS,UAAU,SAAS,UAAU,OAAO;AACnD,UAAI,SAAS;AACX,eAAO,eAAe,oBAAoB;AAAA,MAC5C;AACA,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,qBAAqB;AAAA,EACzB;AACF;AACA,IAAM,qBAAqB,gBAAQ,qBAAqB,oBAAoB,CAAC,YAAY,eAAe,aAAa;AACnH,SAAO;AAAA,IACL,GAAG,6BAA6B,QAAQ;AAAA,IACxC,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF,GAAG;AAAA,EACD,SAAS;AAAA,EACT,oBAAoB;AACtB,CAAC,EAAE,SAAS;", "names": ["getRect", "expand", "shrink", "shift", "createBox", "parse", "suffix", "getWindowScroll", "offset", "withScroll", "scroll", "calculateBox", "getBox", "rafSchd", "wrapperFn", "shared", "isProduction", "invariant", "React", "start", "combine", "update", "noSpacing", "droppable", "isEqual", "draggable", "withDroppableDisplacement", "process", "isVisible", "newIndex", "closest", "withDroppableScroll", "offset", "distance", "getDisplacedBy", "subject", "home", "dropPending", "withMaxScroll", "getWindowScroll", "isActive", "execute", "add", "flush", "drop", "createStore", "scroll", "updateDroppableIsEnabled", "updateDroppableIsCombineEnabled", "updateDroppableScroll", "scrollDroppable", "dimension", "required", "scrollWindow", "change", "move", "prefix", "getSelector", "ref", "register", "focus", "tryGiveFocus", "tryShiftRecord", "tryRestoreFocusRecorded", "tryRecordFoc<PERSON>", "clean", "patch", "listenForCapture", "bindCapturingEvents", "startPendingDrag", "getPhase", "setPhase", "startDragging", "lift", "finish", "tryAbandonLock", "tryReleaseLock", "dragHandleUsageInstructions", "isDragging", "style", "placeholder", "getDimension", "id", "isMovementAllowed", "updateViewportMaxScroll", "ReactDOM", "snapshot"]}