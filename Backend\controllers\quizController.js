const Quiz = require('../models/Quiz');
const Course = require('../models/Course');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');
const { calculateQuizScore, validateQuizConfiguration } = require('../utils/quizScoring');

// Create a new quiz
exports.createQuiz = catchAsync(async (req, res, next) => {
  const { courseId } = req.params;
  const course = await Course.findById(courseId);

  if (!course) {
    return next(new AppError('Course not found', 404));
  }

  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to create quiz for this course', 403));
  }

  // Validate quiz configuration
  const validation = validateQuizConfiguration(req.body);
  if (!validation.isValid) {
    return next(new AppError(`Quiz validation failed: ${validation.errors.join(', ')}`, 400));
  }

  const quiz = await Quiz.create({
    ...req.body,
    course: courseId
  });

  course.quizzes.push(quiz._id);
  await course.save();

  res.status(201).json({
    status: 'success',
    data: quiz
  });
});

// Get a quiz
exports.getQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId)
    .populate('course', 'title');

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: quiz
  });
});

// Update a quiz
exports.updateQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const course = await Course.findById(quiz.course);
  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to update this quiz', 403));
  }

  // Validate quiz configuration
  const validation = validateQuizConfiguration(req.body);
  if (!validation.isValid) {
    return next(new AppError(`Quiz validation failed: ${validation.errors.join(', ')}`, 400));
  }

  const updatedQuiz = await Quiz.findByIdAndUpdate(
    req.params.quizId,
    req.body,
    { new: true, runValidators: true }
  );

  res.status(200).json({
    status: 'success',
    data: updatedQuiz
  });
});

// Delete a quiz
exports.deleteQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const course = await Course.findById(quiz.course);
  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to delete this quiz', 403));
  }

  await Quiz.findByIdAndDelete(req.params.quizId);

  // Remove quiz reference from course
  course.quizzes = course.quizzes.filter(q => q.toString() !== quiz._id.toString());
  await course.save();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Submit quiz attempt with partial credit support
exports.submitQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  // Check attempt limits
  const userAttempts = quiz.attempts.filter(
    attempt => attempt.user.toString() === req.user._id.toString()
  );

  if (userAttempts.length >= quiz.maxAttempts) {
    return res.status(400).json({
      status: 'error',
      message: `Maximum attempts (${quiz.maxAttempts}) reached for this quiz`
    });
  }

  const { answers } = req.body;

  // Use the scoring utility to calculate results
  const scoringResult = calculateQuizScore(quiz, answers);
  const {
    totalScore,
    totalPossibleScore,
    percentage,
    passed,
    certificateEligible,
    questionResults
  } = scoringResult;

  // Save the attempt
  const attemptNumber = userAttempts.length + 1;
  quiz.attempts.push({
    user: req.user._id,
    score: totalScore,
    totalPossibleScore: totalPossibleScore,
    percentage: percentage,
    passed: passed,
    certificateEligible: certificateEligible,
    answers: questionResults,
    completedAt: new Date(),
    attemptNumber: attemptNumber
  });

  await quiz.save();

  // Return detailed results including correct answers
  res.status(200).json({
    status: 'success',
    data: {
      score: totalScore,
      totalPoints: totalPossibleScore,
      percentage: percentage,
      passed: passed,
      certificateEligible: certificateEligible,
      answers: questionResults,
      attemptNumber: attemptNumber,
      remainingAttempts: quiz.maxAttempts - attemptNumber,
      questions: quiz.questions.map(q => ({
        id: q._id,
        question: q.question,
        options: q.options,
        correctAnswer: q.correctAnswer,
        answerOptions: q.answerOptions,
        questionType: q.questionType,
        points: q.points || 1
      }))
    }
  });
});

// Get quiz results
exports.getQuizResults = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId)
    .populate('attempts.user', 'name email');

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const userAttempt = quiz.attempts.find(
    attempt => attempt.user._id.toString() === req.user._id.toString()
  );

  if (!userAttempt) {
    return next(new AppError('No attempt found for this quiz', 404));
  }

  res.status(200).json({
    status: 'success',
    data: userAttempt
  });
});

// Get quiz statistics (for admins/educators)
exports.getQuizStatistics = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId)
    .populate('attempts.user', 'name email')
    .populate('course', 'title creator');

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  // Check authorization
  if (quiz.course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to view quiz statistics', 403));
  }

  // Calculate statistics
  const totalAttempts = quiz.attempts.length;
  const uniqueUsers = [...new Set(quiz.attempts.map(attempt => attempt.user._id.toString()))].length;
  const passedAttempts = quiz.attempts.filter(attempt => attempt.passed).length;
  const certificateEligibleAttempts = quiz.attempts.filter(attempt => attempt.certificateEligible).length;

  const averageScore = totalAttempts > 0 ?
    quiz.attempts.reduce((sum, attempt) => sum + (attempt.percentage || 0), 0) / totalAttempts : 0;

  // Question-wise statistics
  const questionStats = quiz.questions.map((question, index) => {
    const questionAttempts = quiz.attempts.filter(attempt =>
      attempt.answers && attempt.answers[index]
    );

    const correctAnswers = questionAttempts.filter(attempt =>
      attempt.answers[index] && attempt.answers[index].isCorrect
    ).length;

    const averageCredit = questionAttempts.length > 0 ?
      questionAttempts.reduce((sum, attempt) =>
        sum + (attempt.answers[index]?.creditPercentage || 0), 0
      ) / questionAttempts.length : 0;

    return {
      questionIndex: index,
      question: question.question,
      totalAttempts: questionAttempts.length,
      correctAnswers,
      correctPercentage: questionAttempts.length > 0 ? (correctAnswers / questionAttempts.length) * 100 : 0,
      averageCredit: Math.round(averageCredit * 100) / 100
    };
  });

  res.status(200).json({
    status: 'success',
    data: {
      quiz: {
        id: quiz._id,
        title: quiz.title,
        course: quiz.course.title
      },
      statistics: {
        totalAttempts,
        uniqueUsers,
        passedAttempts,
        passRate: totalAttempts > 0 ? (passedAttempts / totalAttempts) * 100 : 0,
        certificateEligibleAttempts,
        certificateEligibilityRate: totalAttempts > 0 ? (certificateEligibleAttempts / totalAttempts) * 100 : 0,
        averageScore: Math.round(averageScore * 100) / 100
      },
      questionStatistics: questionStats,
      recentAttempts: quiz.attempts
        .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt))
        .slice(0, 10)
        .map(attempt => ({
          user: attempt.user,
          score: attempt.score,
          percentage: attempt.percentage,
          passed: attempt.passed,
          certificateEligible: attempt.certificateEligible,
          completedAt: attempt.completedAt,
          attemptNumber: attempt.attemptNumber
        }))
    }
  });
});