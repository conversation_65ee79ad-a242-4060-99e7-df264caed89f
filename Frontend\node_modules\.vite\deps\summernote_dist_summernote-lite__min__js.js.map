{"version": 3, "sources": ["../../summernote/dist/summernote-lite.min.js"], "sourcesContent": ["/*! Summernote v0.9.1 | (c) 2013~ Hackerwins and contributors | MIT license */\n!function(t,e){if(\"object\"==typeof exports&&\"object\"==typeof module)module.exports=e(require(\"jquery\"));else if(\"function\"==typeof define&&define.amd)define([\"jquery\"],e);else{var o=\"object\"==typeof exports?e(require(\"jquery\")):e(t.jQuery);for(var n in o)(\"object\"==typeof exports?exports:t)[n]=o[n]}}(self,(t=>(()=>{\"use strict\";var e={7e3:(t,e,o)=>{var n=o(8938),i=o.n(n);i().summernote=i().summernote||{lang:{}},i().extend(!0,i().summernote.lang,{\"en-US\":{font:{bold:\"Bold\",italic:\"Italic\",underline:\"Underline\",clear:\"Remove Font Style\",height:\"Line Height\",name:\"Font Family\",strikethrough:\"Strikethrough\",subscript:\"Subscript\",superscript:\"Superscript\",size:\"Font Size\",sizeunit:\"Font Size Unit\"},image:{image:\"Picture\",insert:\"Insert Image\",resizeFull:\"Resize full\",resizeHalf:\"Resize half\",resizeQuarter:\"Resize quarter\",resizeNone:\"Original size\",floatLeft:\"Float Left\",floatRight:\"Float Right\",floatNone:\"Remove float\",shapeRounded:\"Shape: Rounded\",shapeCircle:\"Shape: Circle\",shapeThumbnail:\"Shape: Thumbnail\",shapeNone:\"Shape: None\",dragImageHere:\"Drag image or text here\",dropImage:\"Drop image or Text\",selectFromFiles:\"Select from files\",maximumFileSize:\"Maximum file size\",maximumFileSizeError:\"Maximum file size exceeded.\",url:\"Image URL\",remove:\"Remove Image\",original:\"Original\"},video:{video:\"Video\",videoLink:\"Video Link\",insert:\"Insert Video\",url:\"Video URL\",providers:\"(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion, Youku, Peertube)\"},link:{link:\"Link\",insert:\"Insert Link\",unlink:\"Unlink\",edit:\"Edit\",textToDisplay:\"Text to display\",url:\"To what URL should this link go?\",openInNewWindow:\"Open in new window\"},table:{table:\"Table\",addRowAbove:\"Add row above\",addRowBelow:\"Add row below\",addColLeft:\"Add column left\",addColRight:\"Add column right\",delRow:\"Delete row\",delCol:\"Delete column\",delTable:\"Delete table\"},hr:{insert:\"Insert Horizontal Rule\"},style:{style:\"Style\",p:\"Normal\",blockquote:\"Quote\",pre:\"Code\",h1:\"Header 1\",h2:\"Header 2\",h3:\"Header 3\",h4:\"Header 4\",h5:\"Header 5\",h6:\"Header 6\"},lists:{unordered:\"Unordered list\",ordered:\"Ordered list\"},options:{help:\"Help\",fullscreen:\"Full Screen\",codeview:\"Code View\"},paragraph:{paragraph:\"Paragraph\",outdent:\"Outdent\",indent:\"Indent\",left:\"Align left\",center:\"Align center\",right:\"Align right\",justify:\"Justify full\"},color:{recent:\"Recent Color\",more:\"More Color\",background:\"Background Color\",foreground:\"Text Color\",transparent:\"Transparent\",setTransparent:\"Set transparent\",reset:\"Reset\",resetToDefault:\"Reset to default\",cpSelect:\"Select\"},shortcut:{shortcuts:\"Keyboard shortcuts\",close:\"Close\",textFormatting:\"Text formatting\",action:\"Action\",paragraphFormatting:\"Paragraph formatting\",documentStyle:\"Document Style\",extraKeys:\"Extra keys\"},help:{escape:\"Escape\",insertParagraph:\"Insert Paragraph\",undo:\"Undo the last command\",redo:\"Redo the last command\",tab:\"Tab\",untab:\"Untab\",bold:\"Set a bold style\",italic:\"Set a italic style\",underline:\"Set a underline style\",strikethrough:\"Set a strikethrough style\",removeFormat:\"Clean a style\",justifyLeft:\"Set left align\",justifyCenter:\"Set center align\",justifyRight:\"Set right align\",justifyFull:\"Set full align\",insertUnorderedList:\"Toggle unordered list\",insertOrderedList:\"Toggle ordered list\",outdent:\"Outdent on current paragraph\",indent:\"Indent on current paragraph\",formatPara:\"Change current block's format as a paragraph(P tag)\",formatH1:\"Change current block's format as H1\",formatH2:\"Change current block's format as H2\",formatH3:\"Change current block's format as H3\",formatH4:\"Change current block's format as H4\",formatH5:\"Change current block's format as H5\",formatH6:\"Change current block's format as H6\",insertHorizontalRule:\"Insert horizontal rule\",\"linkDialog.show\":\"Show Link Dialog\"},history:{undo:\"Undo\",redo:\"Redo\"},specialChar:{specialChar:\"SPECIAL CHARACTERS\",select:\"Select Special characters\"},output:{noSelection:\"No Selection Made!\"}}})},8938:e=>{e.exports=t}},o={};function n(t){var i=o[t];if(void 0!==i)return i.exports;var r=o[t]={exports:{}};return e[t](r,r.exports,n),r.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var i=n(8938),r=n.n(i),a=(n(7e3),[\"sans-serif\",\"serif\",\"monospace\",\"cursive\",\"fantasy\"]);function s(t){return-1===r().inArray(t.toLowerCase(),a)?\"'\".concat(t,\"'\"):t}var l,c=navigator.userAgent,u=/MSIE|Trident/i.test(c);if(u){var d=/MSIE (\\d+[.]\\d+)/.exec(c);d&&(l=parseFloat(d[1])),(d=/Trident\\/.*rv:([0-9]{1,}[.0-9]{0,})/.exec(c))&&(l=parseFloat(d[1]))}var f=/Edge\\/\\d+/.test(c),h=\"ontouchstart\"in window||navigator.MaxTouchPoints>0||navigator.msMaxTouchPoints>0,p=u?\"DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted\":\"input\";const m={isMac:navigator.appVersion.indexOf(\"Mac\")>-1,isMSIE:u,isEdge:f,isFF:!f&&/firefox/i.test(c),isPhantom:/PhantomJS/i.test(c),isWebkit:!f&&/webkit/i.test(c),isChrome:!f&&/chrome/i.test(c),isSafari:!f&&/safari/i.test(c)&&!/chrome/i.test(c),browserVersion:l,isSupportTouch:h,isFontInstalled:function(){var t=document.createElement(\"canvas\"),e=t.getContext(\"2d\",{willReadFrequently:!0});function o(t,o){return e.clearRect(0,0,40,20),e.font=\"20px \"+s(t)+', \"'+o+'\"',e.fillText(\"mw\",20,10),e.getImageData(0,0,40,20).data.join(\"\")}return t.width=40,t.height=20,e.textAlign=\"center\",e.fillStyle=\"black\",e.textBaseline=\"middle\",function(t){var e=\"Comic Sans MS\"===t?\"Courier New\":\"Comic Sans MS\";return o(e,e)!==o(t,e)}}(),isW3CRangeSupport:!!document.createRange,inputEventName:p,genericFontFamilies:a,validFontName:s};var v=0;const g={eq:function(t){return function(e){return t===e}},eq2:function(t,e){return t===e},peq2:function(t){return function(e,o){return e[t]===o[t]}},ok:function(){return!0},fail:function(){return!1},self:function(t){return t},not:function(t){return function(){return!t.apply(t,arguments)}},and:function(t,e){return function(o){return t(o)&&e(o)}},invoke:function(t,e){return function(){return t[e].apply(t,arguments)}},resetUniqueId:function(){v=0},uniqueId:function(t){var e=++v+\"\";return t?t+e:e},rect2bnd:function(t){var e=r()(document);return{top:t.top+e.scrollTop(),left:t.left+e.scrollLeft(),width:t.right-t.left,height:t.bottom-t.top}},invertObject:function(t){var e={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[t[o]]=o);return e},namespaceToCamel:function(t,e){return(e=e||\"\")+t.split(\".\").map((function(t){return t.substring(0,1).toUpperCase()+t.substring(1)})).join(\"\")},debounce:function(t,e,o){var n;return function(){var i=this,r=arguments,a=o&&!n;clearTimeout(n),n=setTimeout((function(){n=null,o||t.apply(i,r)}),e),a&&t.apply(i,r)}},isValidUrl:function(t){return/[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)/gi.test(t)}};function b(t){return t[0]}function y(t){return t[t.length-1]}function k(t){return t.slice(1)}function w(t,e){if(t&&t.length&&e){if(t.indexOf)return-1!==t.indexOf(e);if(t.contains)return t.contains(e)}return!1}const C={head:b,last:y,initial:function(t){return t.slice(0,t.length-1)},tail:k,prev:function(t,e){if(t&&t.length&&e){var o=t.indexOf(e);return-1===o?null:t[o-1]}return null},next:function(t,e){if(t&&t.length&&e){var o=t.indexOf(e);return-1===o?null:t[o+1]}return null},find:function(t,e){for(var o=0,n=t.length;o<n;o++){var i=t[o];if(e(i))return i}},contains:w,all:function(t,e){for(var o=0,n=t.length;o<n;o++)if(!e(t[o]))return!1;return!0},sum:function(t,e){return e=e||g.self,t.reduce((function(t,o){return t+e(o)}),0)},from:function(t){for(var e=[],o=t.length,n=-1;++n<o;)e[n]=t[n];return e},isEmpty:function(t){return!t||!t.length},clusterBy:function(t,e){return t.length?k(t).reduce((function(t,o){var n=y(t);return e(y(n),o)?n[n.length]=o:t[t.length]=[o],t}),[[b(t)]]):[]},compact:function(t){for(var e=[],o=0,n=t.length;o<n;o++)t[o]&&e.push(t[o]);return e},unique:function(t){for(var e=[],o=0,n=t.length;o<n;o++)w(e,t[o])||e.push(t[o]);return e}};var S=String.fromCharCode(160);function x(t){return t&&r()(t).hasClass(\"note-editable\")}function T(t){return t=t.toUpperCase(),function(e){return e&&e.nodeName.toUpperCase()===t}}function E(t){return t&&3===t.nodeType}function P(t){return t&&/^BR|^IMG|^HR|^IFRAME|^BUTTON|^INPUT|^AUDIO|^VIDEO|^EMBED/.test(t.nodeName.toUpperCase())}function N(t){return!x(t)&&(t&&/^DIV|^P|^LI|^H[1-7]/.test(t.nodeName.toUpperCase()))}var $=T(\"PRE\"),I=T(\"LI\");var R=T(\"TABLE\"),A=T(\"DATA\");function L(t){return!(B(t)||F(t)||D(t)||N(t)||R(t)||j(t)||A(t))}function F(t){return t&&/^UL|^OL/.test(t.nodeName.toUpperCase())}var D=T(\"HR\");function H(t){return t&&/^TD|^TH/.test(t.nodeName.toUpperCase())}var j=T(\"BLOCKQUOTE\");function B(t){return H(t)||j(t)||x(t)}var O=T(\"A\");var z=T(\"BODY\");var M=m.isMSIE&&m.browserVersion<11?\"&nbsp;\":\"<br>\";function U(t){return E(t)?t.nodeValue.length:t?t.childNodes.length:0}function W(t){var e=U(t);return 0===e||(!E(t)&&1===e&&t.innerHTML===M||!(!C.all(t.childNodes,E)||\"\"!==t.innerHTML))}function K(t){P(t)||U(t)||(t.innerHTML=M)}function q(t,e){for(;t;){if(e(t))return t;if(x(t))break;t=t.parentNode}return null}function V(t,e){e=e||g.fail;var o=[];return q(t,(function(t){return x(t)||o.push(t),e(t)})),o}function _(t,e){e=e||g.fail;for(var o=[];t&&!e(t);)o.push(t),t=t.nextSibling;return o}function G(t,e){var o=e.nextSibling,n=e.parentNode;return o?n.insertBefore(t,o):n.appendChild(t),t}function Z(t,e,o){return r().each(e,(function(e,n){!o&&I(t)&&null===t.firstChild&&F(n)&&t.appendChild(ut(\"br\")),t.appendChild(n)})),t}function Y(t){return 0===t.offset}function X(t){return t.offset===U(t.node)}function Q(t){return Y(t)||X(t)}function J(t,e){for(;t&&t!==e;){if(0!==et(t))return!1;t=t.parentNode}return!0}function tt(t,e){if(!e)return!1;for(;t&&t!==e;){if(et(t)!==U(t.parentNode)-1)return!1;t=t.parentNode}return!0}function et(t){for(var e=0;t=t.previousSibling;)e+=1;return e}function ot(t){return!!(t&&t.childNodes&&t.childNodes.length)}function nt(t,e){var o,n;if(0===t.offset){if(x(t.node))return null;o=t.node.parentNode,n=et(t.node)}else ot(t.node)?n=U(o=t.node.childNodes[t.offset-1]):(o=t.node,n=e?0:t.offset-1);return{node:o,offset:n}}function it(t,e){var o,n;if(U(t.node)===t.offset){if(x(t.node))return null;var i=at(t.node);i?(o=i,n=0):(o=t.node.parentNode,n=et(t.node)+1)}else ot(t.node)?(o=t.node.childNodes[t.offset],n=0):(o=t.node,n=e?U(t.node):t.offset+1);return{node:o,offset:n}}function rt(t,e){var o,n=0;if(U(t.node)===t.offset){if(x(t.node))return null;o=t.node.parentNode,n=et(t.node)+1,x(o)&&(o=t.node.nextSibling,n=0)}else ot(t.node)?(o=t.node.childNodes[t.offset],n=0):(o=t.node,n=e?U(t.node):t.offset+1);return{node:o,offset:n}}function at(t){if(t.nextSibling&&t.parent===t.nextSibling.parent)return E(t.nextSibling)?t.nextSibling:at(t.nextSibling)}function st(t,e){return t.node===e.node&&t.offset===e.offset}function lt(t,e){var o=e&&e.isSkipPaddingBlankHTML,n=e&&e.isNotSplitEdgePoint,i=e&&e.isDiscardEmptySplits;if(i&&(o=!0),Q(t)&&(E(t.node)||n)){if(Y(t))return t.node;if(X(t))return t.node.nextSibling}if(E(t.node))return t.node.splitText(t.offset);var r=_(t.node.childNodes[t.offset]),a=G(t.node.cloneNode(!1),t.node);return Z(a,r),o||(K(t.node),K(a)),i&&(W(t.node)&&dt(t.node),W(a))?(dt(a),t.node.nextSibling):a}function ct(t,e,o){var n=V(e.node,g.eq(t));if(!n.length)return null;if(1===n.length)return lt(e,o);if(n.length>2){var i=n.slice(0,n.length-1).find((function(t){return t.nextSibling}));if(i&&0!=e.offset&&X(e)){var r,a=i.nextSibling;1==a.nodeType?(n=V(r=a.childNodes[0],g.eq(t)),e={node:r,offset:0}):3!=a.nodeType||a.data.match(/[\\n\\r]/g)||(n=V(r=a,g.eq(t)),e={node:r,offset:0})}}return n.reduce((function(t,n){return t===e.node&&(t=lt(e,o)),lt({node:n,offset:t?et(t):U(n)},o)}))}function ut(t){return document.createElement(t)}function dt(t,e){if(t&&t.parentNode){if(t.removeNode)return t.removeNode(e);var o=t.parentNode;if(!e){for(var n=[],i=0,r=t.childNodes.length;i<r;i++)n.push(t.childNodes[i]);for(var a=0,s=n.length;a<s;a++)o.insertBefore(n[a],t)}o.removeChild(t)}}var ft=T(\"TEXTAREA\");function ht(t,e){var o=ft(t[0])?t.val():t.html();return e?o.replace(/[\\n\\r]/g,\"\"):o}const pt={NBSP_CHAR:S,ZERO_WIDTH_NBSP_CHAR:\"\\ufeff\",blank:M,emptyPara:\"<p>\".concat(M,\"</p>\"),makePredByNodeName:T,isEditable:x,isControlSizing:function(t){return t&&r()(t).hasClass(\"note-control-sizing\")},isText:E,isElement:function(t){return t&&1===t.nodeType},isVoid:P,isPara:N,isPurePara:function(t){return N(t)&&!I(t)},isHeading:function(t){return t&&/^H[1-7]/.test(t.nodeName.toUpperCase())},isInline:L,isBlock:g.not(L),isBodyInline:function(t){return L(t)&&!q(t,N)},isBody:z,isParaInline:function(t){return L(t)&&!!q(t,N)},isPre:$,isList:F,isTable:R,isData:A,isCell:H,isBlockquote:j,isBodyContainer:B,isAnchor:O,isDiv:T(\"DIV\"),isLi:I,isBR:T(\"BR\"),isSpan:T(\"SPAN\"),isB:T(\"B\"),isU:T(\"U\"),isS:T(\"S\"),isI:T(\"I\"),isImg:T(\"IMG\"),isTextarea:ft,deepestChildIsEmpty:function(t){do{if(null===t.firstElementChild||\"\"===t.firstElementChild.innerHTML)break}while(t=t.firstElementChild);return W(t)},isEmpty:W,isEmptyAnchor:g.and(O,W),isClosestSibling:function(t,e){return t.nextSibling===e||t.previousSibling===e},withClosestSiblings:function(t,e){e=e||g.ok;var o=[];return t.previousSibling&&e(t.previousSibling)&&o.push(t.previousSibling),o.push(t),t.nextSibling&&e(t.nextSibling)&&o.push(t.nextSibling),o},nodeLength:U,isLeftEdgePoint:Y,isRightEdgePoint:X,isEdgePoint:Q,isLeftEdgeOf:J,isRightEdgeOf:tt,isLeftEdgePointOf:function(t,e){return Y(t)&&J(t.node,e)},isRightEdgePointOf:function(t,e){return X(t)&&tt(t.node,e)},prevPoint:nt,nextPoint:it,nextPointWithEmptyNode:rt,isSamePoint:st,isVisiblePoint:function(t){if(E(t.node)||!ot(t.node)||W(t.node))return!0;var e=t.node.childNodes[t.offset-1],o=t.node.childNodes[t.offset];return!((e&&!P(e)||o&&!P(o))&&!R(o))},prevPointUntil:function(t,e){for(;t;){if(e(t))return t;t=nt(t)}return null},nextPointUntil:function(t,e){for(;t;){if(e(t))return t;t=it(t)}return null},isCharPoint:function(t){if(!E(t.node))return!1;var e=t.node.nodeValue.charAt(t.offset-1);return e&&\" \"!==e&&e!==S},isSpacePoint:function(t){if(!E(t.node))return!1;var e=t.node.nodeValue.charAt(t.offset-1);return\" \"===e||e===S},walkPoint:function(t,e,o,n){for(var i=t;i&&i.node&&(o(i),!st(i,e));){i=rt(i,n&&t.node!==i.node&&e.node!==i.node)}},ancestor:q,singleChildAncestor:function(t,e){for(t=t.parentNode;t&&1===U(t);){if(e(t))return t;if(x(t))break;t=t.parentNode}return null},listAncestor:V,lastAncestor:function(t,e){var o=V(t);return C.last(o.filter(e))},listNext:_,listPrev:function(t,e){e=e||g.fail;for(var o=[];t&&!e(t);)o.push(t),t=t.previousSibling;return o},listDescendant:function(t,e){var o=[];return e=e||g.ok,function n(i){t!==i&&e(i)&&o.push(i);for(var r=0,a=i.childNodes.length;r<a;r++)n(i.childNodes[r])}(t),o},commonAncestor:function(t,e){for(var o=V(t),n=e;n;n=n.parentNode)if(o.indexOf(n)>-1)return n;return null},wrap:function(t,e){var o=t.parentNode,n=r()(\"<\"+e+\">\")[0];return o.insertBefore(n,t),n.appendChild(t),n},insertAfter:G,appendChildNodes:Z,position:et,hasChildren:ot,makeOffsetPath:function(t,e){return V(e,g.eq(t)).map(et).reverse()},fromOffsetPath:function(t,e){for(var o=t,n=0,i=e.length;n<i;n++)o=o.childNodes.length<=e[n]?o.childNodes[o.childNodes.length-1]:o.childNodes[e[n]];return o},splitTree:ct,splitPoint:function(t,e){var o,n,i=e?N:B,r=V(t.node,i),a=C.last(r)||t.node;i(a)?(o=r[r.length-2],n=a):n=(o=a).parentNode;var s=o&&ct(o,t,{isSkipPaddingBlankHTML:e,isNotSplitEdgePoint:e});return s||n!==t.node||(s=t.node.childNodes[t.offset]),{rightNode:s,container:n}},create:ut,createText:function(t){return document.createTextNode(t)},remove:dt,removeWhile:function(t,e){for(;t&&!x(t)&&e(t);){var o=t.parentNode;dt(t),t=o}},replace:function(t,e){if(t.nodeName.toUpperCase()===e.toUpperCase())return t;var o=ut(e);return t.style.cssText&&(o.style.cssText=t.style.cssText),Z(o,C.from(t.childNodes)),G(o,t),dt(t),o},html:function(t,e){var o=ht(t);if(e){o=(o=o.replace(/<(\\/?)(\\b(?!!)[^>\\s]*)(.*?)(\\s*\\/?>)/g,(function(t,e,o){o=o.toUpperCase();var n=/^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(o)&&!!e,i=/^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(o);return t+(n||i?\"\\n\":\"\")}))).trim()}return o},value:ht,posFromPlaceholder:function(t){var e=r()(t),o=e.offset(),n=e.outerHeight(!0);return{left:o.left,top:o.top+n}},attachEvents:function(t,e){Object.keys(e).forEach((function(o){t.on(o,e[o])}))},detachEvents:function(t,e){Object.keys(e).forEach((function(o){t.off(o,e[o])}))},isCustomStyleTag:function(t){return t&&!E(t)&&C.contains(t.classList,\"note-styletag\")}};function mt(t){return mt=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},mt(t)}function vt(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,gt(n.key),n)}}function gt(t){var e=function(t,e){if(\"object\"!=mt(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=mt(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==mt(e)?e:e+\"\"}var bt=function(){return t=function t(e,o){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.$note=e,this.memos={},this.modules={},this.layoutInfo={},this.options=r().extend(!0,{},o),r().summernote.ui=r().summernote.ui_template(this.options),this.ui=r().summernote.ui,this.initialize()},e=[{key:\"initialize\",value:function(){return this.layoutInfo=this.ui.createLayout(this.$note),this._initialize(),this.$note.hide(),this}},{key:\"destroy\",value:function(){this._destroy(),this.$note.removeData(\"summernote\"),this.ui.removeLayout(this.$note,this.layoutInfo)}},{key:\"reset\",value:function(){var t=this.isDisabled();this.code(pt.emptyPara),this._destroy(),this._initialize(),t&&this.disable()}},{key:\"_initialize\",value:function(){var t=this;this.options.id=g.uniqueId(r().now()),this.options.container=this.options.container||this.layoutInfo.editor;var e=r().extend({},this.options.buttons);Object.keys(e).forEach((function(o){t.memo(\"button.\"+o,e[o])}));var o=r().extend({},this.options.modules,r().summernote.plugins||{});Object.keys(o).forEach((function(e){t.module(e,o[e],!0)})),Object.keys(this.modules).forEach((function(e){t.initializeModule(e)}))}},{key:\"_destroy\",value:function(){var t=this;Object.keys(this.modules).reverse().forEach((function(e){t.removeModule(e)})),Object.keys(this.memos).forEach((function(e){t.removeMemo(e)})),this.triggerEvent(\"destroy\",this)}},{key:\"code\",value:function(t){var e=this.invoke(\"codeview.isActivated\");if(void 0===t)return this.invoke(\"codeview.sync\"),e?this.layoutInfo.codable.val():this.layoutInfo.editable.html();e?this.invoke(\"codeview.sync\",t):this.layoutInfo.editable.html(t),this.$note.val(t),this.triggerEvent(\"change\",t,this.layoutInfo.editable)}},{key:\"isDisabled\",value:function(){return\"false\"===this.layoutInfo.editable.attr(\"contenteditable\")}},{key:\"enable\",value:function(){this.layoutInfo.editable.attr(\"contenteditable\",!0),this.invoke(\"toolbar.activate\",!0),this.triggerEvent(\"disable\",!1),this.options.editing=!0}},{key:\"disable\",value:function(){this.invoke(\"codeview.isActivated\")&&this.invoke(\"codeview.deactivate\"),this.layoutInfo.editable.attr(\"contenteditable\",!1),this.options.editing=!1,this.invoke(\"toolbar.deactivate\",!0),this.triggerEvent(\"disable\",!0)}},{key:\"triggerEvent\",value:function(){var t=C.head(arguments),e=C.tail(C.from(arguments)),o=this.options.callbacks[g.namespaceToCamel(t,\"on\")];o&&o.apply(this.$note[0],e),this.$note.trigger(\"summernote.\"+t,e)}},{key:\"initializeModule\",value:function(t){var e=this.modules[t];e.shouldInitialize=e.shouldInitialize||g.ok,e.shouldInitialize()&&(e.initialize&&e.initialize(),e.events&&pt.attachEvents(this.$note,e.events))}},{key:\"module\",value:function(t,e,o){if(1===arguments.length)return this.modules[t];this.modules[t]=new e(this),o||this.initializeModule(t)}},{key:\"removeModule\",value:function(t){var e=this.modules[t];e.shouldInitialize()&&(e.events&&pt.detachEvents(this.$note,e.events),e.destroy&&e.destroy()),delete this.modules[t]}},{key:\"memo\",value:function(t,e){if(1===arguments.length)return this.memos[t];this.memos[t]=e}},{key:\"removeMemo\",value:function(t){this.memos[t]&&this.memos[t].destroy&&this.memos[t].destroy(),delete this.memos[t]}},{key:\"createInvokeHandlerAndUpdateState\",value:function(t,e){var o=this;return function(n){o.createInvokeHandler(t,e)(n),o.invoke(\"buttons.updateCurrentStyle\")}}},{key:\"createInvokeHandler\",value:function(t,e){var o=this;return function(n){n.preventDefault();var i=r()(n.target);o.invoke(t,e||i.closest(\"[data-value]\").data(\"value\"),i)}}},{key:\"invoke\",value:function(){var t=C.head(arguments),e=C.tail(C.from(arguments)),o=t.split(\".\"),n=o.length>1,i=n&&C.head(o),r=n?C.last(o):C.head(o),a=this.modules[i||\"editor\"];return!i&&this[r]?this[r].apply(this,e):a&&a[r]&&a.shouldInitialize()?a[r].apply(a,e):void 0}}],e&&vt(t.prototype,e),o&&vt(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function yt(t){return yt=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},yt(t)}function kt(t){return kt=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},kt(t)}function wt(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Ct(n.key),n)}}function Ct(t){var e=function(t,e){if(\"object\"!=kt(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=kt(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==kt(e)?e:e+\"\"}function St(t,e){var o,n,i=t.parentElement(),r=document.body.createTextRange(),a=C.from(i.childNodes);for(o=0;o<a.length;o++)if(!pt.isText(a[o])){if(r.moveToElementText(a[o]),r.compareEndPoints(\"StartToStart\",t)>=0)break;n=a[o]}if(0!==o&&pt.isText(a[o-1])){var s=document.body.createTextRange(),l=null;s.moveToElementText(n||i),s.collapse(!n),l=n?n.nextSibling:i.firstChild;var c=t.duplicate();c.setEndPoint(\"StartToStart\",s);for(var u=c.text.replace(/[\\r\\n]/g,\"\").length;u>l.nodeValue.length&&l.nextSibling;)u-=l.nodeValue.length,l=l.nextSibling;l.nodeValue;e&&l.nextSibling&&pt.isText(l.nextSibling)&&u===l.nodeValue.length&&(u-=l.nodeValue.length,l=l.nextSibling),i=l,o=u}return{cont:i,offset:o}}function xt(t){var e=document.body.createTextRange(),o=function t(e,o){var n,i;if(pt.isText(e)){var r=pt.listPrev(e,g.not(pt.isText)),a=C.last(r).previousSibling;n=a||e.parentNode,o+=C.sum(C.tail(r),pt.nodeLength),i=!a}else{if(n=e.childNodes[o]||e,pt.isText(n))return t(n,0);o=0,i=!1}return{node:n,collapseToStart:i,offset:o}}(t.node,t.offset);return e.moveToElementText(o.node),e.collapse(o.collapseToStart),e.moveStart(\"character\",o.offset),e}r().fn.extend({summernote:function(){var t=yt(C.head(arguments)),e=\"string\"===t,o=\"object\"===t,n=r().extend({},r().summernote.options,o?C.head(arguments):{});n.langInfo=r().extend(!0,{},r().summernote.lang[\"en-US\"],r().summernote.lang[n.lang]),n.icons=r().extend(!0,{},r().summernote.options.icons,n.icons),n.tooltip=\"auto\"===n.tooltip?!m.isSupportTouch:n.tooltip,this.each((function(t,e){var o=r()(e);if(!o.data(\"summernote\")){var i=new bt(o,n);o.data(\"summernote\",i),o.data(\"summernote\").triggerEvent(\"init\",i.layoutInfo)}}));var i=this.first();if(i.length){var a=i.data(\"summernote\");if(e)return a.invoke.apply(a,C.from(arguments));n.focus&&a.invoke(\"editor.focus\")}return this}});var Tt=function(){function t(e,o,n,i){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.sc=e,this.so=o,this.ec=n,this.eo=i,this.isOnEditable=this.makeIsOn(pt.isEditable),this.isOnList=this.makeIsOn(pt.isList),this.isOnAnchor=this.makeIsOn(pt.isAnchor),this.isOnCell=this.makeIsOn(pt.isCell),this.isOnData=this.makeIsOn(pt.isData)}return e=t,o=[{key:\"nativeRange\",value:function(){if(m.isW3CRangeSupport){var t=document.createRange();return t.setStart(this.sc,this.so),t.setEnd(this.ec,this.eo),t}var e=xt({node:this.sc,offset:this.so});return e.setEndPoint(\"EndToEnd\",xt({node:this.ec,offset:this.eo})),e}},{key:\"getPoints\",value:function(){return{sc:this.sc,so:this.so,ec:this.ec,eo:this.eo}}},{key:\"getStartPoint\",value:function(){return{node:this.sc,offset:this.so}}},{key:\"getEndPoint\",value:function(){return{node:this.ec,offset:this.eo}}},{key:\"select\",value:function(){var t=this.nativeRange();if(m.isW3CRangeSupport){var e=document.getSelection();e.rangeCount>0&&e.removeAllRanges(),e.addRange(t)}else t.select();return this}},{key:\"scrollIntoView\",value:function(t){var e=r()(t).height();return t.scrollTop+e<this.sc.offsetTop&&(t.scrollTop+=Math.abs(t.scrollTop+e-this.sc.offsetTop)),this}},{key:\"normalize\",value:function(){var e=function(t,e){if(!t)return t;if(pt.isVisiblePoint(t)&&(!pt.isEdgePoint(t)||pt.isRightEdgePoint(t)&&!e||pt.isLeftEdgePoint(t)&&e||pt.isRightEdgePoint(t)&&e&&pt.isVoid(t.node.nextSibling)||pt.isLeftEdgePoint(t)&&!e&&pt.isVoid(t.node.previousSibling)||pt.isBlock(t.node)&&pt.isEmpty(t.node)))return t;var o=pt.ancestor(t.node,pt.isBlock),n=!1;if(!n){var i=pt.prevPoint(t)||{node:null};n=(pt.isLeftEdgePointOf(t,o)||pt.isVoid(i.node))&&!e}var r=!1;if(!r){var a=pt.nextPoint(t)||{node:null};r=(pt.isRightEdgePointOf(t,o)||pt.isVoid(a.node))&&e}if(n||r){if(pt.isVisiblePoint(t))return t;e=!e}return(e?pt.nextPointUntil(pt.nextPoint(t),pt.isVisiblePoint):pt.prevPointUntil(pt.prevPoint(t),pt.isVisiblePoint))||t},o=e(this.getEndPoint(),!1),n=this.isCollapsed()?o:e(this.getStartPoint(),!0);return new t(n.node,n.offset,o.node,o.offset)}},{key:\"nodes\",value:function(t,e){t=t||g.ok;var o=e&&e.includeAncestor,n=e&&e.fullyContains,i=this.getStartPoint(),r=this.getEndPoint(),a=[],s=[];return pt.walkPoint(i,r,(function(e){var i;pt.isEditable(e.node)||(n?(pt.isLeftEdgePoint(e)&&s.push(e.node),pt.isRightEdgePoint(e)&&C.contains(s,e.node)&&(i=e.node)):i=o?pt.ancestor(e.node,t):e.node,i&&t(i)&&a.push(i))}),!0),C.unique(a)}},{key:\"commonAncestor\",value:function(){return pt.commonAncestor(this.sc,this.ec)}},{key:\"expand\",value:function(e){var o=pt.ancestor(this.sc,e),n=pt.ancestor(this.ec,e);if(!o&&!n)return new t(this.sc,this.so,this.ec,this.eo);var i=this.getPoints();return o&&(i.sc=o,i.so=0),n&&(i.ec=n,i.eo=pt.nodeLength(n)),new t(i.sc,i.so,i.ec,i.eo)}},{key:\"collapse\",value:function(e){return e?new t(this.sc,this.so,this.sc,this.so):new t(this.ec,this.eo,this.ec,this.eo)}},{key:\"splitText\",value:function(){var e=this.sc===this.ec,o=this.getPoints();return pt.isText(this.ec)&&!pt.isEdgePoint(this.getEndPoint())&&this.ec.splitText(this.eo),pt.isText(this.sc)&&!pt.isEdgePoint(this.getStartPoint())&&(o.sc=this.sc.splitText(this.so),o.so=0,e&&(o.ec=o.sc,o.eo=this.eo-this.so)),new t(o.sc,o.so,o.ec,o.eo)}},{key:\"deleteContents\",value:function(){if(this.isCollapsed())return this;var e=this.splitText(),o=e.nodes(null,{fullyContains:!0}),n=pt.prevPointUntil(e.getStartPoint(),(function(t){return!C.contains(o,t.node)})),i=[];return r().each(o,(function(t,e){var o=e.parentNode;n.node!==o&&1===pt.nodeLength(o)&&i.push(o),pt.remove(e,!1)})),r().each(i,(function(t,e){pt.remove(e,!1)})),new t(n.node,n.offset,n.node,n.offset).normalize()}},{key:\"makeIsOn\",value:function(t){return function(){var e=pt.ancestor(this.sc,t);return!!e&&e===pt.ancestor(this.ec,t)}}},{key:\"isLeftEdgeOf\",value:function(t){if(!pt.isLeftEdgePoint(this.getStartPoint()))return!1;var e=pt.ancestor(this.sc,t);return e&&pt.isLeftEdgeOf(this.sc,e)}},{key:\"isCollapsed\",value:function(){return this.sc===this.ec&&this.so===this.eo}},{key:\"wrapBodyInlineWithPara\",value:function(){if(pt.isBodyContainer(this.sc)&&pt.isEmpty(this.sc))return this.sc.innerHTML=pt.emptyPara,new t(this.sc.firstChild,0,this.sc.firstChild,0);var e,o=this.normalize();if(pt.isParaInline(this.sc)||pt.isPara(this.sc))return o;if(pt.isInline(o.sc)){var n=pt.listAncestor(o.sc,g.not(pt.isInline));e=C.last(n),pt.isInline(e)||(e=n[n.length-2]||o.sc.childNodes[o.so])}else e=o.sc.childNodes[o.so>0?o.so-1:0];if(e){var i=pt.listPrev(e,pt.isParaInline).reverse();if((i=i.concat(pt.listNext(e.nextSibling,pt.isParaInline))).length){var r=pt.wrap(C.head(i),\"p\");pt.appendChildNodes(r,C.tail(i))}}return this.normalize()}},{key:\"insertNode\",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=this;(pt.isText(t)||pt.isInline(t))&&(o=this.wrapBodyInlineWithPara().deleteContents());var n=pt.splitPoint(o.getStartPoint(),pt.isInline(t));return n.rightNode?(n.rightNode.parentNode.insertBefore(t,n.rightNode),pt.isEmpty(n.rightNode)&&(e||pt.isPara(t))&&n.rightNode.parentNode.removeChild(n.rightNode)):n.container.appendChild(t),t}},{key:\"pasteHTML\",value:function(t){t=((t||\"\")+\"\").trim(t);var e=r()(\"<div></div>\").html(t)[0],o=C.from(e.childNodes),n=this,i=!1;return n.so>=0&&(o=o.reverse(),i=!0),o=o.map((function(t){return n.insertNode(t,!pt.isInline(t))})),i&&(o=o.reverse()),o}},{key:\"toString\",value:function(){var t=this.nativeRange();return m.isW3CRangeSupport?t.toString():t.text}},{key:\"getWordRange\",value:function(e){var o=this.getEndPoint();if(!pt.isCharPoint(o))return this;var n=pt.prevPointUntil(o,(function(t){return!pt.isCharPoint(t)}));return e&&(o=pt.nextPointUntil(o,(function(t){return!pt.isCharPoint(t)}))),new t(n.node,n.offset,o.node,o.offset)}},{key:\"getWordsRange\",value:function(e){var o=this.getEndPoint(),n=function(t){return!pt.isCharPoint(t)&&!pt.isSpacePoint(t)};if(n(o))return this;var i=pt.prevPointUntil(o,n);return e&&(o=pt.nextPointUntil(o,n)),new t(i.node,i.offset,o.node,o.offset)}},{key:\"getWordsMatchRange\",value:function(e){var o=this.getEndPoint(),n=pt.prevPointUntil(o,(function(n){if(!pt.isCharPoint(n)&&!pt.isSpacePoint(n))return!0;var i=new t(n.node,n.offset,o.node,o.offset),r=e.exec(i.toString());return r&&0===r.index})),i=new t(n.node,n.offset,o.node,o.offset),r=i.toString(),a=e.exec(r);return a&&a[0].length===r.length?i:null}},{key:\"bookmark\",value:function(t){return{s:{path:pt.makeOffsetPath(t,this.sc),offset:this.so},e:{path:pt.makeOffsetPath(t,this.ec),offset:this.eo}}}},{key:\"paraBookmark\",value:function(t){return{s:{path:C.tail(pt.makeOffsetPath(C.head(t),this.sc)),offset:this.so},e:{path:C.tail(pt.makeOffsetPath(C.last(t),this.ec)),offset:this.eo}}}},{key:\"getClientRects\",value:function(){return this.nativeRange().getClientRects()}}],o&&wt(e.prototype,o),n&&wt(e,n),Object.defineProperty(e,\"prototype\",{writable:!1}),e;var e,o,n}();const Et={create:function(t,e,o,n){if(4===arguments.length)return new Tt(t,e,o,n);if(2===arguments.length)return new Tt(t,e,o=t,n=e);var i=this.createFromSelection();if(!i&&1===arguments.length){var r=arguments[0];return pt.isEditable(r)&&(r=r.lastChild),this.createFromBodyElement(r,pt.emptyPara===arguments[0].innerHTML)}return i},createFromBodyElement:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.createFromNode(t).collapse(e)},createFromSelection:function(){var t,e,o,n;if(m.isW3CRangeSupport){var i=document.getSelection();if(!i||0===i.rangeCount)return null;if(pt.isBody(i.anchorNode))return null;var r=i.getRangeAt(0);t=r.startContainer,e=r.startOffset,o=r.endContainer,n=r.endOffset}else{var a=document.selection.createRange(),s=a.duplicate();s.collapse(!1);var l=a;l.collapse(!0);var c=St(l,!0),u=St(s,!1);pt.isText(c.node)&&pt.isLeftEdgePoint(c)&&pt.isTextNode(u.node)&&pt.isRightEdgePoint(u)&&u.node.nextSibling===c.node&&(c=u),t=c.cont,e=c.offset,o=u.cont,n=u.offset}return new Tt(t,e,o,n)},createFromNode:function(t){var e=t,o=0,n=t,i=pt.nodeLength(n);return pt.isVoid(e)&&(o=pt.listPrev(e).length-1,e=e.parentNode),pt.isBR(n)?(i=pt.listPrev(n).length-1,n=n.parentNode):pt.isVoid(n)&&(i=pt.listPrev(n).length,n=n.parentNode),this.create(e,o,n,i)},createFromNodeBefore:function(t){return this.createFromNode(t).collapse(!0)},createFromNodeAfter:function(t){return this.createFromNode(t).collapse()},createFromBookmark:function(t,e){var o=pt.fromOffsetPath(t,e.s.path),n=e.s.offset,i=pt.fromOffsetPath(t,e.e.path),r=e.e.offset;return new Tt(o,n,i,r)},createFromParaBookmark:function(t,e){var o=t.s.offset,n=t.e.offset,i=pt.fromOffsetPath(C.head(e),t.s.path),r=pt.fromOffsetPath(C.last(e),t.e.path);return new Tt(i,o,r,n)}};var Pt={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,SPACE:32,DELETE:46,LEFT:37,UP:38,RIGHT:39,DOWN:40,NUM0:48,NUM1:49,NUM2:50,NUM3:51,NUM4:52,NUM5:53,NUM6:54,NUM7:55,NUM8:56,B:66,E:69,I:73,J:74,K:75,L:76,R:82,S:83,U:85,V:86,Y:89,Z:90,SLASH:191,LEFTBRACKET:219,BACKSLASH:220,RIGHTBRACKET:221,HOME:36,END:35,PAGEUP:33,PAGEDOWN:34};const Nt={isEdit:function(t){return C.contains([Pt.BACKSPACE,Pt.TAB,Pt.ENTER,Pt.SPACE,Pt.DELETE],t)},isRemove:function(t){return C.contains([Pt.BACKSPACE,Pt.DELETE],t)},isMove:function(t){return C.contains([Pt.LEFT,Pt.UP,Pt.RIGHT,Pt.DOWN],t)},isNavigation:function(t){return C.contains([Pt.HOME,Pt.END,Pt.PAGEUP,Pt.PAGEDOWN],t)},nameFromCode:g.invertObject(Pt),code:Pt};function $t(t){return $t=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},$t(t)}function It(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Rt(n.key),n)}}function Rt(t){var e=function(t,e){if(\"object\"!=$t(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=$t(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==$t(e)?e:e+\"\"}var At=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.stack=[],this.stackOffset=-1,this.context=e,this.$editable=e.layoutInfo.editable,this.editable=this.$editable[0]},(e=[{key:\"makeSnapshot\",value:function(){var t=Et.create(this.editable);return{contents:this.$editable.html(),bookmark:t&&t.isOnEditable()?t.bookmark(this.editable):{s:{path:[],offset:0},e:{path:[],offset:0}}}}},{key:\"applySnapshot\",value:function(t){null!==t.contents&&this.$editable.html(t.contents),null!==t.bookmark&&Et.createFromBookmark(this.editable,t.bookmark).select()}},{key:\"rewind\",value:function(){this.$editable.html()!==this.stack[this.stackOffset].contents&&this.recordUndo(),this.stackOffset=0,this.applySnapshot(this.stack[this.stackOffset])}},{key:\"commit\",value:function(){this.stack=[],this.stackOffset=-1,this.recordUndo()}},{key:\"reset\",value:function(){this.stack=[],this.stackOffset=-1,this.$editable.html(\"\"),this.recordUndo()}},{key:\"undo\",value:function(){this.$editable.html()!==this.stack[this.stackOffset].contents&&this.recordUndo(),this.stackOffset>0&&(this.stackOffset--,this.applySnapshot(this.stack[this.stackOffset]))}},{key:\"redo\",value:function(){this.stack.length-1>this.stackOffset&&(this.stackOffset++,this.applySnapshot(this.stack[this.stackOffset]))}},{key:\"recordUndo\",value:function(){this.stackOffset++,this.stack.length>this.stackOffset&&(this.stack=this.stack.slice(0,this.stackOffset)),this.stack.push(this.makeSnapshot()),this.stack.length>this.context.options.historyLimit&&(this.stack.shift(),this.stackOffset-=1)}}])&&It(t.prototype,e),o&&It(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Lt(t){return Lt=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Lt(t)}function Ft(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Dt(n.key),n)}}function Dt(t){var e=function(t,e){if(\"object\"!=Lt(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Lt(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Lt(e)?e:e+\"\"}var Ht=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t)},e=[{key:\"jQueryCSS\",value:function(t,e){var o={};return r().each(e,(function(e,n){o[n]=t.css(n)})),o}},{key:\"fromNode\",value:function(t){var e=this.jQueryCSS(t,[\"font-family\",\"font-size\",\"text-align\",\"list-style-type\",\"line-height\"])||{},o=t[0].style.fontSize||e[\"font-size\"];return e[\"font-size\"]=parseInt(o,10),e[\"font-size-unit\"]=o.match(/[a-z%]+$/),e}},{key:\"stylePara\",value:function(t,e){r().each(t.nodes(pt.isPara,{includeAncestor:!0}),(function(t,o){r()(o).css(e)}))}},{key:\"styleNodes\",value:function(t,e){t=t.splitText();var o=e&&e.nodeName||\"SPAN\",n=!(!e||!e.expandClosestSibling),i=!(!e||!e.onlyPartialContains);if(t.isCollapsed())return[t.insertNode(pt.create(o))];var a=pt.makePredByNodeName(o),s=t.nodes(pt.isText,{fullyContains:!0}).map((function(t){return pt.singleChildAncestor(t,a)||pt.wrap(t,o)}));if(n){if(i){var l=t.nodes();a=g.and(a,(function(t){return C.contains(l,t)}))}return s.map((function(t){var e=pt.withClosestSiblings(t,a),o=C.head(e),n=C.tail(e);return r().each(n,(function(t,e){pt.appendChildNodes(o,e.childNodes),pt.remove(e)})),C.head(e)}))}return s}},{key:\"current\",value:function(t){var e=r()(pt.isElement(t.sc)?t.sc:t.sc.parentNode),o=this.fromNode(e);try{o=r().extend(o,{\"font-bold\":document.queryCommandState(\"bold\")?\"bold\":\"normal\",\"font-italic\":document.queryCommandState(\"italic\")?\"italic\":\"normal\",\"font-underline\":document.queryCommandState(\"underline\")?\"underline\":\"normal\",\"font-subscript\":document.queryCommandState(\"subscript\")?\"subscript\":\"normal\",\"font-superscript\":document.queryCommandState(\"superscript\")?\"superscript\":\"normal\",\"font-strikethrough\":document.queryCommandState(\"strikethrough\")?\"strikethrough\":\"normal\",\"font-family\":document.queryCommandValue(\"fontname\")||o[\"font-family\"]})}catch(t){}if(t.isOnList()){var n=[\"circle\",\"disc\",\"disc-leading-zero\",\"square\"].indexOf(o[\"list-style-type\"])>-1;o[\"list-style\"]=n?\"unordered\":\"ordered\"}else o[\"list-style\"]=\"none\";var i=pt.ancestor(t.sc,pt.isPara);if(i&&i.style[\"line-height\"])o[\"line-height\"]=i.style.lineHeight;else{var a=parseInt(o[\"line-height\"],10)/parseInt(o[\"font-size\"],10);o[\"line-height\"]=a.toFixed(1)}return o.anchor=t.isOnAnchor()&&pt.ancestor(t.sc,pt.isAnchor),o.ancestors=pt.listAncestor(t.sc,pt.isEditable),o.range=t,o}}],e&&Ft(t.prototype,e),o&&Ft(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function jt(t){return jt=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},jt(t)}function Bt(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Ot(n.key),n)}}function Ot(t){var e=function(t,e){if(\"object\"!=jt(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=jt(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==jt(e)?e:e+\"\"}var zt=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t)},e=[{key:\"insertOrderedList\",value:function(t){this.toggleList(\"OL\",t)}},{key:\"insertUnorderedList\",value:function(t){this.toggleList(\"UL\",t)}},{key:\"indent\",value:function(t){var e=this,o=Et.create(t).wrapBodyInlineWithPara(),n=o.nodes(pt.isPara,{includeAncestor:!0}),i=C.clusterBy(n,g.peq2(\"parentNode\"));r().each(i,(function(t,o){var n=C.head(o);if(pt.isLi(n)){var i=e.findList(n.previousSibling);i?o.map((function(t){return i.appendChild(t)})):(e.wrapList(o,n.parentNode.nodeName),o.map((function(t){return t.parentNode})).map((function(t){return e.appendToPrevious(t)})))}else r().each(o,(function(t,e){r()(e).css(\"marginLeft\",(function(t,e){return(parseInt(e,10)||0)+25}))}))})),o.select()}},{key:\"outdent\",value:function(t){var e=this,o=Et.create(t).wrapBodyInlineWithPara(),n=o.nodes(pt.isPara,{includeAncestor:!0}),i=C.clusterBy(n,g.peq2(\"parentNode\"));r().each(i,(function(t,o){var n=C.head(o);pt.isLi(n)?e.releaseList([o]):r().each(o,(function(t,e){r()(e).css(\"marginLeft\",(function(t,e){return(e=parseInt(e,10)||0)>25?e-25:\"\"}))}))})),o.select()}},{key:\"toggleList\",value:function(t,e){var o=this,n=Et.create(e).wrapBodyInlineWithPara(),i=n.nodes(pt.isPara,{includeAncestor:!0}),a=n.paraBookmark(i),s=C.clusterBy(i,g.peq2(\"parentNode\"));if(C.find(i,pt.isPurePara)){var l=[];r().each(s,(function(e,n){l=l.concat(o.wrapList(n,t))})),i=l}else{var c=n.nodes(pt.isList,{includeAncestor:!0}).filter((function(e){return!r().nodeName(e,t)}));c.length?r().each(c,(function(e,o){pt.replace(o,t)})):i=this.releaseList(s,!0)}Et.createFromParaBookmark(a,i).select()}},{key:\"wrapList\",value:function(t,e){var o=C.head(t),n=C.last(t),i=pt.isList(o.previousSibling)&&o.previousSibling,r=pt.isList(n.nextSibling)&&n.nextSibling,a=i||pt.insertAfter(pt.create(e||\"UL\"),n);return t=t.map((function(t){return pt.isPurePara(t)?pt.replace(t,\"LI\"):t})),pt.appendChildNodes(a,t,!0),r&&(pt.appendChildNodes(a,C.from(r.childNodes),!0),pt.remove(r)),t}},{key:\"releaseList\",value:function(t,e){var o=this,n=[];return r().each(t,(function(t,i){var a=C.head(i),s=C.last(i),l=e?pt.lastAncestor(a,pt.isList):a.parentNode,c=l.parentNode;if(\"LI\"===l.parentNode.nodeName)i.map((function(t){var e=o.findNextSiblings(t);c.nextSibling?c.parentNode.insertBefore(t,c.nextSibling):c.parentNode.appendChild(t),e.length&&(o.wrapList(e,l.nodeName),t.appendChild(e[0].parentNode))})),0===l.children.length&&c.removeChild(l),0===c.childNodes.length&&c.parentNode.removeChild(c);else{var u=l.childNodes.length>1?pt.splitTree(l,{node:s.parentNode,offset:pt.position(s)+1},{isSkipPaddingBlankHTML:!0}):null,d=pt.splitTree(l,{node:a.parentNode,offset:pt.position(a)},{isSkipPaddingBlankHTML:!0});i=e?pt.listDescendant(d,pt.isLi):C.from(d.childNodes).filter(pt.isLi),!e&&pt.isList(l.parentNode)||(i=i.map((function(t){return pt.replace(t,\"P\")}))),r().each(C.from(i).reverse(),(function(t,e){pt.insertAfter(e,l)}));var f=C.compact([l,d,u]);r().each(f,(function(t,e){var o=[e].concat(pt.listDescendant(e,pt.isList));r().each(o.reverse(),(function(t,e){pt.nodeLength(e)||pt.remove(e,!0)}))}))}n=n.concat(i)})),n}},{key:\"appendToPrevious\",value:function(t){return t.previousSibling?pt.appendChildNodes(t.previousSibling,[t]):this.wrapList([t],\"LI\")}},{key:\"findList\",value:function(t){return t?C.find(t.children,(function(t){return[\"OL\",\"UL\"].indexOf(t.nodeName)>-1})):null}},{key:\"findNextSiblings\",value:function(t){for(var e=[];t.nextSibling;)e.push(t.nextSibling),t=t.nextSibling;return e}}],e&&Bt(t.prototype,e),o&&Bt(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Mt(t){return Mt=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Mt(t)}function Ut(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Wt(n.key),n)}}function Wt(t){var e=function(t,e){if(\"object\"!=Mt(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Mt(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Mt(e)?e:e+\"\"}var Kt=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.bullet=new zt,this.options=e.options},e=[{key:\"insertTab\",value:function(t,e){var o=pt.createText(new Array(e+1).join(pt.NBSP_CHAR));(t=t.deleteContents()).insertNode(o,!0),(t=Et.create(o,e)).select()}},{key:\"insertParagraph\",value:function(t,e){e=(e=(e=e||Et.create(t)).deleteContents()).wrapBodyInlineWithPara();var o,n=pt.ancestor(e.sc,pt.isPara);if(n){if(pt.isLi(n)&&(pt.isEmpty(n)||pt.deepestChildIsEmpty(n)))return void this.bullet.toggleList(n.parentNode.nodeName);var i=null;if(1===this.options.blockquoteBreakingLevel?i=pt.ancestor(n,pt.isBlockquote):2===this.options.blockquoteBreakingLevel&&(i=pt.lastAncestor(n,pt.isBlockquote)),i){o=r()(pt.emptyPara)[0],pt.isRightEdgePoint(e.getStartPoint())&&pt.isBR(e.sc.nextSibling)&&r()(e.sc.nextSibling).remove();var a=pt.splitTree(i,e.getStartPoint(),{isDiscardEmptySplits:!0});a?a.parentNode.insertBefore(o,a):pt.insertAfter(o,i)}else{o=pt.splitTree(n,e.getStartPoint());var s=pt.listDescendant(n,pt.isEmptyAnchor);s=s.concat(pt.listDescendant(o,pt.isEmptyAnchor)),r().each(s,(function(t,e){pt.remove(e)})),(pt.isHeading(o)||pt.isPre(o)||pt.isCustomStyleTag(o))&&pt.isEmpty(o)&&(o=pt.replace(o,\"p\"))}}else{var l=e.sc.childNodes[e.so];o=r()(pt.emptyPara)[0],l?e.sc.insertBefore(o,l):e.sc.appendChild(o)}Et.create(o,0).normalize().select().scrollIntoView(t)}}],e&&Ut(t.prototype,e),o&&Ut(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function qt(t){return qt=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},qt(t)}function Vt(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,_t(n.key),n)}}function _t(t){var e=function(t,e){if(\"object\"!=qt(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=qt(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==qt(e)?e:e+\"\"}var Gt=function t(e,o,n,i){var r={colPos:0,rowPos:0},a=[],s=[];function l(t,e,o,n,i,r,s){var l={baseRow:o,baseCell:n,isRowSpan:i,isColSpan:r,isVirtual:s};a[t]||(a[t]=[]),a[t][e]=l}function c(t,e,o,n){return{baseCell:t.baseCell,action:e,virtualTable:{rowIndex:o,cellIndex:n}}}function u(t,e){if(!a[t])return e;if(!a[t][e])return e;for(var o=e;a[t][o];)if(o++,!a[t][o])return o}function d(t,e){var o=u(t.rowIndex,e.cellIndex),n=e.colSpan>1,i=e.rowSpan>1,a=t.rowIndex===r.rowPos&&e.cellIndex===r.colPos;l(t.rowIndex,o,t,e,i,n,!1);var s=e.attributes.rowSpan?parseInt(e.attributes.rowSpan.value,10):0;if(s>1)for(var c=1;c<s;c++){var d=t.rowIndex+c;f(d,o,e,a),l(d,o,t,e,!0,n,!0)}var h=e.attributes.colSpan?parseInt(e.attributes.colSpan.value,10):0;if(h>1)for(var p=1;p<h;p++){var m=u(t.rowIndex,o+p);f(t.rowIndex,m,e,a),l(t.rowIndex,m,t,e,i,!0,!0)}}function f(t,e,o,n){t===r.rowPos&&r.colPos>=o.cellIndex&&o.cellIndex<=e&&!n&&r.colPos++}function h(e){switch(o){case t.where.Column:if(e.isColSpan)return t.resultAction.SubtractSpanCount;break;case t.where.Row:if(!e.isVirtual&&e.isRowSpan)return t.resultAction.AddCell;if(e.isRowSpan)return t.resultAction.SubtractSpanCount}return t.resultAction.RemoveCell}function p(e){switch(o){case t.where.Column:if(e.isColSpan)return t.resultAction.SumSpanCount;if(e.isRowSpan&&e.isVirtual)return t.resultAction.Ignore;break;case t.where.Row:if(e.isRowSpan)return t.resultAction.SumSpanCount;if(e.isColSpan&&e.isVirtual)return t.resultAction.Ignore}return t.resultAction.AddCell}this.getActionList=function(){for(var e=o===t.where.Row?r.rowPos:-1,i=o===t.where.Column?r.colPos:-1,l=0,u=!0;u;){var d=e>=0?e:l,f=i>=0?i:l,m=a[d];if(!m)return u=!1,s;var v=m[f];if(!v)return u=!1,s;var g=t.resultAction.Ignore;switch(n){case t.requestAction.Add:g=p(v);break;case t.requestAction.Delete:g=h(v)}s.push(c(v,g,d,f)),l++}return s},e&&e.tagName&&(\"td\"===e.tagName.toLowerCase()||\"th\"===e.tagName.toLowerCase())&&(r.colPos=e.cellIndex,e.parentElement&&e.parentElement.tagName&&\"tr\"===e.parentElement.tagName.toLowerCase()&&(r.rowPos=e.parentElement.rowIndex)),function(){for(var t=i.rows,e=0;e<t.length;e++)for(var o=t[e].cells,n=0;n<o.length;n++)d(t[e],o[n])}()};Gt.where={Row:0,Column:1},Gt.requestAction={Add:0,Delete:1},Gt.resultAction={Ignore:0,SubtractSpanCount:1,RemoveCell:2,AddCell:3,SumSpanCount:4};var Zt=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t)},e=[{key:\"tab\",value:function(t,e){var o=pt.ancestor(t.commonAncestor(),pt.isCell),n=pt.ancestor(o,pt.isTable),i=pt.listDescendant(n,pt.isCell),r=C[e?\"prev\":\"next\"](i,o);r&&Et.create(r,0).select()}},{key:\"addRow\",value:function(t,e){for(var o=pt.ancestor(t.commonAncestor(),pt.isCell),n=r()(o).closest(\"tr\"),i=this.recoverAttributes(n),a=r()(\"<tr\"+i+\"></tr>\"),s=new Gt(o,Gt.where.Row,Gt.requestAction.Add,r()(n).closest(\"table\")[0]).getActionList(),l=0;l<s.length;l++){var c=s[l],u=this.recoverAttributes(c.baseCell);switch(c.action){case Gt.resultAction.AddCell:a.append(\"<td\"+u+\">\"+pt.blank+\"</td>\");break;case Gt.resultAction.SumSpanCount:if(\"top\"===e&&(c.baseCell.parent?c.baseCell.closest(\"tr\").rowIndex:0)<=n[0].rowIndex){var d=r()(\"<div></div>\").append(r()(\"<td\"+u+\">\"+pt.blank+\"</td>\").removeAttr(\"rowspan\")).html();a.append(d);break}var f=parseInt(c.baseCell.rowSpan,10);f++,c.baseCell.setAttribute(\"rowSpan\",f)}}if(\"top\"===e)n.before(a);else{if(o.rowSpan>1){var h=n[0].rowIndex+(o.rowSpan-2);return void r()(r()(n).parent().find(\"tr\")[h]).after(r()(a))}n.after(a)}}},{key:\"addCol\",value:function(t,e){var o=pt.ancestor(t.commonAncestor(),pt.isCell),n=r()(o).closest(\"tr\");r()(n).siblings().push(n);for(var i=new Gt(o,Gt.where.Column,Gt.requestAction.Add,r()(n).closest(\"table\")[0]).getActionList(),a=0;a<i.length;a++){var s=i[a],l=this.recoverAttributes(s.baseCell);switch(s.action){case Gt.resultAction.AddCell:\"right\"===e?r()(s.baseCell).after(\"<td\"+l+\">\"+pt.blank+\"</td>\"):r()(s.baseCell).before(\"<td\"+l+\">\"+pt.blank+\"</td>\");break;case Gt.resultAction.SumSpanCount:if(\"right\"===e){var c=parseInt(s.baseCell.colSpan,10);c++,s.baseCell.setAttribute(\"colSpan\",c)}else r()(s.baseCell).before(\"<td\"+l+\">\"+pt.blank+\"</td>\")}}}},{key:\"recoverAttributes\",value:function(t){var e=\"\";if(!t)return e;for(var o=t.attributes||[],n=0;n<o.length;n++)\"id\"!==o[n].name.toLowerCase()&&o[n].specified&&(e+=\" \"+o[n].name+\"='\"+o[n].value+\"'\");return e}},{key:\"deleteRow\",value:function(t){for(var e=pt.ancestor(t.commonAncestor(),pt.isCell),o=r()(e).closest(\"tr\"),n=o.children(\"td, th\").index(r()(e)),i=o[0].rowIndex,a=new Gt(e,Gt.where.Row,Gt.requestAction.Delete,r()(o).closest(\"table\")[0]).getActionList(),s=0;s<a.length;s++)if(a[s]){var l=a[s].baseCell,c=a[s].virtualTable,u=l.rowSpan&&l.rowSpan>1,d=u?parseInt(l.rowSpan,10):0;switch(a[s].action){case Gt.resultAction.Ignore:continue;case Gt.resultAction.AddCell:var f=o.next(\"tr\")[0];if(!f)continue;var h=o[0].cells[n];u&&(d>2?(d--,f.insertBefore(h,f.cells[n]),f.cells[n].setAttribute(\"rowSpan\",d),f.cells[n].innerHTML=\"\"):2===d&&(f.insertBefore(h,f.cells[n]),f.cells[n].removeAttribute(\"rowSpan\"),f.cells[n].innerHTML=\"\"));continue;case Gt.resultAction.SubtractSpanCount:u&&(d>2?(d--,l.setAttribute(\"rowSpan\",d),c.rowIndex!==i&&l.cellIndex===n&&(l.innerHTML=\"\")):2===d&&(l.removeAttribute(\"rowSpan\"),c.rowIndex!==i&&l.cellIndex===n&&(l.innerHTML=\"\")));continue;case Gt.resultAction.RemoveCell:continue}}o.remove()}},{key:\"deleteCol\",value:function(t){for(var e=pt.ancestor(t.commonAncestor(),pt.isCell),o=r()(e).closest(\"tr\"),n=o.children(\"td, th\").index(r()(e)),i=new Gt(e,Gt.where.Column,Gt.requestAction.Delete,r()(o).closest(\"table\")[0]).getActionList(),a=0;a<i.length;a++)if(i[a])switch(i[a].action){case Gt.resultAction.Ignore:continue;case Gt.resultAction.SubtractSpanCount:var s=i[a].baseCell;if(s.colSpan&&s.colSpan>1){var l=s.colSpan?parseInt(s.colSpan,10):0;l>2?(l--,s.setAttribute(\"colSpan\",l),s.cellIndex===n&&(s.innerHTML=\"\")):2===l&&(s.removeAttribute(\"colSpan\"),s.cellIndex===n&&(s.innerHTML=\"\"))}continue;case Gt.resultAction.RemoveCell:pt.remove(i[a].baseCell,!0);continue}}},{key:\"createTable\",value:function(t,e,o){for(var n,i=[],a=0;a<t;a++)i.push(\"<td>\"+pt.blank+\"</td>\");n=i.join(\"\");for(var s,l=[],c=0;c<e;c++)l.push(\"<tr>\"+n+\"</tr>\");s=l.join(\"\");var u=r()(\"<table>\"+s+\"</table>\");return o&&o.tableClassName&&u.addClass(o.tableClassName),u[0]}},{key:\"deleteTable\",value:function(t){var e=pt.ancestor(t.commonAncestor(),pt.isCell);r()(e).closest(\"table\").remove()}}],e&&Vt(t.prototype,e),o&&Vt(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Yt(t){return Yt=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Yt(t)}function Xt(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Qt(n.key),n)}}function Qt(t){var e=function(t,e){if(\"object\"!=Yt(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Yt(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Yt(e)?e:e+\"\"}var Jt=/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,te=/^(\\+?\\d{1,3}[\\s-]?)?(\\d{1,4})[\\s-]?(\\d{1,4})[\\s-]?(\\d{1,4})$/,ee=/^([A-Za-z][A-Za-z0-9+-.]*\\:|#|\\/)/,oe=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.$note=e.layoutInfo.note,this.$editor=e.layoutInfo.editor,this.$editable=e.layoutInfo.editable,this.options=e.options,this.lang=this.options.langInfo,this.editable=this.$editable[0],this.lastRange=null,this.snapshot=null,this.style=new Ht,this.table=new Zt,this.typing=new Kt(e),this.bullet=new zt,this.history=new At(e),this.context.memo(\"help.escape\",this.lang.help.escape),this.context.memo(\"help.undo\",this.lang.help.undo),this.context.memo(\"help.redo\",this.lang.help.redo),this.context.memo(\"help.tab\",this.lang.help.tab),this.context.memo(\"help.untab\",this.lang.help.untab),this.context.memo(\"help.insertParagraph\",this.lang.help.insertParagraph),this.context.memo(\"help.insertOrderedList\",this.lang.help.insertOrderedList),this.context.memo(\"help.insertUnorderedList\",this.lang.help.insertUnorderedList),this.context.memo(\"help.indent\",this.lang.help.indent),this.context.memo(\"help.outdent\",this.lang.help.outdent),this.context.memo(\"help.formatPara\",this.lang.help.formatPara),this.context.memo(\"help.insertHorizontalRule\",this.lang.help.insertHorizontalRule),this.context.memo(\"help.fontName\",this.lang.help.fontName);for(var n=[\"bold\",\"italic\",\"underline\",\"strikethrough\",\"superscript\",\"subscript\",\"justifyLeft\",\"justifyCenter\",\"justifyRight\",\"justifyFull\",\"formatBlock\",\"removeFormat\",\"backColor\"],i=0,a=n.length;i<a;i++)this[n[i]]=function(t){return function(e){o.beforeCommand(),document.execCommand(t,!1,e),o.afterCommand(!0)}}(n[i]),this.context.memo(\"help.\"+n[i],this.lang.help[n[i]]);this.fontName=this.wrapCommand((function(t){return o.fontStyling(\"font-family\",m.validFontName(t))})),this.fontSize=this.wrapCommand((function(t){var e=o.currentStyle()[\"font-size-unit\"];return o.fontStyling(\"font-size\",t+e)})),this.fontSizeUnit=this.wrapCommand((function(t){var e=o.currentStyle()[\"font-size\"];return o.fontStyling(\"font-size\",e+t)}));for(var s=1;s<=6;s++)this[\"formatH\"+s]=function(t){return function(){o.formatBlock(\"H\"+t)}}(s),this.context.memo(\"help.formatH\"+s,this.lang.help[\"formatH\"+s]);this.insertParagraph=this.wrapCommand((function(){o.typing.insertParagraph(o.editable)})),this.insertOrderedList=this.wrapCommand((function(){o.bullet.insertOrderedList(o.editable)})),this.insertUnorderedList=this.wrapCommand((function(){o.bullet.insertUnorderedList(o.editable)})),this.indent=this.wrapCommand((function(){o.bullet.indent(o.editable)})),this.outdent=this.wrapCommand((function(){o.bullet.outdent(o.editable)})),this.insertNode=this.wrapCommand((function(t){o.isLimited(r()(t).text().length)||(o.getLastRange().insertNode(t),o.setLastRange(Et.createFromNodeAfter(t).select()))})),this.insertText=this.wrapCommand((function(t){if(!o.isLimited(t.length)){var e=o.getLastRange().insertNode(pt.createText(t));o.setLastRange(Et.create(e,pt.nodeLength(e)).select())}})),this.pasteHTML=this.wrapCommand((function(t){if(!o.isLimited(t.length)){t=o.context.invoke(\"codeview.purify\",t);var e=o.getLastRange().pasteHTML(t);o.setLastRange(Et.createFromNodeAfter(C.last(e)).select())}})),this.formatBlock=this.wrapCommand((function(t,e){var n=o.options.callbacks.onApplyCustomStyle;n?n.call(o,e,o.context,o.onFormatBlock):o.onFormatBlock(t,e)})),this.insertHorizontalRule=this.wrapCommand((function(){var t=o.getLastRange().insertNode(pt.create(\"HR\"));t.nextSibling&&o.setLastRange(Et.create(t.nextSibling,0).normalize().select())})),this.lineHeight=this.wrapCommand((function(t){o.style.stylePara(o.getLastRange(),{lineHeight:t})})),this.createLink=this.wrapCommand((function(t){var e=[],n=t.url,i=t.text,a=t.isNewWindow,s=o.options.linkAddNoReferrer,l=o.options.linkAddNoOpener,c=t.range||o.getLastRange(),u=i.length-c.toString().length;if(!(u>0&&o.isLimited(u))){var d=c.toString()!==i;\"string\"==typeof n&&(n=n.trim()),n=o.options.onCreateLink?o.options.onCreateLink(n):o.checkLinkUrl(n);var f=[];if(d){var h=(c=c.deleteContents()).insertNode(r()(\"<A></A>\").text(i)[0]);f.push(h)}else f=o.style.styleNodes(c,{nodeName:\"A\",expandClosestSibling:!0,onlyPartialContains:!0});r().each(f,(function(t,o){r()(o).attr(\"href\",n),a?(r()(o).attr(\"target\",\"_blank\"),s&&e.push(\"noreferrer\"),l&&e.push(\"noopener\"),e.length&&r()(o).attr(\"rel\",e.join(\" \"))):r()(o).removeAttr(\"target\")})),o.setLastRange(o.createRangeFromList(f).select())}})),this.color=this.wrapCommand((function(t){var e=t.foreColor,o=t.backColor;e&&document.execCommand(\"foreColor\",!1,e),o&&document.execCommand(\"backColor\",!1,o)})),this.foreColor=this.wrapCommand((function(t){document.execCommand(\"foreColor\",!1,t)})),this.insertTable=this.wrapCommand((function(t){var e=t.split(\"x\");o.getLastRange().deleteContents().insertNode(o.table.createTable(e[0],e[1],o.options))})),this.removeMedia=this.wrapCommand((function(){var t=r()(o.restoreTarget()).parent();t.closest(\"figure\").length?t.closest(\"figure\").remove():t=r()(o.restoreTarget()).detach(),o.setLastRange(Et.createFromSelection(t).select()),o.context.triggerEvent(\"media.delete\",t,o.$editable)})),this.floatMe=this.wrapCommand((function(t){var e=r()(o.restoreTarget());e.toggleClass(\"note-float-left\",\"left\"===t),e.toggleClass(\"note-float-right\",\"right\"===t),e.css(\"float\",\"none\"===t?\"\":t)})),this.resize=this.wrapCommand((function(t){var e=r()(o.restoreTarget());0===(t=parseFloat(t))?e.css(\"width\",\"\"):e.css({width:100*t+\"%\",height:\"\"})}))},e=[{key:\"initialize\",value:function(){var t=this;this.$editable.on(\"keydown\",(function(e){if(e.keyCode===Nt.code.ENTER&&t.context.triggerEvent(\"enter\",e),t.context.triggerEvent(\"keydown\",e),t.snapshot=t.history.makeSnapshot(),t.hasKeyShortCut=!1,e.isDefaultPrevented()||(t.options.shortcuts?t.hasKeyShortCut=t.handleKeyMap(e):t.preventDefaultEditableShortCuts(e)),t.isLimited(1,e)){var o=t.getLastRange();if(o.eo-o.so==0)return!1}t.setLastRange(),t.options.recordEveryKeystroke&&!1===t.hasKeyShortCut&&t.history.recordUndo()})).on(\"keyup\",(function(e){t.setLastRange(),t.context.triggerEvent(\"keyup\",e)})).on(\"focus\",(function(e){t.setLastRange(),t.context.triggerEvent(\"focus\",e)})).on(\"blur\",(function(e){t.context.triggerEvent(\"blur\",e)})).on(\"mousedown\",(function(e){t.context.triggerEvent(\"mousedown\",e)})).on(\"mouseup\",(function(e){t.setLastRange(),t.history.recordUndo(),t.context.triggerEvent(\"mouseup\",e)})).on(\"scroll\",(function(e){t.context.triggerEvent(\"scroll\",e)})).on(\"paste\",(function(e){t.setLastRange(),t.context.triggerEvent(\"paste\",e)})).on(\"copy\",(function(e){t.context.triggerEvent(\"copy\",e)})).on(\"input\",(function(){t.isLimited(0)&&t.snapshot&&t.history.applySnapshot(t.snapshot)})),this.$editable.attr(\"spellcheck\",this.options.spellCheck),this.$editable.attr(\"autocorrect\",this.options.spellCheck),this.options.disableGrammar&&this.$editable.attr(\"data-gramm\",!1),this.$editable.html(pt.html(this.$note)||pt.emptyPara),this.$editable.on(m.inputEventName,g.debounce((function(){t.context.triggerEvent(\"change\",t.$editable.html(),t.$editable)}),10)),this.$editable.on(\"focusin\",(function(e){t.context.triggerEvent(\"focusin\",e)})).on(\"focusout\",(function(e){t.context.triggerEvent(\"focusout\",e)})),this.options.airMode?this.options.overrideContextMenu&&this.$editor.on(\"contextmenu\",(function(e){return t.context.triggerEvent(\"contextmenu\",e),!1})):(this.options.width&&this.$editor.outerWidth(this.options.width),this.options.height&&this.$editable.outerHeight(this.options.height),this.options.maxHeight&&this.$editable.css(\"max-height\",this.options.maxHeight),this.options.minHeight&&this.$editable.css(\"min-height\",this.options.minHeight)),this.history.recordUndo(),this.setLastRange()}},{key:\"destroy\",value:function(){this.$editable.off()}},{key:\"handleKeyMap\",value:function(t){var e=this.options.keyMap[m.isMac?\"mac\":\"pc\"],o=[];t.metaKey&&o.push(\"CMD\"),t.ctrlKey&&!t.altKey&&o.push(\"CTRL\"),t.shiftKey&&o.push(\"SHIFT\");var n=Nt.nameFromCode[t.keyCode];n&&o.push(n);var i=e[o.join(\"+\")];if(\"TAB\"!==n||this.options.tabDisable)if(i){if(!1!==this.context.invoke(i))return t.preventDefault(),!0}else Nt.isEdit(t.keyCode)&&(Nt.isRemove(t.keyCode)&&this.context.invoke(\"removed\"),this.afterCommand());else this.afterCommand();return!1}},{key:\"preventDefaultEditableShortCuts\",value:function(t){(t.ctrlKey||t.metaKey)&&C.contains([66,73,85],t.keyCode)&&t.preventDefault()}},{key:\"isLimited\",value:function(t,e){return t=t||0,(void 0===e||!(Nt.isMove(e.keyCode)||Nt.isNavigation(e.keyCode)||e.ctrlKey||e.metaKey||C.contains([Nt.code.BACKSPACE,Nt.code.DELETE],e.keyCode)))&&this.options.maxTextLength>0&&this.$editable.text().length+t>this.options.maxTextLength}},{key:\"checkLinkUrl\",value:function(t){return Jt.test(t)?\"mailto://\"+t:te.test(t)?\"tel://\"+t:ee.test(t)?t:\"http://\"+t}},{key:\"createRange\",value:function(){return this.focus(),this.setLastRange(),this.getLastRange()}},{key:\"createRangeFromList\",value:function(t){var e=Et.createFromNodeBefore(C.head(t)).getStartPoint(),o=Et.createFromNodeAfter(C.last(t)).getEndPoint();return Et.create(e.node,e.offset,o.node,o.offset)}},{key:\"setLastRange\",value:function(t){t?this.lastRange=t:(this.lastRange=Et.create(this.editable),0===r()(this.lastRange.sc).closest(\".note-editable\").length&&(this.lastRange=Et.createFromBodyElement(this.editable)))}},{key:\"getLastRange\",value:function(){return this.lastRange||this.setLastRange(),this.lastRange}},{key:\"saveRange\",value:function(t){t&&this.getLastRange().collapse().select()}},{key:\"restoreRange\",value:function(){this.lastRange&&(this.lastRange.select(),this.focus())}},{key:\"saveTarget\",value:function(t){this.$editable.data(\"target\",t)}},{key:\"clearTarget\",value:function(){this.$editable.removeData(\"target\")}},{key:\"restoreTarget\",value:function(){return this.$editable.data(\"target\")}},{key:\"currentStyle\",value:function(){var t=Et.create();return t&&(t=t.normalize()),t?this.style.current(t):this.style.fromNode(this.$editable)}},{key:\"styleFromNode\",value:function(t){return this.style.fromNode(t)}},{key:\"undo\",value:function(){this.context.triggerEvent(\"before.command\",this.$editable.html()),this.history.undo(),this.context.triggerEvent(\"change\",this.$editable.html(),this.$editable)}},{key:\"commit\",value:function(){this.context.triggerEvent(\"before.command\",this.$editable.html()),this.history.commit(),this.context.triggerEvent(\"change\",this.$editable.html(),this.$editable)}},{key:\"redo\",value:function(){this.context.triggerEvent(\"before.command\",this.$editable.html()),this.history.redo(),this.context.triggerEvent(\"change\",this.$editable.html(),this.$editable)}},{key:\"beforeCommand\",value:function(){this.context.triggerEvent(\"before.command\",this.$editable.html()),document.execCommand(\"styleWithCSS\",!1,this.options.styleWithCSS),this.focus()}},{key:\"afterCommand\",value:function(t){this.normalizeContent(),this.history.recordUndo(),t||this.context.triggerEvent(\"change\",this.$editable.html(),this.$editable)}},{key:\"tab\",value:function(){var t=this.getLastRange();if(t.isCollapsed()&&t.isOnCell())this.table.tab(t);else{if(0===this.options.tabSize)return!1;this.isLimited(this.options.tabSize)||(this.beforeCommand(),this.typing.insertTab(t,this.options.tabSize),this.afterCommand())}}},{key:\"untab\",value:function(){var t=this.getLastRange();if(t.isCollapsed()&&t.isOnCell())this.table.tab(t,!0);else if(0===this.options.tabSize)return!1}},{key:\"wrapCommand\",value:function(t){return function(){this.beforeCommand(),t.apply(this,arguments),this.afterCommand()}}},{key:\"removed\",value:function(t,e,o){(t=Et.create()).isCollapsed()&&t.isOnCell()&&(o=(e=t.ec).tagName)&&1===e.childElementCount&&\"BR\"===e.childNodes[0].tagName&&(\"P\"===o?e.remove():[\"TH\",\"TD\"].indexOf(o)>=0&&e.firstChild.remove())}},{key:\"insertImage\",value:function(t,e){var o,n=this;return(o=t,r().Deferred((function(t){var e=r()(\"<img>\");e.one(\"load\",(function(){e.off(\"error abort\"),t.resolve(e)})).one(\"error abort\",(function(){e.off(\"load\").detach(),t.reject(e)})).css({display:\"none\"}).appendTo(document.body).attr(\"src\",o)})).promise()).then((function(t){n.beforeCommand(),\"function\"==typeof e?e(t):(\"string\"==typeof e&&t.attr(\"data-filename\",e),t.css(\"width\",Math.min(n.$editable.width(),t.width()))),t.show(),n.getLastRange().insertNode(t[0]),n.setLastRange(Et.createFromNodeAfter(t[0]).select()),n.afterCommand()})).fail((function(t){n.context.triggerEvent(\"image.upload.error\",t)}))}},{key:\"insertImagesAsDataURL\",value:function(t){var e=this;r().each(t,(function(t,o){var n=o.name;e.options.maximumImageFileSize&&e.options.maximumImageFileSize<o.size?e.context.triggerEvent(\"image.upload.error\",e.lang.image.maximumFileSizeError):function(t){return r().Deferred((function(e){r().extend(new FileReader,{onload:function(t){var o=t.target.result;e.resolve(o)},onerror:function(t){e.reject(t)}}).readAsDataURL(t)})).promise()}(o).then((function(t){return e.insertImage(t,n)})).fail((function(){e.context.triggerEvent(\"image.upload.error\")}))}))}},{key:\"insertImagesOrCallback\",value:function(t){this.options.callbacks.onImageUpload?this.context.triggerEvent(\"image.upload\",t):this.insertImagesAsDataURL(t)}},{key:\"getSelectedText\",value:function(){var t=this.getLastRange();return t.isOnAnchor()&&(t=Et.createFromNode(pt.ancestor(t.sc,pt.isAnchor))),t.toString()}},{key:\"onFormatBlock\",value:function(t,e){if(document.execCommand(\"FormatBlock\",!1,m.isMSIE?\"<\"+t+\">\":t),e&&e.length&&(e[0].tagName.toUpperCase()!==t.toUpperCase()&&(e=e.find(t)),e&&e.length)){var o=this.createRange(),n=r()([o.sc,o.ec]).closest(t);n.removeClass();var i=e[0].className||\"\";i&&n.addClass(i)}}},{key:\"formatPara\",value:function(){this.formatBlock(\"P\")}},{key:\"fontStyling\",value:function(t,e){var o=this.getLastRange();if(\"\"!==o){var n=this.style.styleNodes(o);if(this.$editor.find(\".note-status-output\").html(\"\"),r()(n).css(t,e),o.isCollapsed()){var i=C.head(n);i&&!pt.nodeLength(i)&&(i.innerHTML=pt.ZERO_WIDTH_NBSP_CHAR,Et.createFromNode(i.firstChild).select(),this.setLastRange(),this.$editable.data(\"bogus\",i))}else o.select()}else{var a=r().now();this.$editor.find(\".note-status-output\").html('<div id=\"note-status-output-'+a+'\" class=\"alert alert-info\">'+this.lang.output.noSelection+\"</div>\"),setTimeout((function(){r()(\"#note-status-output-\"+a).remove()}),5e3)}}},{key:\"unlink\",value:function(){var t=this.getLastRange();if(t.isOnAnchor()){var e=pt.ancestor(t.sc,pt.isAnchor);(t=Et.createFromNode(e)).select(),this.setLastRange(),this.beforeCommand(),document.execCommand(\"unlink\"),this.afterCommand()}}},{key:\"getLinkInfo\",value:function(){this.hasFocus()||this.focus();var t=this.getLastRange().expand(pt.isAnchor),e=r()(C.head(t.nodes(pt.isAnchor))),o={range:t,text:t.toString(),url:e.length?e.attr(\"href\"):\"\"};return e.length&&(o.isNewWindow=\"_blank\"===e.attr(\"target\")),o}},{key:\"addRow\",value:function(t){var e=this.getLastRange(this.$editable);e.isCollapsed()&&e.isOnCell()&&(this.beforeCommand(),this.table.addRow(e,t),this.afterCommand())}},{key:\"addCol\",value:function(t){var e=this.getLastRange(this.$editable);e.isCollapsed()&&e.isOnCell()&&(this.beforeCommand(),this.table.addCol(e,t),this.afterCommand())}},{key:\"deleteRow\",value:function(){var t=this.getLastRange(this.$editable);t.isCollapsed()&&t.isOnCell()&&(this.beforeCommand(),this.table.deleteRow(t),this.afterCommand())}},{key:\"deleteCol\",value:function(){var t=this.getLastRange(this.$editable);t.isCollapsed()&&t.isOnCell()&&(this.beforeCommand(),this.table.deleteCol(t),this.afterCommand())}},{key:\"deleteTable\",value:function(){var t=this.getLastRange(this.$editable);t.isCollapsed()&&t.isOnCell()&&(this.beforeCommand(),this.table.deleteTable(t),this.afterCommand())}},{key:\"resizeTo\",value:function(t,e,o){var n;if(o){var i=t.y/t.x,r=e.data(\"ratio\");n={width:r>i?t.x:t.y/r,height:r>i?t.x*r:t.y}}else n={width:t.x,height:t.y};e.css(n)}},{key:\"hasFocus\",value:function(){return this.$editable.is(\":focus\")}},{key:\"focus\",value:function(){this.hasFocus()||this.$editable.trigger(\"focus\")}},{key:\"isEmpty\",value:function(){return pt.isEmpty(this.$editable[0])||pt.emptyPara===this.$editable.html()}},{key:\"empty\",value:function(){this.context.invoke(\"code\",pt.emptyPara)}},{key:\"normalizeContent\",value:function(){this.$editable[0].normalize()}}],e&&Xt(t.prototype,e),o&&Xt(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function ne(t){return ne=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},ne(t)}function ie(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,re(n.key),n)}}function re(t){var e=function(t,e){if(\"object\"!=ne(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=ne(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==ne(e)?e:e+\"\"}var ae=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.options=e.options,this.$editable=e.layoutInfo.editable},(e=[{key:\"initialize\",value:function(){this.$editable.on(\"paste\",this.pasteByEvent.bind(this))}},{key:\"pasteByEvent\",value:function(t){var e=this;if(!this.context.isDisabled()){var o=t.originalEvent.clipboardData;if(o&&o.items&&o.items.length){var n=o.files,i=o.getData(\"Text\");n.length>0&&this.options.allowClipboardImagePasting&&(this.context.invoke(\"editor.insertImagesOrCallback\",n),t.preventDefault()),i.length>0&&this.context.invoke(\"editor.isLimited\",i.length)&&t.preventDefault()}else if(window.clipboardData){var r=window.clipboardData.getData(\"text\");this.context.invoke(\"editor.isLimited\",r.length)&&t.preventDefault()}setTimeout((function(){e.context.invoke(\"editor.afterCommand\")}),10)}}}])&&ie(t.prototype,e),o&&ie(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function se(t){return se=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},se(t)}function le(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,ce(n.key),n)}}function ce(t){var e=function(t,e){if(\"object\"!=se(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=se(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==se(e)?e:e+\"\"}var ue=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.$eventListener=r()(document),this.$editor=e.layoutInfo.editor,this.$editable=e.layoutInfo.editable,this.options=e.options,this.lang=this.options.langInfo,this.documentEventHandlers={},this.$dropzone=r()(['<div class=\"note-dropzone\">','<div class=\"note-dropzone-message\"></div>',\"</div>\"].join(\"\")).prependTo(this.$editor)},e=[{key:\"initialize\",value:function(){this.options.disableDragAndDrop?(this.documentEventHandlers.onDrop=function(t){t.preventDefault()},this.$eventListener=this.$dropzone,this.$eventListener.on(\"drop\",this.documentEventHandlers.onDrop)):this.attachDragAndDropEvent()}},{key:\"attachDragAndDropEvent\",value:function(){var t=this,e=r()(),o=this.$dropzone.find(\".note-dropzone-message\");this.documentEventHandlers.onDragenter=function(n){var i=t.context.invoke(\"codeview.isActivated\"),r=t.$editor.width()>0&&t.$editor.height()>0;i||e.length||!r||(t.$editor.addClass(\"dragover\"),t.$dropzone.width(t.$editor.width()),t.$dropzone.height(t.$editor.height()),o.text(t.lang.image.dragImageHere)),e=e.add(n.target)},this.documentEventHandlers.onDragleave=function(o){(e=e.not(o.target)).length&&\"BODY\"!==o.target.nodeName||(e=r()(),t.$editor.removeClass(\"dragover\"))},this.documentEventHandlers.onDrop=function(){e=r()(),t.$editor.removeClass(\"dragover\")},this.$eventListener.on(\"dragenter\",this.documentEventHandlers.onDragenter).on(\"dragleave\",this.documentEventHandlers.onDragleave).on(\"drop\",this.documentEventHandlers.onDrop),this.$dropzone.on(\"dragenter\",(function(){t.$dropzone.addClass(\"hover\"),o.text(t.lang.image.dropImage)})).on(\"dragleave\",(function(){t.$dropzone.removeClass(\"hover\"),o.text(t.lang.image.dragImageHere)})),this.$dropzone.on(\"drop\",(function(e){var o=e.originalEvent.dataTransfer;e.preventDefault(),o&&o.files&&o.files.length?(t.$editable.trigger(\"focus\"),t.context.invoke(\"editor.insertImagesOrCallback\",o.files)):r().each(o.types,(function(e,n){if(!(n.toLowerCase().indexOf(\"_moz_\")>-1)){var i=o.getData(n);n.toLowerCase().indexOf(\"text\")>-1?t.context.invoke(\"editor.pasteHTML\",i):r()(i).each((function(e,o){t.context.invoke(\"editor.insertNode\",o)}))}}))})).on(\"dragover\",!1)}},{key:\"destroy\",value:function(){var t=this;Object.keys(this.documentEventHandlers).forEach((function(e){t.$eventListener.off(e.slice(2).toLowerCase(),t.documentEventHandlers[e])})),this.documentEventHandlers={}}}],e&&le(t.prototype,e),o&&le(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function de(t){return de=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},de(t)}function fe(t,e){var o=\"undefined\"!=typeof Symbol&&t[Symbol.iterator]||t[\"@@iterator\"];if(!o){if(Array.isArray(t)||(o=function(t,e){if(t){if(\"string\"==typeof t)return he(t,e);var o={}.toString.call(t).slice(8,-1);return\"Object\"===o&&t.constructor&&(o=t.constructor.name),\"Map\"===o||\"Set\"===o?Array.from(t):\"Arguments\"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?he(t,e):void 0}}(t))||e&&t&&\"number\"==typeof t.length){o&&(t=o);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}var r,a=!0,s=!1;return{s:function(){o=o.call(t)},n:function(){var t=o.next();return a=t.done,t},e:function(t){s=!0,r=t},f:function(){try{a||null==o.return||o.return()}finally{if(s)throw r}}}}function he(t,e){(null==e||e>t.length)&&(e=t.length);for(var o=0,n=Array(e);o<e;o++)n[o]=t[o];return n}function pe(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,me(n.key),n)}}function me(t){var e=function(t,e){if(\"object\"!=de(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=de(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==de(e)?e:e+\"\"}var ve=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.$editor=e.layoutInfo.editor,this.$editable=e.layoutInfo.editable,this.$codable=e.layoutInfo.codable,this.options=e.options,this.CodeMirrorConstructor=window.CodeMirror,this.options.codemirror.CodeMirrorConstructor&&(this.CodeMirrorConstructor=this.options.codemirror.CodeMirrorConstructor)},e=[{key:\"sync\",value:function(t){var e=this.isActivated(),o=this.CodeMirrorConstructor;e&&(t?o?this.$codable.data(\"cmEditor\").getDoc().setValue(t):this.$codable.val(t):o&&this.$codable.data(\"cmEditor\").save())}},{key:\"initialize\",value:function(){var t=this;this.$codable.on(\"keyup\",(function(e){e.keyCode===Nt.code.ESCAPE&&t.deactivate()}))}},{key:\"isActivated\",value:function(){return this.$editor.hasClass(\"codeview\")}},{key:\"toggle\",value:function(){this.isActivated()?this.deactivate():this.activate(),this.context.triggerEvent(\"codeview.toggled\")}},{key:\"purify\",value:function(t){if(this.options.codeviewFilter&&(t=t.replace(this.options.codeviewFilterRegex,\"\"),this.options.codeviewIframeFilter)){var e=this.options.codeviewIframeWhitelistSrc.concat(this.options.codeviewIframeWhitelistSrcBase);t=t.replace(/(<iframe.*?>.*?(?:<\\/iframe>)?)/gi,(function(t){if(/<.+src(?==?('|\"|\\s)?)[\\s\\S]+src(?=('|\"|\\s)?)[^>]*?>/i.test(t))return\"\";var o,n=fe(e);try{for(n.s();!(o=n.n()).done;){var i=o.value;if(new RegExp('src=\"(https?:)?//'+i.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g,\"\\\\$&\")+'/(.+)\"').test(t))return t}}catch(t){n.e(t)}finally{n.f()}return\"\"}))}return t}},{key:\"activate\",value:function(){var t=this,e=this.CodeMirrorConstructor;if(this.$codable.val(pt.html(this.$editable,this.options.prettifyHtml)),this.$codable.height(this.$editable.height()),this.context.invoke(\"toolbar.updateCodeview\",!0),this.context.invoke(\"airPopover.updateCodeview\",!0),this.$editor.addClass(\"codeview\"),this.$codable.trigger(\"focus\"),e){var o=e.fromTextArea(this.$codable[0],this.options.codemirror);if(this.options.codemirror.tern){var n=new e.TernServer(this.options.codemirror.tern);o.ternServer=n,o.on(\"cursorActivity\",(function(t){n.updateArgHints(t)}))}o.on(\"blur\",(function(e){t.context.triggerEvent(\"blur.codeview\",o.getValue(),e)})),o.on(\"change\",(function(){t.context.triggerEvent(\"change.codeview\",o.getValue(),o)})),o.setSize(null,this.$editable.outerHeight()),this.$codable.data(\"cmEditor\",o)}else this.$codable.on(\"blur\",(function(e){t.context.triggerEvent(\"blur.codeview\",t.$codable.val(),e)})),this.$codable.on(\"input\",(function(){t.context.triggerEvent(\"change.codeview\",t.$codable.val(),t.$codable)}))}},{key:\"deactivate\",value:function(){if(this.CodeMirrorConstructor){var t=this.$codable.data(\"cmEditor\");this.$codable.val(t.getValue()),t.toTextArea()}var e=this.purify(pt.value(this.$codable,this.options.prettifyHtml)||pt.emptyPara),o=this.$editable.html()!==e;this.$editable.html(e),this.$editable.height(this.options.height?this.$codable.height():\"auto\"),this.$editor.removeClass(\"codeview\"),o&&this.context.triggerEvent(\"change\",this.$editable.html(),this.$editable),this.$editable.trigger(\"focus\"),this.context.invoke(\"toolbar.updateCodeview\",!1),this.context.invoke(\"airPopover.updateCodeview\",!1)}},{key:\"destroy\",value:function(){this.isActivated()&&this.deactivate()}}],e&&pe(t.prototype,e),o&&pe(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function ge(t){return ge=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},ge(t)}function be(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,ye(n.key),n)}}function ye(t){var e=function(t,e){if(\"object\"!=ge(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=ge(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==ge(e)?e:e+\"\"}var ke=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.$document=r()(document),this.$statusbar=e.layoutInfo.statusbar,this.$editable=e.layoutInfo.editable,this.$codable=e.layoutInfo.codable,this.options=e.options},(e=[{key:\"initialize\",value:function(){var t=this;this.options.airMode||this.options.disableResizeEditor?this.destroy():this.$statusbar.on(\"mousedown touchstart\",(function(e){e.preventDefault(),e.stopPropagation();var o=t.$editable.offset().top-t.$document.scrollTop(),n=t.$codable.offset().top-t.$document.scrollTop(),i=function(e){var i=\"mousemove\"==e.type?e:e.originalEvent.touches[0],r=i.clientY-(o+24),a=i.clientY-(n+24);r=t.options.minheight>0?Math.max(r,t.options.minheight):r,r=t.options.maxHeight>0?Math.min(r,t.options.maxHeight):r,a=t.options.minheight>0?Math.max(a,t.options.minheight):a,a=t.options.maxHeight>0?Math.min(a,t.options.maxHeight):a,t.$editable.height(r),t.$codable.height(a)};t.$document.on(\"mousemove touchmove\",i).one(\"mouseup touchend\",(function(){t.$document.off(\"mousemove touchmove\",i)}))}))}},{key:\"destroy\",value:function(){this.$statusbar.off(),this.$statusbar.addClass(\"locked\")}}])&&be(t.prototype,e),o&&be(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function we(t){return we=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},we(t)}function Ce(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Se(n.key),n)}}function Se(t){var e=function(t,e){if(\"object\"!=we(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=we(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==we(e)?e:e+\"\"}var xe=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.$editor=e.layoutInfo.editor,this.$toolbar=e.layoutInfo.toolbar,this.$editable=e.layoutInfo.editable,this.$codable=e.layoutInfo.codable,this.$window=r()(window),this.$scrollbar=r()(\"html, body\"),this.scrollbarClassName=\"note-fullscreen-body\",this.onResize=function(){o.resizeTo({h:o.$window.height()-o.$toolbar.outerHeight()})}},(e=[{key:\"resizeTo\",value:function(t){this.$editable.css(\"height\",t.h),this.$codable.css(\"height\",t.h),this.$codable.data(\"cmeditor\")&&this.$codable.data(\"cmeditor\").setsize(null,t.h)}},{key:\"toggle\",value:function(){this.$editor.toggleClass(\"fullscreen\");var t=this.isFullscreen();this.$scrollbar.toggleClass(this.scrollbarClassName,t),t?(this.$editable.data(\"orgHeight\",this.$editable.css(\"height\")),this.$editable.data(\"orgMaxHeight\",this.$editable.css(\"maxHeight\")),this.$editable.css(\"maxHeight\",\"\"),this.$window.on(\"resize\",this.onResize).trigger(\"resize\")):(this.$window.off(\"resize\",this.onResize),this.resizeTo({h:this.$editable.data(\"orgHeight\")}),this.$editable.css(\"maxHeight\",this.$editable.css(\"orgMaxHeight\"))),this.context.invoke(\"toolbar.updateFullscreen\",t)}},{key:\"isFullscreen\",value:function(){return this.$editor.hasClass(\"fullscreen\")}},{key:\"destroy\",value:function(){this.$scrollbar.removeClass(this.scrollbarClassName)}}])&&Ce(t.prototype,e),o&&Ce(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Te(t){return Te=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Te(t)}function Ee(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Pe(n.key),n)}}function Pe(t){var e=function(t,e){if(\"object\"!=Te(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Te(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Te(e)?e:e+\"\"}var Ne=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.$document=r()(document),this.$editingArea=e.layoutInfo.editingArea,this.options=e.options,this.lang=this.options.langInfo,this.events={\"summernote.mousedown\":function(t,e){o.update(e.target,e)&&e.preventDefault()},\"summernote.keyup summernote.scroll summernote.change summernote.dialog.shown\":function(){o.update()},\"summernote.disable summernote.blur\":function(){o.hide()},\"summernote.codeview.toggled\":function(){o.update()}}},e=[{key:\"initialize\",value:function(){var t=this;this.$handle=r()(['<div class=\"note-handle\">','<div class=\"note-control-selection\">','<div class=\"note-control-selection-bg\"></div>','<div class=\"note-control-holder note-control-nw\"></div>','<div class=\"note-control-holder note-control-ne\"></div>','<div class=\"note-control-holder note-control-sw\"></div>','<div class=\"',this.options.disableResizeImage?\"note-control-holder\":\"note-control-sizing\",' note-control-se\"></div>',this.options.disableResizeImage?\"\":'<div class=\"note-control-selection-info\"></div>',\"</div>\",\"</div>\"].join(\"\")).prependTo(this.$editingArea),this.$handle.on(\"mousedown\",(function(e){if(pt.isControlSizing(e.target)){e.preventDefault(),e.stopPropagation();var o=t.$handle.find(\".note-control-selection\").data(\"target\"),n=o.offset(),i=t.$document.scrollTop(),r=function(e){t.context.invoke(\"editor.resizeTo\",{x:e.clientX-n.left,y:e.clientY-(n.top-i)},o,!e.shiftKey),t.update(o[0],e)};t.$document.on(\"mousemove\",r).one(\"mouseup\",(function(e){e.preventDefault(),t.$document.off(\"mousemove\",r),t.context.invoke(\"editor.afterCommand\")})),o.data(\"ratio\")||o.data(\"ratio\",o.height()/o.width())}})),this.$handle.on(\"wheel\",(function(e){e.preventDefault(),t.update()}))}},{key:\"destroy\",value:function(){this.$handle.remove()}},{key:\"update\",value:function(t,e){if(this.context.isDisabled())return!1;var o=pt.isImg(t),n=this.$handle.find(\".note-control-selection\");if(this.context.invoke(\"imagePopover.update\",t,e),o){var i=r()(t),a=this.$editingArea[0].getBoundingClientRect(),s=t.getBoundingClientRect();n.css({display:\"block\",left:s.left-a.left,top:s.top-a.top,width:s.width,height:s.height}).data(\"target\",i);var l=new Image;l.src=i.attr(\"src\");var c=s.width+\"x\"+s.height+\" (\"+this.lang.image.original+\": \"+l.width+\"x\"+l.height+\")\";n.find(\".note-control-selection-info\").text(c),this.context.invoke(\"editor.saveTarget\",t)}else this.hide();return o}},{key:\"hide\",value:function(){this.context.invoke(\"editor.clearTarget\"),this.$handle.children().hide()}}],e&&Ee(t.prototype,e),o&&Ee(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function $e(t){return $e=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},$e(t)}function Ie(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Re(n.key),n)}}function Re(t){var e=function(t,e){if(\"object\"!=$e(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=$e(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==$e(e)?e:e+\"\"}var Ae=/^([A-Za-z][A-Za-z0-9+-.]*\\:[\\/]{2}|tel:|mailto:[A-Z0-9._%+-]+@|xmpp:[A-Z0-9._%+-]+@)?(www\\.)?(.+)$/i,Le=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.options=e.options,this.$editable=e.layoutInfo.editable,this.events={\"summernote.keyup\":function(t,e){e.isDefaultPrevented()||o.handleKeyup(e)},\"summernote.keydown\":function(t,e){o.handleKeydown(e)}}},(e=[{key:\"initialize\",value:function(){this.lastWordRange=null}},{key:\"destroy\",value:function(){this.lastWordRange=null}},{key:\"replace\",value:function(){if(this.lastWordRange){var t=this.lastWordRange.toString(),e=t.match(Ae);if(e&&(e[1]||e[2])){var o=e[1]?t:\"http://\"+t,n=this.options.showDomainOnlyForAutolink?t.replace(/^(?:https?:\\/\\/)?(?:tel?:?)?(?:mailto?:?)?(?:xmpp?:?)?(?:www\\.)?/i,\"\").split(\"/\")[0]:t,i=r()(\"<a></a>\").html(n).attr(\"href\",o)[0];this.context.options.linkTargetBlank&&r()(i).attr(\"target\",\"_blank\"),this.lastWordRange.insertNode(i),this.lastWordRange=null,this.context.invoke(\"editor.focus\"),this.context.triggerEvent(\"change\",this.$editable.html(),this.$editable)}}}},{key:\"handleKeydown\",value:function(t){if(C.contains([Nt.code.ENTER,Nt.code.SPACE],t.keyCode)){var e=this.context.invoke(\"editor.createRange\").getWordRange();this.lastWordRange=e}}},{key:\"handleKeyup\",value:function(t){(Nt.code.SPACE===t.keyCode||Nt.code.ENTER===t.keyCode&&!t.shiftKey)&&this.replace()}}])&&Ie(t.prototype,e),o&&Ie(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Fe(t){return Fe=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Fe(t)}function De(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,He(n.key),n)}}function He(t){var e=function(t,e){if(\"object\"!=Fe(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Fe(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Fe(e)?e:e+\"\"}var je=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.$note=e.layoutInfo.note,this.events={\"summernote.change\":function(){o.$note.val(e.invoke(\"code\"))}}},(e=[{key:\"shouldInitialize\",value:function(){return pt.isTextarea(this.$note[0])}}])&&De(t.prototype,e),o&&De(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Be(t){return Be=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Be(t)}function Oe(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,ze(n.key),n)}}function ze(t){var e=function(t,e){if(\"object\"!=Be(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Be(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Be(e)?e:e+\"\"}var Me=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.options=e.options.replace||{},this.keys=[Nt.code.ENTER,Nt.code.SPACE,Nt.code.PERIOD,Nt.code.COMMA,Nt.code.SEMICOLON,Nt.code.SLASH],this.previousKeydownCode=null,this.events={\"summernote.keyup\":function(t,e){e.isDefaultPrevented()||o.handleKeyup(e)},\"summernote.keydown\":function(t,e){o.handleKeydown(e)}}},(e=[{key:\"shouldInitialize\",value:function(){return!!this.options.match}},{key:\"initialize\",value:function(){this.lastWord=null}},{key:\"destroy\",value:function(){this.lastWord=null}},{key:\"replace\",value:function(){if(this.lastWord){var t=this,e=this.lastWord.toString();this.options.match(e,(function(e){if(e){var o=\"\";if(\"string\"==typeof e?o=pt.createText(e):e instanceof jQuery?o=e[0]:e instanceof Node&&(o=e),!o)return;t.lastWord.insertNode(o),t.lastWord=null,t.context.invoke(\"editor.focus\")}}))}}},{key:\"handleKeydown\",value:function(t){if(this.previousKeydownCode&&C.contains(this.keys,this.previousKeydownCode))this.previousKeydownCode=t.keyCode;else{if(C.contains(this.keys,t.keyCode)){var e=this.context.invoke(\"editor.createRange\").getWordRange();this.lastWord=e}this.previousKeydownCode=t.keyCode}}},{key:\"handleKeyup\",value:function(t){C.contains(this.keys,t.keyCode)&&this.replace()}}])&&Oe(t.prototype,e),o&&Oe(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Ue(t){return Ue=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Ue(t)}function We(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Ke(n.key),n)}}function Ke(t){var e=function(t,e){if(\"object\"!=Ue(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Ue(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Ue(e)?e:e+\"\"}var qe=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.$editingArea=e.layoutInfo.editingArea,this.options=e.options,!0===this.options.inheritPlaceholder&&(this.options.placeholder=this.context.$note.attr(\"placeholder\")||this.options.placeholder),this.events={\"summernote.init summernote.change\":function(){o.update()},\"summernote.codeview.toggled\":function(){o.update()}}},(e=[{key:\"shouldInitialize\",value:function(){return!!this.options.placeholder}},{key:\"initialize\",value:function(){var t=this;this.$placeholder=r()('<div class=\"note-placeholder\"></div>'),this.$placeholder.on(\"click\",(function(){t.context.invoke(\"focus\")})).html(this.options.placeholder).prependTo(this.$editingArea),this.update()}},{key:\"destroy\",value:function(){this.$placeholder.remove()}},{key:\"update\",value:function(){var t=!this.context.invoke(\"codeview.isActivated\")&&this.context.invoke(\"editor.isEmpty\");this.$placeholder.toggle(t)}}])&&We(t.prototype,e),o&&We(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Ve(t){return Ve=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Ve(t)}function _e(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Ge(n.key),n)}}function Ge(t){var e=function(t,e){if(\"object\"!=Ve(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Ve(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Ve(e)?e:e+\"\"}var Ze=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.ui=r().summernote.ui,this.context=e,this.$toolbar=e.layoutInfo.toolbar,this.options=e.options,this.lang=this.options.langInfo,this.invertedKeyMap=g.invertObject(this.options.keyMap[m.isMac?\"mac\":\"pc\"])},e=[{key:\"representShortcut\",value:function(t){var e=this.invertedKeyMap[t];return this.options.shortcuts&&e?(m.isMac&&(e=e.replace(\"CMD\",\"⌘\").replace(\"SHIFT\",\"⇧\")),\" (\"+(e=e.replace(\"BACKSLASH\",\"\\\\\").replace(\"SLASH\",\"/\").replace(\"LEFTBRACKET\",\"[\").replace(\"RIGHTBRACKET\",\"]\"))+\")\"):\"\"}},{key:\"button\",value:function(t){return!this.options.tooltip&&t.tooltip&&delete t.tooltip,t.container=this.options.container,this.ui.button(t)}},{key:\"initialize\",value:function(){this.addToolbarButtons(),this.addImagePopoverButtons(),this.addLinkPopoverButtons(),this.addTablePopoverButtons(),this.fontInstalledMap={}}},{key:\"destroy\",value:function(){delete this.fontInstalledMap}},{key:\"isFontInstalled\",value:function(t){return Object.prototype.hasOwnProperty.call(this.fontInstalledMap,t)||(this.fontInstalledMap[t]=m.isFontInstalled(t)||C.contains(this.options.fontNamesIgnoreCheck,t)),this.fontInstalledMap[t]}},{key:\"isFontDeservedToAdd\",value:function(t){return\"\"!==(t=t.toLowerCase())&&this.isFontInstalled(t)&&-1===m.genericFontFamilies.indexOf(t)}},{key:\"colorPalette\",value:function(t,e,o,n){var i=this;return this.ui.buttonGroup({className:\"note-color \"+t,children:[this.button({className:\"note-current-color-button\",contents:this.ui.icon(this.options.icons.font+\" note-recent-color\"),tooltip:e,click:function(t){var e=r()(t.currentTarget);o&&n?i.context.invoke(\"editor.color\",{backColor:e.attr(\"data-backColor\"),foreColor:e.attr(\"data-foreColor\")}):o?i.context.invoke(\"editor.color\",{backColor:e.attr(\"data-backColor\")}):n&&i.context.invoke(\"editor.color\",{foreColor:e.attr(\"data-foreColor\")})},callback:function(t){var e=t.find(\".note-recent-color\");o&&(e.css(\"background-color\",i.options.colorButton.backColor),t.attr(\"data-backColor\",i.options.colorButton.backColor)),n?(e.css(\"color\",i.options.colorButton.foreColor),t.attr(\"data-foreColor\",i.options.colorButton.foreColor)):e.css(\"color\",\"transparent\")}}),this.button({className:\"dropdown-toggle\",contents:this.ui.dropdownButtonContents(\"\",this.options),tooltip:this.lang.color.more,data:{toggle:\"dropdown\"}}),this.ui.dropdown({items:(o?['<div class=\"note-palette\">','<div class=\"note-palette-title\">'+this.lang.color.background+\"</div>\",\"<div>\",'<button type=\"button\" class=\"note-color-reset btn btn-light btn-default\" data-event=\"backColor\" data-value=\"transparent\">',this.lang.color.transparent,\"</button>\",\"</div>\",'<div class=\"note-holder\" data-event=\"backColor\">\\x3c!-- back colors --\\x3e</div>',\"<div>\",'<button type=\"button\" class=\"note-color-select btn btn-light btn-default\" data-event=\"openPalette\" data-value=\"backColorPicker-'+this.options.id+'\">',this.lang.color.cpSelect,\"</button>\",'<input type=\"color\" id=\"backColorPicker-'+this.options.id+'\" class=\"note-btn note-color-select-btn\" value=\"'+this.options.colorButton.backColor+'\" data-event=\"backColorPalette-'+this.options.id+'\">',\"</div>\",'<div class=\"note-holder-custom\" id=\"backColorPalette-'+this.options.id+'\" data-event=\"backColor\"></div>',\"</div>\"].join(\"\"):\"\")+(n?['<div class=\"note-palette\">','<div class=\"note-palette-title\">'+this.lang.color.foreground+\"</div>\",\"<div>\",'<button type=\"button\" class=\"note-color-reset btn btn-light btn-default\" data-event=\"removeFormat\" data-value=\"foreColor\">',this.lang.color.resetToDefault,\"</button>\",\"</div>\",'<div class=\"note-holder\" data-event=\"foreColor\">\\x3c!-- fore colors --\\x3e</div>',\"<div>\",'<button type=\"button\" class=\"note-color-select btn btn-light btn-default\" data-event=\"openPalette\" data-value=\"foreColorPicker-'+this.options.id+'\">',this.lang.color.cpSelect,\"</button>\",'<input type=\"color\" id=\"foreColorPicker-'+this.options.id+'\" class=\"note-btn note-color-select-btn\" value=\"'+this.options.colorButton.foreColor+'\" data-event=\"foreColorPalette-'+this.options.id+'\">',\"</div>\",'<div class=\"note-holder-custom\" id=\"foreColorPalette-'+this.options.id+'\" data-event=\"foreColor\"></div>',\"</div>\"].join(\"\"):\"\"),callback:function(t){t.find(\".note-holder\").each((function(t,e){var o=r()(e);o.append(i.ui.palette({colors:i.options.colors,colorsName:i.options.colorsName,eventName:o.data(\"event\"),container:i.options.container,tooltip:i.options.tooltip}).render())}));var e=[[\"#FFFFFF\",\"#FFFFFF\",\"#FFFFFF\",\"#FFFFFF\",\"#FFFFFF\",\"#FFFFFF\",\"#FFFFFF\",\"#FFFFFF\"]];t.find(\".note-holder-custom\").each((function(t,o){var n=r()(o);n.append(i.ui.palette({colors:e,colorsName:e,eventName:n.data(\"event\"),container:i.options.container,tooltip:i.options.tooltip}).render())})),t.find(\"input[type=color]\").each((function(e,o){r()(o).on(\"change\",(function(){var e=t.find(\"#\"+r()(this).data(\"event\")).find(\".note-color-btn\").first(),o=this.value.toUpperCase();e.css(\"background-color\",o).attr(\"aria-label\",o).attr(\"data-value\",o).attr(\"data-original-title\",o),e.trigger(\"click\")}))}))},click:function(e){e.stopPropagation();var o=r()(\".\"+t).find(\".note-dropdown-menu\"),n=r()(e.target),a=n.data(\"event\"),s=n.attr(\"data-value\");if(\"openPalette\"===a){var l=o.find(\"#\"+s),c=r()(o.find(\"#\"+l.data(\"event\")).find(\".note-color-row\")[0]),u=c.find(\".note-color-btn\").last().detach(),d=l.val();u.css(\"background-color\",d).attr(\"aria-label\",d).attr(\"data-value\",d).attr(\"data-original-title\",d),c.prepend(u),l.trigger(\"click\")}else{if(C.contains([\"backColor\",\"foreColor\"],a)){var f=\"backColor\"===a?\"background-color\":\"color\",h=n.closest(\".note-color\").find(\".note-recent-color\"),p=n.closest(\".note-color\").find(\".note-current-color-button\");h.css(f,s),p.attr(\"data-\"+a,s)}i.context.invoke(\"editor.\"+a,s)}}})]}).render()}},{key:\"addToolbarButtons\",value:function(){var t=this;this.context.memo(\"button.style\",(function(){return t.ui.buttonGroup([t.button({className:\"dropdown-toggle\",contents:t.ui.dropdownButtonContents(t.ui.icon(t.options.icons.magic),t.options),tooltip:t.lang.style.style,data:{toggle:\"dropdown\"}}),t.ui.dropdown({className:\"dropdown-style\",items:t.options.styleTags,title:t.lang.style.style,template:function(e){\"string\"==typeof e&&(e={tag:e,title:Object.prototype.hasOwnProperty.call(t.lang.style,e)?t.lang.style[e]:e});var o=e.tag,n=e.title;return\"<\"+o+(e.style?' style=\"'+e.style+'\" ':\"\")+(e.className?' class=\"'+e.className+'\"':\"\")+\">\"+n+\"</\"+o+\">\"},click:t.context.createInvokeHandler(\"editor.formatBlock\")})]).render()}));for(var e=function(){var e=t.options.styleTags[o];t.context.memo(\"button.style.\"+e,(function(){return t.button({className:\"note-btn-style-\"+e,contents:'<div data-value=\"'+e+'\">'+e.toUpperCase()+\"</div>\",tooltip:t.lang.style[e],click:t.context.createInvokeHandler(\"editor.formatBlock\")}).render()}))},o=0,n=this.options.styleTags.length;o<n;o++)e();this.context.memo(\"button.bold\",(function(){return t.button({className:\"note-btn-bold\",contents:t.ui.icon(t.options.icons.bold),tooltip:t.lang.font.bold+t.representShortcut(\"bold\"),click:t.context.createInvokeHandlerAndUpdateState(\"editor.bold\")}).render()})),this.context.memo(\"button.italic\",(function(){return t.button({className:\"note-btn-italic\",contents:t.ui.icon(t.options.icons.italic),tooltip:t.lang.font.italic+t.representShortcut(\"italic\"),click:t.context.createInvokeHandlerAndUpdateState(\"editor.italic\")}).render()})),this.context.memo(\"button.underline\",(function(){return t.button({className:\"note-btn-underline\",contents:t.ui.icon(t.options.icons.underline),tooltip:t.lang.font.underline+t.representShortcut(\"underline\"),click:t.context.createInvokeHandlerAndUpdateState(\"editor.underline\")}).render()})),this.context.memo(\"button.clear\",(function(){return t.button({contents:t.ui.icon(t.options.icons.eraser),tooltip:t.lang.font.clear+t.representShortcut(\"removeFormat\"),click:t.context.createInvokeHandler(\"editor.removeFormat\")}).render()})),this.context.memo(\"button.strikethrough\",(function(){return t.button({className:\"note-btn-strikethrough\",contents:t.ui.icon(t.options.icons.strikethrough),tooltip:t.lang.font.strikethrough+t.representShortcut(\"strikethrough\"),click:t.context.createInvokeHandlerAndUpdateState(\"editor.strikethrough\")}).render()})),this.context.memo(\"button.superscript\",(function(){return t.button({className:\"note-btn-superscript\",contents:t.ui.icon(t.options.icons.superscript),tooltip:t.lang.font.superscript,click:t.context.createInvokeHandlerAndUpdateState(\"editor.superscript\")}).render()})),this.context.memo(\"button.subscript\",(function(){return t.button({className:\"note-btn-subscript\",contents:t.ui.icon(t.options.icons.subscript),tooltip:t.lang.font.subscript,click:t.context.createInvokeHandlerAndUpdateState(\"editor.subscript\")}).render()})),this.context.memo(\"button.fontname\",(function(){var e=t.context.invoke(\"editor.currentStyle\");return t.options.addDefaultFonts&&r().each(e[\"font-family\"].split(\",\"),(function(e,o){o=o.trim().replace(/['\"]+/g,\"\"),t.isFontDeservedToAdd(o)&&-1===t.options.fontNames.indexOf(o)&&t.options.fontNames.push(o)})),t.ui.buttonGroup([t.button({className:\"dropdown-toggle\",contents:t.ui.dropdownButtonContents('<span class=\"note-current-fontname\"></span>',t.options),tooltip:t.lang.font.name,data:{toggle:\"dropdown\"}}),t.ui.dropdownCheck({className:\"dropdown-fontname\",checkClassName:t.options.icons.menuCheck,items:t.options.fontNames.filter(t.isFontInstalled.bind(t)),title:t.lang.font.name,template:function(t){return'<span style=\"font-family: '+m.validFontName(t)+'\">'+t+\"</span>\"},click:t.context.createInvokeHandlerAndUpdateState(\"editor.fontName\")})]).render()})),this.context.memo(\"button.fontsize\",(function(){return t.ui.buttonGroup([t.button({className:\"dropdown-toggle\",contents:t.ui.dropdownButtonContents('<span class=\"note-current-fontsize\"></span>',t.options),tooltip:t.lang.font.size,data:{toggle:\"dropdown\"}}),t.ui.dropdownCheck({className:\"dropdown-fontsize\",checkClassName:t.options.icons.menuCheck,items:t.options.fontSizes,title:t.lang.font.size,click:t.context.createInvokeHandlerAndUpdateState(\"editor.fontSize\")})]).render()})),this.context.memo(\"button.fontsizeunit\",(function(){return t.ui.buttonGroup([t.button({className:\"dropdown-toggle\",contents:t.ui.dropdownButtonContents('<span class=\"note-current-fontsizeunit\"></span>',t.options),tooltip:t.lang.font.sizeunit,data:{toggle:\"dropdown\"}}),t.ui.dropdownCheck({className:\"dropdown-fontsizeunit\",checkClassName:t.options.icons.menuCheck,items:t.options.fontSizeUnits,title:t.lang.font.sizeunit,click:t.context.createInvokeHandlerAndUpdateState(\"editor.fontSizeUnit\")})]).render()})),this.context.memo(\"button.color\",(function(){return t.colorPalette(\"note-color-all\",t.lang.color.recent,!0,!0)})),this.context.memo(\"button.forecolor\",(function(){return t.colorPalette(\"note-color-fore\",t.lang.color.foreground,!1,!0)})),this.context.memo(\"button.backcolor\",(function(){return t.colorPalette(\"note-color-back\",t.lang.color.background,!0,!1)})),this.context.memo(\"button.ul\",(function(){return t.button({contents:t.ui.icon(t.options.icons.unorderedlist),tooltip:t.lang.lists.unordered+t.representShortcut(\"insertUnorderedList\"),click:t.context.createInvokeHandler(\"editor.insertUnorderedList\")}).render()})),this.context.memo(\"button.ol\",(function(){return t.button({contents:t.ui.icon(t.options.icons.orderedlist),tooltip:t.lang.lists.ordered+t.representShortcut(\"insertOrderedList\"),click:t.context.createInvokeHandler(\"editor.insertOrderedList\")}).render()}));var i=this.button({contents:this.ui.icon(this.options.icons.alignLeft),tooltip:this.lang.paragraph.left+this.representShortcut(\"justifyLeft\"),click:this.context.createInvokeHandler(\"editor.justifyLeft\")}),a=this.button({contents:this.ui.icon(this.options.icons.alignCenter),tooltip:this.lang.paragraph.center+this.representShortcut(\"justifyCenter\"),click:this.context.createInvokeHandler(\"editor.justifyCenter\")}),s=this.button({contents:this.ui.icon(this.options.icons.alignRight),tooltip:this.lang.paragraph.right+this.representShortcut(\"justifyRight\"),click:this.context.createInvokeHandler(\"editor.justifyRight\")}),l=this.button({contents:this.ui.icon(this.options.icons.alignJustify),tooltip:this.lang.paragraph.justify+this.representShortcut(\"justifyFull\"),click:this.context.createInvokeHandler(\"editor.justifyFull\")}),c=this.button({contents:this.ui.icon(this.options.icons.outdent),tooltip:this.lang.paragraph.outdent+this.representShortcut(\"outdent\"),click:this.context.createInvokeHandler(\"editor.outdent\")}),u=this.button({contents:this.ui.icon(this.options.icons.indent),tooltip:this.lang.paragraph.indent+this.representShortcut(\"indent\"),click:this.context.createInvokeHandler(\"editor.indent\")});this.context.memo(\"button.justifyLeft\",g.invoke(i,\"render\")),this.context.memo(\"button.justifyCenter\",g.invoke(a,\"render\")),this.context.memo(\"button.justifyRight\",g.invoke(s,\"render\")),this.context.memo(\"button.justifyFull\",g.invoke(l,\"render\")),this.context.memo(\"button.outdent\",g.invoke(c,\"render\")),this.context.memo(\"button.indent\",g.invoke(u,\"render\")),this.context.memo(\"button.paragraph\",(function(){return t.ui.buttonGroup([t.button({className:\"dropdown-toggle\",contents:t.ui.dropdownButtonContents(t.ui.icon(t.options.icons.alignLeft),t.options),tooltip:t.lang.paragraph.paragraph,data:{toggle:\"dropdown\"}}),t.ui.dropdown([t.ui.buttonGroup({className:\"note-align\",children:[i,a,s,l]}),t.ui.buttonGroup({className:\"note-list\",children:[c,u]})])]).render()})),this.context.memo(\"button.height\",(function(){return t.ui.buttonGroup([t.button({className:\"dropdown-toggle\",contents:t.ui.dropdownButtonContents(t.ui.icon(t.options.icons.textHeight),t.options),tooltip:t.lang.font.height,data:{toggle:\"dropdown\"}}),t.ui.dropdownCheck({items:t.options.lineHeights,checkClassName:t.options.icons.menuCheck,className:\"dropdown-line-height\",title:t.lang.font.height,click:t.context.createInvokeHandler(\"editor.lineHeight\")})]).render()})),this.context.memo(\"button.table\",(function(){return t.ui.buttonGroup([t.button({className:\"dropdown-toggle\",contents:t.ui.dropdownButtonContents(t.ui.icon(t.options.icons.table),t.options),tooltip:t.lang.table.table,data:{toggle:\"dropdown\"}}),t.ui.dropdown({title:t.lang.table.table,className:\"note-table\",items:['<div class=\"note-dimension-picker\">','<div class=\"note-dimension-picker-mousecatcher\" data-event=\"insertTable\" data-value=\"1x1\"></div>','<div class=\"note-dimension-picker-highlighted\"></div>','<div class=\"note-dimension-picker-unhighlighted\"></div>',\"</div>\",'<div class=\"note-dimension-display\">1 x 1</div>'].join(\"\")})],{callback:function(e){e.find(\".note-dimension-picker-mousecatcher\").css({width:t.options.insertTableMaxSize.col+\"em\",height:t.options.insertTableMaxSize.row+\"em\"}).on(\"mousedown\",t.context.createInvokeHandler(\"editor.insertTable\")).on(\"mousemove\",t.tableMoveHandler.bind(t))}}).render()})),this.context.memo(\"button.link\",(function(){return t.button({contents:t.ui.icon(t.options.icons.link),tooltip:t.lang.link.link+t.representShortcut(\"linkDialog.show\"),click:t.context.createInvokeHandler(\"linkDialog.show\")}).render()})),this.context.memo(\"button.picture\",(function(){return t.button({contents:t.ui.icon(t.options.icons.picture),tooltip:t.lang.image.image,click:t.context.createInvokeHandler(\"imageDialog.show\")}).render()})),this.context.memo(\"button.video\",(function(){return t.button({contents:t.ui.icon(t.options.icons.video),tooltip:t.lang.video.video,click:t.context.createInvokeHandler(\"videoDialog.show\")}).render()})),this.context.memo(\"button.hr\",(function(){return t.button({contents:t.ui.icon(t.options.icons.minus),tooltip:t.lang.hr.insert+t.representShortcut(\"insertHorizontalRule\"),click:t.context.createInvokeHandler(\"editor.insertHorizontalRule\")}).render()})),this.context.memo(\"button.fullscreen\",(function(){return t.button({className:\"btn-fullscreen note-codeview-keep\",contents:t.ui.icon(t.options.icons.arrowsAlt),tooltip:t.lang.options.fullscreen,click:t.context.createInvokeHandler(\"fullscreen.toggle\")}).render()})),this.context.memo(\"button.codeview\",(function(){return t.button({className:\"btn-codeview note-codeview-keep\",contents:t.ui.icon(t.options.icons.code),tooltip:t.lang.options.codeview,click:t.context.createInvokeHandler(\"codeview.toggle\")}).render()})),this.context.memo(\"button.redo\",(function(){return t.button({contents:t.ui.icon(t.options.icons.redo),tooltip:t.lang.history.redo+t.representShortcut(\"redo\"),click:t.context.createInvokeHandler(\"editor.redo\")}).render()})),this.context.memo(\"button.undo\",(function(){return t.button({contents:t.ui.icon(t.options.icons.undo),tooltip:t.lang.history.undo+t.representShortcut(\"undo\"),click:t.context.createInvokeHandler(\"editor.undo\")}).render()})),this.context.memo(\"button.help\",(function(){return t.button({contents:t.ui.icon(t.options.icons.question),tooltip:t.lang.options.help,click:t.context.createInvokeHandler(\"helpDialog.show\")}).render()}))}},{key:\"addImagePopoverButtons\",value:function(){var t=this;this.context.memo(\"button.resizeFull\",(function(){return t.button({contents:'<span class=\"note-fontsize-10\">100%</span>',tooltip:t.lang.image.resizeFull,click:t.context.createInvokeHandler(\"editor.resize\",\"1\")}).render()})),this.context.memo(\"button.resizeHalf\",(function(){return t.button({contents:'<span class=\"note-fontsize-10\">50%</span>',tooltip:t.lang.image.resizeHalf,click:t.context.createInvokeHandler(\"editor.resize\",\"0.5\")}).render()})),this.context.memo(\"button.resizeQuarter\",(function(){return t.button({contents:'<span class=\"note-fontsize-10\">25%</span>',tooltip:t.lang.image.resizeQuarter,click:t.context.createInvokeHandler(\"editor.resize\",\"0.25\")}).render()})),this.context.memo(\"button.resizeNone\",(function(){return t.button({contents:t.ui.icon(t.options.icons.rollback),tooltip:t.lang.image.resizeNone,click:t.context.createInvokeHandler(\"editor.resize\",\"0\")}).render()})),this.context.memo(\"button.floatLeft\",(function(){return t.button({contents:t.ui.icon(t.options.icons.floatLeft),tooltip:t.lang.image.floatLeft,click:t.context.createInvokeHandler(\"editor.floatMe\",\"left\")}).render()})),this.context.memo(\"button.floatRight\",(function(){return t.button({contents:t.ui.icon(t.options.icons.floatRight),tooltip:t.lang.image.floatRight,click:t.context.createInvokeHandler(\"editor.floatMe\",\"right\")}).render()})),this.context.memo(\"button.floatNone\",(function(){return t.button({contents:t.ui.icon(t.options.icons.rollback),tooltip:t.lang.image.floatNone,click:t.context.createInvokeHandler(\"editor.floatMe\",\"none\")}).render()})),this.context.memo(\"button.removeMedia\",(function(){return t.button({contents:t.ui.icon(t.options.icons.trash),tooltip:t.lang.image.remove,click:t.context.createInvokeHandler(\"editor.removeMedia\")}).render()}))}},{key:\"addLinkPopoverButtons\",value:function(){var t=this;this.context.memo(\"button.linkDialogShow\",(function(){return t.button({contents:t.ui.icon(t.options.icons.link),tooltip:t.lang.link.edit,click:t.context.createInvokeHandler(\"linkDialog.show\")}).render()})),this.context.memo(\"button.unlink\",(function(){return t.button({contents:t.ui.icon(t.options.icons.unlink),tooltip:t.lang.link.unlink,click:t.context.createInvokeHandler(\"editor.unlink\")}).render()}))}},{key:\"addTablePopoverButtons\",value:function(){var t=this;this.context.memo(\"button.addRowUp\",(function(){return t.button({className:\"btn-md\",contents:t.ui.icon(t.options.icons.rowAbove),tooltip:t.lang.table.addRowAbove,click:t.context.createInvokeHandler(\"editor.addRow\",\"top\")}).render()})),this.context.memo(\"button.addRowDown\",(function(){return t.button({className:\"btn-md\",contents:t.ui.icon(t.options.icons.rowBelow),tooltip:t.lang.table.addRowBelow,click:t.context.createInvokeHandler(\"editor.addRow\",\"bottom\")}).render()})),this.context.memo(\"button.addColLeft\",(function(){return t.button({className:\"btn-md\",contents:t.ui.icon(t.options.icons.colBefore),tooltip:t.lang.table.addColLeft,click:t.context.createInvokeHandler(\"editor.addCol\",\"left\")}).render()})),this.context.memo(\"button.addColRight\",(function(){return t.button({className:\"btn-md\",contents:t.ui.icon(t.options.icons.colAfter),tooltip:t.lang.table.addColRight,click:t.context.createInvokeHandler(\"editor.addCol\",\"right\")}).render()})),this.context.memo(\"button.deleteRow\",(function(){return t.button({className:\"btn-md\",contents:t.ui.icon(t.options.icons.rowRemove),tooltip:t.lang.table.delRow,click:t.context.createInvokeHandler(\"editor.deleteRow\")}).render()})),this.context.memo(\"button.deleteCol\",(function(){return t.button({className:\"btn-md\",contents:t.ui.icon(t.options.icons.colRemove),tooltip:t.lang.table.delCol,click:t.context.createInvokeHandler(\"editor.deleteCol\")}).render()})),this.context.memo(\"button.deleteTable\",(function(){return t.button({className:\"btn-md\",contents:t.ui.icon(t.options.icons.trash),tooltip:t.lang.table.delTable,click:t.context.createInvokeHandler(\"editor.deleteTable\")}).render()}))}},{key:\"build\",value:function(t,e){for(var o=0,n=e.length;o<n;o++){for(var i=e[o],r=Array.isArray(i)?i[0]:i,a=Array.isArray(i)?1===i.length?[i[0]]:i[1]:[i],s=this.ui.buttonGroup({className:\"note-\"+r}).render(),l=0,c=a.length;l<c;l++){var u=this.context.memo(\"button.\"+a[l]);u&&s.append(\"function\"==typeof u?u(this.context):u)}s.appendTo(t)}}},{key:\"updateCurrentStyle\",value:function(t){var e=t||this.$toolbar,o=this.context.invoke(\"editor.currentStyle\");if(this.updateBtnStates(e,{\".note-btn-bold\":function(){return\"bold\"===o[\"font-bold\"]},\".note-btn-italic\":function(){return\"italic\"===o[\"font-italic\"]},\".note-btn-underline\":function(){return\"underline\"===o[\"font-underline\"]},\".note-btn-subscript\":function(){return\"subscript\"===o[\"font-subscript\"]},\".note-btn-superscript\":function(){return\"superscript\"===o[\"font-superscript\"]},\".note-btn-strikethrough\":function(){return\"strikethrough\"===o[\"font-strikethrough\"]}}),o[\"font-family\"]){var n=o[\"font-family\"].split(\",\").map((function(t){return t.replace(/[\\'\\\"]/g,\"\").replace(/\\s+$/,\"\").replace(/^\\s+/,\"\")})),i=C.find(n,this.isFontInstalled.bind(this));e.find(\".dropdown-fontname a\").each((function(t,e){var o=r()(e),n=o.data(\"value\")+\"\"==i+\"\";o.toggleClass(\"checked\",n)})),e.find(\".note-current-fontname\").text(i).css(\"font-family\",i)}if(o[\"font-size\"]){var a=o[\"font-size\"];e.find(\".dropdown-fontsize a\").each((function(t,e){var o=r()(e),n=o.data(\"value\")+\"\"==a+\"\";o.toggleClass(\"checked\",n)})),e.find(\".note-current-fontsize\").text(a);var s=o[\"font-size-unit\"];e.find(\".dropdown-fontsizeunit a\").each((function(t,e){var o=r()(e),n=o.data(\"value\")+\"\"==s+\"\";o.toggleClass(\"checked\",n)})),e.find(\".note-current-fontsizeunit\").text(s)}if(o[\"line-height\"]){var l=o[\"line-height\"];e.find(\".dropdown-line-height a\").each((function(t,e){var o=r()(e),n=r()(e).data(\"value\")+\"\"==l+\"\";o.toggleClass(\"checked\",n)})),e.find(\".note-current-line-height\").text(l)}}},{key:\"updateBtnStates\",value:function(t,e){var o=this;r().each(e,(function(e,n){o.ui.toggleBtnActive(t.find(e),n())}))}},{key:\"tableMoveHandler\",value:function(t){var e,o=r()(t.target.parentNode),n=o.next(),i=o.find(\".note-dimension-picker-mousecatcher\"),a=o.find(\".note-dimension-picker-highlighted\"),s=o.find(\".note-dimension-picker-unhighlighted\");if(void 0===t.offsetX){var l=r()(t.target).offset();e={x:t.pageX-l.left,y:t.pageY-l.top}}else e={x:t.offsetX,y:t.offsetY};var c=Math.ceil(e.x/18)||1,u=Math.ceil(e.y/18)||1;a.css({width:c+\"em\",height:u+\"em\"}),i.data(\"value\",c+\"x\"+u),c>3&&c<this.options.insertTableMaxSize.col&&s.css({width:c+1+\"em\"}),u>3&&u<this.options.insertTableMaxSize.row&&s.css({height:u+1+\"em\"}),n.html(c+\" x \"+u)}}],e&&_e(t.prototype,e),o&&_e(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Ye(t){return Ye=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Ye(t)}function Xe(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Qe(n.key),n)}}function Qe(t){var e=function(t,e){if(\"object\"!=Ye(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Ye(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Ye(e)?e:e+\"\"}var Je=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.$window=r()(window),this.$document=r()(document),this.ui=r().summernote.ui,this.$note=e.layoutInfo.note,this.$editor=e.layoutInfo.editor,this.$toolbar=e.layoutInfo.toolbar,this.$editable=e.layoutInfo.editable,this.$statusbar=e.layoutInfo.statusbar,this.options=e.options,this.isFollowing=!1,this.followScroll=this.followScroll.bind(this)},(e=[{key:\"shouldInitialize\",value:function(){return!this.options.airMode}},{key:\"initialize\",value:function(){var t=this;this.options.toolbar=this.options.toolbar||[],this.options.toolbar.length?this.context.invoke(\"buttons.build\",this.$toolbar,this.options.toolbar):this.$toolbar.hide(),this.options.toolbarContainer&&this.$toolbar.appendTo(this.options.toolbarContainer),this.changeContainer(!1),this.$note.on(\"summernote.keyup summernote.mouseup summernote.change\",(function(){t.context.invoke(\"buttons.updateCurrentStyle\")})),this.context.invoke(\"buttons.updateCurrentStyle\"),this.options.followingToolbar&&this.$window.on(\"scroll resize\",this.followScroll)}},{key:\"destroy\",value:function(){this.$toolbar.children().remove(),this.options.followingToolbar&&this.$window.off(\"scroll resize\",this.followScroll)}},{key:\"followScroll\",value:function(){if(this.$editor.hasClass(\"fullscreen\"))return!1;var t=this.$editor.outerHeight(),e=this.$editor.width(),o=this.$toolbar.height(),n=this.$statusbar.height(),i=0;this.options.otherStaticBar&&(i=r()(this.options.otherStaticBar).outerHeight());var a=this.$document.scrollTop(),s=this.$editor.offset().top,l=s-i,c=s+t-i-o-n;!this.isFollowing&&a>l&&a<c-o?(this.isFollowing=!0,this.$editable.css({marginTop:this.$toolbar.outerHeight()}),this.$toolbar.css({position:\"fixed\",top:i,width:e,zIndex:1e3})):this.isFollowing&&(a<l||a>c)&&(this.isFollowing=!1,this.$toolbar.css({position:\"relative\",top:0,width:\"100%\",zIndex:\"auto\"}),this.$editable.css({marginTop:\"\"}))}},{key:\"changeContainer\",value:function(t){t?this.$toolbar.prependTo(this.$editor):this.options.toolbarContainer&&this.$toolbar.appendTo(this.options.toolbarContainer),this.options.followingToolbar&&this.followScroll()}},{key:\"updateFullscreen\",value:function(t){this.ui.toggleBtnActive(this.$toolbar.find(\".btn-fullscreen\"),t),this.changeContainer(t)}},{key:\"updateCodeview\",value:function(t){this.ui.toggleBtnActive(this.$toolbar.find(\".btn-codeview\"),t),t?this.deactivate():this.activate()}},{key:\"activate\",value:function(t){var e=this.$toolbar.find(\"button\");t||(e=e.not(\".note-codeview-keep\")),this.ui.toggleBtn(e,!0)}},{key:\"deactivate\",value:function(t){var e=this.$toolbar.find(\"button\");t||(e=e.not(\".note-codeview-keep\")),this.ui.toggleBtn(e,!1)}}])&&Xe(t.prototype,e),o&&Xe(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function to(t){return to=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},to(t)}function eo(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,oo(n.key),n)}}function oo(t){var e=function(t,e){if(\"object\"!=to(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=to(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==to(e)?e:e+\"\"}var no=/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,io=/^(\\+?\\d{1,3}[\\s-]?)?(\\d{1,4})[\\s-]?(\\d{1,4})[\\s-]?(\\d{1,4})$/,ro=/^([A-Za-z][A-Za-z0-9+-.]*\\:|#|\\/)/,ao=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.ui=r().summernote.ui,this.$body=r()(document.body),this.$editor=e.layoutInfo.editor,this.options=e.options,this.lang=this.options.langInfo,e.memo(\"help.linkDialog.show\",this.options.langInfo.help[\"linkDialog.show\"])},(e=[{key:\"initialize\",value:function(){var t=this.options.dialogsInBody?this.$body:this.options.container,e=['<div class=\"form-group note-form-group\">','<label for=\"note-dialog-link-txt-'.concat(this.options.id,'\" class=\"note-form-label\">').concat(this.lang.link.textToDisplay,\"</label>\"),'<input id=\"note-dialog-link-txt-'.concat(this.options.id,'\" class=\"note-link-text form-control note-form-control note-input\" type=\"text\"/>'),\"</div>\",'<div class=\"form-group note-form-group\">','<label for=\"note-dialog-link-url-'.concat(this.options.id,'\" class=\"note-form-label\">').concat(this.lang.link.url,\"</label>\"),'<input id=\"note-dialog-link-url-'.concat(this.options.id,'\" class=\"note-link-url form-control note-form-control note-input\" type=\"text\" value=\"http://\"/>'),\"</div>\",this.options.disableLinkTarget?\"\":r()(\"<div></div>\").append(this.ui.checkbox({className:\"sn-checkbox-open-in-new-window\",text:this.lang.link.openInNewWindow,checked:!0}).render()).html()].join(\"\"),o='<input type=\"button\" href=\"#\" class=\"'.concat(\"btn btn-primary note-btn note-btn-primary note-link-btn\",'\" value=\"').concat(this.lang.link.insert,'\" disabled>');this.$dialog=this.ui.dialog({className:\"link-dialog\",title:this.lang.link.insert,fade:this.options.dialogsFade,body:e,footer:o}).render().appendTo(t)}},{key:\"destroy\",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:\"bindEnterKey\",value:function(t,e){t.on(\"keypress\",(function(t){t.keyCode===Nt.code.ENTER&&(t.preventDefault(),e.trigger(\"click\"))}))}},{key:\"checkLinkUrl\",value:function(t){return no.test(t)?\"mailto://\"+t:io.test(t)?\"tel://\"+t:ro.test(t)?t:\"http://\"+t}},{key:\"onCheckLinkUrl\",value:function(t){var e=this;t.on(\"blur\",(function(t){t.target.value=\"\"==t.target.value?\"\":e.checkLinkUrl(t.target.value)}))}},{key:\"toggleLinkBtn\",value:function(t,e,o){this.ui.toggleBtn(t,e.val()&&o.val())}},{key:\"showLinkDialog\",value:function(t){var e=this;return r().Deferred((function(o){var n=e.$dialog.find(\".note-link-text\"),i=e.$dialog.find(\".note-link-url\"),r=e.$dialog.find(\".note-link-btn\"),a=e.$dialog.find(\".sn-checkbox-open-in-new-window input[type=checkbox]\");e.ui.onDialogShown(e.$dialog,(function(){e.context.triggerEvent(\"dialog.shown\"),!t.url&&g.isValidUrl(t.text)&&(t.url=e.checkLinkUrl(t.text)),n.on(\"input paste propertychange\",(function(){var o=n.val(),a=document.createElement(\"div\");a.innerText=o,o=a.innerHTML,t.text=o,e.toggleLinkBtn(r,n,i)})).val(t.text),i.on(\"input paste propertychange\",(function(){t.text||n.val(i.val()),e.toggleLinkBtn(r,n,i)})).val(t.url),m.isSupportTouch||i.trigger(\"focus\"),e.toggleLinkBtn(r,n,i),e.bindEnterKey(i,r),e.bindEnterKey(n,r),e.onCheckLinkUrl(i);var s=void 0!==t.isNewWindow?t.isNewWindow:e.context.options.linkTargetBlank;a.prop(\"checked\",s),r.one(\"click\",(function(r){r.preventDefault(),o.resolve({range:t.range,url:i.val(),text:n.val(),isNewWindow:a.is(\":checked\")}),e.ui.hideDialog(e.$dialog)}))})),e.ui.onDialogHidden(e.$dialog,(function(){n.off(),i.off(),r.off(),\"pending\"===o.state()&&o.reject()})),e.ui.showDialog(e.$dialog)})).promise()}},{key:\"show\",value:function(){var t=this,e=this.context.invoke(\"editor.getLinkInfo\");this.context.invoke(\"editor.saveRange\"),this.showLinkDialog(e).then((function(e){t.context.invoke(\"editor.restoreRange\"),t.context.invoke(\"editor.createLink\",e)})).fail((function(){t.context.invoke(\"editor.restoreRange\")}))}}])&&eo(t.prototype,e),o&&eo(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function so(t){return so=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},so(t)}function lo(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,co(n.key),n)}}function co(t){var e=function(t,e){if(\"object\"!=so(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=so(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==so(e)?e:e+\"\"}var uo=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.ui=r().summernote.ui,this.options=e.options,this.events={\"summernote.keyup summernote.mouseup summernote.change summernote.scroll\":function(){o.update()},\"summernote.disable summernote.dialog.shown\":function(){o.hide()},\"summernote.blur\":function(t,e){e.originalEvent&&e.originalEvent.relatedTarget&&o.$popover[0].contains(e.originalEvent.relatedTarget)||o.hide()}}},(e=[{key:\"shouldInitialize\",value:function(){return!C.isEmpty(this.options.popover.link)}},{key:\"initialize\",value:function(){this.$popover=this.ui.popover({className:\"note-link-popover\",callback:function(t){t.find(\".popover-content,.note-popover-content\").prepend('<span><a target=\"_blank\"></a>&nbsp;</span>')}}).render().appendTo(this.options.container);var t=this.$popover.find(\".popover-content,.note-popover-content\");this.context.invoke(\"buttons.build\",t,this.options.popover.link),this.$popover.on(\"mousedown\",(function(t){t.preventDefault()}))}},{key:\"destroy\",value:function(){this.$popover.remove()}},{key:\"update\",value:function(){if(this.context.invoke(\"editor.hasFocus\")){var t=this.context.invoke(\"editor.getLastRange\");if(t.isCollapsed()&&t.isOnAnchor()){var e=pt.ancestor(t.sc,pt.isAnchor),o=r()(e).attr(\"href\");this.$popover.find(\"a\").attr(\"href\",o).text(o);var n=pt.posFromPlaceholder(e),i=r()(this.options.container).offset();n.top-=i.top,n.left-=i.left,this.$popover.css({display:\"block\",left:n.left,top:n.top})}else this.hide()}else this.hide()}},{key:\"hide\",value:function(){this.$popover.hide()}}])&&lo(t.prototype,e),o&&lo(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function fo(t){return fo=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},fo(t)}function ho(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,po(n.key),n)}}function po(t){var e=function(t,e){if(\"object\"!=fo(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=fo(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==fo(e)?e:e+\"\"}var mo=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.ui=r().summernote.ui,this.$body=r()(document.body),this.$editor=e.layoutInfo.editor,this.options=e.options,this.lang=this.options.langInfo},(e=[{key:\"initialize\",value:function(){var t=\"\";if(this.options.maximumImageFileSize){var e=Math.floor(Math.log(this.options.maximumImageFileSize)/Math.log(1024)),o=1*(this.options.maximumImageFileSize/Math.pow(1024,e)).toFixed(2)+\" \"+\" KMGTP\"[e]+\"B\";t=\"<small>\".concat(this.lang.image.maximumFileSize+\" : \"+o,\"</small>\")}var n=this.options.dialogsInBody?this.$body:this.options.container,i=['<div class=\"form-group note-form-group note-group-select-from-files\">','<label for=\"note-dialog-image-file-'+this.options.id+'\" class=\"note-form-label\">'+this.lang.image.selectFromFiles+\"</label>\",'<input id=\"note-dialog-image-file-'+this.options.id+'\" class=\"note-image-input form-control-file note-form-control note-input\" ',' type=\"file\" name=\"files\" accept=\"'+this.options.acceptImageFileTypes+'\" multiple=\"multiple\"/>',t,\"</div>\",'<div class=\"form-group note-group-image-url\">','<label for=\"note-dialog-image-url-'+this.options.id+'\" class=\"note-form-label\">'+this.lang.image.url+\"</label>\",'<input id=\"note-dialog-image-url-'+this.options.id+'\" class=\"note-image-url form-control note-form-control note-input\" type=\"text\"/>',\"</div>\"].join(\"\"),r='<input type=\"button\" href=\"#\" class=\"'.concat(\"btn btn-primary note-btn note-btn-primary note-image-btn\",'\" value=\"').concat(this.lang.image.insert,'\" disabled>');this.$dialog=this.ui.dialog({title:this.lang.image.insert,fade:this.options.dialogsFade,body:i,footer:r}).render().appendTo(n)}},{key:\"destroy\",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:\"bindEnterKey\",value:function(t,e){t.on(\"keypress\",(function(t){t.keyCode===Nt.code.ENTER&&(t.preventDefault(),e.trigger(\"click\"))}))}},{key:\"show\",value:function(){var t=this;this.context.invoke(\"editor.saveRange\"),this.showImageDialog().then((function(e){t.ui.hideDialog(t.$dialog),t.context.invoke(\"editor.restoreRange\"),\"string\"==typeof e?t.options.callbacks.onImageLinkInsert?t.context.triggerEvent(\"image.link.insert\",e):t.context.invoke(\"editor.insertImage\",e):t.context.invoke(\"editor.insertImagesOrCallback\",e)})).fail((function(){t.context.invoke(\"editor.restoreRange\")}))}},{key:\"showImageDialog\",value:function(){var t=this;return r().Deferred((function(e){var o=t.$dialog.find(\".note-image-input\"),n=t.$dialog.find(\".note-image-url\"),i=t.$dialog.find(\".note-image-btn\");t.ui.onDialogShown(t.$dialog,(function(){t.context.triggerEvent(\"dialog.shown\"),o.replaceWith(o.clone().on(\"change\",(function(t){e.resolve(t.target.files||t.target.value)})).val(\"\")),n.on(\"input paste propertychange\",(function(){t.ui.toggleBtn(i,n.val())})).val(\"\"),m.isSupportTouch||n.trigger(\"focus\"),i.on(\"click\",(function(t){t.preventDefault(),e.resolve(n.val())})),t.bindEnterKey(n,i)})),t.ui.onDialogHidden(t.$dialog,(function(){o.off(),n.off(),i.off(),\"pending\"===e.state()&&e.reject()})),t.ui.showDialog(t.$dialog)}))}}])&&ho(t.prototype,e),o&&ho(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function vo(t){return vo=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},vo(t)}function go(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,bo(n.key),n)}}function bo(t){var e=function(t,e){if(\"object\"!=vo(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=vo(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==vo(e)?e:e+\"\"}var yo=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.ui=r().summernote.ui,this.editable=e.layoutInfo.editable[0],this.options=e.options,this.events={\"summernote.disable summernote.dialog.shown\":function(){o.hide()},\"summernote.blur\":function(t,e){e.originalEvent&&e.originalEvent.relatedTarget&&o.$popover[0].contains(e.originalEvent.relatedTarget)||o.hide()}}},e=[{key:\"shouldInitialize\",value:function(){return!C.isEmpty(this.options.popover.image)}},{key:\"initialize\",value:function(){this.$popover=this.ui.popover({className:\"note-image-popover\"}).render().appendTo(this.options.container);var t=this.$popover.find(\".popover-content,.note-popover-content\");this.context.invoke(\"buttons.build\",t,this.options.popover.image),this.$popover.on(\"mousedown\",(function(t){t.preventDefault()}))}},{key:\"destroy\",value:function(){this.$popover.remove()}},{key:\"update\",value:function(t,e){if(pt.isImg(t)){var o=r()(t).offset(),n=r()(this.options.container).offset(),i={};this.options.popatmouse?(i.left=e.pageX-20,i.top=e.pageY):i=o,i.top-=n.top,i.left-=n.left,this.$popover.css({display:\"block\",left:i.left,top:i.top})}else this.hide()}},{key:\"hide\",value:function(){this.$popover.hide()}}],e&&go(t.prototype,e),o&&go(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function ko(t){return ko=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},ko(t)}function wo(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Co(n.key),n)}}function Co(t){var e=function(t,e){if(\"object\"!=ko(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=ko(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==ko(e)?e:e+\"\"}var So=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.ui=r().summernote.ui,this.options=e.options,this.events={\"summernote.mousedown\":function(t,e){o.update(e.target)},\"summernote.keyup summernote.scroll summernote.change\":function(){o.update()},\"summernote.disable summernote.dialog.shown\":function(){o.hide()},\"summernote.blur\":function(t,e){e.originalEvent&&e.originalEvent.relatedTarget&&o.$popover[0].contains(e.originalEvent.relatedTarget)||o.hide()}}},e=[{key:\"shouldInitialize\",value:function(){return!C.isEmpty(this.options.popover.table)}},{key:\"initialize\",value:function(){this.$popover=this.ui.popover({className:\"note-table-popover\"}).render().appendTo(this.options.container);var t=this.$popover.find(\".popover-content,.note-popover-content\");this.context.invoke(\"buttons.build\",t,this.options.popover.table),m.isFF&&document.execCommand(\"enableInlineTableEditing\",!1,!1),this.$popover.on(\"mousedown\",(function(t){t.preventDefault()}))}},{key:\"destroy\",value:function(){this.$popover.remove()}},{key:\"update\",value:function(t){if(this.context.isDisabled())return!1;var e=pt.isCell(t)||pt.isCell(null==t?void 0:t.parentElement);if(e){var o=pt.posFromPlaceholder(t),n=r()(this.options.container).offset();o.top-=n.top,o.left-=n.left,this.$popover.css({display:\"block\",left:o.left,top:o.top})}else this.hide();return e}},{key:\"hide\",value:function(){this.$popover.hide()}}],e&&wo(t.prototype,e),o&&wo(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function xo(t){return xo=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},xo(t)}function To(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Eo(n.key),n)}}function Eo(t){var e=function(t,e){if(\"object\"!=xo(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=xo(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==xo(e)?e:e+\"\"}var Po=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.ui=r().summernote.ui,this.$body=r()(document.body),this.$editor=e.layoutInfo.editor,this.options=e.options,this.lang=this.options.langInfo},(e=[{key:\"initialize\",value:function(){var t=this.options.dialogsInBody?this.$body:this.options.container,e=['<div class=\"form-group note-form-group row-fluid\">','<label for=\"note-dialog-video-url-'.concat(this.options.id,'\" class=\"note-form-label\">').concat(this.lang.video.url,' <small class=\"text-muted\">').concat(this.lang.video.providers,\"</small></label>\"),'<input id=\"note-dialog-video-url-'.concat(this.options.id,'\" class=\"note-video-url form-control note-form-control note-input\" type=\"text\"/>'),\"</div>\"].join(\"\"),o='<input type=\"button\" href=\"#\" class=\"'.concat(\"btn btn-primary note-btn note-btn-primary note-video-btn\",'\" value=\"').concat(this.lang.video.insert,'\" disabled>');this.$dialog=this.ui.dialog({title:this.lang.video.insert,fade:this.options.dialogsFade,body:e,footer:o}).render().appendTo(t)}},{key:\"destroy\",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:\"bindEnterKey\",value:function(t,e){t.on(\"keypress\",(function(t){t.keyCode===Nt.code.ENTER&&(t.preventDefault(),e.trigger(\"click\"))}))}},{key:\"createVideoNode\",value:function(t){var e,o=t.match(/(?:youtu\\.be\\/|youtube\\.com\\/(?:[^\\/\\n\\s]+\\/\\S+\\/|(?:v|e(?:mbed)?)\\/|\\S*?[?&]v=|shorts\\/|live\\/))([^&\\n?]+)(?:.*[?&]t=([^&\\n]+))?.*/),n=t.match(/(?:\\.|\\/\\/)drive\\.google\\.com\\/file\\/d\\/(.[a-zA-Z0-9_-]*)\\/view/),i=t.match(/(?:www\\.|\\/\\/)instagram\\.com\\/(reel|p)\\/(.[a-zA-Z0-9_-]*)/),a=t.match(/\\/\\/vine\\.co\\/v\\/([a-zA-Z0-9]+)/),s=t.match(/\\/\\/(player\\.)?vimeo\\.com\\/([a-z]*\\/)*(\\d+)[?]?.*/),l=t.match(/.+dailymotion.com\\/(video|hub)\\/([^_]+)[^#]*(#video=([^_&]+))?/),c=t.match(/\\/\\/v\\.youku\\.com\\/v_show\\/id_(\\w+)=*\\.html/),u=t.match(/\\/\\/(.*)\\/videos\\/watch\\/([^?]*)(?:\\?(?:start=(\\w*))?(?:&stop=(\\w*))?(?:&loop=([10]))?(?:&autoplay=([10]))?(?:&muted=([10]))?)?/),d=t.match(/\\/\\/v\\.qq\\.com.*?vid=(.+)/),f=t.match(/\\/\\/v\\.qq\\.com\\/x?\\/?(page|cover).*?\\/([^\\/]+)\\.html\\??.*/),h=t.match(/^.+.(mp4|m4v)$/),p=t.match(/^.+.(ogg|ogv)$/),m=t.match(/^.+.(webm)$/),v=t.match(/(?:www\\.|\\/\\/)facebook\\.com\\/([^\\/]+)\\/videos\\/([0-9]+)/);if(o&&11===o[1].length){var g=o[1],b=0;if(void 0!==o[2]){var y=o[2].match(/^(?:(\\d+)h)?(?:(\\d+)m)?(?:(\\d+)s)?$/);if(y)for(var k=[3600,60,1],w=0,C=k.length;w<C;w++)b+=void 0!==y[w+1]?k[w]*parseInt(y[w+1],10):0;else b=parseInt(o[2],10)}e=r()(\"<iframe>\").attr(\"frameborder\",0).attr(\"src\",\"//www.youtube.com/embed/\"+g+(b>0?\"?start=\"+b:\"\")).attr(\"width\",\"640\").attr(\"height\",\"360\")}else if(n&&n[0].length)e=r()(\"<iframe>\").attr(\"frameborder\",0).attr(\"src\",\"https://drive.google.com/file/d/\"+n[1]+\"/preview\").attr(\"width\",\"640\").attr(\"height\",\"480\");else if(i&&i[0].length)e=r()(\"<iframe>\").attr(\"frameborder\",0).attr(\"src\",\"https://instagram.com/p/\"+i[2]+\"/embed/\").attr(\"width\",\"612\").attr(\"height\",\"710\").attr(\"scrolling\",\"no\").attr(\"allowtransparency\",\"true\");else if(a&&a[0].length)e=r()(\"<iframe>\").attr(\"frameborder\",0).attr(\"src\",a[0]+\"/embed/simple\").attr(\"width\",\"600\").attr(\"height\",\"600\").attr(\"class\",\"vine-embed\");else if(s&&s[3].length)e=r()(\"<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>\").attr(\"frameborder\",0).attr(\"src\",\"//player.vimeo.com/video/\"+s[3]).attr(\"width\",\"640\").attr(\"height\",\"360\");else if(l&&l[2].length)e=r()(\"<iframe>\").attr(\"frameborder\",0).attr(\"src\",\"//www.dailymotion.com/embed/video/\"+l[2]).attr(\"width\",\"640\").attr(\"height\",\"360\");else if(c&&c[1].length)e=r()(\"<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>\").attr(\"frameborder\",0).attr(\"height\",\"498\").attr(\"width\",\"510\").attr(\"src\",\"//player.youku.com/embed/\"+c[1]);else if(u&&u[0].length){var S=0;\"undefined\"!==u[2]&&(S=u[2]);var x=0;\"undefined\"!==u[3]&&(x=u[3]);var T=0;\"undefined\"!==u[4]&&(T=u[4]);var E=0;\"undefined\"!==u[5]&&(E=u[5]);var P=0;\"undefined\"!==u[6]&&(P=u[6]),e=r()('<iframe allowfullscreen sandbox=\"allow-same-origin allow-scripts allow-popups\">').attr(\"frameborder\",0).attr(\"src\",\"//\"+u[1]+\"/videos/embed/\"+u[2]+\"?loop=\"+T+\"&autoplay=\"+E+\"&muted=\"+P+(S>0?\"&start=\"+S:\"\")+(x>0?\"&end=\"+b:\"\")).attr(\"width\",\"560\").attr(\"height\",\"315\")}else if(d&&d[1].length||f&&f[2].length){var N=d&&d[1].length?d[1]:f[2];e=r()(\"<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>\").attr(\"frameborder\",0).attr(\"height\",\"310\").attr(\"width\",\"500\").attr(\"src\",\"https://v.qq.com/txp/iframe/player.html?vid=\"+N+\"&amp;auto=0\")}else if(h||p||m)e=r()(\"<video controls>\").attr(\"src\",t).attr(\"width\",\"640\").attr(\"height\",\"360\");else{if(!v||!v[0].length)return!1;e=r()(\"<iframe>\").attr(\"frameborder\",0).attr(\"src\",\"https://www.facebook.com/plugins/video.php?href=\"+encodeURIComponent(v[0])+\"&show_text=0&width=560\").attr(\"width\",\"560\").attr(\"height\",\"301\").attr(\"scrolling\",\"no\").attr(\"allowtransparency\",\"true\")}return e.addClass(\"note-video-clip\"),e[0]}},{key:\"show\",value:function(){var t=this,e=this.context.invoke(\"editor.getSelectedText\");this.context.invoke(\"editor.saveRange\"),this.showVideoDialog(e).then((function(e){t.ui.hideDialog(t.$dialog),t.context.invoke(\"editor.restoreRange\");var o=t.createVideoNode(e);o&&t.context.invoke(\"editor.insertNode\",o)})).fail((function(){t.context.invoke(\"editor.restoreRange\")}))}},{key:\"showVideoDialog\",value:function(){var t=this;return r().Deferred((function(e){var o=t.$dialog.find(\".note-video-url\"),n=t.$dialog.find(\".note-video-btn\");t.ui.onDialogShown(t.$dialog,(function(){t.context.triggerEvent(\"dialog.shown\"),o.on(\"input paste propertychange\",(function(){t.ui.toggleBtn(n,o.val())})),m.isSupportTouch||o.trigger(\"focus\"),n.on(\"click\",(function(t){t.preventDefault(),e.resolve(o.val())})),t.bindEnterKey(o,n)})),t.ui.onDialogHidden(t.$dialog,(function(){o.off(),n.off(),\"pending\"===e.state()&&e.reject()})),t.ui.showDialog(t.$dialog)}))}}])&&To(t.prototype,e),o&&To(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function No(t){return No=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},No(t)}function $o(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Io(n.key),n)}}function Io(t){var e=function(t,e){if(\"object\"!=No(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=No(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==No(e)?e:e+\"\"}var Ro=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.ui=r().summernote.ui,this.$body=r()(document.body),this.$editor=e.layoutInfo.editor,this.options=e.options,this.lang=this.options.langInfo},e=[{key:\"initialize\",value:function(){var t=this.options.dialogsInBody?this.$body:this.options.container,e=['<p class=\"text-center\">','<a href=\"http://summernote.org/\" target=\"_blank\" rel=\"noopener noreferrer\">Summernote 0.9.1</a> · ','<a href=\"https://github.com/summernote/summernote\" target=\"_blank\" rel=\"noopener noreferrer\">Project</a> · ','<a href=\"https://github.com/summernote/summernote/issues\" target=\"_blank\" rel=\"noopener noreferrer\">Issues</a>',\"</p>\"].join(\"\");this.$dialog=this.ui.dialog({title:this.lang.options.help,fade:this.options.dialogsFade,body:this.createShortcutList(),footer:e,callback:function(t){t.find(\".modal-body,.note-modal-body\").css({\"max-height\":300,overflow:\"scroll\"})}}).render().appendTo(t)}},{key:\"destroy\",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:\"createShortcutList\",value:function(){var t=this,e=this.options.keyMap[m.isMac?\"mac\":\"pc\"];return Object.keys(e).map((function(o){var n=e[o],i=r()('<div><div class=\"help-list-item\"></div></div>');return i.append(r()(\"<label><kbd>\"+o+\"</kdb></label>\").css({width:180,\"margin-right\":10})).append(r()(\"<span></span>\").html(t.context.memo(\"help.\"+n)||n)),i.html()})).join(\"\")}},{key:\"showHelpDialog\",value:function(){var t=this;return r().Deferred((function(e){t.ui.onDialogShown(t.$dialog,(function(){t.context.triggerEvent(\"dialog.shown\"),e.resolve()})),t.ui.showDialog(t.$dialog)})).promise()}},{key:\"show\",value:function(){var t=this;this.context.invoke(\"editor.saveRange\"),this.showHelpDialog().then((function(){t.context.invoke(\"editor.restoreRange\")}))}}],e&&$o(t.prototype,e),o&&$o(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Ao(t){return Ao=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Ao(t)}function Lo(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Fo(n.key),n)}}function Fo(t){var e=function(t,e){if(\"object\"!=Ao(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Ao(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Ao(e)?e:e+\"\"}var Do=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.ui=r().summernote.ui,this.options=e.options,this.hidable=!0,this.onContextmenu=!1,this.pageX=null,this.pageY=null,this.events={\"summernote.contextmenu\":function(t){o.options.editing&&(t.preventDefault(),t.stopPropagation(),o.onContextmenu=!0,o.update(!0))},\"summernote.mousedown\":function(t,e){o.pageX=e.pageX,o.pageY=e.pageY},\"summernote.keyup summernote.mouseup summernote.scroll\":function(t,e){if(o.options.editing&&!o.onContextmenu){if(\"keyup\"==e.type){var n=o.context.invoke(\"editor.getLastRange\").getWordRange(),i=g.rect2bnd(C.last(n.getClientRects()));o.pageX=i.left,o.pageY=i.top}else o.pageX=e.pageX,o.pageY=e.pageY;o.update()}o.onContextmenu=!1},\"summernote.disable summernote.change summernote.dialog.shown summernote.blur\":function(){o.hide()},\"summernote.focusout\":function(){o.$popover.is(\":active,:focus\")||o.hide()}}},(e=[{key:\"shouldInitialize\",value:function(){return this.options.airMode&&!C.isEmpty(this.options.popover.air)}},{key:\"initialize\",value:function(){var t=this;this.$popover=this.ui.popover({className:\"note-air-popover\"}).render().appendTo(this.options.container);var e=this.$popover.find(\".popover-content\");this.context.invoke(\"buttons.build\",e,this.options.popover.air),this.$popover.on(\"mousedown\",(function(){t.hidable=!1})),this.$popover.on(\"mouseup\",(function(){t.hidable=!0}))}},{key:\"destroy\",value:function(){this.$popover.remove()}},{key:\"update\",value:function(t){var e=this.context.invoke(\"editor.currentStyle\");if(!e.range||e.range.isCollapsed()&&!t)this.hide();else{var o={left:this.pageX,top:this.pageY},n=r()(this.options.container).offset();o.top-=n.top,o.left-=n.left,this.$popover.css({display:\"block\",left:Math.max(o.left,0)+-5,top:o.top+5}),this.context.invoke(\"buttons.updateCurrentStyle\",this.$popover)}}},{key:\"updateCodeview\",value:function(t){this.ui.toggleBtnActive(this.$popover.find(\".btn-codeview\"),t),t&&this.hide()}},{key:\"hide\",value:function(){this.hidable&&this.$popover.hide()}}])&&Lo(t.prototype,e),o&&Lo(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Ho(t){return Ho=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Ho(t)}function jo(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Bo(n.key),n)}}function Bo(t){var e=function(t,e){if(\"object\"!=Ho(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Ho(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Ho(e)?e:e+\"\"}var Oo=function(){return t=function t(e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.context=e,this.ui=r().summernote.ui,this.$editable=e.layoutInfo.editable,this.options=e.options,this.hint=this.options.hint||[],this.direction=this.options.hintDirection||\"bottom\",this.hints=Array.isArray(this.hint)?this.hint:[this.hint],this.events={\"summernote.keyup\":function(t,e){e.isDefaultPrevented()||o.handleKeyup(e)},\"summernote.keydown\":function(t,e){o.handleKeydown(e)},\"summernote.disable summernote.dialog.shown summernote.blur\":function(){o.hide()}}},e=[{key:\"shouldInitialize\",value:function(){return this.hints.length>0}},{key:\"initialize\",value:function(){var t=this;this.lastWordRange=null,this.matchingWord=null,this.$popover=this.ui.popover({className:\"note-hint-popover\",hideArrow:!0,direction:\"\"}).render().appendTo(this.options.container),this.$popover.hide(),this.$content=this.$popover.find(\".popover-content,.note-popover-content\"),this.$content.on(\"click\",\".note-hint-item\",(function(e){t.$content.find(\".active\").removeClass(\"active\"),r()(e.currentTarget).addClass(\"active\"),t.replace()})),this.$popover.on(\"mousedown\",(function(t){t.preventDefault()}))}},{key:\"destroy\",value:function(){this.$popover.remove()}},{key:\"selectItem\",value:function(t){this.$content.find(\".active\").removeClass(\"active\"),t.addClass(\"active\"),this.$content[0].scrollTop=t[0].offsetTop-this.$content.innerHeight()/2}},{key:\"moveDown\",value:function(){var t=this.$content.find(\".note-hint-item.active\"),e=t.next();if(e.length)this.selectItem(e);else{var o=t.parent().next();o.length||(o=this.$content.find(\".note-hint-group\").first()),this.selectItem(o.find(\".note-hint-item\").first())}}},{key:\"moveUp\",value:function(){var t=this.$content.find(\".note-hint-item.active\"),e=t.prev();if(e.length)this.selectItem(e);else{var o=t.parent().prev();o.length||(o=this.$content.find(\".note-hint-group\").last()),this.selectItem(o.find(\".note-hint-item\").last())}}},{key:\"replace\",value:function(){var t=this.$content.find(\".note-hint-item.active\");if(t.length){var e=this.nodeFromItem(t);if(null!==this.matchingWord&&0===this.matchingWord.length)this.lastWordRange.so=this.lastWordRange.eo;else if(null!==this.matchingWord&&this.matchingWord.length>0&&!this.lastWordRange.isCollapsed()){var o=this.lastWordRange.eo-this.lastWordRange.so-this.matchingWord.length;o>0&&(this.lastWordRange.so+=o)}if(this.lastWordRange.insertNode(e),\"next\"===this.options.hintSelect){var n=document.createTextNode(\"\");r()(e).after(n),Et.createFromNodeBefore(n).select()}else Et.createFromNodeAfter(e).select();this.lastWordRange=null,this.hide(),this.context.invoke(\"editor.focus\"),this.context.triggerEvent(\"change\",this.$editable.html(),this.$editable)}}},{key:\"nodeFromItem\",value:function(t){var e=this.hints[t.data(\"index\")],o=t.data(\"item\"),n=e.content?e.content(o):o;return\"string\"==typeof n&&(n=pt.createText(n)),n}},{key:\"createItemTemplates\",value:function(t,e){var o=this.hints[t];return e.map((function(e,n){var i=r()('<div class=\"note-hint-item\"></div>');return i.append(o.template?o.template(e):e+\"\"),i.data({index:t,item:e}),0===t&&0===n&&i.addClass(\"active\"),i}))}},{key:\"handleKeydown\",value:function(t){this.$popover.is(\":visible\")&&(t.keyCode===Nt.code.ENTER?(t.preventDefault(),this.replace()):t.keyCode===Nt.code.UP?(t.preventDefault(),this.moveUp()):t.keyCode===Nt.code.DOWN&&(t.preventDefault(),this.moveDown()))}},{key:\"searchKeyword\",value:function(t,e,o){var n=this.hints[t];if(n&&n.match.test(e)&&n.search){var i=n.match.exec(e);this.matchingWord=i[0],n.search(i[1],o)}else o()}},{key:\"createGroup\",value:function(t,e){var o=this,n=r()('<div class=\"note-hint-group note-hint-group-'+t+'\"></div>');return this.searchKeyword(t,e,(function(e){(e=e||[]).length&&(n.html(o.createItemTemplates(t,e)),o.show())})),n}},{key:\"handleKeyup\",value:function(t){var e=this;if(!C.contains([Nt.code.ENTER,Nt.code.UP,Nt.code.DOWN],t.keyCode)){var o,n,i=this.context.invoke(\"editor.getLastRange\");if(\"words\"===this.options.hintMode){if(o=i.getWordsRange(i),n=o.toString(),this.hints.forEach((function(t){if(t.match.test(n))return o=i.getWordsMatchRange(t.match),!1})),!o)return void this.hide();n=o.toString()}else o=i.getWordRange(),n=o.toString();if(this.hints.length&&n){this.$content.empty();var a=g.rect2bnd(C.last(o.getClientRects())),s=r()(this.options.container).offset();a&&(a.top-=s.top,a.left-=s.left,this.$popover.hide(),this.lastWordRange=o,this.hints.forEach((function(t,o){t.match.test(n)&&e.createGroup(o,n).appendTo(e.$content)})),this.$content.find(\".note-hint-item\").first().addClass(\"active\"),\"top\"===this.direction?this.$popover.css({left:a.left,top:a.top-this.$popover.outerHeight()-5}):this.$popover.css({left:a.left,top:a.top+a.height+5}))}else this.hide()}}},{key:\"show\",value:function(){this.$popover.show()}},{key:\"hide\",value:function(){this.$popover.hide()}}],e&&jo(t.prototype,e),o&&jo(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function zo(t){return zo=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},zo(t)}function Mo(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Uo(n.key),n)}}function Uo(t){var e=function(t,e){if(\"object\"!=zo(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=zo(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==zo(e)?e:e+\"\"}r().summernote=r().extend(r().summernote,{version:\"0.9.1\",plugins:{},dom:pt,range:Et,lists:C,options:{langInfo:r().summernote.lang[\"en-US\"],editing:!0,modules:{editor:oe,clipboard:ae,dropzone:ue,codeview:ve,statusbar:ke,fullscreen:xe,handle:Ne,hintPopover:Oo,autoLink:Le,autoSync:je,autoReplace:Me,placeholder:qe,buttons:Ze,toolbar:Je,linkDialog:ao,linkPopover:uo,imageDialog:mo,imagePopover:yo,tablePopover:So,videoDialog:Po,helpDialog:Ro,airPopover:Do},buttons:{},lang:\"en-US\",followingToolbar:!1,toolbarPosition:\"top\",otherStaticBar:\"\",codeviewKeepButton:!1,toolbar:[[\"style\",[\"style\"]],[\"font\",[\"bold\",\"underline\",\"clear\"]],[\"fontname\",[\"fontname\"]],[\"color\",[\"color\"]],[\"para\",[\"ul\",\"ol\",\"paragraph\"]],[\"table\",[\"table\"]],[\"insert\",[\"link\",\"picture\",\"video\"]],[\"view\",[\"fullscreen\",\"codeview\",\"help\"]]],popatmouse:!0,popover:{image:[[\"resize\",[\"resizeFull\",\"resizeHalf\",\"resizeQuarter\",\"resizeNone\"]],[\"float\",[\"floatLeft\",\"floatRight\",\"floatNone\"]],[\"remove\",[\"removeMedia\"]]],link:[[\"link\",[\"linkDialogShow\",\"unlink\"]]],table:[[\"add\",[\"addRowDown\",\"addRowUp\",\"addColLeft\",\"addColRight\"]],[\"delete\",[\"deleteRow\",\"deleteCol\",\"deleteTable\"]]],air:[[\"color\",[\"color\"]],[\"font\",[\"bold\",\"underline\",\"clear\"]],[\"para\",[\"ul\",\"paragraph\"]],[\"table\",[\"table\"]],[\"insert\",[\"link\",\"picture\"]],[\"view\",[\"fullscreen\",\"codeview\"]]]},linkAddNoReferrer:!1,addLinkNoOpener:!1,airMode:!1,overrideContextMenu:!1,width:null,height:null,linkTargetBlank:!0,focus:!1,tabDisable:!1,tabSize:4,styleWithCSS:!1,shortcuts:!0,textareaAutoSync:!0,tooltip:\"auto\",container:null,maxTextLength:0,blockquoteBreakingLevel:2,spellCheck:!0,disableGrammar:!1,placeholder:null,inheritPlaceholder:!1,recordEveryKeystroke:!1,historyLimit:200,showDomainOnlyForAutolink:!1,hintMode:\"word\",hintSelect:\"after\",hintDirection:\"bottom\",styleTags:[\"p\",\"blockquote\",\"pre\",\"h1\",\"h2\",\"h3\",\"h4\",\"h5\",\"h6\"],fontNames:[\"Arial\",\"Arial Black\",\"Comic Sans MS\",\"Courier New\",\"Helvetica Neue\",\"Helvetica\",\"Impact\",\"Lucida Grande\",\"Tahoma\",\"Times New Roman\",\"Verdana\"],fontNamesIgnoreCheck:[],addDefaultFonts:!0,fontSizes:[\"8\",\"9\",\"10\",\"11\",\"12\",\"14\",\"18\",\"24\",\"36\"],fontSizeUnits:[\"px\",\"pt\"],colors:[[\"#000000\",\"#424242\",\"#636363\",\"#9C9C94\",\"#CEC6CE\",\"#EFEFEF\",\"#F7F7F7\",\"#FFFFFF\"],[\"#FF0000\",\"#FF9C00\",\"#FFFF00\",\"#00FF00\",\"#00FFFF\",\"#0000FF\",\"#9C00FF\",\"#FF00FF\"],[\"#F7C6CE\",\"#FFE7CE\",\"#FFEFC6\",\"#D6EFD6\",\"#CEDEE7\",\"#CEE7F7\",\"#D6D6E7\",\"#E7D6DE\"],[\"#E79C9C\",\"#FFC69C\",\"#FFE79C\",\"#B5D6A5\",\"#A5C6CE\",\"#9CC6EF\",\"#B5A5D6\",\"#D6A5BD\"],[\"#E76363\",\"#F7AD6B\",\"#FFD663\",\"#94BD7B\",\"#73A5AD\",\"#6BADDE\",\"#8C7BC6\",\"#C67BA5\"],[\"#CE0000\",\"#E79439\",\"#EFC631\",\"#6BA54A\",\"#4A7B8C\",\"#3984C6\",\"#634AA5\",\"#A54A7B\"],[\"#9C0000\",\"#B56308\",\"#BD9400\",\"#397B21\",\"#104A5A\",\"#085294\",\"#311873\",\"#731842\"],[\"#630000\",\"#7B3900\",\"#846300\",\"#295218\",\"#083139\",\"#003163\",\"#21104A\",\"#4A1031\"]],colorsName:[[\"Black\",\"Tundora\",\"Dove Gray\",\"Star Dust\",\"Pale Slate\",\"Gallery\",\"Alabaster\",\"White\"],[\"Red\",\"Orange Peel\",\"Yellow\",\"Green\",\"Cyan\",\"Blue\",\"Electric Violet\",\"Magenta\"],[\"Azalea\",\"Karry\",\"Egg White\",\"Zanah\",\"Botticelli\",\"Tropical Blue\",\"Mischka\",\"Twilight\"],[\"Tonys Pink\",\"Peach Orange\",\"Cream Brulee\",\"Sprout\",\"Casper\",\"Perano\",\"Cold Purple\",\"Careys Pink\"],[\"Mandy\",\"Rajah\",\"Dandelion\",\"Olivine\",\"Gulf Stream\",\"Viking\",\"Blue Marguerite\",\"Puce\"],[\"Guardsman Red\",\"Fire Bush\",\"Golden Dream\",\"Chelsea Cucumber\",\"Smalt Blue\",\"Boston Blue\",\"Butterfly Bush\",\"Cadillac\"],[\"Sangria\",\"Mai Tai\",\"Buddha Gold\",\"Forest Green\",\"Eden\",\"Venice Blue\",\"Meteorite\",\"Claret\"],[\"Rosewood\",\"Cinnamon\",\"Olive\",\"Parsley\",\"Tiber\",\"Midnight Blue\",\"Valentino\",\"Loulou\"]],colorButton:{foreColor:\"#000000\",backColor:\"#FFFF00\"},lineHeights:[\"1.0\",\"1.2\",\"1.4\",\"1.5\",\"1.6\",\"1.8\",\"2.0\",\"3.0\"],tableClassName:\"table table-bordered\",insertTableMaxSize:{col:10,row:10},dialogsInBody:!1,dialogsFade:!1,maximumImageFileSize:null,acceptImageFileTypes:\"image/*\",allowClipboardImagePasting:!0,callbacks:{onBeforeCommand:null,onBlur:null,onBlurCodeview:null,onChange:null,onChangeCodeview:null,onDialogShown:null,onEnter:null,onFocus:null,onImageLinkInsert:null,onImageUpload:null,onImageUploadError:null,onInit:null,onKeydown:null,onKeyup:null,onMousedown:null,onMouseup:null,onPaste:null,onScroll:null},codemirror:{mode:\"text/html\",htmlMode:!0,lineNumbers:!0},codeviewFilter:!0,codeviewFilterRegex:/<\\/*(?:applet|b(?:ase|gsound|link)|embed|frame(?:set)?|ilayer|l(?:ayer|ink)|meta|object|s(?:cript|tyle)|t(?:itle|extarea)|xml)[^>]*?>/gi,codeviewIframeFilter:!0,codeviewIframeWhitelistSrc:[],codeviewIframeWhitelistSrcBase:[\"www.youtube.com\",\"www.youtube-nocookie.com\",\"www.facebook.com\",\"vine.co\",\"instagram.com\",\"player.vimeo.com\",\"www.dailymotion.com\",\"player.youku.com\",\"jumpingbean.tv\",\"v.qq.com\"],keyMap:{pc:{ESC:\"escape\",ENTER:\"insertParagraph\",\"CTRL+Z\":\"undo\",\"CTRL+Y\":\"redo\",TAB:\"tab\",\"SHIFT+TAB\":\"untab\",\"CTRL+B\":\"bold\",\"CTRL+I\":\"italic\",\"CTRL+U\":\"underline\",\"CTRL+SHIFT+S\":\"strikethrough\",\"CTRL+BACKSLASH\":\"removeFormat\",\"CTRL+SHIFT+L\":\"justifyLeft\",\"CTRL+SHIFT+E\":\"justifyCenter\",\"CTRL+SHIFT+R\":\"justifyRight\",\"CTRL+SHIFT+J\":\"justifyFull\",\"CTRL+SHIFT+NUM7\":\"insertUnorderedList\",\"CTRL+SHIFT+NUM8\":\"insertOrderedList\",\"CTRL+LEFTBRACKET\":\"outdent\",\"CTRL+RIGHTBRACKET\":\"indent\",\"CTRL+NUM0\":\"formatPara\",\"CTRL+NUM1\":\"formatH1\",\"CTRL+NUM2\":\"formatH2\",\"CTRL+NUM3\":\"formatH3\",\"CTRL+NUM4\":\"formatH4\",\"CTRL+NUM5\":\"formatH5\",\"CTRL+NUM6\":\"formatH6\",\"CTRL+ENTER\":\"insertHorizontalRule\",\"CTRL+K\":\"linkDialog.show\"},mac:{ESC:\"escape\",ENTER:\"insertParagraph\",\"CMD+Z\":\"undo\",\"CMD+SHIFT+Z\":\"redo\",TAB:\"tab\",\"SHIFT+TAB\":\"untab\",\"CMD+B\":\"bold\",\"CMD+I\":\"italic\",\"CMD+U\":\"underline\",\"CMD+SHIFT+S\":\"strikethrough\",\"CMD+BACKSLASH\":\"removeFormat\",\"CMD+SHIFT+L\":\"justifyLeft\",\"CMD+SHIFT+E\":\"justifyCenter\",\"CMD+SHIFT+R\":\"justifyRight\",\"CMD+SHIFT+J\":\"justifyFull\",\"CMD+SHIFT+NUM7\":\"insertUnorderedList\",\"CMD+SHIFT+NUM8\":\"insertOrderedList\",\"CMD+LEFTBRACKET\":\"outdent\",\"CMD+RIGHTBRACKET\":\"indent\",\"CMD+NUM0\":\"formatPara\",\"CMD+NUM1\":\"formatH1\",\"CMD+NUM2\":\"formatH2\",\"CMD+NUM3\":\"formatH3\",\"CMD+NUM4\":\"formatH4\",\"CMD+NUM5\":\"formatH5\",\"CMD+NUM6\":\"formatH6\",\"CMD+ENTER\":\"insertHorizontalRule\",\"CMD+K\":\"linkDialog.show\"}},icons:{align:\"note-icon-align\",alignCenter:\"note-icon-align-center\",alignJustify:\"note-icon-align-justify\",alignLeft:\"note-icon-align-left\",alignRight:\"note-icon-align-right\",rowBelow:\"note-icon-row-below\",colBefore:\"note-icon-col-before\",colAfter:\"note-icon-col-after\",rowAbove:\"note-icon-row-above\",rowRemove:\"note-icon-row-remove\",colRemove:\"note-icon-col-remove\",indent:\"note-icon-align-indent\",outdent:\"note-icon-align-outdent\",arrowsAlt:\"note-icon-arrows-alt\",bold:\"note-icon-bold\",caret:\"note-icon-caret\",circle:\"note-icon-circle\",close:\"note-icon-close\",code:\"note-icon-code\",eraser:\"note-icon-eraser\",floatLeft:\"note-icon-float-left\",floatRight:\"note-icon-float-right\",font:\"note-icon-font\",frame:\"note-icon-frame\",italic:\"note-icon-italic\",link:\"note-icon-link\",unlink:\"note-icon-chain-broken\",magic:\"note-icon-magic\",menuCheck:\"note-icon-menu-check\",minus:\"note-icon-minus\",orderedlist:\"note-icon-orderedlist\",pencil:\"note-icon-pencil\",picture:\"note-icon-picture\",question:\"note-icon-question\",redo:\"note-icon-redo\",rollback:\"note-icon-rollback\",square:\"note-icon-square\",strikethrough:\"note-icon-strikethrough\",subscript:\"note-icon-subscript\",superscript:\"note-icon-superscript\",table:\"note-icon-table\",textHeight:\"note-icon-text-height\",trash:\"note-icon-trash\",underline:\"note-icon-underline\",undo:\"note-icon-undo\",unorderedlist:\"note-icon-unorderedlist\",video:\"note-icon-video\"}}});var Wo=function(){return t=function t(e,o,n,i){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.markup=e,this.children=o,this.options=n,this.callback=i},(e=[{key:\"render\",value:function(t){var e=r()(this.markup);if(this.options&&this.options.contents&&e.html(this.options.contents),this.options&&this.options.className&&e.addClass(this.options.className),this.options&&this.options.data&&r().each(this.options.data,(function(t,o){e.attr(\"data-\"+t,o)})),this.options&&this.options.click&&e.on(\"click\",this.options.click),this.children){var o=e.find(\".note-children-container\");this.children.forEach((function(t){t.render(o.length?o:e)}))}return this.callback&&this.callback(e,this.options),this.options&&this.options.callback&&this.options.callback(e),t&&t.append(e),e}}])&&Mo(t.prototype,e),o&&Mo(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();const Ko=function(t,e){return function(){var o=\"object\"===zo(arguments[1])?arguments[1]:arguments[0],n=Array.isArray(arguments[0])?arguments[0]:[];return o&&o.children&&(n=o.children),new Wo(t,n,o,e)}};function qo(t){return qo=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},qo(t)}function Vo(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,_o(n.key),n)}}function _o(t){var e=function(t,e){if(\"object\"!=qo(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=qo(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==qo(e)?e:e+\"\"}const Go=function(){return t=function t(e,o){if(function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.$node=e,this.options=r().extend({},{title:\"\",target:o.container,trigger:\"hover focus\",placement:\"bottom\"},o),this.$tooltip=r()(['<div class=\"note-tooltip\">','<div class=\"note-tooltip-arrow\"></div>','<div class=\"note-tooltip-content\"></div>',\"</div>\"].join(\"\")),\"manual\"!==this.options.trigger){var n=this.show.bind(this),i=this.hide.bind(this),a=this.toggle.bind(this);this.options.trigger.split(\" \").forEach((function(t){\"hover\"===t?(e.off(\"mouseenter mouseleave\"),e.on(\"mouseenter\",n).on(\"mouseleave\",i)):\"click\"===t?e.on(\"click\",a):\"focus\"===t&&e.on(\"focus\",n).on(\"blur\",i)}))}},(e=[{key:\"show\",value:function(){var t=this.$node,e=t.offset(),o=r()(this.options.target).offset();e.top-=o.top,e.left-=o.left;var n=this.$tooltip,i=this.options.title||t.attr(\"title\")||t.data(\"title\"),a=this.options.placement||t.data(\"placement\");n.addClass(a),n.find(\".note-tooltip-content\").text(i),n.appendTo(this.options.target);var s=t.outerWidth(),l=t.outerHeight(),c=n.outerWidth(),u=n.outerHeight();\"bottom\"===a?n.css({top:e.top+l,left:e.left+(s/2-c/2)}):\"top\"===a?n.css({top:e.top-u,left:e.left+(s/2-c/2)}):\"left\"===a?n.css({top:e.top+(l/2-u/2),left:e.left-c}):\"right\"===a&&n.css({top:e.top+(l/2-u/2),left:e.left+s}),n.addClass(\"in\")}},{key:\"hide\",value:function(){var t=this;this.$tooltip.removeClass(\"in\"),setTimeout((function(){t.$tooltip.remove()}),200)}},{key:\"toggle\",value:function(){this.$tooltip.hasClass(\"in\")?this.hide():this.show()}}])&&Vo(t.prototype,e),o&&Vo(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();function Zo(t){return Zo=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},Zo(t)}function Yo(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,Xo(n.key),n)}}function Xo(t){var e=function(t,e){if(\"object\"!=Zo(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=Zo(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==Zo(e)?e:e+\"\"}var Qo=function(){return t=function t(e,o){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.$button=e,this.options=r().extend({},{target:o.container},o),this.setEvent()},e=[{key:\"setEvent\",value:function(){var t=this;this.$button.on(\"click\",(function(e){t.toggle(),e.stopImmediatePropagation()}))}},{key:\"clear\",value:function(){var t=r()(\".note-btn-group.open\");t.find(\".note-btn.active\").removeClass(\"active\"),t.removeClass(\"open\")}},{key:\"show\",value:function(){this.$button.addClass(\"active\"),this.$button.parent().addClass(\"open\");var t=this.$button.next(),e=t.offset(),o=t.outerWidth(),n=r()(window).width(),i=parseFloat(r()(this.options.target).css(\"margin-right\"));e.left+o>n-i?t.css(\"margin-left\",n-i-(e.left+o)):t.css(\"margin-left\",\"\")}},{key:\"hide\",value:function(){this.$button.removeClass(\"active\"),this.$button.parent().removeClass(\"open\")}},{key:\"toggle\",value:function(){var t=this.$button.parent().hasClass(\"open\");this.clear(),t?this.hide():this.show()}}],e&&Yo(t.prototype,e),o&&Yo(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();r()(document).on(\"click.note-dropdown-menu\",(function(t){r()(t.target).closest(\".note-btn-group\").length||(r()(\".note-btn-group.open .note-btn.active\").removeClass(\"active\"),r()(\".note-btn-group.open\").removeClass(\"open\"))})),r()(document).on(\"click.note-dropdown-menu\",(function(t){r()(t.target).closest(\".note-dropdown-menu\").parent().removeClass(\"open\"),r()(t.target).closest(\".note-dropdown-menu\").parent().find(\".note-btn.active\").removeClass(\"active\")}));const Jo=Qo;function tn(t){return tn=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},tn(t)}function en(t,e){for(var o=0;o<e.length;o++){var n=e[o];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(t,on(n.key),n)}}function on(t){var e=function(t,e){if(\"object\"!=tn(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||\"default\");if(\"object\"!=tn(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}(t,\"string\");return\"symbol\"==tn(e)?e:e+\"\"}const nn=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}(this,t),this.$modal=e,this.$backdrop=r()('<div class=\"note-modal-backdrop\"></div>')},(e=[{key:\"show\",value:function(){var t=this;this.$backdrop.appendTo(document.body).show(),this.$modal.addClass(\"open\").show(),this.$modal.trigger(\"note.modal.show\"),this.$modal.off(\"click\",\".close\").on(\"click\",\".close\",this.hide.bind(this)),this.$modal.on(\"keydown\",(function(e){27===e.which&&(e.preventDefault(),t.hide())}))}},{key:\"hide\",value:function(){this.$modal.removeClass(\"open\").hide(),this.$backdrop.hide(),this.$modal.trigger(\"note.modal.hide\"),this.$modal.off(\"keydown\")}}])&&en(t.prototype,e),o&&en(t,o),Object.defineProperty(t,\"prototype\",{writable:!1}),t;var t,e,o}();var rn=Ko('<div class=\"note-editor note-frame\"></div>'),an=Ko('<div class=\"note-toolbar\" role=\"toolbar\"></div>'),sn=Ko('<div class=\"note-editing-area\"></div>'),ln=Ko('<textarea class=\"note-codable\" aria-multiline=\"true\"></textarea>'),cn=Ko('<div class=\"note-editable\" contentEditable=\"true\" role=\"textbox\" aria-multiline=\"true\"></div>'),un=Ko(['<output class=\"note-status-output\" role=\"status\" aria-live=\"polite\"></output>','<div class=\"note-statusbar\" role=\"status\">','<div class=\"note-resizebar\" aria-label=\"resize\">','<div class=\"note-icon-bar\"></div>','<div class=\"note-icon-bar\"></div>','<div class=\"note-icon-bar\"></div>',\"</div>\",\"</div>\"].join(\"\")),dn=Ko('<div class=\"note-editor note-airframe\"></div>'),fn=Ko(['<div class=\"note-editable\" contentEditable=\"true\" role=\"textbox\" aria-multiline=\"true\"></div>','<output class=\"note-status-output\" role=\"status\" aria-live=\"polite\"></output>'].join(\"\")),hn=Ko('<div class=\"note-btn-group\"></div>'),pn=Ko('<button type=\"button\" class=\"note-btn\" tabindex=\"-1\"></button>',(function(t,e){e&&e.tooltip&&(t.attr({\"aria-label\":e.tooltip}),t.data(\"_lite_tooltip\",new Go(t,{title:e.tooltip,container:e.container})).on(\"click\",(function(t){r()(t.currentTarget).data(\"_lite_tooltip\").hide()}))),e.contents&&t.html(e.contents),e&&e.data&&\"dropdown\"===e.data.toggle&&t.data(\"_lite_dropdown\",new Jo(t,{container:e.container})),e&&e.codeviewKeepButton&&t.addClass(\"note-codeview-keep\")})),mn=Ko('<div class=\"note-dropdown-menu\" role=\"list\"></div>',(function(t,e){var o=Array.isArray(e.items)?e.items.map((function(t){var o=\"string\"==typeof t?t:t.value||\"\",n=e.template?e.template(t):t,i=r()('<a class=\"note-dropdown-item\" href=\"#\" data-value=\"'+o+'\" role=\"listitem\" aria-label=\"'+o+'\"></a>');return i.html(n).data(\"item\",t),i})):e.items;t.html(o).attr({\"aria-label\":e.title}),t.on(\"click\",\"> .note-dropdown-item\",(function(t){var o=r()(this),n=o.data(\"item\"),i=o.data(\"value\");n.click?n.click(o):e.itemClick&&e.itemClick(t,n,i)})),e&&e.codeviewKeepButton&&t.addClass(\"note-codeview-keep\")})),vn=Ko('<div class=\"note-dropdown-menu note-check\" role=\"list\"></div>',(function(t,e){var o=Array.isArray(e.items)?e.items.map((function(t){var o=\"string\"==typeof t?t:t.value||\"\",n=e.template?e.template(t):t,i=r()('<a class=\"note-dropdown-item\" href=\"#\" data-value=\"'+o+'\" role=\"listitem\" aria-label=\"'+t+'\"></a>');return i.html([In(e.checkClassName),\" \",n]).data(\"item\",t),i})):e.items;t.html(o).attr({\"aria-label\":e.title}),t.on(\"click\",\"> .note-dropdown-item\",(function(t){var o=r()(this),n=o.data(\"item\"),i=o.data(\"value\");n.click?n.click(o):e.itemClick&&e.itemClick(t,n,i)})),e&&e.codeviewKeepButton&&t.addClass(\"note-codeview-keep\")})),gn=function(t,e){return t+\" \"+In(e.icons.caret,\"span\")},bn=function(t,e){return hn([pn({className:\"dropdown-toggle\",contents:t.title+\" \"+In(\"note-icon-caret\"),tooltip:t.tooltip,data:{toggle:\"dropdown\"}}),mn({className:t.className,items:t.items,template:t.template,itemClick:t.itemClick})],{callback:e}).render()},yn=function(t,e){return hn([pn({className:\"dropdown-toggle\",contents:t.title+\" \"+In(\"note-icon-caret\"),tooltip:t.tooltip,data:{toggle:\"dropdown\"}}),vn({className:t.className,checkClassName:t.checkClassName,items:t.items,template:t.template,itemClick:t.itemClick})],{callback:e}).render()},kn=function(t){return hn([pn({className:\"dropdown-toggle\",contents:t.title+\" \"+In(\"note-icon-caret\"),tooltip:t.tooltip,data:{toggle:\"dropdown\"}}),mn([hn({className:\"note-align\",children:t.items[0]}),hn({className:\"note-list\",children:t.items[1]})])]).render()},wn=function(t){return hn([pn({className:\"dropdown-toggle\",contents:t.title+\" \"+In(\"note-icon-caret\"),tooltip:t.tooltip,data:{toggle:\"dropdown\"}}),mn({className:\"note-table\",items:['<div class=\"note-dimension-picker\">','<div class=\"note-dimension-picker-mousecatcher\" data-event=\"insertTable\" data-value=\"1x1\"></div>','<div class=\"note-dimension-picker-highlighted\"></div>','<div class=\"note-dimension-picker-unhighlighted\"></div>',\"</div>\",'<div class=\"note-dimension-display\">1 x 1</div>'].join(\"\")})],{callback:function(e){e.find(\".note-dimension-picker-mousecatcher\").css({width:t.col+\"em\",height:t.row+\"em\"}).on(\"mouseup\",t.itemClick).on(\"mousemove\",(function(e){!function(t,e,o){var n,i=r()(t.target.parentNode),a=i.next(),s=i.find(\".note-dimension-picker-mousecatcher\"),l=i.find(\".note-dimension-picker-highlighted\"),c=i.find(\".note-dimension-picker-unhighlighted\");if(void 0===t.offsetX){var u=r()(t.target).offset();n={x:t.pageX-u.left,y:t.pageY-u.top}}else n={x:t.offsetX,y:t.offsetY};var d=Math.ceil(n.x/18)||1,f=Math.ceil(n.y/18)||1;l.css({width:d+\"em\",height:f+\"em\"}),s.data(\"value\",d+\"x\"+f),d>3&&d<e&&c.css({width:d+1+\"em\"}),f>3&&f<o&&c.css({height:f+1+\"em\"}),a.html(d+\" x \"+f)}(e,t.col,t.row)}))}}).render()},Cn=Ko('<div class=\"note-color-palette\"></div>',(function(t,e){for(var o=[],n=0,i=e.colors.length;n<i;n++){for(var a=e.eventName,s=e.colors[n],l=e.colorsName[n],c=[],u=0,d=s.length;u<d;u++){var f=s[u],h=l[u];c.push(['<button type=\"button\" class=\"note-btn note-color-btn\"','style=\"background-color:',f,'\" ','data-event=\"',a,'\" ','data-value=\"',f,'\" ','data-title=\"',h,'\" ','aria-label=\"',h,'\" ','data-toggle=\"button\" tabindex=\"-1\"></button>'].join(\"\"))}o.push('<div class=\"note-color-row\">'+c.join(\"\")+\"</div>\")}t.html(o.join(\"\")),t.find(\".note-color-btn\").each((function(){r()(this).data(\"_lite_tooltip\",new Go(r()(this),{container:e.container}))}))})),Sn=function(t,e){return hn({className:\"note-color\",children:[pn({className:\"note-current-color-button\",contents:t.title,tooltip:t.lang.color.recent,click:t.currentClick,callback:function(t){var o=t.find(\".note-recent-color\");\"foreColor\"!==e&&(o.css(\"background-color\",\"#FFFF00\"),t.attr(\"data-backColor\",\"#FFFF00\"))}}),pn({className:\"dropdown-toggle\",contents:In(\"note-icon-caret\"),tooltip:t.lang.color.more,data:{toggle:\"dropdown\"}}),mn({items:[\"<div>\",'<div class=\"note-btn-group btn-background-color\">','<div class=\"note-palette-title\">'+t.lang.color.background+\"</div>\",\"<div>\",'<button type=\"button\" class=\"note-color-reset note-btn note-btn-block\" data-event=\"backColor\" data-value=\"transparent\">',t.lang.color.transparent,\"</button>\",\"</div>\",'<div class=\"note-holder\" data-event=\"backColor\"></div>','<div class=\"btn-sm\">','<input type=\"color\" id=\"html5bcp\" class=\"note-btn btn-default\" value=\"#21104A\" style=\"width:100%;\" data-value=\"cp\">','<button type=\"button\" class=\"note-color-reset btn\" data-event=\"backColor\" data-value=\"cpbackColor\">',t.lang.color.cpSelect,\"</button>\",\"</div>\",\"</div>\",'<div class=\"note-btn-group btn-foreground-color\">','<div class=\"note-palette-title\">'+t.lang.color.foreground+\"</div>\",\"<div>\",'<button type=\"button\" class=\"note-color-reset note-btn note-btn-block\" data-event=\"removeFormat\" data-value=\"foreColor\">',t.lang.color.resetToDefault,\"</button>\",\"</div>\",'<div class=\"note-holder\" data-event=\"foreColor\"></div>','<div class=\"btn-sm\">','<input type=\"color\" id=\"html5fcp\" class=\"note-btn btn-default\" value=\"#21104A\" style=\"width:100%;\" data-value=\"cp\">','<button type=\"button\" class=\"note-color-reset btn\" data-event=\"foreColor\" data-value=\"cpforeColor\">',t.lang.color.cpSelect,\"</button>\",\"</div>\",\"</div>\",\"</div>\"].join(\"\"),callback:function(o){o.find(\".note-holder\").each((function(){var e=r()(this);e.append(Cn({colors:t.colors,eventName:e.data(\"event\")}).render())})),\"fore\"===e?(o.find(\".btn-background-color\").hide(),o.css({\"min-width\":\"210px\"})):\"back\"===e&&(o.find(\".btn-foreground-color\").hide(),o.css({\"min-width\":\"210px\"}))},click:function(o){var n=r()(o.target),i=n.data(\"event\"),a=n.data(\"value\"),s=document.getElementById(\"html5fcp\").value,l=document.getElementById(\"html5bcp\").value;if(\"cp\"===a?o.stopPropagation():\"cpbackColor\"===a?a=l:\"cpforeColor\"===a&&(a=s),i&&a){var c=\"backColor\"===i?\"background-color\":\"color\",u=n.closest(\".note-color\").find(\".note-recent-color\"),d=n.closest(\".note-color\").find(\".note-current-color-button\");u.css(c,a),d.attr(\"data-\"+i,a),\"fore\"===e?t.itemClick(\"foreColor\",a):\"back\"===e?t.itemClick(\"backColor\",a):t.itemClick(i,a)}}})]}).render()},xn=Ko('<div class=\"note-modal\" aria-hidden=\"false\" tabindex=\"-1\" role=\"dialog\"></div>',(function(t,e){e.fade&&t.addClass(\"fade\"),t.attr({\"aria-label\":e.title}),t.html(['<div class=\"note-modal-content\">',e.title?'<div class=\"note-modal-header\"><button type=\"button\" class=\"close\" aria-label=\"Close\" aria-hidden=\"true\"><i class=\"note-icon-close\"></i></button><h4 class=\"note-modal-title\">'+e.title+\"</h4></div>\":\"\",'<div class=\"note-modal-body\">'+e.body+\"</div>\",e.footer?'<div class=\"note-modal-footer\">'+e.footer+\"</div>\":\"\",\"</div>\"].join(\"\")),t.data(\"modal\",new nn(t,e))})),Tn=function(t){var e='<div class=\"note-form-group\"><label for=\"note-dialog-video-url-'+t.id+'\" class=\"note-form-label\">'+t.lang.video.url+' <small class=\"text-muted\">'+t.lang.video.providers+'</small></label><input id=\"note-dialog-video-url-'+t.id+'\" class=\"note-video-url note-input\" type=\"text\"/></div>',o=['<button type=\"button\" href=\"#\" class=\"note-btn note-btn-primary note-video-btn disabled\" disabled>',t.lang.video.insert,\"</button>\"].join(\"\");return xn({title:t.lang.video.insert,fade:t.fade,body:e,footer:o}).render()},En=function(t){var e='<div class=\"note-form-group note-group-select-from-files\"><label for=\"note-dialog-image-file-'+t.id+'\" class=\"note-form-label\">'+t.lang.image.selectFromFiles+'</label><input id=\"note-dialog-image-file-'+t.id+'\" class=\"note-note-image-input note-input\" type=\"file\" name=\"files\" accept=\"image/*\" multiple=\"multiple\"/>'+t.imageLimitation+'</div><div class=\"note-form-group\"><label for=\"note-dialog-image-url-'+t.id+'\" class=\"note-form-label\">'+t.lang.image.url+'</label><input id=\"note-dialog-image-url-'+t.id+'\" class=\"note-image-url note-input\" type=\"text\"/></div>',o=['<button href=\"#\" type=\"button\" class=\"note-btn note-btn-primary note-btn-large note-image-btn disabled\" disabled>',t.lang.image.insert,\"</button>\"].join(\"\");return xn({title:t.lang.image.insert,fade:t.fade,body:e,footer:o}).render()},Pn=function(t){var e='<div class=\"note-form-group\"><label for=\"note-dialog-link-txt-'+t.id+'\" class=\"note-form-label\">'+t.lang.link.textToDisplay+'</label><input id=\"note-dialog-link-txt-'+t.id+'\" class=\"note-link-text note-input\" type=\"text\"/></div><div class=\"note-form-group\"><label for=\"note-dialog-link-url-'+t.id+'\" class=\"note-form-label\">'+t.lang.link.url+'</label><input id=\"note-dialog-link-url-'+t.id+'\" class=\"note-link-url note-input\" type=\"text\" value=\"http://\"/></div>'+(t.disableLinkTarget?\"\":'<div class=\"checkbox\"><label for=\"note-dialog-link-nw-'+t.id+'\"><input id=\"note-dialog-link-nw-'+t.id+'\" type=\"checkbox\" checked> '+t.lang.link.openInNewWindow+\"</label></div>\"),o=['<button href=\"#\" type=\"button\" class=\"note-btn note-btn-primary note-link-btn disabled\" disabled>',t.lang.link.insert,\"</button>\"].join(\"\");return xn({className:\"link-dialog\",title:t.lang.link.insert,fade:t.fade,body:e,footer:o}).render()},Nn=Ko(['<div class=\"note-popover bottom\">','<div class=\"note-popover-arrow\"></div>','<div class=\"popover-content note-children-container\"></div>',\"</div>\"].join(\"\"),(function(t,e){var o=void 0!==e.direction?e.direction:\"bottom\";t.addClass(o).hide(),e.hideArrow&&t.find(\".note-popover-arrow\").hide()})),$n=Ko('<div class=\"checkbox\"></div>',(function(t,e){t.html([\"<label\"+(e.id?' for=\"note-'+e.id+'\"':\"\")+\">\",'<input role=\"checkbox\" type=\"checkbox\"'+(e.id?' id=\"note-'+e.id+'\"':\"\"),e.checked?\" checked\":\"\",' aria-checked=\"'+(e.checked?\"true\":\"false\")+'\"/>',e.text?e.text:\"\",\"</label>\"].join(\"\"))})),In=function(t,e){return t.match(/^</)?t:\"<\"+(e=e||\"i\")+' class=\"'+t+'\"></'+e+\">\"};return r().summernote=r().extend(r().summernote,{ui_template:function(t){return{editor:rn,toolbar:an,editingArea:sn,codable:ln,editable:cn,statusbar:un,airEditor:dn,airEditable:fn,buttonGroup:hn,button:pn,dropdown:mn,dropdownCheck:vn,dropdownButton:bn,dropdownButtonContents:gn,dropdownCheckButton:yn,paragraphDropdownButton:kn,tableDropdownButton:wn,colorDropdownButton:Sn,palette:Cn,dialog:xn,videoDialog:Tn,imageDialog:En,linkDialog:Pn,popover:Nn,checkbox:$n,icon:In,options:t,toggleBtn:function(t,e){t.toggleClass(\"disabled\",!e),t.attr(\"disabled\",!e)},toggleBtnActive:function(t,e){t.toggleClass(\"active\",e)},check:function(t,e){t.find(\".checked\").removeClass(\"checked\"),t.find('[data-value=\"'+e+'\"]').addClass(\"checked\")},onDialogShown:function(t,e){t.one(\"note.modal.show\",e)},onDialogHidden:function(t,e){t.one(\"note.modal.hide\",e)},showDialog:function(t){t.data(\"modal\").show()},hideDialog:function(t){t.data(\"modal\").hide()},getPopoverContent:function(t){return t.find(\".note-popover-content\")},getDialogBody:function(t){return t.find(\".note-modal-body\")},createLayout:function(e){var o=(t.airMode?dn([sn([ln(),fn()])]):\"bottom\"===t.toolbarPosition?rn([sn([ln(),cn()]),an(),un()]):rn([an(),sn([ln(),cn()]),un()])).render();return o.insertAfter(e),{note:e,editor:o,toolbar:o.find(\".note-toolbar\"),editingArea:o.find(\".note-editing-area\"),editable:o.find(\".note-editable\"),codable:o.find(\".note-codable\"),statusbar:o.find(\".note-statusbar\")}},removeLayout:function(t,e){t.html(e.editable.html()),e.editor.remove(),t.off(\"summernote\"),t.show()}}},interface:\"lite\"}),{}})()));"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,KAAC,SAAS,GAAE,GAAE;AAAC,UAAG,YAAU,OAAO,WAAS,YAAU,OAAO,OAAO,QAAO,UAAQ,EAAE,gBAAiB;AAAA,eAAU,cAAY,OAAO,UAAQ,OAAO,IAAI,QAAO,CAAC,QAAQ,GAAE,CAAC;AAAA,WAAM;AAAC,YAAI,IAAE,YAAU,OAAO,UAAQ,EAAE,gBAAiB,IAAE,EAAE,EAAE,MAAM;AAAE,iBAAQ,KAAK,EAAE,EAAC,YAAU,OAAO,UAAQ,UAAQ,GAAG,CAAC,IAAE,EAAE,CAAC;AAAA,MAAC;AAAA,IAAC,EAAE,MAAM,QAAI,MAAI;AAAC;AAAa,UAAI,IAAE,EAAC,KAAI,CAACA,IAAEC,IAAEC,OAAI;AAAC,YAAIC,KAAED,GAAE,IAAI,GAAEE,KAAEF,GAAE,EAAEC,EAAC;AAAE,QAAAC,GAAE,EAAE,aAAWA,GAAE,EAAE,cAAY,EAAC,MAAK,CAAC,EAAC,GAAEA,GAAE,EAAE,OAAO,MAAGA,GAAE,EAAE,WAAW,MAAK,EAAC,SAAQ,EAAC,MAAK,EAAC,MAAK,QAAO,QAAO,UAAS,WAAU,aAAY,OAAM,qBAAoB,QAAO,eAAc,MAAK,eAAc,eAAc,iBAAgB,WAAU,aAAY,aAAY,eAAc,MAAK,aAAY,UAAS,iBAAgB,GAAE,OAAM,EAAC,OAAM,WAAU,QAAO,gBAAe,YAAW,eAAc,YAAW,eAAc,eAAc,kBAAiB,YAAW,iBAAgB,WAAU,cAAa,YAAW,eAAc,WAAU,gBAAe,cAAa,kBAAiB,aAAY,iBAAgB,gBAAe,oBAAmB,WAAU,eAAc,eAAc,2BAA0B,WAAU,sBAAqB,iBAAgB,qBAAoB,iBAAgB,qBAAoB,sBAAqB,+BAA8B,KAAI,aAAY,QAAO,gBAAe,UAAS,WAAU,GAAE,OAAM,EAAC,OAAM,SAAQ,WAAU,cAAa,QAAO,gBAAe,KAAI,aAAY,WAAU,gFAA+E,GAAE,MAAK,EAAC,MAAK,QAAO,QAAO,eAAc,QAAO,UAAS,MAAK,QAAO,eAAc,mBAAkB,KAAI,oCAAmC,iBAAgB,qBAAoB,GAAE,OAAM,EAAC,OAAM,SAAQ,aAAY,iBAAgB,aAAY,iBAAgB,YAAW,mBAAkB,aAAY,oBAAmB,QAAO,cAAa,QAAO,iBAAgB,UAAS,eAAc,GAAE,IAAG,EAAC,QAAO,yBAAwB,GAAE,OAAM,EAAC,OAAM,SAAQ,GAAE,UAAS,YAAW,SAAQ,KAAI,QAAO,IAAG,YAAW,IAAG,YAAW,IAAG,YAAW,IAAG,YAAW,IAAG,YAAW,IAAG,WAAU,GAAE,OAAM,EAAC,WAAU,kBAAiB,SAAQ,eAAc,GAAE,SAAQ,EAAC,MAAK,QAAO,YAAW,eAAc,UAAS,YAAW,GAAE,WAAU,EAAC,WAAU,aAAY,SAAQ,WAAU,QAAO,UAAS,MAAK,cAAa,QAAO,gBAAe,OAAM,eAAc,SAAQ,eAAc,GAAE,OAAM,EAAC,QAAO,gBAAe,MAAK,cAAa,YAAW,oBAAmB,YAAW,cAAa,aAAY,eAAc,gBAAe,mBAAkB,OAAM,SAAQ,gBAAe,oBAAmB,UAAS,SAAQ,GAAE,UAAS,EAAC,WAAU,sBAAqB,OAAM,SAAQ,gBAAe,mBAAkB,QAAO,UAAS,qBAAoB,wBAAuB,eAAc,kBAAiB,WAAU,aAAY,GAAE,MAAK,EAAC,QAAO,UAAS,iBAAgB,oBAAmB,MAAK,yBAAwB,MAAK,yBAAwB,KAAI,OAAM,OAAM,SAAQ,MAAK,oBAAmB,QAAO,sBAAqB,WAAU,yBAAwB,eAAc,6BAA4B,cAAa,iBAAgB,aAAY,kBAAiB,eAAc,oBAAmB,cAAa,mBAAkB,aAAY,kBAAiB,qBAAoB,yBAAwB,mBAAkB,uBAAsB,SAAQ,gCAA+B,QAAO,+BAA8B,YAAW,uDAAsD,UAAS,uCAAsC,UAAS,uCAAsC,UAAS,uCAAsC,UAAS,uCAAsC,UAAS,uCAAsC,UAAS,uCAAsC,sBAAqB,0BAAyB,mBAAkB,mBAAkB,GAAE,SAAQ,EAAC,MAAK,QAAO,MAAK,OAAM,GAAE,aAAY,EAAC,aAAY,sBAAqB,QAAO,4BAA2B,GAAE,QAAO,EAAC,aAAY,qBAAoB,EAAC,EAAC,CAAC;AAAA,MAAC,GAAE,MAAK,CAAAH,OAAG;AAAC,QAAAA,GAAE,UAAQ;AAAA,MAAC,EAAC,GAAE,IAAE,CAAC;AAAE,eAAS,EAAED,IAAE;AAAC,YAAII,KAAE,EAAEJ,EAAC;AAAE,YAAG,WAASI,GAAE,QAAOA,GAAE;AAAQ,YAAIC,KAAE,EAAEL,EAAC,IAAE,EAAC,SAAQ,CAAC,EAAC;AAAE,eAAO,EAAEA,EAAC,EAAEK,IAAEA,GAAE,SAAQ,CAAC,GAAEA,GAAE;AAAA,MAAO;AAAC,QAAE,IAAE,CAAAL,OAAG;AAAC,YAAIC,KAAED,MAAGA,GAAE,aAAW,MAAIA,GAAE,UAAQ,MAAIA;AAAE,eAAO,EAAE,EAAEC,IAAE,EAAC,GAAEA,GAAC,CAAC,GAAEA;AAAA,MAAC,GAAE,EAAE,IAAE,CAACD,IAAEC,OAAI;AAAC,iBAAQC,MAAKD,GAAE,GAAE,EAAEA,IAAEC,EAAC,KAAG,CAAC,EAAE,EAAEF,IAAEE,EAAC,KAAG,OAAO,eAAeF,IAAEE,IAAE,EAAC,YAAW,MAAG,KAAID,GAAEC,EAAC,EAAC,CAAC;AAAA,MAAC,GAAE,EAAE,IAAE,CAACF,IAAEC,OAAI,OAAO,UAAU,eAAe,KAAKD,IAAEC,EAAC;AAAE,UAAI,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,EAAE,CAAC,GAAE,KAAG,EAAE,GAAG,GAAE,CAAC,cAAa,SAAQ,aAAY,WAAU,SAAS;AAAG,eAAS,EAAED,IAAE;AAAC,eAAM,OAAK,EAAE,EAAE,QAAQA,GAAE,YAAY,GAAE,CAAC,IAAE,IAAI,OAAOA,IAAE,GAAG,IAAEA;AAAA,MAAC;AAAC,UAAI,GAAE,IAAE,UAAU,WAAU,IAAE,gBAAgB,KAAK,CAAC;AAAE,UAAG,GAAE;AAAC,YAAI,IAAE,mBAAmB,KAAK,CAAC;AAAE,cAAI,IAAE,WAAW,EAAE,CAAC,CAAC,KAAI,IAAE,sCAAsC,KAAK,CAAC,OAAK,IAAE,WAAW,EAAE,CAAC,CAAC;AAAA,MAAE;AAAC,UAAI,IAAE,YAAY,KAAK,CAAC,GAAE,IAAE,kBAAiB,UAAQ,UAAU,iBAAe,KAAG,UAAU,mBAAiB,GAAE,IAAE,IAAE,gEAA8D;AAAQ,YAAM,IAAE,EAAC,OAAM,UAAU,WAAW,QAAQ,KAAK,IAAE,IAAG,QAAO,GAAE,QAAO,GAAE,MAAK,CAAC,KAAG,WAAW,KAAK,CAAC,GAAE,WAAU,aAAa,KAAK,CAAC,GAAE,UAAS,CAAC,KAAG,UAAU,KAAK,CAAC,GAAE,UAAS,CAAC,KAAG,UAAU,KAAK,CAAC,GAAE,UAAS,CAAC,KAAG,UAAU,KAAK,CAAC,KAAG,CAAC,UAAU,KAAK,CAAC,GAAE,gBAAe,GAAE,gBAAe,GAAE,iBAAgB,WAAU;AAAC,YAAIA,KAAE,SAAS,cAAc,QAAQ,GAAEC,KAAED,GAAE,WAAW,MAAK,EAAC,oBAAmB,KAAE,CAAC;AAAE,iBAASE,GAAEF,IAAEE,IAAE;AAAC,iBAAOD,GAAE,UAAU,GAAE,GAAE,IAAG,EAAE,GAAEA,GAAE,OAAK,UAAQ,EAAED,EAAC,IAAE,QAAME,KAAE,KAAID,GAAE,SAAS,MAAK,IAAG,EAAE,GAAEA,GAAE,aAAa,GAAE,GAAE,IAAG,EAAE,EAAE,KAAK,KAAK,EAAE;AAAA,QAAC;AAAC,eAAOD,GAAE,QAAM,IAAGA,GAAE,SAAO,IAAGC,GAAE,YAAU,UAASA,GAAE,YAAU,SAAQA,GAAE,eAAa,UAAS,SAASD,IAAE;AAAC,cAAIC,KAAE,oBAAkBD,KAAE,gBAAc;AAAgB,iBAAOE,GAAED,IAAEA,EAAC,MAAIC,GAAEF,IAAEC,EAAC;AAAA,QAAC;AAAA,MAAC,EAAE,GAAE,mBAAkB,CAAC,CAAC,SAAS,aAAY,gBAAe,GAAE,qBAAoB,GAAE,eAAc,EAAC;AAAE,UAAI,IAAE;AAAE,YAAM,IAAE,EAAC,IAAG,SAASD,IAAE;AAAC,eAAO,SAASC,IAAE;AAAC,iBAAOD,OAAIC;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,SAASD,IAAEC,IAAE;AAAC,eAAOD,OAAIC;AAAA,MAAC,GAAE,MAAK,SAASD,IAAE;AAAC,eAAO,SAASC,IAAEC,IAAE;AAAC,iBAAOD,GAAED,EAAC,MAAIE,GAAEF,EAAC;AAAA,QAAC;AAAA,MAAC,GAAE,IAAG,WAAU;AAAC,eAAM;AAAA,MAAE,GAAE,MAAK,WAAU;AAAC,eAAM;AAAA,MAAE,GAAE,MAAK,SAASA,IAAE;AAAC,eAAOA;AAAA,MAAC,GAAE,KAAI,SAASA,IAAE;AAAC,eAAO,WAAU;AAAC,iBAAM,CAACA,GAAE,MAAMA,IAAE,SAAS;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,SAASA,IAAEC,IAAE;AAAC,eAAO,SAASC,IAAE;AAAC,iBAAOF,GAAEE,EAAC,KAAGD,GAAEC,EAAC;AAAA,QAAC;AAAA,MAAC,GAAE,QAAO,SAASF,IAAEC,IAAE;AAAC,eAAO,WAAU;AAAC,iBAAOD,GAAEC,EAAC,EAAE,MAAMD,IAAE,SAAS;AAAA,QAAC;AAAA,MAAC,GAAE,eAAc,WAAU;AAAC,YAAE;AAAA,MAAC,GAAE,UAAS,SAASA,IAAE;AAAC,YAAIC,KAAE,EAAE,IAAE;AAAG,eAAOD,KAAEA,KAAEC,KAAEA;AAAA,MAAC,GAAE,UAAS,SAASD,IAAE;AAAC,YAAIC,KAAE,EAAE,EAAE,QAAQ;AAAE,eAAM,EAAC,KAAID,GAAE,MAAIC,GAAE,UAAU,GAAE,MAAKD,GAAE,OAAKC,GAAE,WAAW,GAAE,OAAMD,GAAE,QAAMA,GAAE,MAAK,QAAOA,GAAE,SAAOA,GAAE,IAAG;AAAA,MAAC,GAAE,cAAa,SAASA,IAAE;AAAC,YAAIC,KAAE,CAAC;AAAE,iBAAQC,MAAKF,GAAE,QAAO,UAAU,eAAe,KAAKA,IAAEE,EAAC,MAAID,GAAED,GAAEE,EAAC,CAAC,IAAEA;AAAG,eAAOD;AAAA,MAAC,GAAE,kBAAiB,SAASD,IAAEC,IAAE;AAAC,gBAAOA,KAAEA,MAAG,MAAID,GAAE,MAAM,GAAG,EAAE,IAAK,SAASA,IAAE;AAAC,iBAAOA,GAAE,UAAU,GAAE,CAAC,EAAE,YAAY,IAAEA,GAAE,UAAU,CAAC;AAAA,QAAC,CAAE,EAAE,KAAK,EAAE;AAAA,MAAC,GAAE,UAAS,SAASA,IAAEC,IAAEC,IAAE;AAAC,YAAIC;AAAE,eAAO,WAAU;AAAC,cAAIC,KAAE,MAAKC,KAAE,WAAUC,KAAEJ,MAAG,CAACC;AAAE,uBAAaA,EAAC,GAAEA,KAAE,WAAY,WAAU;AAAC,YAAAA,KAAE,MAAKD,MAAGF,GAAE,MAAMI,IAAEC,EAAC;AAAA,UAAC,GAAGJ,EAAC,GAAEK,MAAGN,GAAE,MAAMI,IAAEC,EAAC;AAAA,QAAC;AAAA,MAAC,GAAE,YAAW,SAASL,IAAE;AAAC,eAAM,6EAA6E,KAAKA,EAAC;AAAA,MAAC,EAAC;AAAE,eAAS,EAAEA,IAAE;AAAC,eAAOA,GAAE,CAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAOA,GAAEA,GAAE,SAAO,CAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAOA,GAAE,MAAM,CAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAE;AAAC,YAAGD,MAAGA,GAAE,UAAQC,IAAE;AAAC,cAAGD,GAAE,QAAQ,QAAM,OAAKA,GAAE,QAAQC,EAAC;AAAE,cAAGD,GAAE,SAAS,QAAOA,GAAE,SAASC,EAAC;AAAA,QAAC;AAAC,eAAM;AAAA,MAAE;AAAC,YAAM,IAAE,EAAC,MAAK,GAAE,MAAK,GAAE,SAAQ,SAASD,IAAE;AAAC,eAAOA,GAAE,MAAM,GAAEA,GAAE,SAAO,CAAC;AAAA,MAAC,GAAE,MAAK,GAAE,MAAK,SAASA,IAAEC,IAAE;AAAC,YAAGD,MAAGA,GAAE,UAAQC,IAAE;AAAC,cAAIC,KAAEF,GAAE,QAAQC,EAAC;AAAE,iBAAM,OAAKC,KAAE,OAAKF,GAAEE,KAAE,CAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAI,GAAE,MAAK,SAASF,IAAEC,IAAE;AAAC,YAAGD,MAAGA,GAAE,UAAQC,IAAE;AAAC,cAAIC,KAAEF,GAAE,QAAQC,EAAC;AAAE,iBAAM,OAAKC,KAAE,OAAKF,GAAEE,KAAE,CAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAI,GAAE,MAAK,SAASF,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEC,KAAEH,GAAE,QAAOE,KAAEC,IAAED,MAAI;AAAC,cAAIE,KAAEJ,GAAEE,EAAC;AAAE,cAAGD,GAAEG,EAAC,EAAE,QAAOA;AAAA,QAAC;AAAA,MAAC,GAAE,UAAS,GAAE,KAAI,SAASJ,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEC,KAAEH,GAAE,QAAOE,KAAEC,IAAED,KAAI,KAAG,CAACD,GAAED,GAAEE,EAAC,CAAC,EAAE,QAAM;AAAG,eAAM;AAAA,MAAE,GAAE,KAAI,SAASF,IAAEC,IAAE;AAAC,eAAOA,KAAEA,MAAG,EAAE,MAAKD,GAAE,OAAQ,SAASA,IAAEE,IAAE;AAAC,iBAAOF,KAAEC,GAAEC,EAAC;AAAA,QAAC,GAAG,CAAC;AAAA,MAAC,GAAE,MAAK,SAASF,IAAE;AAAC,iBAAQC,KAAE,CAAC,GAAEC,KAAEF,GAAE,QAAOG,KAAE,IAAG,EAAEA,KAAED,KAAG,CAAAD,GAAEE,EAAC,IAAEH,GAAEG,EAAC;AAAE,eAAOF;AAAA,MAAC,GAAE,SAAQ,SAASD,IAAE;AAAC,eAAM,CAACA,MAAG,CAACA,GAAE;AAAA,MAAM,GAAE,WAAU,SAASA,IAAEC,IAAE;AAAC,eAAOD,GAAE,SAAO,EAAEA,EAAC,EAAE,OAAQ,SAASA,IAAEE,IAAE;AAAC,cAAIC,KAAE,EAAEH,EAAC;AAAE,iBAAOC,GAAE,EAAEE,EAAC,GAAED,EAAC,IAAEC,GAAEA,GAAE,MAAM,IAAED,KAAEF,GAAEA,GAAE,MAAM,IAAE,CAACE,EAAC,GAAEF;AAAA,QAAC,GAAG,CAAC,CAAC,EAAEA,EAAC,CAAC,CAAC,CAAC,IAAE,CAAC;AAAA,MAAC,GAAE,SAAQ,SAASA,IAAE;AAAC,iBAAQC,KAAE,CAAC,GAAEC,KAAE,GAAEC,KAAEH,GAAE,QAAOE,KAAEC,IAAED,KAAI,CAAAF,GAAEE,EAAC,KAAGD,GAAE,KAAKD,GAAEE,EAAC,CAAC;AAAE,eAAOD;AAAA,MAAC,GAAE,QAAO,SAASD,IAAE;AAAC,iBAAQC,KAAE,CAAC,GAAEC,KAAE,GAAEC,KAAEH,GAAE,QAAOE,KAAEC,IAAED,KAAI,GAAED,IAAED,GAAEE,EAAC,CAAC,KAAGD,GAAE,KAAKD,GAAEE,EAAC,CAAC;AAAE,eAAOD;AAAA,MAAC,EAAC;AAAE,UAAI,IAAE,OAAO,aAAa,GAAG;AAAE,eAAS,EAAED,IAAE;AAAC,eAAOA,MAAG,EAAE,EAAEA,EAAC,EAAE,SAAS,eAAe;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAOA,KAAEA,GAAE,YAAY,GAAE,SAASC,IAAE;AAAC,iBAAOA,MAAGA,GAAE,SAAS,YAAY,MAAID;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,MAAIA,GAAE;AAAA,MAAQ;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,2DAA2D,KAAKA,GAAE,SAAS,YAAY,CAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAM,CAAC,EAAEA,EAAC,MAAIA,MAAG,sBAAsB,KAAKA,GAAE,SAAS,YAAY,CAAC;AAAA,MAAE;AAAC,UAAI,IAAE,EAAE,KAAK,GAAE,IAAE,EAAE,IAAI;AAAE,UAAI,IAAE,EAAE,OAAO,GAAE,IAAE,EAAE,MAAM;AAAE,eAAS,EAAEA,IAAE;AAAC,eAAM,EAAE,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC;AAAA,MAAE;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,UAAU,KAAKA,GAAE,SAAS,YAAY,CAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,IAAI;AAAE,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,UAAU,KAAKA,GAAE,SAAS,YAAY,CAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,YAAY;AAAE,eAAS,EAAEA,IAAE;AAAC,eAAO,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,GAAG;AAAE,UAAI,IAAE,EAAE,MAAM;AAAE,UAAI,IAAE,EAAE,UAAQ,EAAE,iBAAe,KAAG,WAAS;AAAO,eAAS,EAAEA,IAAE;AAAC,eAAO,EAAEA,EAAC,IAAEA,GAAE,UAAU,SAAOA,KAAEA,GAAE,WAAW,SAAO;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,YAAIC,KAAE,EAAED,EAAC;AAAE,eAAO,MAAIC,OAAI,CAAC,EAAED,EAAC,KAAG,MAAIC,MAAGD,GAAE,cAAY,KAAG,EAAE,CAAC,EAAE,IAAIA,GAAE,YAAW,CAAC,KAAG,OAAKA,GAAE;AAAA,MAAW;AAAC,eAAS,EAAEA,IAAE;AAAC,UAAEA,EAAC,KAAG,EAAEA,EAAC,MAAIA,GAAE,YAAU;AAAA,MAAE;AAAC,eAAS,EAAEA,IAAEC,IAAE;AAAC,eAAKD,MAAG;AAAC,cAAGC,GAAED,EAAC,EAAE,QAAOA;AAAE,cAAG,EAAEA,EAAC,EAAE;AAAM,UAAAA,KAAEA,GAAE;AAAA,QAAU;AAAC,eAAO;AAAA,MAAI;AAAC,eAAS,EAAEA,IAAEC,IAAE;AAAC,QAAAA,KAAEA,MAAG,EAAE;AAAK,YAAIC,KAAE,CAAC;AAAE,eAAO,EAAEF,IAAG,SAASA,IAAE;AAAC,iBAAO,EAAEA,EAAC,KAAGE,GAAE,KAAKF,EAAC,GAAEC,GAAED,EAAC;AAAA,QAAC,CAAE,GAAEE;AAAA,MAAC;AAAC,eAAS,EAAEF,IAAEC,IAAE;AAAC,QAAAA,KAAEA,MAAG,EAAE;AAAK,iBAAQC,KAAE,CAAC,GAAEF,MAAG,CAACC,GAAED,EAAC,IAAG,CAAAE,GAAE,KAAKF,EAAC,GAAEA,KAAEA,GAAE;AAAY,eAAOE;AAAA,MAAC;AAAC,eAAS,EAAEF,IAAEC,IAAE;AAAC,YAAIC,KAAED,GAAE,aAAYE,KAAEF,GAAE;AAAW,eAAOC,KAAEC,GAAE,aAAaH,IAAEE,EAAC,IAAEC,GAAE,YAAYH,EAAC,GAAEA;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,eAAO,EAAE,EAAE,KAAKD,IAAG,SAASA,IAAEE,IAAE;AAAC,WAACD,MAAG,EAAEF,EAAC,KAAG,SAAOA,GAAE,cAAY,EAAEG,EAAC,KAAGH,GAAE,YAAY,GAAG,IAAI,CAAC,GAAEA,GAAE,YAAYG,EAAC;AAAA,QAAC,CAAE,GAAEH;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAO,MAAIA,GAAE;AAAA,MAAM;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAOA,GAAE,WAAS,EAAEA,GAAE,IAAI;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAO,EAAEA,EAAC,KAAG,EAAEA,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAE;AAAC,eAAKD,MAAGA,OAAIC,MAAG;AAAC,cAAG,MAAI,GAAGD,EAAC,EAAE,QAAM;AAAG,UAAAA,KAAEA,GAAE;AAAA,QAAU;AAAC,eAAM;AAAA,MAAE;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,YAAG,CAACA,GAAE,QAAM;AAAG,eAAKD,MAAGA,OAAIC,MAAG;AAAC,cAAG,GAAGD,EAAC,MAAI,EAAEA,GAAE,UAAU,IAAE,EAAE,QAAM;AAAG,UAAAA,KAAEA,GAAE;AAAA,QAAU;AAAC,eAAM;AAAA,MAAE;AAAC,eAAS,GAAGA,IAAE;AAAC,iBAAQC,KAAE,GAAED,KAAEA,GAAE,kBAAiB,CAAAC,MAAG;AAAE,eAAOA;AAAA,MAAC;AAAC,eAAS,GAAGD,IAAE;AAAC,eAAM,CAAC,EAAEA,MAAGA,GAAE,cAAYA,GAAE,WAAW;AAAA,MAAO;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,YAAIC,IAAEC;AAAE,YAAG,MAAIH,GAAE,QAAO;AAAC,cAAG,EAAEA,GAAE,IAAI,EAAE,QAAO;AAAK,UAAAE,KAAEF,GAAE,KAAK,YAAWG,KAAE,GAAGH,GAAE,IAAI;AAAA,QAAC,MAAM,IAAGA,GAAE,IAAI,IAAEG,KAAE,EAAED,KAAEF,GAAE,KAAK,WAAWA,GAAE,SAAO,CAAC,CAAC,KAAGE,KAAEF,GAAE,MAAKG,KAAEF,KAAE,IAAED,GAAE,SAAO;AAAG,eAAM,EAAC,MAAKE,IAAE,QAAOC,GAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAEC,IAAE;AAAC,YAAIC,IAAEC;AAAE,YAAG,EAAEH,GAAE,IAAI,MAAIA,GAAE,QAAO;AAAC,cAAG,EAAEA,GAAE,IAAI,EAAE,QAAO;AAAK,cAAII,KAAE,GAAGJ,GAAE,IAAI;AAAE,UAAAI,MAAGF,KAAEE,IAAED,KAAE,MAAID,KAAEF,GAAE,KAAK,YAAWG,KAAE,GAAGH,GAAE,IAAI,IAAE;AAAA,QAAE,MAAM,IAAGA,GAAE,IAAI,KAAGE,KAAEF,GAAE,KAAK,WAAWA,GAAE,MAAM,GAAEG,KAAE,MAAID,KAAEF,GAAE,MAAKG,KAAEF,KAAE,EAAED,GAAE,IAAI,IAAEA,GAAE,SAAO;AAAG,eAAM,EAAC,MAAKE,IAAE,QAAOC,GAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAEC,IAAE;AAAC,YAAIC,IAAEC,KAAE;AAAE,YAAG,EAAEH,GAAE,IAAI,MAAIA,GAAE,QAAO;AAAC,cAAG,EAAEA,GAAE,IAAI,EAAE,QAAO;AAAK,UAAAE,KAAEF,GAAE,KAAK,YAAWG,KAAE,GAAGH,GAAE,IAAI,IAAE,GAAE,EAAEE,EAAC,MAAIA,KAAEF,GAAE,KAAK,aAAYG,KAAE;AAAA,QAAE,MAAM,IAAGH,GAAE,IAAI,KAAGE,KAAEF,GAAE,KAAK,WAAWA,GAAE,MAAM,GAAEG,KAAE,MAAID,KAAEF,GAAE,MAAKG,KAAEF,KAAE,EAAED,GAAE,IAAI,IAAEA,GAAE,SAAO;AAAG,eAAM,EAAC,MAAKE,IAAE,QAAOC,GAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAGA,GAAE,eAAaA,GAAE,WAASA,GAAE,YAAY,OAAO,QAAO,EAAEA,GAAE,WAAW,IAAEA,GAAE,cAAY,GAAGA,GAAE,WAAW;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,eAAOD,GAAE,SAAOC,GAAE,QAAMD,GAAE,WAASC,GAAE;AAAA,MAAM;AAAC,eAAS,GAAGD,IAAEC,IAAE;AAAC,YAAIC,KAAED,MAAGA,GAAE,wBAAuBE,KAAEF,MAAGA,GAAE,qBAAoBG,KAAEH,MAAGA,GAAE;AAAqB,YAAGG,OAAIF,KAAE,OAAI,EAAEF,EAAC,MAAI,EAAEA,GAAE,IAAI,KAAGG,KAAG;AAAC,cAAG,EAAEH,EAAC,EAAE,QAAOA,GAAE;AAAK,cAAG,EAAEA,EAAC,EAAE,QAAOA,GAAE,KAAK;AAAA,QAAW;AAAC,YAAG,EAAEA,GAAE,IAAI,EAAE,QAAOA,GAAE,KAAK,UAAUA,GAAE,MAAM;AAAE,YAAIK,KAAE,EAAEL,GAAE,KAAK,WAAWA,GAAE,MAAM,CAAC,GAAEM,KAAE,EAAEN,GAAE,KAAK,UAAU,KAAE,GAAEA,GAAE,IAAI;AAAE,eAAO,EAAEM,IAAED,EAAC,GAAEH,OAAI,EAAEF,GAAE,IAAI,GAAE,EAAEM,EAAC,IAAGF,OAAI,EAAEJ,GAAE,IAAI,KAAG,GAAGA,GAAE,IAAI,GAAE,EAAEM,EAAC,MAAI,GAAGA,EAAC,GAAEN,GAAE,KAAK,eAAaM;AAAA,MAAC;AAAC,eAAS,GAAGN,IAAEC,IAAEC,IAAE;AAAC,YAAIC,KAAE,EAAEF,GAAE,MAAK,EAAE,GAAGD,EAAC,CAAC;AAAE,YAAG,CAACG,GAAE,OAAO,QAAO;AAAK,YAAG,MAAIA,GAAE,OAAO,QAAO,GAAGF,IAAEC,EAAC;AAAE,YAAGC,GAAE,SAAO,GAAE;AAAC,cAAIC,KAAED,GAAE,MAAM,GAAEA,GAAE,SAAO,CAAC,EAAE,KAAM,SAASH,IAAE;AAAC,mBAAOA,GAAE;AAAA,UAAW,CAAE;AAAE,cAAGI,MAAG,KAAGH,GAAE,UAAQ,EAAEA,EAAC,GAAE;AAAC,gBAAII,IAAEC,KAAEF,GAAE;AAAY,iBAAGE,GAAE,YAAUH,KAAE,EAAEE,KAAEC,GAAE,WAAW,CAAC,GAAE,EAAE,GAAGN,EAAC,CAAC,GAAEC,KAAE,EAAC,MAAKI,IAAE,QAAO,EAAC,KAAG,KAAGC,GAAE,YAAUA,GAAE,KAAK,MAAM,SAAS,MAAIH,KAAE,EAAEE,KAAEC,IAAE,EAAE,GAAGN,EAAC,CAAC,GAAEC,KAAE,EAAC,MAAKI,IAAE,QAAO,EAAC;AAAA,UAAE;AAAA,QAAC;AAAC,eAAOF,GAAE,OAAQ,SAASH,IAAEG,IAAE;AAAC,iBAAOH,OAAIC,GAAE,SAAOD,KAAE,GAAGC,IAAEC,EAAC,IAAG,GAAG,EAAC,MAAKC,IAAE,QAAOH,KAAE,GAAGA,EAAC,IAAE,EAAEG,EAAC,EAAC,GAAED,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC;AAAC,eAAS,GAAGF,IAAE;AAAC,eAAO,SAAS,cAAcA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,YAAGD,MAAGA,GAAE,YAAW;AAAC,cAAGA,GAAE,WAAW,QAAOA,GAAE,WAAWC,EAAC;AAAE,cAAIC,KAAEF,GAAE;AAAW,cAAG,CAACC,IAAE;AAAC,qBAAQE,KAAE,CAAC,GAAEC,KAAE,GAAEC,KAAEL,GAAE,WAAW,QAAOI,KAAEC,IAAED,KAAI,CAAAD,GAAE,KAAKH,GAAE,WAAWI,EAAC,CAAC;AAAE,qBAAQE,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAEC,IAAED,KAAI,CAAAJ,GAAE,aAAaC,GAAEG,EAAC,GAAEN,EAAC;AAAA,UAAC;AAAC,UAAAE,GAAE,YAAYF,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,UAAI,KAAG,EAAE,UAAU;AAAE,eAAS,GAAGA,IAAEC,IAAE;AAAC,YAAIC,KAAE,GAAGF,GAAE,CAAC,CAAC,IAAEA,GAAE,IAAI,IAAEA,GAAE,KAAK;AAAE,eAAOC,KAAEC,GAAE,QAAQ,WAAU,EAAE,IAAEA;AAAA,MAAC;AAAC,YAAM,KAAG,EAAC,WAAU,GAAE,sBAAqB,UAAS,OAAM,GAAE,WAAU,MAAM,OAAO,GAAE,MAAM,GAAE,oBAAmB,GAAE,YAAW,GAAE,iBAAgB,SAASF,IAAE;AAAC,eAAOA,MAAG,EAAE,EAAEA,EAAC,EAAE,SAAS,qBAAqB;AAAA,MAAC,GAAE,QAAO,GAAE,WAAU,SAASA,IAAE;AAAC,eAAOA,MAAG,MAAIA,GAAE;AAAA,MAAQ,GAAE,QAAO,GAAE,QAAO,GAAE,YAAW,SAASA,IAAE;AAAC,eAAO,EAAEA,EAAC,KAAG,CAAC,EAAEA,EAAC;AAAA,MAAC,GAAE,WAAU,SAASA,IAAE;AAAC,eAAOA,MAAG,UAAU,KAAKA,GAAE,SAAS,YAAY,CAAC;AAAA,MAAC,GAAE,UAAS,GAAE,SAAQ,EAAE,IAAI,CAAC,GAAE,cAAa,SAASA,IAAE;AAAC,eAAO,EAAEA,EAAC,KAAG,CAAC,EAAEA,IAAE,CAAC;AAAA,MAAC,GAAE,QAAO,GAAE,cAAa,SAASA,IAAE;AAAC,eAAO,EAAEA,EAAC,KAAG,CAAC,CAAC,EAAEA,IAAE,CAAC;AAAA,MAAC,GAAE,OAAM,GAAE,QAAO,GAAE,SAAQ,GAAE,QAAO,GAAE,QAAO,GAAE,cAAa,GAAE,iBAAgB,GAAE,UAAS,GAAE,OAAM,EAAE,KAAK,GAAE,MAAK,GAAE,MAAK,EAAE,IAAI,GAAE,QAAO,EAAE,MAAM,GAAE,KAAI,EAAE,GAAG,GAAE,KAAI,EAAE,GAAG,GAAE,KAAI,EAAE,GAAG,GAAE,KAAI,EAAE,GAAG,GAAE,OAAM,EAAE,KAAK,GAAE,YAAW,IAAG,qBAAoB,SAASA,IAAE;AAAC,WAAE;AAAC,cAAG,SAAOA,GAAE,qBAAmB,OAAKA,GAAE,kBAAkB,UAAU;AAAA,QAAK,SAAOA,KAAEA,GAAE;AAAmB,eAAO,EAAEA,EAAC;AAAA,MAAC,GAAE,SAAQ,GAAE,eAAc,EAAE,IAAI,GAAE,CAAC,GAAE,kBAAiB,SAASA,IAAEC,IAAE;AAAC,eAAOD,GAAE,gBAAcC,MAAGD,GAAE,oBAAkBC;AAAA,MAAC,GAAE,qBAAoB,SAASD,IAAEC,IAAE;AAAC,QAAAA,KAAEA,MAAG,EAAE;AAAG,YAAIC,KAAE,CAAC;AAAE,eAAOF,GAAE,mBAAiBC,GAAED,GAAE,eAAe,KAAGE,GAAE,KAAKF,GAAE,eAAe,GAAEE,GAAE,KAAKF,EAAC,GAAEA,GAAE,eAAaC,GAAED,GAAE,WAAW,KAAGE,GAAE,KAAKF,GAAE,WAAW,GAAEE;AAAA,MAAC,GAAE,YAAW,GAAE,iBAAgB,GAAE,kBAAiB,GAAE,aAAY,GAAE,cAAa,GAAE,eAAc,IAAG,mBAAkB,SAASF,IAAEC,IAAE;AAAC,eAAO,EAAED,EAAC,KAAG,EAAEA,GAAE,MAAKC,EAAC;AAAA,MAAC,GAAE,oBAAmB,SAASD,IAAEC,IAAE;AAAC,eAAO,EAAED,EAAC,KAAG,GAAGA,GAAE,MAAKC,EAAC;AAAA,MAAC,GAAE,WAAU,IAAG,WAAU,IAAG,wBAAuB,IAAG,aAAY,IAAG,gBAAe,SAASD,IAAE;AAAC,YAAG,EAAEA,GAAE,IAAI,KAAG,CAAC,GAAGA,GAAE,IAAI,KAAG,EAAEA,GAAE,IAAI,EAAE,QAAM;AAAG,YAAIC,KAAED,GAAE,KAAK,WAAWA,GAAE,SAAO,CAAC,GAAEE,KAAEF,GAAE,KAAK,WAAWA,GAAE,MAAM;AAAE,eAAM,GAAGC,MAAG,CAAC,EAAEA,EAAC,KAAGC,MAAG,CAAC,EAAEA,EAAC,MAAI,CAAC,EAAEA,EAAC;AAAA,MAAE,GAAE,gBAAe,SAASF,IAAEC,IAAE;AAAC,eAAKD,MAAG;AAAC,cAAGC,GAAED,EAAC,EAAE,QAAOA;AAAE,UAAAA,KAAE,GAAGA,EAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAI,GAAE,gBAAe,SAASA,IAAEC,IAAE;AAAC,eAAKD,MAAG;AAAC,cAAGC,GAAED,EAAC,EAAE,QAAOA;AAAE,UAAAA,KAAE,GAAGA,EAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAI,GAAE,aAAY,SAASA,IAAE;AAAC,YAAG,CAAC,EAAEA,GAAE,IAAI,EAAE,QAAM;AAAG,YAAIC,KAAED,GAAE,KAAK,UAAU,OAAOA,GAAE,SAAO,CAAC;AAAE,eAAOC,MAAG,QAAMA,MAAGA,OAAI;AAAA,MAAC,GAAE,cAAa,SAASD,IAAE;AAAC,YAAG,CAAC,EAAEA,GAAE,IAAI,EAAE,QAAM;AAAG,YAAIC,KAAED,GAAE,KAAK,UAAU,OAAOA,GAAE,SAAO,CAAC;AAAE,eAAM,QAAMC,MAAGA,OAAI;AAAA,MAAC,GAAE,WAAU,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAQC,KAAEJ,IAAEI,MAAGA,GAAE,SAAOF,GAAEE,EAAC,GAAE,CAAC,GAAGA,IAAEH,EAAC,MAAI;AAAC,UAAAG,KAAE,GAAGA,IAAED,MAAGH,GAAE,SAAOI,GAAE,QAAMH,GAAE,SAAOG,GAAE,IAAI;AAAA,QAAC;AAAA,MAAC,GAAE,UAAS,GAAE,qBAAoB,SAASJ,IAAEC,IAAE;AAAC,aAAID,KAAEA,GAAE,YAAWA,MAAG,MAAI,EAAEA,EAAC,KAAG;AAAC,cAAGC,GAAED,EAAC,EAAE,QAAOA;AAAE,cAAG,EAAEA,EAAC,EAAE;AAAM,UAAAA,KAAEA,GAAE;AAAA,QAAU;AAAC,eAAO;AAAA,MAAI,GAAE,cAAa,GAAE,cAAa,SAASA,IAAEC,IAAE;AAAC,YAAIC,KAAE,EAAEF,EAAC;AAAE,eAAO,EAAE,KAAKE,GAAE,OAAOD,EAAC,CAAC;AAAA,MAAC,GAAE,UAAS,GAAE,UAAS,SAASD,IAAEC,IAAE;AAAC,QAAAA,KAAEA,MAAG,EAAE;AAAK,iBAAQC,KAAE,CAAC,GAAEF,MAAG,CAACC,GAAED,EAAC,IAAG,CAAAE,GAAE,KAAKF,EAAC,GAAEA,KAAEA,GAAE;AAAgB,eAAOE;AAAA,MAAC,GAAE,gBAAe,SAASF,IAAEC,IAAE;AAAC,YAAIC,KAAE,CAAC;AAAE,eAAOD,KAAEA,MAAG,EAAE,IAAG,SAASE,GAAEC,IAAE;AAAC,UAAAJ,OAAII,MAAGH,GAAEG,EAAC,KAAGF,GAAE,KAAKE,EAAC;AAAE,mBAAQC,KAAE,GAAEC,KAAEF,GAAE,WAAW,QAAOC,KAAEC,IAAED,KAAI,CAAAF,GAAEC,GAAE,WAAWC,EAAC,CAAC;AAAA,QAAC,EAAEL,EAAC,GAAEE;AAAA,MAAC,GAAE,gBAAe,SAASF,IAAEC,IAAE;AAAC,iBAAQC,KAAE,EAAEF,EAAC,GAAEG,KAAEF,IAAEE,IAAEA,KAAEA,GAAE,WAAW,KAAGD,GAAE,QAAQC,EAAC,IAAE,GAAG,QAAOA;AAAE,eAAO;AAAA,MAAI,GAAE,MAAK,SAASH,IAAEC,IAAE;AAAC,YAAIC,KAAEF,GAAE,YAAWG,KAAE,EAAE,EAAE,MAAIF,KAAE,GAAG,EAAE,CAAC;AAAE,eAAOC,GAAE,aAAaC,IAAEH,EAAC,GAAEG,GAAE,YAAYH,EAAC,GAAEG;AAAA,MAAC,GAAE,aAAY,GAAE,kBAAiB,GAAE,UAAS,IAAG,aAAY,IAAG,gBAAe,SAASH,IAAEC,IAAE;AAAC,eAAO,EAAEA,IAAE,EAAE,GAAGD,EAAC,CAAC,EAAE,IAAI,EAAE,EAAE,QAAQ;AAAA,MAAC,GAAE,gBAAe,SAASA,IAAEC,IAAE;AAAC,iBAAQC,KAAEF,IAAEG,KAAE,GAAEC,KAAEH,GAAE,QAAOE,KAAEC,IAAED,KAAI,CAAAD,KAAEA,GAAE,WAAW,UAAQD,GAAEE,EAAC,IAAED,GAAE,WAAWA,GAAE,WAAW,SAAO,CAAC,IAAEA,GAAE,WAAWD,GAAEE,EAAC,CAAC;AAAE,eAAOD;AAAA,MAAC,GAAE,WAAU,IAAG,YAAW,SAASF,IAAEC,IAAE;AAAC,YAAIC,IAAEC,IAAEC,KAAEH,KAAE,IAAE,GAAEI,KAAE,EAAEL,GAAE,MAAKI,EAAC,GAAEE,KAAE,EAAE,KAAKD,EAAC,KAAGL,GAAE;AAAK,QAAAI,GAAEE,EAAC,KAAGJ,KAAEG,GAAEA,GAAE,SAAO,CAAC,GAAEF,KAAEG,MAAGH,MAAGD,KAAEI,IAAG;AAAW,YAAIC,KAAEL,MAAG,GAAGA,IAAEF,IAAE,EAAC,wBAAuBC,IAAE,qBAAoBA,GAAC,CAAC;AAAE,eAAOM,MAAGJ,OAAIH,GAAE,SAAOO,KAAEP,GAAE,KAAK,WAAWA,GAAE,MAAM,IAAG,EAAC,WAAUO,IAAE,WAAUJ,GAAC;AAAA,MAAC,GAAE,QAAO,IAAG,YAAW,SAASH,IAAE;AAAC,eAAO,SAAS,eAAeA,EAAC;AAAA,MAAC,GAAE,QAAO,IAAG,aAAY,SAASA,IAAEC,IAAE;AAAC,eAAKD,MAAG,CAAC,EAAEA,EAAC,KAAGC,GAAED,EAAC,KAAG;AAAC,cAAIE,KAAEF,GAAE;AAAW,aAAGA,EAAC,GAAEA,KAAEE;AAAA,QAAC;AAAA,MAAC,GAAE,SAAQ,SAASF,IAAEC,IAAE;AAAC,YAAGD,GAAE,SAAS,YAAY,MAAIC,GAAE,YAAY,EAAE,QAAOD;AAAE,YAAIE,KAAE,GAAGD,EAAC;AAAE,eAAOD,GAAE,MAAM,YAAUE,GAAE,MAAM,UAAQF,GAAE,MAAM,UAAS,EAAEE,IAAE,EAAE,KAAKF,GAAE,UAAU,CAAC,GAAE,EAAEE,IAAEF,EAAC,GAAE,GAAGA,EAAC,GAAEE;AAAA,MAAC,GAAE,MAAK,SAASF,IAAEC,IAAE;AAAC,YAAIC,KAAE,GAAGF,EAAC;AAAE,YAAGC,IAAE;AAAC,UAAAC,MAAGA,KAAEA,GAAE,QAAQ,yCAAyC,SAASF,IAAEC,IAAEC,IAAE;AAAC,YAAAA,KAAEA,GAAE,YAAY;AAAE,gBAAIC,KAAE,8BAA8B,KAAKD,EAAC,KAAG,CAAC,CAACD,IAAEG,KAAE,4CAA4C,KAAKF,EAAC;AAAE,mBAAOF,MAAGG,MAAGC,KAAE,OAAK;AAAA,UAAG,CAAE,GAAG,KAAK;AAAA,QAAC;AAAC,eAAOF;AAAA,MAAC,GAAE,OAAM,IAAG,oBAAmB,SAASF,IAAE;AAAC,YAAIC,KAAE,EAAE,EAAED,EAAC,GAAEE,KAAED,GAAE,OAAO,GAAEE,KAAEF,GAAE,YAAY,IAAE;AAAE,eAAM,EAAC,MAAKC,GAAE,MAAK,KAAIA,GAAE,MAAIC,GAAC;AAAA,MAAC,GAAE,cAAa,SAASH,IAAEC,IAAE;AAAC,eAAO,KAAKA,EAAC,EAAE,QAAS,SAASC,IAAE;AAAC,UAAAF,GAAE,GAAGE,IAAED,GAAEC,EAAC,CAAC;AAAA,QAAC,CAAE;AAAA,MAAC,GAAE,cAAa,SAASF,IAAEC,IAAE;AAAC,eAAO,KAAKA,EAAC,EAAE,QAAS,SAASC,IAAE;AAAC,UAAAF,GAAE,IAAIE,IAAED,GAAEC,EAAC,CAAC;AAAA,QAAC,CAAE;AAAA,MAAC,GAAE,kBAAiB,SAASF,IAAE;AAAC,eAAOA,MAAG,CAAC,EAAEA,EAAC,KAAG,EAAE,SAASA,GAAE,WAAU,eAAe;AAAA,MAAC,EAAC;AAAE,eAAS,GAAGA,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAEC,IAAE;AAAC,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,QAAMC,IAAE,KAAK,QAAM,CAAC,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,UAAQ,EAAE,EAAE,OAAO,MAAG,CAAC,GAAEC,EAAC,GAAE,EAAE,EAAE,WAAW,KAAG,EAAE,EAAE,WAAW,YAAY,KAAK,OAAO,GAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,WAAW;AAAA,QAAC,GAAED,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,iBAAO,KAAK,aAAW,KAAK,GAAG,aAAa,KAAK,KAAK,GAAE,KAAK,YAAY,GAAE,KAAK,MAAM,KAAK,GAAE;AAAA,QAAI,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,SAAS,GAAE,KAAK,MAAM,WAAW,YAAY,GAAE,KAAK,GAAG,aAAa,KAAK,OAAM,KAAK,UAAU;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,WAAU;AAAC,cAAID,KAAE,KAAK,WAAW;AAAE,eAAK,KAAK,GAAG,SAAS,GAAE,KAAK,SAAS,GAAE,KAAK,YAAY,GAAEA,MAAG,KAAK,QAAQ;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,cAAIA,KAAE;AAAK,eAAK,QAAQ,KAAG,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC,GAAE,KAAK,QAAQ,YAAU,KAAK,QAAQ,aAAW,KAAK,WAAW;AAAO,cAAIC,KAAE,EAAE,EAAE,OAAO,CAAC,GAAE,KAAK,QAAQ,OAAO;AAAE,iBAAO,KAAKA,EAAC,EAAE,QAAS,SAASC,IAAE;AAAC,YAAAF,GAAE,KAAK,YAAUE,IAAED,GAAEC,EAAC,CAAC;AAAA,UAAC,CAAE;AAAE,cAAIA,KAAE,EAAE,EAAE,OAAO,CAAC,GAAE,KAAK,QAAQ,SAAQ,EAAE,EAAE,WAAW,WAAS,CAAC,CAAC;AAAE,iBAAO,KAAKA,EAAC,EAAE,QAAS,SAASD,IAAE;AAAC,YAAAD,GAAE,OAAOC,IAAEC,GAAED,EAAC,GAAE,IAAE;AAAA,UAAC,CAAE,GAAE,OAAO,KAAK,KAAK,OAAO,EAAE,QAAS,SAASA,IAAE;AAAC,YAAAD,GAAE,iBAAiBC,EAAC;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,iBAAO,KAAK,KAAK,OAAO,EAAE,QAAQ,EAAE,QAAS,SAASC,IAAE;AAAC,YAAAD,GAAE,aAAaC,EAAC;AAAA,UAAC,CAAE,GAAE,OAAO,KAAK,KAAK,KAAK,EAAE,QAAS,SAASA,IAAE;AAAC,YAAAD,GAAE,WAAWC,EAAC;AAAA,UAAC,CAAE,GAAE,KAAK,aAAa,WAAU,IAAI;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE,KAAK,OAAO,sBAAsB;AAAE,cAAG,WAASD,GAAE,QAAO,KAAK,OAAO,eAAe,GAAEC,KAAE,KAAK,WAAW,QAAQ,IAAI,IAAE,KAAK,WAAW,SAAS,KAAK;AAAE,UAAAA,KAAE,KAAK,OAAO,iBAAgBD,EAAC,IAAE,KAAK,WAAW,SAAS,KAAKA,EAAC,GAAE,KAAK,MAAM,IAAIA,EAAC,GAAE,KAAK,aAAa,UAASA,IAAE,KAAK,WAAW,QAAQ;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,iBAAM,YAAU,KAAK,WAAW,SAAS,KAAK,iBAAiB;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,eAAK,WAAW,SAAS,KAAK,mBAAkB,IAAE,GAAE,KAAK,OAAO,oBAAmB,IAAE,GAAE,KAAK,aAAa,WAAU,KAAE,GAAE,KAAK,QAAQ,UAAQ;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,OAAO,sBAAsB,KAAG,KAAK,OAAO,qBAAqB,GAAE,KAAK,WAAW,SAAS,KAAK,mBAAkB,KAAE,GAAE,KAAK,QAAQ,UAAQ,OAAG,KAAK,OAAO,sBAAqB,IAAE,GAAE,KAAK,aAAa,WAAU,IAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,WAAU;AAAC,cAAIA,KAAE,EAAE,KAAK,SAAS,GAAEC,KAAE,EAAE,KAAK,EAAE,KAAK,SAAS,CAAC,GAAEC,KAAE,KAAK,QAAQ,UAAU,EAAE,iBAAiBF,IAAE,IAAI,CAAC;AAAE,UAAAE,MAAGA,GAAE,MAAM,KAAK,MAAM,CAAC,GAAED,EAAC,GAAE,KAAK,MAAM,QAAQ,gBAAcD,IAAEC,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE,KAAK,QAAQD,EAAC;AAAE,UAAAC,GAAE,mBAAiBA,GAAE,oBAAkB,EAAE,IAAGA,GAAE,iBAAiB,MAAIA,GAAE,cAAYA,GAAE,WAAW,GAAEA,GAAE,UAAQ,GAAG,aAAa,KAAK,OAAMA,GAAE,MAAM;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASD,IAAEC,IAAEC,IAAE;AAAC,cAAG,MAAI,UAAU,OAAO,QAAO,KAAK,QAAQF,EAAC;AAAE,eAAK,QAAQA,EAAC,IAAE,IAAIC,GAAE,IAAI,GAAEC,MAAG,KAAK,iBAAiBF,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAE,KAAK,QAAQD,EAAC;AAAE,UAAAC,GAAE,iBAAiB,MAAIA,GAAE,UAAQ,GAAG,aAAa,KAAK,OAAMA,GAAE,MAAM,GAAEA,GAAE,WAASA,GAAE,QAAQ,IAAG,OAAO,KAAK,QAAQD,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,SAASA,IAAEC,IAAE;AAAC,cAAG,MAAI,UAAU,OAAO,QAAO,KAAK,MAAMD,EAAC;AAAE,eAAK,MAAMA,EAAC,IAAEC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAASD,IAAE;AAAC,eAAK,MAAMA,EAAC,KAAG,KAAK,MAAMA,EAAC,EAAE,WAAS,KAAK,MAAMA,EAAC,EAAE,QAAQ,GAAE,OAAO,KAAK,MAAMA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,qCAAoC,OAAM,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,iBAAO,SAASC,IAAE;AAAC,YAAAD,GAAE,oBAAoBF,IAAEC,EAAC,EAAEE,EAAC,GAAED,GAAE,OAAO,4BAA4B;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,uBAAsB,OAAM,SAASF,IAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,iBAAO,SAASC,IAAE;AAAC,YAAAA,GAAE,eAAe;AAAE,gBAAIC,KAAE,EAAE,EAAED,GAAE,MAAM;AAAE,YAAAD,GAAE,OAAOF,IAAEC,MAAGG,GAAE,QAAQ,cAAc,EAAE,KAAK,OAAO,GAAEA,EAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,cAAIJ,KAAE,EAAE,KAAK,SAAS,GAAEC,KAAE,EAAE,KAAK,EAAE,KAAK,SAAS,CAAC,GAAEC,KAAEF,GAAE,MAAM,GAAG,GAAEG,KAAED,GAAE,SAAO,GAAEE,KAAED,MAAG,EAAE,KAAKD,EAAC,GAAEG,KAAEF,KAAE,EAAE,KAAKD,EAAC,IAAE,EAAE,KAAKA,EAAC,GAAEI,KAAE,KAAK,QAAQF,MAAG,QAAQ;AAAE,iBAAM,CAACA,MAAG,KAAKC,EAAC,IAAE,KAAKA,EAAC,EAAE,MAAM,MAAKJ,EAAC,IAAEK,MAAGA,GAAED,EAAC,KAAGC,GAAE,iBAAiB,IAAEA,GAAED,EAAC,EAAE,MAAMC,IAAEL,EAAC,IAAE;AAAA,QAAM,EAAC,CAAC,GAAEA,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,eAAS,GAAGD,IAAEC,IAAE;AAAC,YAAIC,IAAEC,IAAEC,KAAEJ,GAAE,cAAc,GAAEK,KAAE,SAAS,KAAK,gBAAgB,GAAEC,KAAE,EAAE,KAAKF,GAAE,UAAU;AAAE,aAAIF,KAAE,GAAEA,KAAEI,GAAE,QAAOJ,KAAI,KAAG,CAAC,GAAG,OAAOI,GAAEJ,EAAC,CAAC,GAAE;AAAC,cAAGG,GAAE,kBAAkBC,GAAEJ,EAAC,CAAC,GAAEG,GAAE,iBAAiB,gBAAeL,EAAC,KAAG,EAAE;AAAM,UAAAG,KAAEG,GAAEJ,EAAC;AAAA,QAAC;AAAC,YAAG,MAAIA,MAAG,GAAG,OAAOI,GAAEJ,KAAE,CAAC,CAAC,GAAE;AAAC,cAAIK,KAAE,SAAS,KAAK,gBAAgB,GAAEC,KAAE;AAAK,UAAAD,GAAE,kBAAkBJ,MAAGC,EAAC,GAAEG,GAAE,SAAS,CAACJ,EAAC,GAAEK,KAAEL,KAAEA,GAAE,cAAYC,GAAE;AAAW,cAAIK,KAAET,GAAE,UAAU;AAAE,UAAAS,GAAE,YAAY,gBAAeF,EAAC;AAAE,mBAAQG,KAAED,GAAE,KAAK,QAAQ,WAAU,EAAE,EAAE,QAAOC,KAAEF,GAAE,UAAU,UAAQA,GAAE,cAAa,CAAAE,MAAGF,GAAE,UAAU,QAAOA,KAAEA,GAAE;AAAY,UAAAA,GAAE;AAAU,UAAAP,MAAGO,GAAE,eAAa,GAAG,OAAOA,GAAE,WAAW,KAAGE,OAAIF,GAAE,UAAU,WAASE,MAAGF,GAAE,UAAU,QAAOA,KAAEA,GAAE,cAAaJ,KAAEI,IAAEN,KAAEQ;AAAA,QAAC;AAAC,eAAM,EAAC,MAAKN,IAAE,QAAOF,GAAC;AAAA,MAAC;AAAC,eAAS,GAAGF,IAAE;AAAC,YAAIC,KAAE,SAAS,KAAK,gBAAgB,GAAEC,KAAE,SAASF,GAAEC,IAAEC,IAAE;AAAC,cAAIC,IAAEC;AAAE,cAAG,GAAG,OAAOH,EAAC,GAAE;AAAC,gBAAII,KAAE,GAAG,SAASJ,IAAE,EAAE,IAAI,GAAG,MAAM,CAAC,GAAEK,KAAE,EAAE,KAAKD,EAAC,EAAE;AAAgB,YAAAF,KAAEG,MAAGL,GAAE,YAAWC,MAAG,EAAE,IAAI,EAAE,KAAKG,EAAC,GAAE,GAAG,UAAU,GAAED,KAAE,CAACE;AAAA,UAAC,OAAK;AAAC,gBAAGH,KAAEF,GAAE,WAAWC,EAAC,KAAGD,IAAE,GAAG,OAAOE,EAAC,EAAE,QAAOH,GAAEG,IAAE,CAAC;AAAE,YAAAD,KAAE,GAAEE,KAAE;AAAA,UAAE;AAAC,iBAAM,EAAC,MAAKD,IAAE,iBAAgBC,IAAE,QAAOF,GAAC;AAAA,QAAC,EAAEF,GAAE,MAAKA,GAAE,MAAM;AAAE,eAAOC,GAAE,kBAAkBC,GAAE,IAAI,GAAED,GAAE,SAASC,GAAE,eAAe,GAAED,GAAE,UAAU,aAAYC,GAAE,MAAM,GAAED;AAAA,MAAC;AAAC,QAAE,EAAE,GAAG,OAAO,EAAC,YAAW,WAAU;AAAC,YAAID,KAAE,GAAG,EAAE,KAAK,SAAS,CAAC,GAAEC,KAAE,aAAWD,IAAEE,KAAE,aAAWF,IAAEG,KAAE,EAAE,EAAE,OAAO,CAAC,GAAE,EAAE,EAAE,WAAW,SAAQD,KAAE,EAAE,KAAK,SAAS,IAAE,CAAC,CAAC;AAAE,QAAAC,GAAE,WAAS,EAAE,EAAE,OAAO,MAAG,CAAC,GAAE,EAAE,EAAE,WAAW,KAAK,OAAO,GAAE,EAAE,EAAE,WAAW,KAAKA,GAAE,IAAI,CAAC,GAAEA,GAAE,QAAM,EAAE,EAAE,OAAO,MAAG,CAAC,GAAE,EAAE,EAAE,WAAW,QAAQ,OAAMA,GAAE,KAAK,GAAEA,GAAE,UAAQ,WAASA,GAAE,UAAQ,CAAC,EAAE,iBAAeA,GAAE,SAAQ,KAAK,KAAM,SAASH,IAAEC,IAAE;AAAC,cAAIC,KAAE,EAAE,EAAED,EAAC;AAAE,cAAG,CAACC,GAAE,KAAK,YAAY,GAAE;AAAC,gBAAIE,KAAE,IAAI,GAAGF,IAAEC,EAAC;AAAE,YAAAD,GAAE,KAAK,cAAaE,EAAC,GAAEF,GAAE,KAAK,YAAY,EAAE,aAAa,QAAOE,GAAE,UAAU;AAAA,UAAC;AAAA,QAAC,CAAE;AAAE,YAAIA,KAAE,KAAK,MAAM;AAAE,YAAGA,GAAE,QAAO;AAAC,cAAIE,KAAEF,GAAE,KAAK,YAAY;AAAE,cAAGH,GAAE,QAAOK,GAAE,OAAO,MAAMA,IAAE,EAAE,KAAK,SAAS,CAAC;AAAE,UAAAH,GAAE,SAAOG,GAAE,OAAO,cAAc;AAAA,QAAC;AAAC,eAAO;AAAA,MAAI,EAAC,CAAC;AAAE,UAAI,KAAG,WAAU;AAAC,iBAASN,GAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAC,SAASJ,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,KAAGC,IAAE,KAAK,KAAGC,IAAE,KAAK,KAAGC,IAAE,KAAK,KAAGC,IAAE,KAAK,eAAa,KAAK,SAAS,GAAG,UAAU,GAAE,KAAK,WAAS,KAAK,SAAS,GAAG,MAAM,GAAE,KAAK,aAAW,KAAK,SAAS,GAAG,QAAQ,GAAE,KAAK,WAAS,KAAK,SAAS,GAAG,MAAM,GAAE,KAAK,WAAS,KAAK,SAAS,GAAG,MAAM;AAAA,QAAC;AAAC,eAAOH,KAAED,IAAEE,KAAE,CAAC,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,cAAG,EAAE,mBAAkB;AAAC,gBAAIF,KAAE,SAAS,YAAY;AAAE,mBAAOA,GAAE,SAAS,KAAK,IAAG,KAAK,EAAE,GAAEA,GAAE,OAAO,KAAK,IAAG,KAAK,EAAE,GAAEA;AAAA,UAAC;AAAC,cAAIC,KAAE,GAAG,EAAC,MAAK,KAAK,IAAG,QAAO,KAAK,GAAE,CAAC;AAAE,iBAAOA,GAAE,YAAY,YAAW,GAAG,EAAC,MAAK,KAAK,IAAG,QAAO,KAAK,GAAE,CAAC,CAAC,GAAEA;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,WAAU;AAAC,iBAAM,EAAC,IAAG,KAAK,IAAG,IAAG,KAAK,IAAG,IAAG,KAAK,IAAG,IAAG,KAAK,GAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,WAAU;AAAC,iBAAM,EAAC,MAAK,KAAK,IAAG,QAAO,KAAK,GAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,iBAAM,EAAC,MAAK,KAAK,IAAG,QAAO,KAAK,GAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,cAAID,KAAE,KAAK,YAAY;AAAE,cAAG,EAAE,mBAAkB;AAAC,gBAAIC,KAAE,SAAS,aAAa;AAAE,YAAAA,GAAE,aAAW,KAAGA,GAAE,gBAAgB,GAAEA,GAAE,SAASD,EAAC;AAAA,UAAC,MAAM,CAAAA,GAAE,OAAO;AAAE,iBAAO;AAAA,QAAI,EAAC,GAAE,EAAC,KAAI,kBAAiB,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAE,EAAE,EAAED,EAAC,EAAE,OAAO;AAAE,iBAAOA,GAAE,YAAUC,KAAE,KAAK,GAAG,cAAYD,GAAE,aAAW,KAAK,IAAIA,GAAE,YAAUC,KAAE,KAAK,GAAG,SAAS,IAAG;AAAA,QAAI,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,WAAU;AAAC,cAAIA,KAAE,SAASD,IAAEC,IAAE;AAAC,gBAAG,CAACD,GAAE,QAAOA;AAAE,gBAAG,GAAG,eAAeA,EAAC,MAAI,CAAC,GAAG,YAAYA,EAAC,KAAG,GAAG,iBAAiBA,EAAC,KAAG,CAACC,MAAG,GAAG,gBAAgBD,EAAC,KAAGC,MAAG,GAAG,iBAAiBD,EAAC,KAAGC,MAAG,GAAG,OAAOD,GAAE,KAAK,WAAW,KAAG,GAAG,gBAAgBA,EAAC,KAAG,CAACC,MAAG,GAAG,OAAOD,GAAE,KAAK,eAAe,KAAG,GAAG,QAAQA,GAAE,IAAI,KAAG,GAAG,QAAQA,GAAE,IAAI,GAAG,QAAOA;AAAE,gBAAIE,KAAE,GAAG,SAASF,GAAE,MAAK,GAAG,OAAO,GAAEG,KAAE;AAAG,gBAAG,CAACA,IAAE;AAAC,kBAAIC,KAAE,GAAG,UAAUJ,EAAC,KAAG,EAAC,MAAK,KAAI;AAAE,cAAAG,MAAG,GAAG,kBAAkBH,IAAEE,EAAC,KAAG,GAAG,OAAOE,GAAE,IAAI,MAAI,CAACH;AAAA,YAAC;AAAC,gBAAII,KAAE;AAAG,gBAAG,CAACA,IAAE;AAAC,kBAAIC,KAAE,GAAG,UAAUN,EAAC,KAAG,EAAC,MAAK,KAAI;AAAE,cAAAK,MAAG,GAAG,mBAAmBL,IAAEE,EAAC,KAAG,GAAG,OAAOI,GAAE,IAAI,MAAIL;AAAA,YAAC;AAAC,gBAAGE,MAAGE,IAAE;AAAC,kBAAG,GAAG,eAAeL,EAAC,EAAE,QAAOA;AAAE,cAAAC,KAAE,CAACA;AAAA,YAAC;AAAC,oBAAOA,KAAE,GAAG,eAAe,GAAG,UAAUD,EAAC,GAAE,GAAG,cAAc,IAAE,GAAG,eAAe,GAAG,UAAUA,EAAC,GAAE,GAAG,cAAc,MAAIA;AAAA,UAAC,GAAEE,KAAED,GAAE,KAAK,YAAY,GAAE,KAAE,GAAEE,KAAE,KAAK,YAAY,IAAED,KAAED,GAAE,KAAK,cAAc,GAAE,IAAE;AAAE,iBAAO,IAAID,GAAEG,GAAE,MAAKA,GAAE,QAAOD,GAAE,MAAKA,GAAE,MAAM;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,SAASF,IAAEC,IAAE;AAAC,UAAAD,KAAEA,MAAG,EAAE;AAAG,cAAIE,KAAED,MAAGA,GAAE,iBAAgBE,KAAEF,MAAGA,GAAE,eAAcG,KAAE,KAAK,cAAc,GAAEC,KAAE,KAAK,YAAY,GAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,iBAAO,GAAG,UAAUH,IAAEC,IAAG,SAASJ,IAAE;AAAC,gBAAIG;AAAE,eAAG,WAAWH,GAAE,IAAI,MAAIE,MAAG,GAAG,gBAAgBF,EAAC,KAAGM,GAAE,KAAKN,GAAE,IAAI,GAAE,GAAG,iBAAiBA,EAAC,KAAG,EAAE,SAASM,IAAEN,GAAE,IAAI,MAAIG,KAAEH,GAAE,SAAOG,KAAEF,KAAE,GAAG,SAASD,GAAE,MAAKD,EAAC,IAAEC,GAAE,MAAKG,MAAGJ,GAAEI,EAAC,KAAGE,GAAE,KAAKF,EAAC;AAAA,UAAE,GAAG,IAAE,GAAE,EAAE,OAAOE,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,kBAAiB,OAAM,WAAU;AAAC,iBAAO,GAAG,eAAe,KAAK,IAAG,KAAK,EAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASL,IAAE;AAAC,cAAIC,KAAE,GAAG,SAAS,KAAK,IAAGD,EAAC,GAAEE,KAAE,GAAG,SAAS,KAAK,IAAGF,EAAC;AAAE,cAAG,CAACC,MAAG,CAACC,GAAE,QAAO,IAAIH,GAAE,KAAK,IAAG,KAAK,IAAG,KAAK,IAAG,KAAK,EAAE;AAAE,cAAII,KAAE,KAAK,UAAU;AAAE,iBAAOF,OAAIE,GAAE,KAAGF,IAAEE,GAAE,KAAG,IAAGD,OAAIC,GAAE,KAAGD,IAAEC,GAAE,KAAG,GAAG,WAAWD,EAAC,IAAG,IAAIH,GAAEI,GAAE,IAAGA,GAAE,IAAGA,GAAE,IAAGA,GAAE,EAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASH,IAAE;AAAC,iBAAOA,KAAE,IAAID,GAAE,KAAK,IAAG,KAAK,IAAG,KAAK,IAAG,KAAK,EAAE,IAAE,IAAIA,GAAE,KAAK,IAAG,KAAK,IAAG,KAAK,IAAG,KAAK,EAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,WAAU;AAAC,cAAIC,KAAE,KAAK,OAAK,KAAK,IAAGC,KAAE,KAAK,UAAU;AAAE,iBAAO,GAAG,OAAO,KAAK,EAAE,KAAG,CAAC,GAAG,YAAY,KAAK,YAAY,CAAC,KAAG,KAAK,GAAG,UAAU,KAAK,EAAE,GAAE,GAAG,OAAO,KAAK,EAAE,KAAG,CAAC,GAAG,YAAY,KAAK,cAAc,CAAC,MAAIA,GAAE,KAAG,KAAK,GAAG,UAAU,KAAK,EAAE,GAAEA,GAAE,KAAG,GAAED,OAAIC,GAAE,KAAGA,GAAE,IAAGA,GAAE,KAAG,KAAK,KAAG,KAAK,MAAK,IAAIF,GAAEE,GAAE,IAAGA,GAAE,IAAGA,GAAE,IAAGA,GAAE,EAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,kBAAiB,OAAM,WAAU;AAAC,cAAG,KAAK,YAAY,EAAE,QAAO;AAAK,cAAID,KAAE,KAAK,UAAU,GAAEC,KAAED,GAAE,MAAM,MAAK,EAAC,eAAc,KAAE,CAAC,GAAEE,KAAE,GAAG,eAAeF,GAAE,cAAc,GAAG,SAASD,IAAE;AAAC,mBAAM,CAAC,EAAE,SAASE,IAAEF,GAAE,IAAI;AAAA,UAAC,CAAE,GAAEI,KAAE,CAAC;AAAE,iBAAO,EAAE,EAAE,KAAKF,IAAG,SAASF,IAAEC,IAAE;AAAC,gBAAIC,KAAED,GAAE;AAAW,YAAAE,GAAE,SAAOD,MAAG,MAAI,GAAG,WAAWA,EAAC,KAAGE,GAAE,KAAKF,EAAC,GAAE,GAAG,OAAOD,IAAE,KAAE;AAAA,UAAC,CAAE,GAAE,EAAE,EAAE,KAAKG,IAAG,SAASJ,IAAEC,IAAE;AAAC,eAAG,OAAOA,IAAE,KAAE;AAAA,UAAC,CAAE,GAAE,IAAID,GAAEG,GAAE,MAAKA,GAAE,QAAOA,GAAE,MAAKA,GAAE,MAAM,EAAE,UAAU;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASH,IAAE;AAAC,iBAAO,WAAU;AAAC,gBAAIC,KAAE,GAAG,SAAS,KAAK,IAAGD,EAAC;AAAE,mBAAM,CAAC,CAACC,MAAGA,OAAI,GAAG,SAAS,KAAK,IAAGD,EAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASA,IAAE;AAAC,cAAG,CAAC,GAAG,gBAAgB,KAAK,cAAc,CAAC,EAAE,QAAM;AAAG,cAAIC,KAAE,GAAG,SAAS,KAAK,IAAGD,EAAC;AAAE,iBAAOC,MAAG,GAAG,aAAa,KAAK,IAAGA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,iBAAO,KAAK,OAAK,KAAK,MAAI,KAAK,OAAK,KAAK;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,0BAAyB,OAAM,WAAU;AAAC,cAAG,GAAG,gBAAgB,KAAK,EAAE,KAAG,GAAG,QAAQ,KAAK,EAAE,EAAE,QAAO,KAAK,GAAG,YAAU,GAAG,WAAU,IAAID,GAAE,KAAK,GAAG,YAAW,GAAE,KAAK,GAAG,YAAW,CAAC;AAAE,cAAIC,IAAEC,KAAE,KAAK,UAAU;AAAE,cAAG,GAAG,aAAa,KAAK,EAAE,KAAG,GAAG,OAAO,KAAK,EAAE,EAAE,QAAOA;AAAE,cAAG,GAAG,SAASA,GAAE,EAAE,GAAE;AAAC,gBAAIC,KAAE,GAAG,aAAaD,GAAE,IAAG,EAAE,IAAI,GAAG,QAAQ,CAAC;AAAE,YAAAD,KAAE,EAAE,KAAKE,EAAC,GAAE,GAAG,SAASF,EAAC,MAAIA,KAAEE,GAAEA,GAAE,SAAO,CAAC,KAAGD,GAAE,GAAG,WAAWA,GAAE,EAAE;AAAA,UAAE,MAAM,CAAAD,KAAEC,GAAE,GAAG,WAAWA,GAAE,KAAG,IAAEA,GAAE,KAAG,IAAE,CAAC;AAAE,cAAGD,IAAE;AAAC,gBAAIG,KAAE,GAAG,SAASH,IAAE,GAAG,YAAY,EAAE,QAAQ;AAAE,iBAAIG,KAAEA,GAAE,OAAO,GAAG,SAASH,GAAE,aAAY,GAAG,YAAY,CAAC,GAAG,QAAO;AAAC,kBAAII,KAAE,GAAG,KAAK,EAAE,KAAKD,EAAC,GAAE,GAAG;AAAE,iBAAG,iBAAiBC,IAAE,EAAE,KAAKD,EAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,iBAAO,KAAK,UAAU;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAASJ,IAAE;AAAC,cAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC,GAAEC,KAAE;AAAK,WAAC,GAAG,OAAOF,EAAC,KAAG,GAAG,SAASA,EAAC,OAAKE,KAAE,KAAK,uBAAuB,EAAE,eAAe;AAAG,cAAIC,KAAE,GAAG,WAAWD,GAAE,cAAc,GAAE,GAAG,SAASF,EAAC,CAAC;AAAE,iBAAOG,GAAE,aAAWA,GAAE,UAAU,WAAW,aAAaH,IAAEG,GAAE,SAAS,GAAE,GAAG,QAAQA,GAAE,SAAS,MAAIF,MAAG,GAAG,OAAOD,EAAC,MAAIG,GAAE,UAAU,WAAW,YAAYA,GAAE,SAAS,KAAGA,GAAE,UAAU,YAAYH,EAAC,GAAEA;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASA,IAAE;AAAC,UAAAA,OAAIA,MAAG,MAAI,IAAI,KAAKA,EAAC;AAAE,cAAIC,KAAE,EAAE,EAAE,aAAa,EAAE,KAAKD,EAAC,EAAE,CAAC,GAAEE,KAAE,EAAE,KAAKD,GAAE,UAAU,GAAEE,KAAE,MAAKC,KAAE;AAAG,iBAAOD,GAAE,MAAI,MAAID,KAAEA,GAAE,QAAQ,GAAEE,KAAE,OAAIF,KAAEA,GAAE,IAAK,SAASF,IAAE;AAAC,mBAAOG,GAAE,WAAWH,IAAE,CAAC,GAAG,SAASA,EAAC,CAAC;AAAA,UAAC,CAAE,GAAEI,OAAIF,KAAEA,GAAE,QAAQ,IAAGA;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,WAAU;AAAC,cAAIF,KAAE,KAAK,YAAY;AAAE,iBAAO,EAAE,oBAAkBA,GAAE,SAAS,IAAEA,GAAE;AAAA,QAAI,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASC,IAAE;AAAC,cAAIC,KAAE,KAAK,YAAY;AAAE,cAAG,CAAC,GAAG,YAAYA,EAAC,EAAE,QAAO;AAAK,cAAIC,KAAE,GAAG,eAAeD,IAAG,SAASF,IAAE;AAAC,mBAAM,CAAC,GAAG,YAAYA,EAAC;AAAA,UAAC,CAAE;AAAE,iBAAOC,OAAIC,KAAE,GAAG,eAAeA,IAAG,SAASF,IAAE;AAAC,mBAAM,CAAC,GAAG,YAAYA,EAAC;AAAA,UAAC,CAAE,IAAG,IAAIA,GAAEG,GAAE,MAAKA,GAAE,QAAOD,GAAE,MAAKA,GAAE,MAAM;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE,KAAK,YAAY,GAAEC,KAAE,SAASH,IAAE;AAAC,mBAAM,CAAC,GAAG,YAAYA,EAAC,KAAG,CAAC,GAAG,aAAaA,EAAC;AAAA,UAAC;AAAE,cAAGG,GAAED,EAAC,EAAE,QAAO;AAAK,cAAIE,KAAE,GAAG,eAAeF,IAAEC,EAAC;AAAE,iBAAOF,OAAIC,KAAE,GAAG,eAAeA,IAAEC,EAAC,IAAG,IAAIH,GAAEI,GAAE,MAAKA,GAAE,QAAOF,GAAE,MAAKA,GAAE,MAAM;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,sBAAqB,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE,KAAK,YAAY,GAAEC,KAAE,GAAG,eAAeD,IAAG,SAASC,IAAE;AAAC,gBAAG,CAAC,GAAG,YAAYA,EAAC,KAAG,CAAC,GAAG,aAAaA,EAAC,EAAE,QAAM;AAAG,gBAAIC,KAAE,IAAIJ,GAAEG,GAAE,MAAKA,GAAE,QAAOD,GAAE,MAAKA,GAAE,MAAM,GAAEG,KAAEJ,GAAE,KAAKG,GAAE,SAAS,CAAC;AAAE,mBAAOC,MAAG,MAAIA,GAAE;AAAA,UAAK,CAAE,GAAED,KAAE,IAAIJ,GAAEG,GAAE,MAAKA,GAAE,QAAOD,GAAE,MAAKA,GAAE,MAAM,GAAEG,KAAED,GAAE,SAAS,GAAEE,KAAEL,GAAE,KAAKI,EAAC;AAAE,iBAAOC,MAAGA,GAAE,CAAC,EAAE,WAASD,GAAE,SAAOD,KAAE;AAAA,QAAI,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASJ,IAAE;AAAC,iBAAM,EAAC,GAAE,EAAC,MAAK,GAAG,eAAeA,IAAE,KAAK,EAAE,GAAE,QAAO,KAAK,GAAE,GAAE,GAAE,EAAC,MAAK,GAAG,eAAeA,IAAE,KAAK,EAAE,GAAE,QAAO,KAAK,GAAE,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASA,IAAE;AAAC,iBAAM,EAAC,GAAE,EAAC,MAAK,EAAE,KAAK,GAAG,eAAe,EAAE,KAAKA,EAAC,GAAE,KAAK,EAAE,CAAC,GAAE,QAAO,KAAK,GAAE,GAAE,GAAE,EAAC,MAAK,EAAE,KAAK,GAAG,eAAe,EAAE,KAAKA,EAAC,GAAE,KAAK,EAAE,CAAC,GAAE,QAAO,KAAK,GAAE,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,kBAAiB,OAAM,WAAU;AAAC,iBAAO,KAAK,YAAY,EAAE,eAAe;AAAA,QAAC,EAAC,CAAC,GAAEE,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,YAAM,KAAG,EAAC,QAAO,SAASH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAG,MAAI,UAAU,OAAO,QAAO,IAAI,GAAGH,IAAEC,IAAEC,IAAEC,EAAC;AAAE,YAAG,MAAI,UAAU,OAAO,QAAO,IAAI,GAAGH,IAAEC,IAAEC,KAAEF,IAAEG,KAAEF,EAAC;AAAE,YAAIG,KAAE,KAAK,oBAAoB;AAAE,YAAG,CAACA,MAAG,MAAI,UAAU,QAAO;AAAC,cAAIC,KAAE,UAAU,CAAC;AAAE,iBAAO,GAAG,WAAWA,EAAC,MAAIA,KAAEA,GAAE,YAAW,KAAK,sBAAsBA,IAAE,GAAG,cAAY,UAAU,CAAC,EAAE,SAAS;AAAA,QAAC;AAAC,eAAOD;AAAA,MAAC,GAAE,uBAAsB,SAASJ,IAAE;AAAC,YAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,eAAO,KAAK,eAAeD,EAAC,EAAE,SAASC,EAAC;AAAA,MAAC,GAAE,qBAAoB,WAAU;AAAC,YAAID,IAAEC,IAAEC,IAAEC;AAAE,YAAG,EAAE,mBAAkB;AAAC,cAAIC,KAAE,SAAS,aAAa;AAAE,cAAG,CAACA,MAAG,MAAIA,GAAE,WAAW,QAAO;AAAK,cAAG,GAAG,OAAOA,GAAE,UAAU,EAAE,QAAO;AAAK,cAAIC,KAAED,GAAE,WAAW,CAAC;AAAE,UAAAJ,KAAEK,GAAE,gBAAeJ,KAAEI,GAAE,aAAYH,KAAEG,GAAE,cAAaF,KAAEE,GAAE;AAAA,QAAS,OAAK;AAAC,cAAIC,KAAE,SAAS,UAAU,YAAY,GAAEC,KAAED,GAAE,UAAU;AAAE,UAAAC,GAAE,SAAS,KAAE;AAAE,cAAIC,KAAEF;AAAE,UAAAE,GAAE,SAAS,IAAE;AAAE,cAAIC,KAAE,GAAGD,IAAE,IAAE,GAAEE,KAAE,GAAGH,IAAE,KAAE;AAAE,aAAG,OAAOE,GAAE,IAAI,KAAG,GAAG,gBAAgBA,EAAC,KAAG,GAAG,WAAWC,GAAE,IAAI,KAAG,GAAG,iBAAiBA,EAAC,KAAGA,GAAE,KAAK,gBAAcD,GAAE,SAAOA,KAAEC,KAAGV,KAAES,GAAE,MAAKR,KAAEQ,GAAE,QAAOP,KAAEQ,GAAE,MAAKP,KAAEO,GAAE;AAAA,QAAM;AAAC,eAAO,IAAI,GAAGV,IAAEC,IAAEC,IAAEC,EAAC;AAAA,MAAC,GAAE,gBAAe,SAASH,IAAE;AAAC,YAAIC,KAAED,IAAEE,KAAE,GAAEC,KAAEH,IAAEI,KAAE,GAAG,WAAWD,EAAC;AAAE,eAAO,GAAG,OAAOF,EAAC,MAAIC,KAAE,GAAG,SAASD,EAAC,EAAE,SAAO,GAAEA,KAAEA,GAAE,aAAY,GAAG,KAAKE,EAAC,KAAGC,KAAE,GAAG,SAASD,EAAC,EAAE,SAAO,GAAEA,KAAEA,GAAE,cAAY,GAAG,OAAOA,EAAC,MAAIC,KAAE,GAAG,SAASD,EAAC,EAAE,QAAOA,KAAEA,GAAE,aAAY,KAAK,OAAOF,IAAEC,IAAEC,IAAEC,EAAC;AAAA,MAAC,GAAE,sBAAqB,SAASJ,IAAE;AAAC,eAAO,KAAK,eAAeA,EAAC,EAAE,SAAS,IAAE;AAAA,MAAC,GAAE,qBAAoB,SAASA,IAAE;AAAC,eAAO,KAAK,eAAeA,EAAC,EAAE,SAAS;AAAA,MAAC,GAAE,oBAAmB,SAASA,IAAEC,IAAE;AAAC,YAAIC,KAAE,GAAG,eAAeF,IAAEC,GAAE,EAAE,IAAI,GAAEE,KAAEF,GAAE,EAAE,QAAOG,KAAE,GAAG,eAAeJ,IAAEC,GAAE,EAAE,IAAI,GAAEI,KAAEJ,GAAE,EAAE;AAAO,eAAO,IAAI,GAAGC,IAAEC,IAAEC,IAAEC,EAAC;AAAA,MAAC,GAAE,wBAAuB,SAASL,IAAEC,IAAE;AAAC,YAAIC,KAAEF,GAAE,EAAE,QAAOG,KAAEH,GAAE,EAAE,QAAOI,KAAE,GAAG,eAAe,EAAE,KAAKH,EAAC,GAAED,GAAE,EAAE,IAAI,GAAEK,KAAE,GAAG,eAAe,EAAE,KAAKJ,EAAC,GAAED,GAAE,EAAE,IAAI;AAAE,eAAO,IAAI,GAAGI,IAAEF,IAAEG,IAAEF,EAAC;AAAA,MAAC,EAAC;AAAE,UAAI,KAAG,EAAC,WAAU,GAAE,KAAI,GAAE,OAAM,IAAG,QAAO,IAAG,OAAM,IAAG,QAAO,IAAG,MAAK,IAAG,IAAG,IAAG,OAAM,IAAG,MAAK,IAAG,MAAK,IAAG,MAAK,IAAG,MAAK,IAAG,MAAK,IAAG,MAAK,IAAG,MAAK,IAAG,MAAK,IAAG,MAAK,IAAG,MAAK,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,OAAM,KAAI,aAAY,KAAI,WAAU,KAAI,cAAa,KAAI,MAAK,IAAG,KAAI,IAAG,QAAO,IAAG,UAAS,GAAE;AAAE,YAAM,KAAG,EAAC,QAAO,SAASH,IAAE;AAAC,eAAO,EAAE,SAAS,CAAC,GAAG,WAAU,GAAG,KAAI,GAAG,OAAM,GAAG,OAAM,GAAG,MAAM,GAAEA,EAAC;AAAA,MAAC,GAAE,UAAS,SAASA,IAAE;AAAC,eAAO,EAAE,SAAS,CAAC,GAAG,WAAU,GAAG,MAAM,GAAEA,EAAC;AAAA,MAAC,GAAE,QAAO,SAASA,IAAE;AAAC,eAAO,EAAE,SAAS,CAAC,GAAG,MAAK,GAAG,IAAG,GAAG,OAAM,GAAG,IAAI,GAAEA,EAAC;AAAA,MAAC,GAAE,cAAa,SAASA,IAAE;AAAC,eAAO,EAAE,SAAS,CAAC,GAAG,MAAK,GAAG,KAAI,GAAG,QAAO,GAAG,QAAQ,GAAEA,EAAC;AAAA,MAAC,GAAE,cAAa,EAAE,aAAa,EAAE,GAAE,MAAK,GAAE;AAAE,eAAS,GAAGA,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,QAAM,CAAC,GAAE,KAAK,cAAY,IAAG,KAAK,UAAQC,IAAE,KAAK,YAAUA,GAAE,WAAW,UAAS,KAAK,WAAS,KAAK,UAAU,CAAC;AAAA,QAAC,IAAGA,KAAE,CAAC,EAAC,KAAI,gBAAe,OAAM,WAAU;AAAC,cAAID,KAAE,GAAG,OAAO,KAAK,QAAQ;AAAE,iBAAM,EAAC,UAAS,KAAK,UAAU,KAAK,GAAE,UAASA,MAAGA,GAAE,aAAa,IAAEA,GAAE,SAAS,KAAK,QAAQ,IAAE,EAAC,GAAE,EAAC,MAAK,CAAC,GAAE,QAAO,EAAC,GAAE,GAAE,EAAC,MAAK,CAAC,GAAE,QAAO,EAAC,EAAC,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASA,IAAE;AAAC,mBAAOA,GAAE,YAAU,KAAK,UAAU,KAAKA,GAAE,QAAQ,GAAE,SAAOA,GAAE,YAAU,GAAG,mBAAmB,KAAK,UAASA,GAAE,QAAQ,EAAE,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,eAAK,UAAU,KAAK,MAAI,KAAK,MAAM,KAAK,WAAW,EAAE,YAAU,KAAK,WAAW,GAAE,KAAK,cAAY,GAAE,KAAK,cAAc,KAAK,MAAM,KAAK,WAAW,CAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,eAAK,QAAM,CAAC,GAAE,KAAK,cAAY,IAAG,KAAK,WAAW;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,WAAU;AAAC,eAAK,QAAM,CAAC,GAAE,KAAK,cAAY,IAAG,KAAK,UAAU,KAAK,EAAE,GAAE,KAAK,WAAW;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,UAAU,KAAK,MAAI,KAAK,MAAM,KAAK,WAAW,EAAE,YAAU,KAAK,WAAW,GAAE,KAAK,cAAY,MAAI,KAAK,eAAc,KAAK,cAAc,KAAK,MAAM,KAAK,WAAW,CAAC;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,MAAM,SAAO,IAAE,KAAK,gBAAc,KAAK,eAAc,KAAK,cAAc,KAAK,MAAM,KAAK,WAAW,CAAC;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,eAAK,eAAc,KAAK,MAAM,SAAO,KAAK,gBAAc,KAAK,QAAM,KAAK,MAAM,MAAM,GAAE,KAAK,WAAW,IAAG,KAAK,MAAM,KAAK,KAAK,aAAa,CAAC,GAAE,KAAK,MAAM,SAAO,KAAK,QAAQ,QAAQ,iBAAe,KAAK,MAAM,MAAM,GAAE,KAAK,eAAa;AAAA,QAAE,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,KAAG;AAAC,WAAC,SAASA,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC;AAAA,QAAC,GAAEC,KAAE,CAAC,EAAC,KAAI,aAAY,OAAM,SAASD,IAAEC,IAAE;AAAC,cAAIC,KAAE,CAAC;AAAE,iBAAO,EAAE,EAAE,KAAKD,IAAG,SAASA,IAAEE,IAAE;AAAC,YAAAD,GAAEC,EAAC,IAAEH,GAAE,IAAIG,EAAC;AAAA,UAAC,CAAE,GAAED;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASF,IAAE;AAAC,cAAIC,KAAE,KAAK,UAAUD,IAAE,CAAC,eAAc,aAAY,cAAa,mBAAkB,aAAa,CAAC,KAAG,CAAC,GAAEE,KAAEF,GAAE,CAAC,EAAE,MAAM,YAAUC,GAAE,WAAW;AAAE,iBAAOA,GAAE,WAAW,IAAE,SAASC,IAAE,EAAE,GAAED,GAAE,gBAAgB,IAAEC,GAAE,MAAM,UAAU,GAAED;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASD,IAAEC,IAAE;AAAC,YAAE,EAAE,KAAKD,GAAE,MAAM,GAAG,QAAO,EAAC,iBAAgB,KAAE,CAAC,GAAG,SAASA,IAAEE,IAAE;AAAC,cAAE,EAAEA,EAAC,EAAE,IAAID,EAAC;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAASD,IAAEC,IAAE;AAAC,UAAAD,KAAEA,GAAE,UAAU;AAAE,cAAIE,KAAED,MAAGA,GAAE,YAAU,QAAOE,KAAE,EAAE,CAACF,MAAG,CAACA,GAAE,uBAAsBG,KAAE,EAAE,CAACH,MAAG,CAACA,GAAE;AAAqB,cAAGD,GAAE,YAAY,EAAE,QAAM,CAACA,GAAE,WAAW,GAAG,OAAOE,EAAC,CAAC,CAAC;AAAE,cAAII,KAAE,GAAG,mBAAmBJ,EAAC,GAAEK,KAAEP,GAAE,MAAM,GAAG,QAAO,EAAC,eAAc,KAAE,CAAC,EAAE,IAAK,SAASA,IAAE;AAAC,mBAAO,GAAG,oBAAoBA,IAAEM,EAAC,KAAG,GAAG,KAAKN,IAAEE,EAAC;AAAA,UAAC,CAAE;AAAE,cAAGC,IAAE;AAAC,gBAAGC,IAAE;AAAC,kBAAII,KAAER,GAAE,MAAM;AAAE,cAAAM,KAAE,EAAE,IAAIA,IAAG,SAASN,IAAE;AAAC,uBAAO,EAAE,SAASQ,IAAER,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAC,mBAAOO,GAAE,IAAK,SAASP,IAAE;AAAC,kBAAIC,KAAE,GAAG,oBAAoBD,IAAEM,EAAC,GAAEJ,KAAE,EAAE,KAAKD,EAAC,GAAEE,KAAE,EAAE,KAAKF,EAAC;AAAE,qBAAO,EAAE,EAAE,KAAKE,IAAG,SAASH,IAAEC,IAAE;AAAC,mBAAG,iBAAiBC,IAAED,GAAE,UAAU,GAAE,GAAG,OAAOA,EAAC;AAAA,cAAC,CAAE,GAAE,EAAE,KAAKA,EAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAC,iBAAOM;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,SAASP,IAAE;AAAC,cAAIC,KAAE,EAAE,EAAE,GAAG,UAAUD,GAAE,EAAE,IAAEA,GAAE,KAAGA,GAAE,GAAG,UAAU,GAAEE,KAAE,KAAK,SAASD,EAAC;AAAE,cAAG;AAAC,YAAAC,KAAE,EAAE,EAAE,OAAOA,IAAE,EAAC,aAAY,SAAS,kBAAkB,MAAM,IAAE,SAAO,UAAS,eAAc,SAAS,kBAAkB,QAAQ,IAAE,WAAS,UAAS,kBAAiB,SAAS,kBAAkB,WAAW,IAAE,cAAY,UAAS,kBAAiB,SAAS,kBAAkB,WAAW,IAAE,cAAY,UAAS,oBAAmB,SAAS,kBAAkB,aAAa,IAAE,gBAAc,UAAS,sBAAqB,SAAS,kBAAkB,eAAe,IAAE,kBAAgB,UAAS,eAAc,SAAS,kBAAkB,UAAU,KAAGA,GAAE,aAAa,EAAC,CAAC;AAAA,UAAC,SAAOF,IAAE;AAAA,UAAC;AAAC,cAAGA,GAAE,SAAS,GAAE;AAAC,gBAAIG,KAAE,CAAC,UAAS,QAAO,qBAAoB,QAAQ,EAAE,QAAQD,GAAE,iBAAiB,CAAC,IAAE;AAAG,YAAAA,GAAE,YAAY,IAAEC,KAAE,cAAY;AAAA,UAAS,MAAM,CAAAD,GAAE,YAAY,IAAE;AAAO,cAAIE,KAAE,GAAG,SAASJ,GAAE,IAAG,GAAG,MAAM;AAAE,cAAGI,MAAGA,GAAE,MAAM,aAAa,EAAE,CAAAF,GAAE,aAAa,IAAEE,GAAE,MAAM;AAAA,eAAe;AAAC,gBAAIE,KAAE,SAASJ,GAAE,aAAa,GAAE,EAAE,IAAE,SAASA,GAAE,WAAW,GAAE,EAAE;AAAE,YAAAA,GAAE,aAAa,IAAEI,GAAE,QAAQ,CAAC;AAAA,UAAC;AAAC,iBAAOJ,GAAE,SAAOF,GAAE,WAAW,KAAG,GAAG,SAASA,GAAE,IAAG,GAAG,QAAQ,GAAEE,GAAE,YAAU,GAAG,aAAaF,GAAE,IAAG,GAAG,UAAU,GAAEE,GAAE,QAAMF,IAAEE;AAAA,QAAC,EAAC,CAAC,GAAED,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,KAAG;AAAC,WAAC,SAASA,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC;AAAA,QAAC,GAAEC,KAAE,CAAC,EAAC,KAAI,qBAAoB,OAAM,SAASD,IAAE;AAAC,eAAK,WAAW,MAAKA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,uBAAsB,OAAM,SAASA,IAAE;AAAC,eAAK,WAAW,MAAKA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAE,MAAKC,KAAE,GAAG,OAAOF,EAAC,EAAE,uBAAuB,GAAEG,KAAED,GAAE,MAAM,GAAG,QAAO,EAAC,iBAAgB,KAAE,CAAC,GAAEE,KAAE,EAAE,UAAUD,IAAE,EAAE,KAAK,YAAY,CAAC;AAAE,YAAE,EAAE,KAAKC,IAAG,SAASJ,IAAEE,IAAE;AAAC,gBAAIC,KAAE,EAAE,KAAKD,EAAC;AAAE,gBAAG,GAAG,KAAKC,EAAC,GAAE;AAAC,kBAAIC,KAAEH,GAAE,SAASE,GAAE,eAAe;AAAE,cAAAC,KAAEF,GAAE,IAAK,SAASF,IAAE;AAAC,uBAAOI,GAAE,YAAYJ,EAAC;AAAA,cAAC,CAAE,KAAGC,GAAE,SAASC,IAAEC,GAAE,WAAW,QAAQ,GAAED,GAAE,IAAK,SAASF,IAAE;AAAC,uBAAOA,GAAE;AAAA,cAAU,CAAE,EAAE,IAAK,SAASA,IAAE;AAAC,uBAAOC,GAAE,iBAAiBD,EAAC;AAAA,cAAC,CAAE;AAAA,YAAE,MAAM,GAAE,EAAE,KAAKE,IAAG,SAASF,IAAEC,IAAE;AAAC,gBAAE,EAAEA,EAAC,EAAE,IAAI,cAAc,SAASD,IAAEC,IAAE;AAAC,wBAAO,SAASA,IAAE,EAAE,KAAG,KAAG;AAAA,cAAE,CAAE;AAAA,YAAC,CAAE;AAAA,UAAC,CAAE,GAAEC,GAAE,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,SAASF,IAAE;AAAC,cAAIC,KAAE,MAAKC,KAAE,GAAG,OAAOF,EAAC,EAAE,uBAAuB,GAAEG,KAAED,GAAE,MAAM,GAAG,QAAO,EAAC,iBAAgB,KAAE,CAAC,GAAEE,KAAE,EAAE,UAAUD,IAAE,EAAE,KAAK,YAAY,CAAC;AAAE,YAAE,EAAE,KAAKC,IAAG,SAASJ,IAAEE,IAAE;AAAC,gBAAIC,KAAE,EAAE,KAAKD,EAAC;AAAE,eAAG,KAAKC,EAAC,IAAEF,GAAE,YAAY,CAACC,EAAC,CAAC,IAAE,EAAE,EAAE,KAAKA,IAAG,SAASF,IAAEC,IAAE;AAAC,gBAAE,EAAEA,EAAC,EAAE,IAAI,cAAc,SAASD,IAAEC,IAAE;AAAC,wBAAOA,KAAE,SAASA,IAAE,EAAE,KAAG,KAAG,KAAGA,KAAE,KAAG;AAAA,cAAE,CAAE;AAAA,YAAC,CAAE;AAAA,UAAC,CAAE,GAAEC,GAAE,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAASF,IAAEC,IAAE;AAAC,cAAIC,KAAE,MAAKC,KAAE,GAAG,OAAOF,EAAC,EAAE,uBAAuB,GAAEG,KAAED,GAAE,MAAM,GAAG,QAAO,EAAC,iBAAgB,KAAE,CAAC,GAAEG,KAAEH,GAAE,aAAaC,EAAC,GAAEG,KAAE,EAAE,UAAUH,IAAE,EAAE,KAAK,YAAY,CAAC;AAAE,cAAG,EAAE,KAAKA,IAAE,GAAG,UAAU,GAAE;AAAC,gBAAII,KAAE,CAAC;AAAE,cAAE,EAAE,KAAKD,IAAG,SAASN,IAAEE,IAAE;AAAC,cAAAK,KAAEA,GAAE,OAAON,GAAE,SAASC,IAAEH,EAAC,CAAC;AAAA,YAAC,CAAE,GAAEI,KAAEI;AAAA,UAAC,OAAK;AAAC,gBAAIC,KAAEN,GAAE,MAAM,GAAG,QAAO,EAAC,iBAAgB,KAAE,CAAC,EAAE,OAAQ,SAASF,IAAE;AAAC,qBAAM,CAAC,EAAE,EAAE,SAASA,IAAED,EAAC;AAAA,YAAC,CAAE;AAAE,YAAAS,GAAE,SAAO,EAAE,EAAE,KAAKA,IAAG,SAASR,IAAEC,IAAE;AAAC,iBAAG,QAAQA,IAAEF,EAAC;AAAA,YAAC,CAAE,IAAEI,KAAE,KAAK,YAAYG,IAAE,IAAE;AAAA,UAAC;AAAC,aAAG,uBAAuBD,IAAEF,EAAC,EAAE,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASJ,IAAEC,IAAE;AAAC,cAAIC,KAAE,EAAE,KAAKF,EAAC,GAAEG,KAAE,EAAE,KAAKH,EAAC,GAAEI,KAAE,GAAG,OAAOF,GAAE,eAAe,KAAGA,GAAE,iBAAgBG,KAAE,GAAG,OAAOF,GAAE,WAAW,KAAGA,GAAE,aAAYG,KAAEF,MAAG,GAAG,YAAY,GAAG,OAAOH,MAAG,IAAI,GAAEE,EAAC;AAAE,iBAAOH,KAAEA,GAAE,IAAK,SAASA,IAAE;AAAC,mBAAO,GAAG,WAAWA,EAAC,IAAE,GAAG,QAAQA,IAAE,IAAI,IAAEA;AAAA,UAAC,CAAE,GAAE,GAAG,iBAAiBM,IAAEN,IAAE,IAAE,GAAEK,OAAI,GAAG,iBAAiBC,IAAE,EAAE,KAAKD,GAAE,UAAU,GAAE,IAAE,GAAE,GAAG,OAAOA,EAAC,IAAGL;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASA,IAAEC,IAAE;AAAC,cAAIC,KAAE,MAAKC,KAAE,CAAC;AAAE,iBAAO,EAAE,EAAE,KAAKH,IAAG,SAASA,IAAEI,IAAE;AAAC,gBAAIE,KAAE,EAAE,KAAKF,EAAC,GAAEG,KAAE,EAAE,KAAKH,EAAC,GAAEI,KAAEP,KAAE,GAAG,aAAaK,IAAE,GAAG,MAAM,IAAEA,GAAE,YAAWG,KAAED,GAAE;AAAW,gBAAG,SAAOA,GAAE,WAAW,SAAS,CAAAJ,GAAE,IAAK,SAASJ,IAAE;AAAC,kBAAIC,KAAEC,GAAE,iBAAiBF,EAAC;AAAE,cAAAS,GAAE,cAAYA,GAAE,WAAW,aAAaT,IAAES,GAAE,WAAW,IAAEA,GAAE,WAAW,YAAYT,EAAC,GAAEC,GAAE,WAASC,GAAE,SAASD,IAAEO,GAAE,QAAQ,GAAER,GAAE,YAAYC,GAAE,CAAC,EAAE,UAAU;AAAA,YAAE,CAAE,GAAE,MAAIO,GAAE,SAAS,UAAQC,GAAE,YAAYD,EAAC,GAAE,MAAIC,GAAE,WAAW,UAAQA,GAAE,WAAW,YAAYA,EAAC;AAAA,iBAAM;AAAC,kBAAIC,KAAEF,GAAE,WAAW,SAAO,IAAE,GAAG,UAAUA,IAAE,EAAC,MAAKD,GAAE,YAAW,QAAO,GAAG,SAASA,EAAC,IAAE,EAAC,GAAE,EAAC,wBAAuB,KAAE,CAAC,IAAE,MAAKI,KAAE,GAAG,UAAUH,IAAE,EAAC,MAAKF,GAAE,YAAW,QAAO,GAAG,SAASA,EAAC,EAAC,GAAE,EAAC,wBAAuB,KAAE,CAAC;AAAE,cAAAF,KAAEH,KAAE,GAAG,eAAeU,IAAE,GAAG,IAAI,IAAE,EAAE,KAAKA,GAAE,UAAU,EAAE,OAAO,GAAG,IAAI,GAAE,CAACV,MAAG,GAAG,OAAOO,GAAE,UAAU,MAAIJ,KAAEA,GAAE,IAAK,SAASJ,IAAE;AAAC,uBAAO,GAAG,QAAQA,IAAE,GAAG;AAAA,cAAC,CAAE,IAAG,EAAE,EAAE,KAAK,EAAE,KAAKI,EAAC,EAAE,QAAQ,GAAG,SAASJ,IAAEC,IAAE;AAAC,mBAAG,YAAYA,IAAEO,EAAC;AAAA,cAAC,CAAE;AAAE,kBAAII,KAAE,EAAE,QAAQ,CAACJ,IAAEG,IAAED,EAAC,CAAC;AAAE,gBAAE,EAAE,KAAKE,IAAG,SAASZ,IAAEC,IAAE;AAAC,oBAAIC,KAAE,CAACD,EAAC,EAAE,OAAO,GAAG,eAAeA,IAAE,GAAG,MAAM,CAAC;AAAE,kBAAE,EAAE,KAAKC,GAAE,QAAQ,GAAG,SAASF,IAAEC,IAAE;AAAC,qBAAG,WAAWA,EAAC,KAAG,GAAG,OAAOA,IAAE,IAAE;AAAA,gBAAC,CAAE;AAAA,cAAC,CAAE;AAAA,YAAC;AAAC,YAAAE,KAAEA,GAAE,OAAOC,EAAC;AAAA,UAAC,CAAE,GAAED;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,SAASH,IAAE;AAAC,iBAAOA,GAAE,kBAAgB,GAAG,iBAAiBA,GAAE,iBAAgB,CAACA,EAAC,CAAC,IAAE,KAAK,SAAS,CAACA,EAAC,GAAE,IAAI;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASA,IAAE;AAAC,iBAAOA,KAAE,EAAE,KAAKA,GAAE,UAAU,SAASA,IAAE;AAAC,mBAAM,CAAC,MAAK,IAAI,EAAE,QAAQA,GAAE,QAAQ,IAAE;AAAA,UAAE,CAAE,IAAE;AAAA,QAAI,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,SAASA,IAAE;AAAC,mBAAQC,KAAE,CAAC,GAAED,GAAE,cAAa,CAAAC,GAAE,KAAKD,GAAE,WAAW,GAAEA,KAAEA,GAAE;AAAY,iBAAOC;AAAA,QAAC,EAAC,CAAC,GAAEA,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,SAAO,IAAI,MAAG,KAAK,UAAQC,GAAE;AAAA,QAAO,GAAEA,KAAE,CAAC,EAAC,KAAI,aAAY,OAAM,SAASD,IAAEC,IAAE;AAAC,cAAIC,KAAE,GAAG,WAAW,IAAI,MAAMD,KAAE,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC;AAAE,WAACD,KAAEA,GAAE,eAAe,GAAG,WAAWE,IAAE,IAAE,IAAGF,KAAE,GAAG,OAAOE,IAAED,EAAC,GAAG,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,mBAAkB,OAAM,SAASD,IAAEC,IAAE;AAAC,UAAAA,MAAGA,MAAGA,KAAEA,MAAG,GAAG,OAAOD,EAAC,GAAG,eAAe,GAAG,uBAAuB;AAAE,cAAIE,IAAEC,KAAE,GAAG,SAASF,GAAE,IAAG,GAAG,MAAM;AAAE,cAAGE,IAAE;AAAC,gBAAG,GAAG,KAAKA,EAAC,MAAI,GAAG,QAAQA,EAAC,KAAG,GAAG,oBAAoBA,EAAC,GAAG,QAAO,KAAK,KAAK,OAAO,WAAWA,GAAE,WAAW,QAAQ;AAAE,gBAAIC,KAAE;AAAK,gBAAG,MAAI,KAAK,QAAQ,0BAAwBA,KAAE,GAAG,SAASD,IAAE,GAAG,YAAY,IAAE,MAAI,KAAK,QAAQ,4BAA0BC,KAAE,GAAG,aAAaD,IAAE,GAAG,YAAY,IAAGC,IAAE;AAAC,cAAAF,KAAE,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC,GAAE,GAAG,iBAAiBD,GAAE,cAAc,CAAC,KAAG,GAAG,KAAKA,GAAE,GAAG,WAAW,KAAG,EAAE,EAAEA,GAAE,GAAG,WAAW,EAAE,OAAO;AAAE,kBAAIK,KAAE,GAAG,UAAUF,IAAEH,GAAE,cAAc,GAAE,EAAC,sBAAqB,KAAE,CAAC;AAAE,cAAAK,KAAEA,GAAE,WAAW,aAAaJ,IAAEI,EAAC,IAAE,GAAG,YAAYJ,IAAEE,EAAC;AAAA,YAAC,OAAK;AAAC,cAAAF,KAAE,GAAG,UAAUC,IAAEF,GAAE,cAAc,CAAC;AAAE,kBAAIM,KAAE,GAAG,eAAeJ,IAAE,GAAG,aAAa;AAAE,cAAAI,KAAEA,GAAE,OAAO,GAAG,eAAeL,IAAE,GAAG,aAAa,CAAC,GAAE,EAAE,EAAE,KAAKK,IAAG,SAASP,IAAEC,IAAE;AAAC,mBAAG,OAAOA,EAAC;AAAA,cAAC,CAAE,IAAG,GAAG,UAAUC,EAAC,KAAG,GAAG,MAAMA,EAAC,KAAG,GAAG,iBAAiBA,EAAC,MAAI,GAAG,QAAQA,EAAC,MAAIA,KAAE,GAAG,QAAQA,IAAE,GAAG;AAAA,YAAE;AAAA,UAAC,OAAK;AAAC,gBAAIM,KAAEP,GAAE,GAAG,WAAWA,GAAE,EAAE;AAAE,YAAAC,KAAE,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC,GAAEM,KAAEP,GAAE,GAAG,aAAaC,IAAEM,EAAC,IAAEP,GAAE,GAAG,YAAYC,EAAC;AAAA,UAAC;AAAC,aAAG,OAAOA,IAAE,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,eAAeF,EAAC;AAAA,QAAC,EAAC,CAAC,GAAEC,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,SAASD,GAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAIC,KAAE,EAAC,QAAO,GAAE,QAAO,EAAC,GAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,iBAASC,GAAER,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEE,IAAE;AAAC,cAAIC,KAAE,EAAC,SAAQN,IAAE,UAASC,IAAE,WAAUC,IAAE,WAAUC,IAAE,WAAUE,GAAC;AAAE,UAAAD,GAAEN,EAAC,MAAIM,GAAEN,EAAC,IAAE,CAAC,IAAGM,GAAEN,EAAC,EAAEC,EAAC,IAAEO;AAAA,QAAC;AAAC,iBAASC,GAAET,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAM,EAAC,UAASH,GAAE,UAAS,QAAOC,IAAE,cAAa,EAAC,UAASC,IAAE,WAAUC,GAAC,EAAC;AAAA,QAAC;AAAC,iBAASO,GAAEV,IAAEC,IAAE;AAAC,cAAG,CAACK,GAAEN,EAAC,EAAE,QAAOC;AAAE,cAAG,CAACK,GAAEN,EAAC,EAAEC,EAAC,EAAE,QAAOA;AAAE,mBAAQC,KAAED,IAAEK,GAAEN,EAAC,EAAEE,EAAC,IAAG,KAAGA,MAAI,CAACI,GAAEN,EAAC,EAAEE,EAAC,EAAE,QAAOA;AAAA,QAAC;AAAC,iBAASS,GAAEX,IAAEC,IAAE;AAAC,cAAIC,KAAEQ,GAAEV,GAAE,UAASC,GAAE,SAAS,GAAEE,KAAEF,GAAE,UAAQ,GAAEG,KAAEH,GAAE,UAAQ,GAAEK,KAAEN,GAAE,aAAWK,GAAE,UAAQJ,GAAE,cAAYI,GAAE;AAAO,UAAAG,GAAER,GAAE,UAASE,IAAEF,IAAEC,IAAEG,IAAED,IAAE,KAAE;AAAE,cAAII,KAAEN,GAAE,WAAW,UAAQ,SAASA,GAAE,WAAW,QAAQ,OAAM,EAAE,IAAE;AAAE,cAAGM,KAAE,EAAE,UAAQE,KAAE,GAAEA,KAAEF,IAAEE,MAAI;AAAC,gBAAIE,KAAEX,GAAE,WAASS;AAAE,YAAAG,GAAED,IAAET,IAAED,IAAEK,EAAC,GAAEE,GAAEG,IAAET,IAAEF,IAAEC,IAAE,MAAGE,IAAE,IAAE;AAAA,UAAC;AAAC,cAAIU,KAAEZ,GAAE,WAAW,UAAQ,SAASA,GAAE,WAAW,QAAQ,OAAM,EAAE,IAAE;AAAE,cAAGY,KAAE,EAAE,UAAQC,KAAE,GAAEA,KAAED,IAAEC,MAAI;AAAC,gBAAIC,KAAEL,GAAEV,GAAE,UAASE,KAAEY,EAAC;AAAE,YAAAF,GAAEZ,GAAE,UAASe,IAAEd,IAAEK,EAAC,GAAEE,GAAER,GAAE,UAASe,IAAEf,IAAEC,IAAEG,IAAE,MAAG,IAAE;AAAA,UAAC;AAAA,QAAC;AAAC,iBAASQ,GAAEZ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAAH,OAAIK,GAAE,UAAQA,GAAE,UAAQH,GAAE,aAAWA,GAAE,aAAWD,MAAG,CAACE,MAAGE,GAAE;AAAA,QAAQ;AAAC,iBAASQ,GAAEZ,IAAE;AAAC,kBAAOC,IAAE;AAAA,YAAC,KAAKF,GAAE,MAAM;AAAO,kBAAGC,GAAE,UAAU,QAAOD,GAAE,aAAa;AAAkB;AAAA,YAAM,KAAKA,GAAE,MAAM;AAAI,kBAAG,CAACC,GAAE,aAAWA,GAAE,UAAU,QAAOD,GAAE,aAAa;AAAQ,kBAAGC,GAAE,UAAU,QAAOD,GAAE,aAAa;AAAA,UAAiB;AAAC,iBAAOA,GAAE,aAAa;AAAA,QAAU;AAAC,iBAASc,GAAEb,IAAE;AAAC,kBAAOC,IAAE;AAAA,YAAC,KAAKF,GAAE,MAAM;AAAO,kBAAGC,GAAE,UAAU,QAAOD,GAAE,aAAa;AAAa,kBAAGC,GAAE,aAAWA,GAAE,UAAU,QAAOD,GAAE,aAAa;AAAO;AAAA,YAAM,KAAKA,GAAE,MAAM;AAAI,kBAAGC,GAAE,UAAU,QAAOD,GAAE,aAAa;AAAa,kBAAGC,GAAE,aAAWA,GAAE,UAAU,QAAOD,GAAE,aAAa;AAAA,UAAM;AAAC,iBAAOA,GAAE,aAAa;AAAA,QAAO;AAAC,aAAK,gBAAc,WAAU;AAAC,mBAAQC,KAAEC,OAAIF,GAAE,MAAM,MAAIK,GAAE,SAAO,IAAGD,KAAEF,OAAIF,GAAE,MAAM,SAAOK,GAAE,SAAO,IAAGG,KAAE,GAAEE,KAAE,MAAGA,MAAG;AAAC,gBAAIC,KAAEV,MAAG,IAAEA,KAAEO,IAAEI,KAAER,MAAG,IAAEA,KAAEI,IAAEO,KAAET,GAAEK,EAAC;AAAE,gBAAG,CAACI,GAAE,QAAOL,KAAE,OAAGH;AAAE,gBAAIS,KAAED,GAAEH,EAAC;AAAE,gBAAG,CAACI,GAAE,QAAON,KAAE,OAAGH;AAAE,gBAAIU,KAAEjB,GAAE,aAAa;AAAO,oBAAOG,IAAE;AAAA,cAAC,KAAKH,GAAE,cAAc;AAAI,gBAAAiB,KAAEH,GAAEE,EAAC;AAAE;AAAA,cAAM,KAAKhB,GAAE,cAAc;AAAO,gBAAAiB,KAAEJ,GAAEG,EAAC;AAAA,YAAC;AAAC,YAAAT,GAAE,KAAKE,GAAEO,IAAEC,IAAEN,IAAEC,EAAC,CAAC,GAAEJ;AAAA,UAAG;AAAC,iBAAOD;AAAA,QAAC,GAAEN,MAAGA,GAAE,YAAU,SAAOA,GAAE,QAAQ,YAAY,KAAG,SAAOA,GAAE,QAAQ,YAAY,OAAKI,GAAE,SAAOJ,GAAE,WAAUA,GAAE,iBAAeA,GAAE,cAAc,WAAS,SAAOA,GAAE,cAAc,QAAQ,YAAY,MAAII,GAAE,SAAOJ,GAAE,cAAc,YAAW,WAAU;AAAC,mBAAQD,KAAEI,GAAE,MAAKH,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,UAAQC,KAAEF,GAAEC,EAAC,EAAE,OAAME,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAQ,GAAEX,GAAEC,EAAC,GAAEC,GAAEC,EAAC,CAAC;AAAA,QAAC,EAAE;AAAA,MAAC;AAAE,SAAG,QAAM,EAAC,KAAI,GAAE,QAAO,EAAC,GAAE,GAAG,gBAAc,EAAC,KAAI,GAAE,QAAO,EAAC,GAAE,GAAG,eAAa,EAAC,QAAO,GAAE,mBAAkB,GAAE,YAAW,GAAE,SAAQ,GAAE,cAAa,EAAC;AAAE,UAAI,KAAG,WAAU;AAAC,eAAOH,KAAE,SAASA,KAAG;AAAC,WAAC,SAASA,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC;AAAA,QAAC,GAAEC,KAAE,CAAC,EAAC,KAAI,OAAM,OAAM,SAASD,IAAEC,IAAE;AAAC,cAAIC,KAAE,GAAG,SAASF,GAAE,eAAe,GAAE,GAAG,MAAM,GAAEG,KAAE,GAAG,SAASD,IAAE,GAAG,OAAO,GAAEE,KAAE,GAAG,eAAeD,IAAE,GAAG,MAAM,GAAEE,KAAE,EAAEJ,KAAE,SAAO,MAAM,EAAEG,IAAEF,EAAC;AAAE,UAAAG,MAAG,GAAG,OAAOA,IAAE,CAAC,EAAE,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASL,IAAEC,IAAE;AAAC,mBAAQC,KAAE,GAAG,SAASF,GAAE,eAAe,GAAE,GAAG,MAAM,GAAEG,KAAE,EAAE,EAAED,EAAC,EAAE,QAAQ,IAAI,GAAEE,KAAE,KAAK,kBAAkBD,EAAC,GAAEG,KAAE,EAAE,EAAE,QAAMF,KAAE,QAAQ,GAAEG,KAAE,IAAI,GAAGL,IAAE,GAAG,MAAM,KAAI,GAAG,cAAc,KAAI,EAAE,EAAEC,EAAC,EAAE,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,cAAc,GAAEK,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,gBAAIC,KAAEF,GAAEC,EAAC,GAAEE,KAAE,KAAK,kBAAkBD,GAAE,QAAQ;AAAE,oBAAOA,GAAE,QAAO;AAAA,cAAC,KAAK,GAAG,aAAa;AAAQ,gBAAAH,GAAE,OAAO,QAAMI,KAAE,MAAI,GAAG,QAAM,OAAO;AAAE;AAAA,cAAM,KAAK,GAAG,aAAa;AAAa,oBAAG,UAAQT,OAAIQ,GAAE,SAAS,SAAOA,GAAE,SAAS,QAAQ,IAAI,EAAE,WAAS,MAAIN,GAAE,CAAC,EAAE,UAAS;AAAC,sBAAIQ,KAAE,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE,QAAMD,KAAE,MAAI,GAAG,QAAM,OAAO,EAAE,WAAW,SAAS,CAAC,EAAE,KAAK;AAAE,kBAAAJ,GAAE,OAAOK,EAAC;AAAE;AAAA,gBAAK;AAAC,oBAAIC,KAAE,SAASH,GAAE,SAAS,SAAQ,EAAE;AAAE,gBAAAG,MAAIH,GAAE,SAAS,aAAa,WAAUG,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,cAAG,UAAQX,GAAE,CAAAE,GAAE,OAAOG,EAAC;AAAA,eAAM;AAAC,gBAAGJ,GAAE,UAAQ,GAAE;AAAC,kBAAIW,KAAEV,GAAE,CAAC,EAAE,YAAUD,GAAE,UAAQ;AAAG,qBAAO,KAAK,EAAE,EAAE,EAAE,EAAEC,EAAC,EAAE,OAAO,EAAE,KAAK,IAAI,EAAEU,EAAC,CAAC,EAAE,MAAM,EAAE,EAAEP,EAAC,CAAC;AAAA,YAAC;AAAC,YAAAH,GAAE,MAAMG,EAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASN,IAAEC,IAAE;AAAC,cAAIC,KAAE,GAAG,SAASF,GAAE,eAAe,GAAE,GAAG,MAAM,GAAEG,KAAE,EAAE,EAAED,EAAC,EAAE,QAAQ,IAAI;AAAE,YAAE,EAAEC,EAAC,EAAE,SAAS,EAAE,KAAKA,EAAC;AAAE,mBAAQC,KAAE,IAAI,GAAGF,IAAE,GAAG,MAAM,QAAO,GAAG,cAAc,KAAI,EAAE,EAAEC,EAAC,EAAE,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,cAAc,GAAEG,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,gBAAIC,KAAEH,GAAEE,EAAC,GAAEE,KAAE,KAAK,kBAAkBD,GAAE,QAAQ;AAAE,oBAAOA,GAAE,QAAO;AAAA,cAAC,KAAK,GAAG,aAAa;AAAQ,4BAAUN,KAAE,EAAE,EAAEM,GAAE,QAAQ,EAAE,MAAM,QAAMC,KAAE,MAAI,GAAG,QAAM,OAAO,IAAE,EAAE,EAAED,GAAE,QAAQ,EAAE,OAAO,QAAMC,KAAE,MAAI,GAAG,QAAM,OAAO;AAAE;AAAA,cAAM,KAAK,GAAG,aAAa;AAAa,oBAAG,YAAUP,IAAE;AAAC,sBAAIQ,KAAE,SAASF,GAAE,SAAS,SAAQ,EAAE;AAAE,kBAAAE,MAAIF,GAAE,SAAS,aAAa,WAAUE,EAAC;AAAA,gBAAC,MAAM,GAAE,EAAEF,GAAE,QAAQ,EAAE,OAAO,QAAMC,KAAE,MAAI,GAAG,QAAM,OAAO;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,qBAAoB,OAAM,SAASR,IAAE;AAAC,cAAIC,KAAE;AAAG,cAAG,CAACD,GAAE,QAAOC;AAAE,mBAAQC,KAAEF,GAAE,cAAY,CAAC,GAAEG,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,UAAOD,GAAEC,EAAC,EAAE,KAAK,YAAY,KAAGD,GAAEC,EAAC,EAAE,cAAYF,MAAG,MAAIC,GAAEC,EAAC,EAAE,OAAK,OAAKD,GAAEC,EAAC,EAAE,QAAM;AAAK,iBAAOF;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASD,IAAE;AAAC,mBAAQC,KAAE,GAAG,SAASD,GAAE,eAAe,GAAE,GAAG,MAAM,GAAEE,KAAE,EAAE,EAAED,EAAC,EAAE,QAAQ,IAAI,GAAEE,KAAED,GAAE,SAAS,QAAQ,EAAE,MAAM,EAAE,EAAED,EAAC,CAAC,GAAEG,KAAEF,GAAE,CAAC,EAAE,UAASI,KAAE,IAAI,GAAGL,IAAE,GAAG,MAAM,KAAI,GAAG,cAAc,QAAO,EAAE,EAAEC,EAAC,EAAE,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,cAAc,GAAEK,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,KAAGD,GAAEC,EAAC,GAAE;AAAC,gBAAIC,KAAEF,GAAEC,EAAC,EAAE,UAASE,KAAEH,GAAEC,EAAC,EAAE,cAAaG,KAAEF,GAAE,WAASA,GAAE,UAAQ,GAAEG,KAAED,KAAE,SAASF,GAAE,SAAQ,EAAE,IAAE;AAAE,oBAAOF,GAAEC,EAAC,EAAE,QAAO;AAAA,cAAC,KAAK,GAAG,aAAa;AAAO;AAAA,cAAS,KAAK,GAAG,aAAa;AAAQ,oBAAIK,KAAEV,GAAE,KAAK,IAAI,EAAE,CAAC;AAAE,oBAAG,CAACU,GAAE;AAAS,oBAAIC,KAAEX,GAAE,CAAC,EAAE,MAAMC,EAAC;AAAE,gBAAAO,OAAIC,KAAE,KAAGA,MAAIC,GAAE,aAAaC,IAAED,GAAE,MAAMT,EAAC,CAAC,GAAES,GAAE,MAAMT,EAAC,EAAE,aAAa,WAAUQ,EAAC,GAAEC,GAAE,MAAMT,EAAC,EAAE,YAAU,MAAI,MAAIQ,OAAIC,GAAE,aAAaC,IAAED,GAAE,MAAMT,EAAC,CAAC,GAAES,GAAE,MAAMT,EAAC,EAAE,gBAAgB,SAAS,GAAES,GAAE,MAAMT,EAAC,EAAE,YAAU;AAAK;AAAA,cAAS,KAAK,GAAG,aAAa;AAAkB,gBAAAO,OAAIC,KAAE,KAAGA,MAAIH,GAAE,aAAa,WAAUG,EAAC,GAAEF,GAAE,aAAWL,MAAGI,GAAE,cAAYL,OAAIK,GAAE,YAAU,OAAK,MAAIG,OAAIH,GAAE,gBAAgB,SAAS,GAAEC,GAAE,aAAWL,MAAGI,GAAE,cAAYL,OAAIK,GAAE,YAAU;AAAM;AAAA,cAAS,KAAK,GAAG,aAAa;AAAW;AAAA,YAAQ;AAAA,UAAC;AAAC,UAAAN,GAAE,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASF,IAAE;AAAC,mBAAQC,KAAE,GAAG,SAASD,GAAE,eAAe,GAAE,GAAG,MAAM,GAAEE,KAAE,EAAE,EAAED,EAAC,EAAE,QAAQ,IAAI,GAAEE,KAAED,GAAE,SAAS,QAAQ,EAAE,MAAM,EAAE,EAAED,EAAC,CAAC,GAAEG,KAAE,IAAI,GAAGH,IAAE,GAAG,MAAM,QAAO,GAAG,cAAc,QAAO,EAAE,EAAEC,EAAC,EAAE,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,cAAc,GAAEI,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,KAAGF,GAAEE,EAAC,EAAE,SAAOF,GAAEE,EAAC,EAAE,QAAO;AAAA,YAAC,KAAK,GAAG,aAAa;AAAO;AAAA,YAAS,KAAK,GAAG,aAAa;AAAkB,kBAAIC,KAAEH,GAAEE,EAAC,EAAE;AAAS,kBAAGC,GAAE,WAASA,GAAE,UAAQ,GAAE;AAAC,oBAAIC,KAAED,GAAE,UAAQ,SAASA,GAAE,SAAQ,EAAE,IAAE;AAAE,gBAAAC,KAAE,KAAGA,MAAID,GAAE,aAAa,WAAUC,EAAC,GAAED,GAAE,cAAYJ,OAAII,GAAE,YAAU,OAAK,MAAIC,OAAID,GAAE,gBAAgB,SAAS,GAAEA,GAAE,cAAYJ,OAAII,GAAE,YAAU;AAAA,cAAI;AAAC;AAAA,YAAS,KAAK,GAAG,aAAa;AAAW,iBAAG,OAAOH,GAAEE,EAAC,EAAE,UAAS,IAAE;AAAE;AAAA,UAAQ;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASN,IAAEC,IAAEC,IAAE;AAAC,mBAAQC,IAAEC,KAAE,CAAC,GAAEE,KAAE,GAAEA,KAAEN,IAAEM,KAAI,CAAAF,GAAE,KAAK,SAAO,GAAG,QAAM,OAAO;AAAE,UAAAD,KAAEC,GAAE,KAAK,EAAE;AAAE,mBAAQG,IAAEC,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAER,IAAEQ,KAAI,CAAAD,GAAE,KAAK,SAAOL,KAAE,OAAO;AAAE,UAAAI,KAAEC,GAAE,KAAK,EAAE;AAAE,cAAIE,KAAE,EAAE,EAAE,YAAUH,KAAE,UAAU;AAAE,iBAAOL,MAAGA,GAAE,kBAAgBQ,GAAE,SAASR,GAAE,cAAc,GAAEQ,GAAE,CAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASV,IAAE;AAAC,cAAIC,KAAE,GAAG,SAASD,GAAE,eAAe,GAAE,GAAG,MAAM;AAAE,YAAE,EAAEC,EAAC,EAAE,QAAQ,OAAO,EAAE,OAAO;AAAA,QAAC,EAAC,CAAC,GAAEA,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,oDAAmD,KAAG,gEAA+D,KAAG,qCAAoC,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,QAAMA,GAAE,WAAW,MAAK,KAAK,UAAQA,GAAE,WAAW,QAAO,KAAK,YAAUA,GAAE,WAAW,UAAS,KAAK,UAAQA,GAAE,SAAQ,KAAK,OAAK,KAAK,QAAQ,UAAS,KAAK,WAAS,KAAK,UAAU,CAAC,GAAE,KAAK,YAAU,MAAK,KAAK,WAAS,MAAK,KAAK,QAAM,IAAI,MAAG,KAAK,QAAM,IAAI,MAAG,KAAK,SAAO,IAAI,GAAGA,EAAC,GAAE,KAAK,SAAO,IAAI,MAAG,KAAK,UAAQ,IAAI,GAAGA,EAAC,GAAE,KAAK,QAAQ,KAAK,eAAc,KAAK,KAAK,KAAK,MAAM,GAAE,KAAK,QAAQ,KAAK,aAAY,KAAK,KAAK,KAAK,IAAI,GAAE,KAAK,QAAQ,KAAK,aAAY,KAAK,KAAK,KAAK,IAAI,GAAE,KAAK,QAAQ,KAAK,YAAW,KAAK,KAAK,KAAK,GAAG,GAAE,KAAK,QAAQ,KAAK,cAAa,KAAK,KAAK,KAAK,KAAK,GAAE,KAAK,QAAQ,KAAK,wBAAuB,KAAK,KAAK,KAAK,eAAe,GAAE,KAAK,QAAQ,KAAK,0BAAyB,KAAK,KAAK,KAAK,iBAAiB,GAAE,KAAK,QAAQ,KAAK,4BAA2B,KAAK,KAAK,KAAK,mBAAmB,GAAE,KAAK,QAAQ,KAAK,eAAc,KAAK,KAAK,KAAK,MAAM,GAAE,KAAK,QAAQ,KAAK,gBAAe,KAAK,KAAK,KAAK,OAAO,GAAE,KAAK,QAAQ,KAAK,mBAAkB,KAAK,KAAK,KAAK,UAAU,GAAE,KAAK,QAAQ,KAAK,6BAA4B,KAAK,KAAK,KAAK,oBAAoB,GAAE,KAAK,QAAQ,KAAK,iBAAgB,KAAK,KAAK,KAAK,QAAQ;AAAE,mBAAQE,KAAE,CAAC,QAAO,UAAS,aAAY,iBAAgB,eAAc,aAAY,eAAc,iBAAgB,gBAAe,eAAc,eAAc,gBAAe,WAAW,GAAEC,KAAE,GAAEE,KAAEH,GAAE,QAAOC,KAAEE,IAAEF,KAAI,MAAKD,GAAEC,EAAC,CAAC,IAAE,yBAASJ,IAAE;AAAC,mBAAO,SAASC,IAAE;AAAC,cAAAC,GAAE,cAAc,GAAE,SAAS,YAAYF,IAAE,OAAGC,EAAC,GAAEC,GAAE,aAAa,IAAE;AAAA,YAAC;AAAA,UAAC,EAAEC,GAAEC,EAAC,CAAC,GAAE,KAAK,QAAQ,KAAK,UAAQD,GAAEC,EAAC,GAAE,KAAK,KAAK,KAAKD,GAAEC,EAAC,CAAC,CAAC;AAAE,eAAK,WAAS,KAAK,YAAa,SAASJ,IAAE;AAAC,mBAAOE,GAAE,YAAY,eAAc,EAAE,cAAcF,EAAC,CAAC;AAAA,UAAC,CAAE,GAAE,KAAK,WAAS,KAAK,YAAa,SAASA,IAAE;AAAC,gBAAIC,KAAEC,GAAE,aAAa,EAAE,gBAAgB;AAAE,mBAAOA,GAAE,YAAY,aAAYF,KAAEC,EAAC;AAAA,UAAC,CAAE,GAAE,KAAK,eAAa,KAAK,YAAa,SAASD,IAAE;AAAC,gBAAIC,KAAEC,GAAE,aAAa,EAAE,WAAW;AAAE,mBAAOA,GAAE,YAAY,aAAYD,KAAED,EAAC;AAAA,UAAC,CAAE;AAAE,mBAAQO,KAAE,GAAEA,MAAG,GAAEA,KAAI,MAAK,YAAUA,EAAC,IAAE,yBAASP,IAAE;AAAC,mBAAO,WAAU;AAAC,cAAAE,GAAE,YAAY,MAAIF,EAAC;AAAA,YAAC;AAAA,UAAC,EAAEO,EAAC,GAAE,KAAK,QAAQ,KAAK,iBAAeA,IAAE,KAAK,KAAK,KAAK,YAAUA,EAAC,CAAC;AAAE,eAAK,kBAAgB,KAAK,YAAa,WAAU;AAAC,YAAAL,GAAE,OAAO,gBAAgBA,GAAE,QAAQ;AAAA,UAAC,CAAE,GAAE,KAAK,oBAAkB,KAAK,YAAa,WAAU;AAAC,YAAAA,GAAE,OAAO,kBAAkBA,GAAE,QAAQ;AAAA,UAAC,CAAE,GAAE,KAAK,sBAAoB,KAAK,YAAa,WAAU;AAAC,YAAAA,GAAE,OAAO,oBAAoBA,GAAE,QAAQ;AAAA,UAAC,CAAE,GAAE,KAAK,SAAO,KAAK,YAAa,WAAU;AAAC,YAAAA,GAAE,OAAO,OAAOA,GAAE,QAAQ;AAAA,UAAC,CAAE,GAAE,KAAK,UAAQ,KAAK,YAAa,WAAU;AAAC,YAAAA,GAAE,OAAO,QAAQA,GAAE,QAAQ;AAAA,UAAC,CAAE,GAAE,KAAK,aAAW,KAAK,YAAa,SAASF,IAAE;AAAC,YAAAE,GAAE,UAAU,EAAE,EAAEF,EAAC,EAAE,KAAK,EAAE,MAAM,MAAIE,GAAE,aAAa,EAAE,WAAWF,EAAC,GAAEE,GAAE,aAAa,GAAG,oBAAoBF,EAAC,EAAE,OAAO,CAAC;AAAA,UAAE,CAAE,GAAE,KAAK,aAAW,KAAK,YAAa,SAASA,IAAE;AAAC,gBAAG,CAACE,GAAE,UAAUF,GAAE,MAAM,GAAE;AAAC,kBAAIC,KAAEC,GAAE,aAAa,EAAE,WAAW,GAAG,WAAWF,EAAC,CAAC;AAAE,cAAAE,GAAE,aAAa,GAAG,OAAOD,IAAE,GAAG,WAAWA,EAAC,CAAC,EAAE,OAAO,CAAC;AAAA,YAAC;AAAA,UAAC,CAAE,GAAE,KAAK,YAAU,KAAK,YAAa,SAASD,IAAE;AAAC,gBAAG,CAACE,GAAE,UAAUF,GAAE,MAAM,GAAE;AAAC,cAAAA,KAAEE,GAAE,QAAQ,OAAO,mBAAkBF,EAAC;AAAE,kBAAIC,KAAEC,GAAE,aAAa,EAAE,UAAUF,EAAC;AAAE,cAAAE,GAAE,aAAa,GAAG,oBAAoB,EAAE,KAAKD,EAAC,CAAC,EAAE,OAAO,CAAC;AAAA,YAAC;AAAA,UAAC,CAAE,GAAE,KAAK,cAAY,KAAK,YAAa,SAASD,IAAEC,IAAE;AAAC,gBAAIE,KAAED,GAAE,QAAQ,UAAU;AAAmB,YAAAC,KAAEA,GAAE,KAAKD,IAAED,IAAEC,GAAE,SAAQA,GAAE,aAAa,IAAEA,GAAE,cAAcF,IAAEC,EAAC;AAAA,UAAC,CAAE,GAAE,KAAK,uBAAqB,KAAK,YAAa,WAAU;AAAC,gBAAID,KAAEE,GAAE,aAAa,EAAE,WAAW,GAAG,OAAO,IAAI,CAAC;AAAE,YAAAF,GAAE,eAAaE,GAAE,aAAa,GAAG,OAAOF,GAAE,aAAY,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC;AAAA,UAAC,CAAE,GAAE,KAAK,aAAW,KAAK,YAAa,SAASA,IAAE;AAAC,YAAAE,GAAE,MAAM,UAAUA,GAAE,aAAa,GAAE,EAAC,YAAWF,GAAC,CAAC;AAAA,UAAC,CAAE,GAAE,KAAK,aAAW,KAAK,YAAa,SAASA,IAAE;AAAC,gBAAIC,KAAE,CAAC,GAAEE,KAAEH,GAAE,KAAII,KAAEJ,GAAE,MAAKM,KAAEN,GAAE,aAAYO,KAAEL,GAAE,QAAQ,mBAAkBM,KAAEN,GAAE,QAAQ,iBAAgBO,KAAET,GAAE,SAAOE,GAAE,aAAa,GAAEQ,KAAEN,GAAE,SAAOK,GAAE,SAAS,EAAE;AAAO,gBAAG,EAAEC,KAAE,KAAGR,GAAE,UAAUQ,EAAC,IAAG;AAAC,kBAAIC,KAAEF,GAAE,SAAS,MAAIL;AAAE,0BAAU,OAAOD,OAAIA,KAAEA,GAAE,KAAK,IAAGA,KAAED,GAAE,QAAQ,eAAaA,GAAE,QAAQ,aAAaC,EAAC,IAAED,GAAE,aAAaC,EAAC;AAAE,kBAAIS,KAAE,CAAC;AAAE,kBAAGD,IAAE;AAAC,oBAAIE,MAAGJ,KAAEA,GAAE,eAAe,GAAG,WAAW,EAAE,EAAE,SAAS,EAAE,KAAKL,EAAC,EAAE,CAAC,CAAC;AAAE,gBAAAQ,GAAE,KAAKC,EAAC;AAAA,cAAC,MAAM,CAAAD,KAAEV,GAAE,MAAM,WAAWO,IAAE,EAAC,UAAS,KAAI,sBAAqB,MAAG,qBAAoB,KAAE,CAAC;AAAE,gBAAE,EAAE,KAAKG,IAAG,SAASZ,IAAEE,IAAE;AAAC,kBAAE,EAAEA,EAAC,EAAE,KAAK,QAAOC,EAAC,GAAEG,MAAG,EAAE,EAAEJ,EAAC,EAAE,KAAK,UAAS,QAAQ,GAAEK,MAAGN,GAAE,KAAK,YAAY,GAAEO,MAAGP,GAAE,KAAK,UAAU,GAAEA,GAAE,UAAQ,EAAE,EAAEC,EAAC,EAAE,KAAK,OAAMD,GAAE,KAAK,GAAG,CAAC,KAAG,EAAE,EAAEC,EAAC,EAAE,WAAW,QAAQ;AAAA,cAAC,CAAE,GAAEA,GAAE,aAAaA,GAAE,oBAAoBU,EAAC,EAAE,OAAO,CAAC;AAAA,YAAC;AAAA,UAAC,CAAE,GAAE,KAAK,QAAM,KAAK,YAAa,SAASZ,IAAE;AAAC,gBAAIC,KAAED,GAAE,WAAUE,KAAEF,GAAE;AAAU,YAAAC,MAAG,SAAS,YAAY,aAAY,OAAGA,EAAC,GAAEC,MAAG,SAAS,YAAY,aAAY,OAAGA,EAAC;AAAA,UAAC,CAAE,GAAE,KAAK,YAAU,KAAK,YAAa,SAASF,IAAE;AAAC,qBAAS,YAAY,aAAY,OAAGA,EAAC;AAAA,UAAC,CAAE,GAAE,KAAK,cAAY,KAAK,YAAa,SAASA,IAAE;AAAC,gBAAIC,KAAED,GAAE,MAAM,GAAG;AAAE,YAAAE,GAAE,aAAa,EAAE,eAAe,EAAE,WAAWA,GAAE,MAAM,YAAYD,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,GAAE,OAAO,CAAC;AAAA,UAAC,CAAE,GAAE,KAAK,cAAY,KAAK,YAAa,WAAU;AAAC,gBAAIF,KAAE,EAAE,EAAEE,GAAE,cAAc,CAAC,EAAE,OAAO;AAAE,YAAAF,GAAE,QAAQ,QAAQ,EAAE,SAAOA,GAAE,QAAQ,QAAQ,EAAE,OAAO,IAAEA,KAAE,EAAE,EAAEE,GAAE,cAAc,CAAC,EAAE,OAAO,GAAEA,GAAE,aAAa,GAAG,oBAAoBF,EAAC,EAAE,OAAO,CAAC,GAAEE,GAAE,QAAQ,aAAa,gBAAeF,IAAEE,GAAE,SAAS;AAAA,UAAC,CAAE,GAAE,KAAK,UAAQ,KAAK,YAAa,SAASF,IAAE;AAAC,gBAAIC,KAAE,EAAE,EAAEC,GAAE,cAAc,CAAC;AAAE,YAAAD,GAAE,YAAY,mBAAkB,WAASD,EAAC,GAAEC,GAAE,YAAY,oBAAmB,YAAUD,EAAC,GAAEC,GAAE,IAAI,SAAQ,WAASD,KAAE,KAAGA,EAAC;AAAA,UAAC,CAAE,GAAE,KAAK,SAAO,KAAK,YAAa,SAASA,IAAE;AAAC,gBAAIC,KAAE,EAAE,EAAEC,GAAE,cAAc,CAAC;AAAE,mBAAKF,KAAE,WAAWA,EAAC,KAAGC,GAAE,IAAI,SAAQ,EAAE,IAAEA,GAAE,IAAI,EAAC,OAAM,MAAID,KAAE,KAAI,QAAO,GAAE,CAAC;AAAA,UAAC,CAAE;AAAA,QAAC,GAAEC,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,UAAU,GAAG,WAAW,SAASC,IAAE;AAAC,gBAAGA,GAAE,YAAU,GAAG,KAAK,SAAOD,GAAE,QAAQ,aAAa,SAAQC,EAAC,GAAED,GAAE,QAAQ,aAAa,WAAUC,EAAC,GAAED,GAAE,WAASA,GAAE,QAAQ,aAAa,GAAEA,GAAE,iBAAe,OAAGC,GAAE,mBAAmB,MAAID,GAAE,QAAQ,YAAUA,GAAE,iBAAeA,GAAE,aAAaC,EAAC,IAAED,GAAE,gCAAgCC,EAAC,IAAGD,GAAE,UAAU,GAAEC,EAAC,GAAE;AAAC,kBAAIC,KAAEF,GAAE,aAAa;AAAE,kBAAGE,GAAE,KAAGA,GAAE,MAAI,EAAE,QAAM;AAAA,YAAE;AAAC,YAAAF,GAAE,aAAa,GAAEA,GAAE,QAAQ,wBAAsB,UAAKA,GAAE,kBAAgBA,GAAE,QAAQ,WAAW;AAAA,UAAC,CAAE,EAAE,GAAG,SAAS,SAASC,IAAE;AAAC,YAAAD,GAAE,aAAa,GAAEA,GAAE,QAAQ,aAAa,SAAQC,EAAC;AAAA,UAAC,CAAE,EAAE,GAAG,SAAS,SAASA,IAAE;AAAC,YAAAD,GAAE,aAAa,GAAEA,GAAE,QAAQ,aAAa,SAAQC,EAAC;AAAA,UAAC,CAAE,EAAE,GAAG,QAAQ,SAASA,IAAE;AAAC,YAAAD,GAAE,QAAQ,aAAa,QAAOC,EAAC;AAAA,UAAC,CAAE,EAAE,GAAG,aAAa,SAASA,IAAE;AAAC,YAAAD,GAAE,QAAQ,aAAa,aAAYC,EAAC;AAAA,UAAC,CAAE,EAAE,GAAG,WAAW,SAASA,IAAE;AAAC,YAAAD,GAAE,aAAa,GAAEA,GAAE,QAAQ,WAAW,GAAEA,GAAE,QAAQ,aAAa,WAAUC,EAAC;AAAA,UAAC,CAAE,EAAE,GAAG,UAAU,SAASA,IAAE;AAAC,YAAAD,GAAE,QAAQ,aAAa,UAASC,EAAC;AAAA,UAAC,CAAE,EAAE,GAAG,SAAS,SAASA,IAAE;AAAC,YAAAD,GAAE,aAAa,GAAEA,GAAE,QAAQ,aAAa,SAAQC,EAAC;AAAA,UAAC,CAAE,EAAE,GAAG,QAAQ,SAASA,IAAE;AAAC,YAAAD,GAAE,QAAQ,aAAa,QAAOC,EAAC;AAAA,UAAC,CAAE,EAAE,GAAG,SAAS,WAAU;AAAC,YAAAD,GAAE,UAAU,CAAC,KAAGA,GAAE,YAAUA,GAAE,QAAQ,cAAcA,GAAE,QAAQ;AAAA,UAAC,CAAE,GAAE,KAAK,UAAU,KAAK,cAAa,KAAK,QAAQ,UAAU,GAAE,KAAK,UAAU,KAAK,eAAc,KAAK,QAAQ,UAAU,GAAE,KAAK,QAAQ,kBAAgB,KAAK,UAAU,KAAK,cAAa,KAAE,GAAE,KAAK,UAAU,KAAK,GAAG,KAAK,KAAK,KAAK,KAAG,GAAG,SAAS,GAAE,KAAK,UAAU,GAAG,EAAE,gBAAe,EAAE,SAAU,WAAU;AAAC,YAAAA,GAAE,QAAQ,aAAa,UAASA,GAAE,UAAU,KAAK,GAAEA,GAAE,SAAS;AAAA,UAAC,GAAG,EAAE,CAAC,GAAE,KAAK,UAAU,GAAG,WAAW,SAASC,IAAE;AAAC,YAAAD,GAAE,QAAQ,aAAa,WAAUC,EAAC;AAAA,UAAC,CAAE,EAAE,GAAG,YAAY,SAASA,IAAE;AAAC,YAAAD,GAAE,QAAQ,aAAa,YAAWC,EAAC;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,UAAQ,KAAK,QAAQ,uBAAqB,KAAK,QAAQ,GAAG,eAAe,SAASA,IAAE;AAAC,mBAAOD,GAAE,QAAQ,aAAa,eAAcC,EAAC,GAAE;AAAA,UAAE,CAAE,KAAG,KAAK,QAAQ,SAAO,KAAK,QAAQ,WAAW,KAAK,QAAQ,KAAK,GAAE,KAAK,QAAQ,UAAQ,KAAK,UAAU,YAAY,KAAK,QAAQ,MAAM,GAAE,KAAK,QAAQ,aAAW,KAAK,UAAU,IAAI,cAAa,KAAK,QAAQ,SAAS,GAAE,KAAK,QAAQ,aAAW,KAAK,UAAU,IAAI,cAAa,KAAK,QAAQ,SAAS,IAAG,KAAK,QAAQ,WAAW,GAAE,KAAK,aAAa;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,UAAU,IAAI;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE,KAAK,QAAQ,OAAO,EAAE,QAAM,QAAM,IAAI,GAAEC,KAAE,CAAC;AAAE,UAAAF,GAAE,WAASE,GAAE,KAAK,KAAK,GAAEF,GAAE,WAAS,CAACA,GAAE,UAAQE,GAAE,KAAK,MAAM,GAAEF,GAAE,YAAUE,GAAE,KAAK,OAAO;AAAE,cAAIC,KAAE,GAAG,aAAaH,GAAE,OAAO;AAAE,UAAAG,MAAGD,GAAE,KAAKC,EAAC;AAAE,cAAIC,KAAEH,GAAEC,GAAE,KAAK,GAAG,CAAC;AAAE,cAAG,UAAQC,MAAG,KAAK,QAAQ,WAAW,KAAGC,IAAE;AAAC,gBAAG,UAAK,KAAK,QAAQ,OAAOA,EAAC,EAAE,QAAOJ,GAAE,eAAe,GAAE;AAAA,UAAE,MAAM,IAAG,OAAOA,GAAE,OAAO,MAAI,GAAG,SAASA,GAAE,OAAO,KAAG,KAAK,QAAQ,OAAO,SAAS,GAAE,KAAK,aAAa;AAAA,cAAQ,MAAK,aAAa;AAAE,iBAAM;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,mCAAkC,OAAM,SAASA,IAAE;AAAC,WAACA,GAAE,WAASA,GAAE,YAAU,EAAE,SAAS,CAAC,IAAG,IAAG,EAAE,GAAEA,GAAE,OAAO,KAAGA,GAAE,eAAe;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASA,IAAEC,IAAE;AAAC,iBAAOD,KAAEA,MAAG,IAAG,WAASC,MAAG,EAAE,GAAG,OAAOA,GAAE,OAAO,KAAG,GAAG,aAAaA,GAAE,OAAO,KAAGA,GAAE,WAASA,GAAE,WAAS,EAAE,SAAS,CAAC,GAAG,KAAK,WAAU,GAAG,KAAK,MAAM,GAAEA,GAAE,OAAO,OAAK,KAAK,QAAQ,gBAAc,KAAG,KAAK,UAAU,KAAK,EAAE,SAAOD,KAAE,KAAK,QAAQ;AAAA,QAAa,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASA,IAAE;AAAC,iBAAO,GAAG,KAAKA,EAAC,IAAE,cAAYA,KAAE,GAAG,KAAKA,EAAC,IAAE,WAASA,KAAE,GAAG,KAAKA,EAAC,IAAEA,KAAE,YAAUA;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,iBAAO,KAAK,MAAM,GAAE,KAAK,aAAa,GAAE,KAAK,aAAa;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,uBAAsB,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAE,GAAG,qBAAqB,EAAE,KAAKD,EAAC,CAAC,EAAE,cAAc,GAAEE,KAAE,GAAG,oBAAoB,EAAE,KAAKF,EAAC,CAAC,EAAE,YAAY;AAAE,iBAAO,GAAG,OAAOC,GAAE,MAAKA,GAAE,QAAOC,GAAE,MAAKA,GAAE,MAAM;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASF,IAAE;AAAC,UAAAA,KAAE,KAAK,YAAUA,MAAG,KAAK,YAAU,GAAG,OAAO,KAAK,QAAQ,GAAE,MAAI,EAAE,EAAE,KAAK,UAAU,EAAE,EAAE,QAAQ,gBAAgB,EAAE,WAAS,KAAK,YAAU,GAAG,sBAAsB,KAAK,QAAQ;AAAA,QAAG,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,WAAU;AAAC,iBAAO,KAAK,aAAW,KAAK,aAAa,GAAE,KAAK;AAAA,QAAS,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASA,IAAE;AAAC,UAAAA,MAAG,KAAK,aAAa,EAAE,SAAS,EAAE,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,WAAU;AAAC,eAAK,cAAY,KAAK,UAAU,OAAO,GAAE,KAAK,MAAM;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAASA,IAAE;AAAC,eAAK,UAAU,KAAK,UAASA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,eAAK,UAAU,WAAW,QAAQ;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,WAAU;AAAC,iBAAO,KAAK,UAAU,KAAK,QAAQ;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,WAAU;AAAC,cAAIA,KAAE,GAAG,OAAO;AAAE,iBAAOA,OAAIA,KAAEA,GAAE,UAAU,IAAGA,KAAE,KAAK,MAAM,QAAQA,EAAC,IAAE,KAAK,MAAM,SAAS,KAAK,SAAS;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASA,IAAE;AAAC,iBAAO,KAAK,MAAM,SAASA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,QAAQ,aAAa,kBAAiB,KAAK,UAAU,KAAK,CAAC,GAAE,KAAK,QAAQ,KAAK,GAAE,KAAK,QAAQ,aAAa,UAAS,KAAK,UAAU,KAAK,GAAE,KAAK,SAAS;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,eAAK,QAAQ,aAAa,kBAAiB,KAAK,UAAU,KAAK,CAAC,GAAE,KAAK,QAAQ,OAAO,GAAE,KAAK,QAAQ,aAAa,UAAS,KAAK,UAAU,KAAK,GAAE,KAAK,SAAS;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,QAAQ,aAAa,kBAAiB,KAAK,UAAU,KAAK,CAAC,GAAE,KAAK,QAAQ,KAAK,GAAE,KAAK,QAAQ,aAAa,UAAS,KAAK,UAAU,KAAK,GAAE,KAAK,SAAS;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,WAAU;AAAC,eAAK,QAAQ,aAAa,kBAAiB,KAAK,UAAU,KAAK,CAAC,GAAE,SAAS,YAAY,gBAAe,OAAG,KAAK,QAAQ,YAAY,GAAE,KAAK,MAAM;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASA,IAAE;AAAC,eAAK,iBAAiB,GAAE,KAAK,QAAQ,WAAW,GAAEA,MAAG,KAAK,QAAQ,aAAa,UAAS,KAAK,UAAU,KAAK,GAAE,KAAK,SAAS;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,OAAM,OAAM,WAAU;AAAC,cAAIA,KAAE,KAAK,aAAa;AAAE,cAAGA,GAAE,YAAY,KAAGA,GAAE,SAAS,EAAE,MAAK,MAAM,IAAIA,EAAC;AAAA,eAAM;AAAC,gBAAG,MAAI,KAAK,QAAQ,QAAQ,QAAM;AAAG,iBAAK,UAAU,KAAK,QAAQ,OAAO,MAAI,KAAK,cAAc,GAAE,KAAK,OAAO,UAAUA,IAAE,KAAK,QAAQ,OAAO,GAAE,KAAK,aAAa;AAAA,UAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,WAAU;AAAC,cAAIA,KAAE,KAAK,aAAa;AAAE,cAAGA,GAAE,YAAY,KAAGA,GAAE,SAAS,EAAE,MAAK,MAAM,IAAIA,IAAE,IAAE;AAAA,mBAAU,MAAI,KAAK,QAAQ,QAAQ,QAAM;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASA,IAAE;AAAC,iBAAO,WAAU;AAAC,iBAAK,cAAc,GAAEA,GAAE,MAAM,MAAK,SAAS,GAAE,KAAK,aAAa;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAACF,KAAE,GAAG,OAAO,GAAG,YAAY,KAAGA,GAAE,SAAS,MAAIE,MAAGD,KAAED,GAAE,IAAI,YAAU,MAAIC,GAAE,qBAAmB,SAAOA,GAAE,WAAW,CAAC,EAAE,YAAU,QAAMC,KAAED,GAAE,OAAO,IAAE,CAAC,MAAK,IAAI,EAAE,QAAQC,EAAC,KAAG,KAAGD,GAAE,WAAW,OAAO;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASD,IAAEC,IAAE;AAAC,cAAIC,IAAEC,KAAE;AAAK,kBAAOD,KAAEF,IAAE,EAAE,EAAE,SAAU,SAASA,IAAE;AAAC,gBAAIC,KAAE,EAAE,EAAE,OAAO;AAAE,YAAAA,GAAE,IAAI,QAAQ,WAAU;AAAC,cAAAA,GAAE,IAAI,aAAa,GAAED,GAAE,QAAQC,EAAC;AAAA,YAAC,CAAE,EAAE,IAAI,eAAe,WAAU;AAAC,cAAAA,GAAE,IAAI,MAAM,EAAE,OAAO,GAAED,GAAE,OAAOC,EAAC;AAAA,YAAC,CAAE,EAAE,IAAI,EAAC,SAAQ,OAAM,CAAC,EAAE,SAAS,SAAS,IAAI,EAAE,KAAK,OAAMC,EAAC;AAAA,UAAC,CAAE,EAAE,QAAQ,GAAG,KAAM,SAASF,IAAE;AAAC,YAAAG,GAAE,cAAc,GAAE,cAAY,OAAOF,KAAEA,GAAED,EAAC,KAAG,YAAU,OAAOC,MAAGD,GAAE,KAAK,iBAAgBC,EAAC,GAAED,GAAE,IAAI,SAAQ,KAAK,IAAIG,GAAE,UAAU,MAAM,GAAEH,GAAE,MAAM,CAAC,CAAC,IAAGA,GAAE,KAAK,GAAEG,GAAE,aAAa,EAAE,WAAWH,GAAE,CAAC,CAAC,GAAEG,GAAE,aAAa,GAAG,oBAAoBH,GAAE,CAAC,CAAC,EAAE,OAAO,CAAC,GAAEG,GAAE,aAAa;AAAA,UAAC,CAAE,EAAE,KAAM,SAASH,IAAE;AAAC,YAAAG,GAAE,QAAQ,aAAa,sBAAqBH,EAAC;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,yBAAwB,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAE;AAAK,YAAE,EAAE,KAAKD,IAAG,SAASA,IAAEE,IAAE;AAAC,gBAAIC,KAAED,GAAE;AAAK,YAAAD,GAAE,QAAQ,wBAAsBA,GAAE,QAAQ,uBAAqBC,GAAE,OAAKD,GAAE,QAAQ,aAAa,sBAAqBA,GAAE,KAAK,MAAM,oBAAoB,IAAE,SAASD,IAAE;AAAC,qBAAO,EAAE,EAAE,SAAU,SAASC,IAAE;AAAC,kBAAE,EAAE,OAAO,IAAI,cAAW,EAAC,QAAO,SAASD,IAAE;AAAC,sBAAIE,KAAEF,GAAE,OAAO;AAAO,kBAAAC,GAAE,QAAQC,EAAC;AAAA,gBAAC,GAAE,SAAQ,SAASF,IAAE;AAAC,kBAAAC,GAAE,OAAOD,EAAC;AAAA,gBAAC,EAAC,CAAC,EAAE,cAAcA,EAAC;AAAA,cAAC,CAAE,EAAE,QAAQ;AAAA,YAAC,EAAEE,EAAC,EAAE,KAAM,SAASF,IAAE;AAAC,qBAAOC,GAAE,YAAYD,IAAEG,EAAC;AAAA,YAAC,CAAE,EAAE,KAAM,WAAU;AAAC,cAAAF,GAAE,QAAQ,aAAa,oBAAoB;AAAA,YAAC,CAAE;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,0BAAyB,OAAM,SAASD,IAAE;AAAC,eAAK,QAAQ,UAAU,gBAAc,KAAK,QAAQ,aAAa,gBAAeA,EAAC,IAAE,KAAK,sBAAsBA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,mBAAkB,OAAM,WAAU;AAAC,cAAIA,KAAE,KAAK,aAAa;AAAE,iBAAOA,GAAE,WAAW,MAAIA,KAAE,GAAG,eAAe,GAAG,SAASA,GAAE,IAAG,GAAG,QAAQ,CAAC,IAAGA,GAAE,SAAS;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASA,IAAEC,IAAE;AAAC,cAAG,SAAS,YAAY,eAAc,OAAG,EAAE,SAAO,MAAID,KAAE,MAAIA,EAAC,GAAEC,MAAGA,GAAE,WAASA,GAAE,CAAC,EAAE,QAAQ,YAAY,MAAID,GAAE,YAAY,MAAIC,KAAEA,GAAE,KAAKD,EAAC,IAAGC,MAAGA,GAAE,SAAQ;AAAC,gBAAIC,KAAE,KAAK,YAAY,GAAEC,KAAE,EAAE,EAAE,CAACD,GAAE,IAAGA,GAAE,EAAE,CAAC,EAAE,QAAQF,EAAC;AAAE,YAAAG,GAAE,YAAY;AAAE,gBAAIC,KAAEH,GAAE,CAAC,EAAE,aAAW;AAAG,YAAAG,MAAGD,GAAE,SAASC,EAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,eAAK,YAAY,GAAG;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASJ,IAAEC,IAAE;AAAC,cAAIC,KAAE,KAAK,aAAa;AAAE,cAAG,OAAKA,IAAE;AAAC,gBAAIC,KAAE,KAAK,MAAM,WAAWD,EAAC;AAAE,gBAAG,KAAK,QAAQ,KAAK,qBAAqB,EAAE,KAAK,EAAE,GAAE,EAAE,EAAEC,EAAC,EAAE,IAAIH,IAAEC,EAAC,GAAEC,GAAE,YAAY,GAAE;AAAC,kBAAIE,KAAE,EAAE,KAAKD,EAAC;AAAE,cAAAC,MAAG,CAAC,GAAG,WAAWA,EAAC,MAAIA,GAAE,YAAU,GAAG,sBAAqB,GAAG,eAAeA,GAAE,UAAU,EAAE,OAAO,GAAE,KAAK,aAAa,GAAE,KAAK,UAAU,KAAK,SAAQA,EAAC;AAAA,YAAE,MAAM,CAAAF,GAAE,OAAO;AAAA,UAAC,OAAK;AAAC,gBAAII,KAAE,EAAE,EAAE,IAAI;AAAE,iBAAK,QAAQ,KAAK,qBAAqB,EAAE,KAAK,iCAA+BA,KAAE,gCAA8B,KAAK,KAAK,OAAO,cAAY,QAAQ,GAAE,WAAY,WAAU;AAAC,gBAAE,EAAE,yBAAuBA,EAAC,EAAE,OAAO;AAAA,YAAC,GAAG,GAAG;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,cAAIN,KAAE,KAAK,aAAa;AAAE,cAAGA,GAAE,WAAW,GAAE;AAAC,gBAAIC,KAAE,GAAG,SAASD,GAAE,IAAG,GAAG,QAAQ;AAAE,aAACA,KAAE,GAAG,eAAeC,EAAC,GAAG,OAAO,GAAE,KAAK,aAAa,GAAE,KAAK,cAAc,GAAE,SAAS,YAAY,QAAQ,GAAE,KAAK,aAAa;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,eAAK,SAAS,KAAG,KAAK,MAAM;AAAE,cAAID,KAAE,KAAK,aAAa,EAAE,OAAO,GAAG,QAAQ,GAAEC,KAAE,EAAE,EAAE,EAAE,KAAKD,GAAE,MAAM,GAAG,QAAQ,CAAC,CAAC,GAAEE,KAAE,EAAC,OAAMF,IAAE,MAAKA,GAAE,SAAS,GAAE,KAAIC,GAAE,SAAOA,GAAE,KAAK,MAAM,IAAE,GAAE;AAAE,iBAAOA,GAAE,WAASC,GAAE,cAAY,aAAWD,GAAE,KAAK,QAAQ,IAAGC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASF,IAAE;AAAC,cAAIC,KAAE,KAAK,aAAa,KAAK,SAAS;AAAE,UAAAA,GAAE,YAAY,KAAGA,GAAE,SAAS,MAAI,KAAK,cAAc,GAAE,KAAK,MAAM,OAAOA,IAAED,EAAC,GAAE,KAAK,aAAa;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAE,KAAK,aAAa,KAAK,SAAS;AAAE,UAAAA,GAAE,YAAY,KAAGA,GAAE,SAAS,MAAI,KAAK,cAAc,GAAE,KAAK,MAAM,OAAOA,IAAED,EAAC,GAAE,KAAK,aAAa;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,WAAU;AAAC,cAAIA,KAAE,KAAK,aAAa,KAAK,SAAS;AAAE,UAAAA,GAAE,YAAY,KAAGA,GAAE,SAAS,MAAI,KAAK,cAAc,GAAE,KAAK,MAAM,UAAUA,EAAC,GAAE,KAAK,aAAa;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,WAAU;AAAC,cAAIA,KAAE,KAAK,aAAa,KAAK,SAAS;AAAE,UAAAA,GAAE,YAAY,KAAGA,GAAE,SAAS,MAAI,KAAK,cAAc,GAAE,KAAK,MAAM,UAAUA,EAAC,GAAE,KAAK,aAAa;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,cAAIA,KAAE,KAAK,aAAa,KAAK,SAAS;AAAE,UAAAA,GAAE,YAAY,KAAGA,GAAE,SAAS,MAAI,KAAK,cAAc,GAAE,KAAK,MAAM,YAAYA,EAAC,GAAE,KAAK,aAAa;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASA,IAAEC,IAAEC,IAAE;AAAC,cAAIC;AAAE,cAAGD,IAAE;AAAC,gBAAIE,KAAEJ,GAAE,IAAEA,GAAE,GAAEK,KAAEJ,GAAE,KAAK,OAAO;AAAE,YAAAE,KAAE,EAAC,OAAME,KAAED,KAAEJ,GAAE,IAAEA,GAAE,IAAEK,IAAE,QAAOA,KAAED,KAAEJ,GAAE,IAAEK,KAAEL,GAAE,EAAC;AAAA,UAAC,MAAM,CAAAG,KAAE,EAAC,OAAMH,GAAE,GAAE,QAAOA,GAAE,EAAC;AAAE,UAAAC,GAAE,IAAIE,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,WAAU;AAAC,iBAAO,KAAK,UAAU,GAAG,QAAQ;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,WAAU;AAAC,eAAK,SAAS,KAAG,KAAK,UAAU,QAAQ,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,iBAAO,GAAG,QAAQ,KAAK,UAAU,CAAC,CAAC,KAAG,GAAG,cAAY,KAAK,UAAU,KAAK;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,WAAU;AAAC,eAAK,QAAQ,OAAO,QAAO,GAAG,SAAS;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,eAAK,UAAU,CAAC,EAAE,UAAU;AAAA,QAAC,EAAC,CAAC,GAAEF,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,UAAQA,GAAE,SAAQ,KAAK,YAAUA,GAAE,WAAW;AAAA,QAAQ,IAAGA,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,eAAK,UAAU,GAAG,SAAQ,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE;AAAK,cAAG,CAAC,KAAK,QAAQ,WAAW,GAAE;AAAC,gBAAIC,KAAEF,GAAE,cAAc;AAAc,gBAAGE,MAAGA,GAAE,SAAOA,GAAE,MAAM,QAAO;AAAC,kBAAIC,KAAED,GAAE,OAAME,KAAEF,GAAE,QAAQ,MAAM;AAAE,cAAAC,GAAE,SAAO,KAAG,KAAK,QAAQ,+BAA6B,KAAK,QAAQ,OAAO,iCAAgCA,EAAC,GAAEH,GAAE,eAAe,IAAGI,GAAE,SAAO,KAAG,KAAK,QAAQ,OAAO,oBAAmBA,GAAE,MAAM,KAAGJ,GAAE,eAAe;AAAA,YAAC,WAAS,OAAO,eAAc;AAAC,kBAAIK,KAAE,OAAO,cAAc,QAAQ,MAAM;AAAE,mBAAK,QAAQ,OAAO,oBAAmBA,GAAE,MAAM,KAAGL,GAAE,eAAe;AAAA,YAAC;AAAC,uBAAY,WAAU;AAAC,cAAAC,GAAE,QAAQ,OAAO,qBAAqB;AAAA,YAAC,GAAG,EAAE;AAAA,UAAC;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,iBAAe,EAAE,EAAE,QAAQ,GAAE,KAAK,UAAQA,GAAE,WAAW,QAAO,KAAK,YAAUA,GAAE,WAAW,UAAS,KAAK,UAAQA,GAAE,SAAQ,KAAK,OAAK,KAAK,QAAQ,UAAS,KAAK,wBAAsB,CAAC,GAAE,KAAK,YAAU,EAAE,EAAE,CAAC,+BAA8B,6CAA4C,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,KAAK,OAAO;AAAA,QAAC,GAAEA,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,eAAK,QAAQ,sBAAoB,KAAK,sBAAsB,SAAO,SAASD,IAAE;AAAC,YAAAA,GAAE,eAAe;AAAA,UAAC,GAAE,KAAK,iBAAe,KAAK,WAAU,KAAK,eAAe,GAAG,QAAO,KAAK,sBAAsB,MAAM,KAAG,KAAK,uBAAuB;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,0BAAyB,OAAM,WAAU;AAAC,cAAIA,KAAE,MAAKC,KAAE,EAAE,EAAE,GAAEC,KAAE,KAAK,UAAU,KAAK,wBAAwB;AAAE,eAAK,sBAAsB,cAAY,SAASC,IAAE;AAAC,gBAAIC,KAAEJ,GAAE,QAAQ,OAAO,sBAAsB,GAAEK,KAAEL,GAAE,QAAQ,MAAM,IAAE,KAAGA,GAAE,QAAQ,OAAO,IAAE;AAAE,YAAAI,MAAGH,GAAE,UAAQ,CAACI,OAAIL,GAAE,QAAQ,SAAS,UAAU,GAAEA,GAAE,UAAU,MAAMA,GAAE,QAAQ,MAAM,CAAC,GAAEA,GAAE,UAAU,OAAOA,GAAE,QAAQ,OAAO,CAAC,GAAEE,GAAE,KAAKF,GAAE,KAAK,MAAM,aAAa,IAAGC,KAAEA,GAAE,IAAIE,GAAE,MAAM;AAAA,UAAC,GAAE,KAAK,sBAAsB,cAAY,SAASD,IAAE;AAAC,aAACD,KAAEA,GAAE,IAAIC,GAAE,MAAM,GAAG,UAAQ,WAASA,GAAE,OAAO,aAAWD,KAAE,EAAE,EAAE,GAAED,GAAE,QAAQ,YAAY,UAAU;AAAA,UAAE,GAAE,KAAK,sBAAsB,SAAO,WAAU;AAAC,YAAAC,KAAE,EAAE,EAAE,GAAED,GAAE,QAAQ,YAAY,UAAU;AAAA,UAAC,GAAE,KAAK,eAAe,GAAG,aAAY,KAAK,sBAAsB,WAAW,EAAE,GAAG,aAAY,KAAK,sBAAsB,WAAW,EAAE,GAAG,QAAO,KAAK,sBAAsB,MAAM,GAAE,KAAK,UAAU,GAAG,aAAa,WAAU;AAAC,YAAAA,GAAE,UAAU,SAAS,OAAO,GAAEE,GAAE,KAAKF,GAAE,KAAK,MAAM,SAAS;AAAA,UAAC,CAAE,EAAE,GAAG,aAAa,WAAU;AAAC,YAAAA,GAAE,UAAU,YAAY,OAAO,GAAEE,GAAE,KAAKF,GAAE,KAAK,MAAM,aAAa;AAAA,UAAC,CAAE,GAAE,KAAK,UAAU,GAAG,QAAQ,SAASC,IAAE;AAAC,gBAAIC,KAAED,GAAE,cAAc;AAAa,YAAAA,GAAE,eAAe,GAAEC,MAAGA,GAAE,SAAOA,GAAE,MAAM,UAAQF,GAAE,UAAU,QAAQ,OAAO,GAAEA,GAAE,QAAQ,OAAO,iCAAgCE,GAAE,KAAK,KAAG,EAAE,EAAE,KAAKA,GAAE,OAAO,SAASD,IAAEE,IAAE;AAAC,kBAAG,EAAEA,GAAE,YAAY,EAAE,QAAQ,OAAO,IAAE,KAAI;AAAC,oBAAIC,KAAEF,GAAE,QAAQC,EAAC;AAAE,gBAAAA,GAAE,YAAY,EAAE,QAAQ,MAAM,IAAE,KAAGH,GAAE,QAAQ,OAAO,oBAAmBI,EAAC,IAAE,EAAE,EAAEA,EAAC,EAAE,KAAM,SAASH,IAAEC,IAAE;AAAC,kBAAAF,GAAE,QAAQ,OAAO,qBAAoBE,EAAC;AAAA,gBAAC,CAAE;AAAA,cAAC;AAAA,YAAC,CAAE;AAAA,UAAC,CAAE,EAAE,GAAG,YAAW,KAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,cAAIF,KAAE;AAAK,iBAAO,KAAK,KAAK,qBAAqB,EAAE,QAAS,SAASC,IAAE;AAAC,YAAAD,GAAE,eAAe,IAAIC,GAAE,MAAM,CAAC,EAAE,YAAY,GAAED,GAAE,sBAAsBC,EAAC,CAAC;AAAA,UAAC,CAAE,GAAE,KAAK,wBAAsB,CAAC;AAAA,QAAC,EAAC,CAAC,GAAEA,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,YAAIC,KAAE,eAAa,OAAO,UAAQF,GAAE,OAAO,QAAQ,KAAGA,GAAE,YAAY;AAAE,YAAG,CAACE,IAAE;AAAC,cAAG,MAAM,QAAQF,EAAC,MAAIE,KAAE,SAASF,IAAEC,IAAE;AAAC,gBAAGD,IAAE;AAAC,kBAAG,YAAU,OAAOA,GAAE,QAAO,GAAGA,IAAEC,EAAC;AAAE,kBAAIC,KAAE,CAAC,EAAE,SAAS,KAAKF,EAAC,EAAE,MAAM,GAAE,EAAE;AAAE,qBAAM,aAAWE,MAAGF,GAAE,gBAAcE,KAAEF,GAAE,YAAY,OAAM,UAAQE,MAAG,UAAQA,KAAE,MAAM,KAAKF,EAAC,IAAE,gBAAcE,MAAG,2CAA2C,KAAKA,EAAC,IAAE,GAAGF,IAAEC,EAAC,IAAE;AAAA,YAAM;AAAA,UAAC,EAAED,EAAC,MAAIC,MAAGD,MAAG,YAAU,OAAOA,GAAE,QAAO;AAAC,YAAAE,OAAIF,KAAEE;AAAG,gBAAIC,KAAE,GAAEC,KAAE,WAAU;AAAA,YAAC;AAAE,mBAAM,EAAC,GAAEA,IAAE,GAAE,WAAU;AAAC,qBAAOD,MAAGH,GAAE,SAAO,EAAC,MAAK,KAAE,IAAE,EAAC,MAAK,OAAG,OAAMA,GAAEG,IAAG,EAAC;AAAA,YAAC,GAAE,GAAE,SAASH,IAAE;AAAC,oBAAMA;AAAA,YAAC,GAAE,GAAEI,GAAC;AAAA,UAAC;AAAC,gBAAM,IAAI,UAAU,uIAAuI;AAAA,QAAC;AAAC,YAAIC,IAAEC,KAAE,MAAGC,KAAE;AAAG,eAAM,EAAC,GAAE,WAAU;AAAC,UAAAL,KAAEA,GAAE,KAAKF,EAAC;AAAA,QAAC,GAAE,GAAE,WAAU;AAAC,cAAIA,KAAEE,GAAE,KAAK;AAAE,iBAAOI,KAAEN,GAAE,MAAKA;AAAA,QAAC,GAAE,GAAE,SAASA,IAAE;AAAC,UAAAO,KAAE,MAAGF,KAAEL;AAAA,QAAC,GAAE,GAAE,WAAU;AAAC,cAAG;AAAC,YAAAM,MAAG,QAAMJ,GAAE,UAAQA,GAAE,OAAO;AAAA,UAAC,UAAC;AAAQ,gBAAGK,GAAE,OAAMF;AAAA,UAAC;AAAA,QAAC,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGL,IAAEC,IAAE;AAAC,SAAC,QAAMA,MAAGA,KAAED,GAAE,YAAUC,KAAED,GAAE;AAAQ,iBAAQE,KAAE,GAAEC,KAAE,MAAMF,EAAC,GAAEC,KAAED,IAAEC,KAAI,CAAAC,GAAED,EAAC,IAAEF,GAAEE,EAAC;AAAE,eAAOC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,UAAQA,GAAE,WAAW,QAAO,KAAK,YAAUA,GAAE,WAAW,UAAS,KAAK,WAASA,GAAE,WAAW,SAAQ,KAAK,UAAQA,GAAE,SAAQ,KAAK,wBAAsB,OAAO,YAAW,KAAK,QAAQ,WAAW,0BAAwB,KAAK,wBAAsB,KAAK,QAAQ,WAAW;AAAA,QAAsB,GAAEA,KAAE,CAAC,EAAC,KAAI,QAAO,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE,KAAK,YAAY,GAAEC,KAAE,KAAK;AAAsB,UAAAD,OAAID,KAAEE,KAAE,KAAK,SAAS,KAAK,UAAU,EAAE,OAAO,EAAE,SAASF,EAAC,IAAE,KAAK,SAAS,IAAIA,EAAC,IAAEE,MAAG,KAAK,SAAS,KAAK,UAAU,EAAE,KAAK;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAIF,KAAE;AAAK,eAAK,SAAS,GAAG,SAAS,SAASC,IAAE;AAAC,YAAAA,GAAE,YAAU,GAAG,KAAK,UAAQD,GAAE,WAAW;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,iBAAO,KAAK,QAAQ,SAAS,UAAU;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,eAAK,YAAY,IAAE,KAAK,WAAW,IAAE,KAAK,SAAS,GAAE,KAAK,QAAQ,aAAa,kBAAkB;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASA,IAAE;AAAC,cAAG,KAAK,QAAQ,mBAAiBA,KAAEA,GAAE,QAAQ,KAAK,QAAQ,qBAAoB,EAAE,GAAE,KAAK,QAAQ,uBAAsB;AAAC,gBAAIC,KAAE,KAAK,QAAQ,2BAA2B,OAAO,KAAK,QAAQ,8BAA8B;AAAE,YAAAD,KAAEA,GAAE,QAAQ,qCAAqC,SAASA,IAAE;AAAC,kBAAG,uDAAuD,KAAKA,EAAC,EAAE,QAAM;AAAG,kBAAIE,IAAEC,KAAE,GAAGF,EAAC;AAAE,kBAAG;AAAC,qBAAIE,GAAE,EAAE,GAAE,EAAED,KAAEC,GAAE,EAAE,GAAG,QAAM;AAAC,sBAAIC,KAAEF,GAAE;AAAM,sBAAG,IAAI,OAAO,sBAAoBE,GAAE,QAAQ,0BAAyB,MAAM,IAAE,QAAQ,EAAE,KAAKJ,EAAC,EAAE,QAAOA;AAAA,gBAAC;AAAA,cAAC,SAAOA,IAAE;AAAC,gBAAAG,GAAE,EAAEH,EAAC;AAAA,cAAC,UAAC;AAAQ,gBAAAG,GAAE,EAAE;AAAA,cAAC;AAAC,qBAAM;AAAA,YAAE,CAAE;AAAA,UAAC;AAAC,iBAAOH;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,WAAU;AAAC,cAAIA,KAAE,MAAKC,KAAE,KAAK;AAAsB,cAAG,KAAK,SAAS,IAAI,GAAG,KAAK,KAAK,WAAU,KAAK,QAAQ,YAAY,CAAC,GAAE,KAAK,SAAS,OAAO,KAAK,UAAU,OAAO,CAAC,GAAE,KAAK,QAAQ,OAAO,0BAAyB,IAAE,GAAE,KAAK,QAAQ,OAAO,6BAA4B,IAAE,GAAE,KAAK,QAAQ,SAAS,UAAU,GAAE,KAAK,SAAS,QAAQ,OAAO,GAAEA,IAAE;AAAC,gBAAIC,KAAED,GAAE,aAAa,KAAK,SAAS,CAAC,GAAE,KAAK,QAAQ,UAAU;AAAE,gBAAG,KAAK,QAAQ,WAAW,MAAK;AAAC,kBAAIE,KAAE,IAAIF,GAAE,WAAW,KAAK,QAAQ,WAAW,IAAI;AAAE,cAAAC,GAAE,aAAWC,IAAED,GAAE,GAAG,kBAAkB,SAASF,IAAE;AAAC,gBAAAG,GAAE,eAAeH,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAC,YAAAE,GAAE,GAAG,QAAQ,SAASD,IAAE;AAAC,cAAAD,GAAE,QAAQ,aAAa,iBAAgBE,GAAE,SAAS,GAAED,EAAC;AAAA,YAAC,CAAE,GAAEC,GAAE,GAAG,UAAU,WAAU;AAAC,cAAAF,GAAE,QAAQ,aAAa,mBAAkBE,GAAE,SAAS,GAAEA,EAAC;AAAA,YAAC,CAAE,GAAEA,GAAE,QAAQ,MAAK,KAAK,UAAU,YAAY,CAAC,GAAE,KAAK,SAAS,KAAK,YAAWA,EAAC;AAAA,UAAC,MAAM,MAAK,SAAS,GAAG,QAAQ,SAASD,IAAE;AAAC,YAAAD,GAAE,QAAQ,aAAa,iBAAgBA,GAAE,SAAS,IAAI,GAAEC,EAAC;AAAA,UAAC,CAAE,GAAE,KAAK,SAAS,GAAG,SAAS,WAAU;AAAC,YAAAD,GAAE,QAAQ,aAAa,mBAAkBA,GAAE,SAAS,IAAI,GAAEA,GAAE,QAAQ;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAG,KAAK,uBAAsB;AAAC,gBAAIA,KAAE,KAAK,SAAS,KAAK,UAAU;AAAE,iBAAK,SAAS,IAAIA,GAAE,SAAS,CAAC,GAAEA,GAAE,WAAW;AAAA,UAAC;AAAC,cAAIC,KAAE,KAAK,OAAO,GAAG,MAAM,KAAK,UAAS,KAAK,QAAQ,YAAY,KAAG,GAAG,SAAS,GAAEC,KAAE,KAAK,UAAU,KAAK,MAAID;AAAE,eAAK,UAAU,KAAKA,EAAC,GAAE,KAAK,UAAU,OAAO,KAAK,QAAQ,SAAO,KAAK,SAAS,OAAO,IAAE,MAAM,GAAE,KAAK,QAAQ,YAAY,UAAU,GAAEC,MAAG,KAAK,QAAQ,aAAa,UAAS,KAAK,UAAU,KAAK,GAAE,KAAK,SAAS,GAAE,KAAK,UAAU,QAAQ,OAAO,GAAE,KAAK,QAAQ,OAAO,0BAAyB,KAAE,GAAE,KAAK,QAAQ,OAAO,6BAA4B,KAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,YAAY,KAAG,KAAK,WAAW;AAAA,QAAC,EAAC,CAAC,GAAED,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,YAAU,EAAE,EAAE,QAAQ,GAAE,KAAK,aAAWC,GAAE,WAAW,WAAU,KAAK,YAAUA,GAAE,WAAW,UAAS,KAAK,WAASA,GAAE,WAAW,SAAQ,KAAK,UAAQA,GAAE;AAAA,QAAO,IAAGA,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,QAAQ,WAAS,KAAK,QAAQ,sBAAoB,KAAK,QAAQ,IAAE,KAAK,WAAW,GAAG,wBAAwB,SAASC,IAAE;AAAC,YAAAA,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAE,gBAAIC,KAAEF,GAAE,UAAU,OAAO,EAAE,MAAIA,GAAE,UAAU,UAAU,GAAEG,KAAEH,GAAE,SAAS,OAAO,EAAE,MAAIA,GAAE,UAAU,UAAU,GAAEI,KAAE,SAASH,IAAE;AAAC,kBAAIG,KAAE,eAAaH,GAAE,OAAKA,KAAEA,GAAE,cAAc,QAAQ,CAAC,GAAEI,KAAED,GAAE,WAASF,KAAE,KAAII,KAAEF,GAAE,WAASD,KAAE;AAAI,cAAAE,KAAEL,GAAE,QAAQ,YAAU,IAAE,KAAK,IAAIK,IAAEL,GAAE,QAAQ,SAAS,IAAEK,IAAEA,KAAEL,GAAE,QAAQ,YAAU,IAAE,KAAK,IAAIK,IAAEL,GAAE,QAAQ,SAAS,IAAEK,IAAEC,KAAEN,GAAE,QAAQ,YAAU,IAAE,KAAK,IAAIM,IAAEN,GAAE,QAAQ,SAAS,IAAEM,IAAEA,KAAEN,GAAE,QAAQ,YAAU,IAAE,KAAK,IAAIM,IAAEN,GAAE,QAAQ,SAAS,IAAEM,IAAEN,GAAE,UAAU,OAAOK,EAAC,GAAEL,GAAE,SAAS,OAAOM,EAAC;AAAA,YAAC;AAAE,YAAAN,GAAE,UAAU,GAAG,uBAAsBI,EAAC,EAAE,IAAI,oBAAoB,WAAU;AAAC,cAAAJ,GAAE,UAAU,IAAI,uBAAsBI,EAAC;AAAA,YAAC,CAAE;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,WAAW,IAAI,GAAE,KAAK,WAAW,SAAS,QAAQ;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGJ,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,UAAQA,GAAE,WAAW,QAAO,KAAK,WAASA,GAAE,WAAW,SAAQ,KAAK,YAAUA,GAAE,WAAW,UAAS,KAAK,WAASA,GAAE,WAAW,SAAQ,KAAK,UAAQ,EAAE,EAAE,MAAM,GAAE,KAAK,aAAW,EAAE,EAAE,YAAY,GAAE,KAAK,qBAAmB,wBAAuB,KAAK,WAAS,WAAU;AAAC,YAAAC,GAAE,SAAS,EAAC,GAAEA,GAAE,QAAQ,OAAO,IAAEA,GAAE,SAAS,YAAY,EAAC,CAAC;AAAA,UAAC;AAAA,QAAC,IAAGD,KAAE,CAAC,EAAC,KAAI,YAAW,OAAM,SAASD,IAAE;AAAC,eAAK,UAAU,IAAI,UAASA,GAAE,CAAC,GAAE,KAAK,SAAS,IAAI,UAASA,GAAE,CAAC,GAAE,KAAK,SAAS,KAAK,UAAU,KAAG,KAAK,SAAS,KAAK,UAAU,EAAE,QAAQ,MAAKA,GAAE,CAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,eAAK,QAAQ,YAAY,YAAY;AAAE,cAAIA,KAAE,KAAK,aAAa;AAAE,eAAK,WAAW,YAAY,KAAK,oBAAmBA,EAAC,GAAEA,MAAG,KAAK,UAAU,KAAK,aAAY,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAE,KAAK,UAAU,KAAK,gBAAe,KAAK,UAAU,IAAI,WAAW,CAAC,GAAE,KAAK,UAAU,IAAI,aAAY,EAAE,GAAE,KAAK,QAAQ,GAAG,UAAS,KAAK,QAAQ,EAAE,QAAQ,QAAQ,MAAI,KAAK,QAAQ,IAAI,UAAS,KAAK,QAAQ,GAAE,KAAK,SAAS,EAAC,GAAE,KAAK,UAAU,KAAK,WAAW,EAAC,CAAC,GAAE,KAAK,UAAU,IAAI,aAAY,KAAK,UAAU,IAAI,cAAc,CAAC,IAAG,KAAK,QAAQ,OAAO,4BAA2BA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,WAAU;AAAC,iBAAO,KAAK,QAAQ,SAAS,YAAY;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,WAAW,YAAY,KAAK,kBAAkB;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,YAAU,EAAE,EAAE,QAAQ,GAAE,KAAK,eAAaA,GAAE,WAAW,aAAY,KAAK,UAAQA,GAAE,SAAQ,KAAK,OAAK,KAAK,QAAQ,UAAS,KAAK,SAAO,EAAC,wBAAuB,SAASD,IAAEC,IAAE;AAAC,YAAAC,GAAE,OAAOD,GAAE,QAAOA,EAAC,KAAGA,GAAE,eAAe;AAAA,UAAC,GAAE,gFAA+E,WAAU;AAAC,YAAAC,GAAE,OAAO;AAAA,UAAC,GAAE,sCAAqC,WAAU;AAAC,YAAAA,GAAE,KAAK;AAAA,UAAC,GAAE,+BAA8B,WAAU;AAAC,YAAAA,GAAE,OAAO;AAAA,UAAC,EAAC;AAAA,QAAC,GAAED,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,UAAQ,EAAE,EAAE,CAAC,6BAA4B,wCAAuC,iDAAgD,2DAA0D,2DAA0D,2DAA0D,gBAAe,KAAK,QAAQ,qBAAmB,wBAAsB,uBAAsB,4BAA2B,KAAK,QAAQ,qBAAmB,KAAG,mDAAkD,UAAS,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,KAAK,YAAY,GAAE,KAAK,QAAQ,GAAG,aAAa,SAASC,IAAE;AAAC,gBAAG,GAAG,gBAAgBA,GAAE,MAAM,GAAE;AAAC,cAAAA,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAE,kBAAIC,KAAEF,GAAE,QAAQ,KAAK,yBAAyB,EAAE,KAAK,QAAQ,GAAEG,KAAED,GAAE,OAAO,GAAEE,KAAEJ,GAAE,UAAU,UAAU,GAAEK,KAAE,SAASJ,IAAE;AAAC,gBAAAD,GAAE,QAAQ,OAAO,mBAAkB,EAAC,GAAEC,GAAE,UAAQE,GAAE,MAAK,GAAEF,GAAE,WAASE,GAAE,MAAIC,IAAE,GAAEF,IAAE,CAACD,GAAE,QAAQ,GAAED,GAAE,OAAOE,GAAE,CAAC,GAAED,EAAC;AAAA,cAAC;AAAE,cAAAD,GAAE,UAAU,GAAG,aAAYK,EAAC,EAAE,IAAI,WAAW,SAASJ,IAAE;AAAC,gBAAAA,GAAE,eAAe,GAAED,GAAE,UAAU,IAAI,aAAYK,EAAC,GAAEL,GAAE,QAAQ,OAAO,qBAAqB;AAAA,cAAC,CAAE,GAAEE,GAAE,KAAK,OAAO,KAAGA,GAAE,KAAK,SAAQA,GAAE,OAAO,IAAEA,GAAE,MAAM,CAAC;AAAA,YAAC;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,GAAG,SAAS,SAASD,IAAE;AAAC,YAAAA,GAAE,eAAe,GAAED,GAAE,OAAO;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,QAAQ,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASA,IAAEC,IAAE;AAAC,cAAG,KAAK,QAAQ,WAAW,EAAE,QAAM;AAAG,cAAIC,KAAE,GAAG,MAAMF,EAAC,GAAEG,KAAE,KAAK,QAAQ,KAAK,yBAAyB;AAAE,cAAG,KAAK,QAAQ,OAAO,uBAAsBH,IAAEC,EAAC,GAAEC,IAAE;AAAC,gBAAIE,KAAE,EAAE,EAAEJ,EAAC,GAAEM,KAAE,KAAK,aAAa,CAAC,EAAE,sBAAsB,GAAEC,KAAEP,GAAE,sBAAsB;AAAE,YAAAG,GAAE,IAAI,EAAC,SAAQ,SAAQ,MAAKI,GAAE,OAAKD,GAAE,MAAK,KAAIC,GAAE,MAAID,GAAE,KAAI,OAAMC,GAAE,OAAM,QAAOA,GAAE,OAAM,CAAC,EAAE,KAAK,UAASH,EAAC;AAAE,gBAAII,KAAE,IAAI;AAAM,YAAAA,GAAE,MAAIJ,GAAE,KAAK,KAAK;AAAE,gBAAIK,KAAEF,GAAE,QAAM,MAAIA,GAAE,SAAO,OAAK,KAAK,KAAK,MAAM,WAAS,OAAKC,GAAE,QAAM,MAAIA,GAAE,SAAO;AAAI,YAAAL,GAAE,KAAK,8BAA8B,EAAE,KAAKM,EAAC,GAAE,KAAK,QAAQ,OAAO,qBAAoBT,EAAC;AAAA,UAAC,MAAM,MAAK,KAAK;AAAE,iBAAOE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,QAAQ,OAAO,oBAAoB,GAAE,KAAK,QAAQ,SAAS,EAAE,KAAK;AAAA,QAAC,EAAC,CAAC,GAAED,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,uGAAsG,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,UAAQA,GAAE,SAAQ,KAAK,YAAUA,GAAE,WAAW,UAAS,KAAK,SAAO,EAAC,oBAAmB,SAASD,IAAEC,IAAE;AAAC,YAAAA,GAAE,mBAAmB,KAAGC,GAAE,YAAYD,EAAC;AAAA,UAAC,GAAE,sBAAqB,SAASD,IAAEC,IAAE;AAAC,YAAAC,GAAE,cAAcD,EAAC;AAAA,UAAC,EAAC;AAAA,QAAC,IAAGA,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,eAAK,gBAAc;AAAA,QAAI,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,gBAAc;AAAA,QAAI,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,cAAG,KAAK,eAAc;AAAC,gBAAID,KAAE,KAAK,cAAc,SAAS,GAAEC,KAAED,GAAE,MAAM,EAAE;AAAE,gBAAGC,OAAIA,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAG;AAAC,kBAAIC,KAAED,GAAE,CAAC,IAAED,KAAE,YAAUA,IAAEG,KAAE,KAAK,QAAQ,4BAA0BH,GAAE,QAAQ,qEAAoE,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,IAAEA,IAAEI,KAAE,EAAE,EAAE,SAAS,EAAE,KAAKD,EAAC,EAAE,KAAK,QAAOD,EAAC,EAAE,CAAC;AAAE,mBAAK,QAAQ,QAAQ,mBAAiB,EAAE,EAAEE,EAAC,EAAE,KAAK,UAAS,QAAQ,GAAE,KAAK,cAAc,WAAWA,EAAC,GAAE,KAAK,gBAAc,MAAK,KAAK,QAAQ,OAAO,cAAc,GAAE,KAAK,QAAQ,aAAa,UAAS,KAAK,UAAU,KAAK,GAAE,KAAK,SAAS;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASJ,IAAE;AAAC,cAAG,EAAE,SAAS,CAAC,GAAG,KAAK,OAAM,GAAG,KAAK,KAAK,GAAEA,GAAE,OAAO,GAAE;AAAC,gBAAIC,KAAE,KAAK,QAAQ,OAAO,oBAAoB,EAAE,aAAa;AAAE,iBAAK,gBAAcA;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASD,IAAE;AAAC,WAAC,GAAG,KAAK,UAAQA,GAAE,WAAS,GAAG,KAAK,UAAQA,GAAE,WAAS,CAACA,GAAE,aAAW,KAAK,QAAQ;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,QAAMC,GAAE,WAAW,MAAK,KAAK,SAAO,EAAC,qBAAoB,WAAU;AAAC,YAAAC,GAAE,MAAM,IAAID,GAAE,OAAO,MAAM,CAAC;AAAA,UAAC,EAAC;AAAA,QAAC,IAAGA,KAAE,CAAC,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,iBAAO,GAAG,WAAW,KAAK,MAAM,CAAC,CAAC;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,UAAQA,GAAE,QAAQ,WAAS,CAAC,GAAE,KAAK,OAAK,CAAC,GAAG,KAAK,OAAM,GAAG,KAAK,OAAM,GAAG,KAAK,QAAO,GAAG,KAAK,OAAM,GAAG,KAAK,WAAU,GAAG,KAAK,KAAK,GAAE,KAAK,sBAAoB,MAAK,KAAK,SAAO,EAAC,oBAAmB,SAASD,IAAEC,IAAE;AAAC,YAAAA,GAAE,mBAAmB,KAAGC,GAAE,YAAYD,EAAC;AAAA,UAAC,GAAE,sBAAqB,SAASD,IAAEC,IAAE;AAAC,YAAAC,GAAE,cAAcD,EAAC;AAAA,UAAC,EAAC;AAAA,QAAC,IAAGA,KAAE,CAAC,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,iBAAM,CAAC,CAAC,KAAK,QAAQ;AAAA,QAAK,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,eAAK,WAAS;AAAA,QAAI,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,WAAS;AAAA,QAAI,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,cAAG,KAAK,UAAS;AAAC,gBAAID,KAAE,MAAKC,KAAE,KAAK,SAAS,SAAS;AAAE,iBAAK,QAAQ,MAAMA,IAAG,SAASA,IAAE;AAAC,kBAAGA,IAAE;AAAC,oBAAIC,KAAE;AAAG,oBAAG,YAAU,OAAOD,KAAEC,KAAE,GAAG,WAAWD,EAAC,IAAEA,cAAa,SAAOC,KAAED,GAAE,CAAC,IAAEA,cAAa,SAAOC,KAAED,KAAG,CAACC,GAAE;AAAO,gBAAAF,GAAE,SAAS,WAAWE,EAAC,GAAEF,GAAE,WAAS,MAAKA,GAAE,QAAQ,OAAO,cAAc;AAAA,cAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASA,IAAE;AAAC,cAAG,KAAK,uBAAqB,EAAE,SAAS,KAAK,MAAK,KAAK,mBAAmB,EAAE,MAAK,sBAAoBA,GAAE;AAAA,eAAY;AAAC,gBAAG,EAAE,SAAS,KAAK,MAAKA,GAAE,OAAO,GAAE;AAAC,kBAAIC,KAAE,KAAK,QAAQ,OAAO,oBAAoB,EAAE,aAAa;AAAE,mBAAK,WAASA;AAAA,YAAC;AAAC,iBAAK,sBAAoBD,GAAE;AAAA,UAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASA,IAAE;AAAC,YAAE,SAAS,KAAK,MAAKA,GAAE,OAAO,KAAG,KAAK,QAAQ;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,eAAaA,GAAE,WAAW,aAAY,KAAK,UAAQA,GAAE,SAAQ,SAAK,KAAK,QAAQ,uBAAqB,KAAK,QAAQ,cAAY,KAAK,QAAQ,MAAM,KAAK,aAAa,KAAG,KAAK,QAAQ,cAAa,KAAK,SAAO,EAAC,qCAAoC,WAAU;AAAC,YAAAC,GAAE,OAAO;AAAA,UAAC,GAAE,+BAA8B,WAAU;AAAC,YAAAA,GAAE,OAAO;AAAA,UAAC,EAAC;AAAA,QAAC,IAAGD,KAAE,CAAC,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,iBAAM,CAAC,CAAC,KAAK,QAAQ;AAAA,QAAW,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,eAAa,EAAE,EAAE,sCAAsC,GAAE,KAAK,aAAa,GAAG,SAAS,WAAU;AAAC,YAAAA,GAAE,QAAQ,OAAO,OAAO;AAAA,UAAC,CAAE,EAAE,KAAK,KAAK,QAAQ,WAAW,EAAE,UAAU,KAAK,YAAY,GAAE,KAAK,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,aAAa,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,cAAIA,KAAE,CAAC,KAAK,QAAQ,OAAO,sBAAsB,KAAG,KAAK,QAAQ,OAAO,gBAAgB;AAAE,eAAK,aAAa,OAAOA,EAAC;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,UAAQC,IAAE,KAAK,WAASA,GAAE,WAAW,SAAQ,KAAK,UAAQA,GAAE,SAAQ,KAAK,OAAK,KAAK,QAAQ,UAAS,KAAK,iBAAe,EAAE,aAAa,KAAK,QAAQ,OAAO,EAAE,QAAM,QAAM,IAAI,CAAC;AAAA,QAAC,GAAEA,KAAE,CAAC,EAAC,KAAI,qBAAoB,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE,KAAK,eAAeD,EAAC;AAAE,iBAAO,KAAK,QAAQ,aAAWC,MAAG,EAAE,UAAQA,KAAEA,GAAE,QAAQ,OAAM,GAAG,EAAE,QAAQ,SAAQ,GAAG,IAAG,QAAMA,KAAEA,GAAE,QAAQ,aAAY,IAAI,EAAE,QAAQ,SAAQ,GAAG,EAAE,QAAQ,eAAc,GAAG,EAAE,QAAQ,gBAAe,GAAG,KAAG,OAAK;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASD,IAAE;AAAC,iBAAM,CAAC,KAAK,QAAQ,WAASA,GAAE,WAAS,OAAOA,GAAE,SAAQA,GAAE,YAAU,KAAK,QAAQ,WAAU,KAAK,GAAG,OAAOA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,eAAK,kBAAkB,GAAE,KAAK,uBAAuB,GAAE,KAAK,sBAAsB,GAAE,KAAK,uBAAuB,GAAE,KAAK,mBAAiB,CAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,iBAAO,KAAK;AAAA,QAAgB,EAAC,GAAE,EAAC,KAAI,mBAAkB,OAAM,SAASA,IAAE;AAAC,iBAAO,OAAO,UAAU,eAAe,KAAK,KAAK,kBAAiBA,EAAC,MAAI,KAAK,iBAAiBA,EAAC,IAAE,EAAE,gBAAgBA,EAAC,KAAG,EAAE,SAAS,KAAK,QAAQ,sBAAqBA,EAAC,IAAG,KAAK,iBAAiBA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,uBAAsB,OAAM,SAASA,IAAE;AAAC,iBAAM,QAAMA,KAAEA,GAAE,YAAY,MAAI,KAAK,gBAAgBA,EAAC,KAAG,OAAK,EAAE,oBAAoB,QAAQA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,iBAAO,KAAK,GAAG,YAAY,EAAC,WAAU,gBAAcJ,IAAE,UAAS,CAAC,KAAK,OAAO,EAAC,WAAU,6BAA4B,UAAS,KAAK,GAAG,KAAK,KAAK,QAAQ,MAAM,OAAK,oBAAoB,GAAE,SAAQC,IAAE,OAAM,SAASD,IAAE;AAAC,gBAAIC,KAAE,EAAE,EAAED,GAAE,aAAa;AAAE,YAAAE,MAAGC,KAAEC,GAAE,QAAQ,OAAO,gBAAe,EAAC,WAAUH,GAAE,KAAK,gBAAgB,GAAE,WAAUA,GAAE,KAAK,gBAAgB,EAAC,CAAC,IAAEC,KAAEE,GAAE,QAAQ,OAAO,gBAAe,EAAC,WAAUH,GAAE,KAAK,gBAAgB,EAAC,CAAC,IAAEE,MAAGC,GAAE,QAAQ,OAAO,gBAAe,EAAC,WAAUH,GAAE,KAAK,gBAAgB,EAAC,CAAC;AAAA,UAAC,GAAE,UAAS,SAASD,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAK,oBAAoB;AAAE,YAAAE,OAAID,GAAE,IAAI,oBAAmBG,GAAE,QAAQ,YAAY,SAAS,GAAEJ,GAAE,KAAK,kBAAiBI,GAAE,QAAQ,YAAY,SAAS,IAAGD,MAAGF,GAAE,IAAI,SAAQG,GAAE,QAAQ,YAAY,SAAS,GAAEJ,GAAE,KAAK,kBAAiBI,GAAE,QAAQ,YAAY,SAAS,KAAGH,GAAE,IAAI,SAAQ,aAAa;AAAA,UAAC,EAAC,CAAC,GAAE,KAAK,OAAO,EAAC,WAAU,mBAAkB,UAAS,KAAK,GAAG,uBAAuB,IAAG,KAAK,OAAO,GAAE,SAAQ,KAAK,KAAK,MAAM,MAAK,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAE,KAAK,GAAG,SAAS,EAAC,QAAOC,KAAE,CAAC,8BAA6B,qCAAmC,KAAK,KAAK,MAAM,aAAW,UAAS,SAAQ,6HAA4H,KAAK,KAAK,MAAM,aAAY,aAAY,UAAS,8EAAmF,SAAQ,oIAAkI,KAAK,QAAQ,KAAG,MAAK,KAAK,KAAK,MAAM,UAAS,aAAY,6CAA2C,KAAK,QAAQ,KAAG,qDAAmD,KAAK,QAAQ,YAAY,YAAU,oCAAkC,KAAK,QAAQ,KAAG,MAAK,UAAS,0DAAwD,KAAK,QAAQ,KAAG,mCAAkC,QAAQ,EAAE,KAAK,EAAE,IAAE,OAAKC,KAAE,CAAC,8BAA6B,qCAAmC,KAAK,KAAK,MAAM,aAAW,UAAS,SAAQ,8HAA6H,KAAK,KAAK,MAAM,gBAAe,aAAY,UAAS,8EAAmF,SAAQ,oIAAkI,KAAK,QAAQ,KAAG,MAAK,KAAK,KAAK,MAAM,UAAS,aAAY,6CAA2C,KAAK,QAAQ,KAAG,qDAAmD,KAAK,QAAQ,YAAY,YAAU,oCAAkC,KAAK,QAAQ,KAAG,MAAK,UAAS,0DAAwD,KAAK,QAAQ,KAAG,mCAAkC,QAAQ,EAAE,KAAK,EAAE,IAAE,KAAI,UAAS,SAASH,IAAE;AAAC,YAAAA,GAAE,KAAK,cAAc,EAAE,KAAM,SAASA,IAAEC,IAAE;AAAC,kBAAIC,KAAE,EAAE,EAAED,EAAC;AAAE,cAAAC,GAAE,OAAOE,GAAE,GAAG,QAAQ,EAAC,QAAOA,GAAE,QAAQ,QAAO,YAAWA,GAAE,QAAQ,YAAW,WAAUF,GAAE,KAAK,OAAO,GAAE,WAAUE,GAAE,QAAQ,WAAU,SAAQA,GAAE,QAAQ,QAAO,CAAC,EAAE,OAAO,CAAC;AAAA,YAAC,CAAE;AAAE,gBAAIH,KAAE,CAAC,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,CAAC;AAAE,YAAAD,GAAE,KAAK,qBAAqB,EAAE,KAAM,SAASA,IAAEE,IAAE;AAAC,kBAAIC,KAAE,EAAE,EAAED,EAAC;AAAE,cAAAC,GAAE,OAAOC,GAAE,GAAG,QAAQ,EAAC,QAAOH,IAAE,YAAWA,IAAE,WAAUE,GAAE,KAAK,OAAO,GAAE,WAAUC,GAAE,QAAQ,WAAU,SAAQA,GAAE,QAAQ,QAAO,CAAC,EAAE,OAAO,CAAC;AAAA,YAAC,CAAE,GAAEJ,GAAE,KAAK,mBAAmB,EAAE,KAAM,SAASC,IAAEC,IAAE;AAAC,gBAAE,EAAEA,EAAC,EAAE,GAAG,UAAU,WAAU;AAAC,oBAAID,KAAED,GAAE,KAAK,MAAI,EAAE,EAAE,IAAI,EAAE,KAAK,OAAO,CAAC,EAAE,KAAK,iBAAiB,EAAE,MAAM,GAAEE,KAAE,KAAK,MAAM,YAAY;AAAE,gBAAAD,GAAE,IAAI,oBAAmBC,EAAC,EAAE,KAAK,cAAaA,EAAC,EAAE,KAAK,cAAaA,EAAC,EAAE,KAAK,uBAAsBA,EAAC,GAAED,GAAE,QAAQ,OAAO;AAAA,cAAC,CAAE;AAAA,YAAC,CAAE;AAAA,UAAC,GAAE,OAAM,SAASA,IAAE;AAAC,YAAAA,GAAE,gBAAgB;AAAE,gBAAIC,KAAE,EAAE,EAAE,MAAIF,EAAC,EAAE,KAAK,qBAAqB,GAAEG,KAAE,EAAE,EAAEF,GAAE,MAAM,GAAEK,KAAEH,GAAE,KAAK,OAAO,GAAEI,KAAEJ,GAAE,KAAK,YAAY;AAAE,gBAAG,kBAAgBG,IAAE;AAAC,kBAAIE,KAAEN,GAAE,KAAK,MAAIK,EAAC,GAAEE,KAAE,EAAE,EAAEP,GAAE,KAAK,MAAIM,GAAE,KAAK,OAAO,CAAC,EAAE,KAAK,iBAAiB,EAAE,CAAC,CAAC,GAAEE,KAAED,GAAE,KAAK,iBAAiB,EAAE,KAAK,EAAE,OAAO,GAAEE,KAAEH,GAAE,IAAI;AAAE,cAAAE,GAAE,IAAI,oBAAmBC,EAAC,EAAE,KAAK,cAAaA,EAAC,EAAE,KAAK,cAAaA,EAAC,EAAE,KAAK,uBAAsBA,EAAC,GAAEF,GAAE,QAAQC,EAAC,GAAEF,GAAE,QAAQ,OAAO;AAAA,YAAC,OAAK;AAAC,kBAAG,EAAE,SAAS,CAAC,aAAY,WAAW,GAAEF,EAAC,GAAE;AAAC,oBAAIM,KAAE,gBAAcN,KAAE,qBAAmB,SAAQO,KAAEV,GAAE,QAAQ,aAAa,EAAE,KAAK,oBAAoB,GAAEW,KAAEX,GAAE,QAAQ,aAAa,EAAE,KAAK,4BAA4B;AAAE,gBAAAU,GAAE,IAAID,IAAEL,EAAC,GAAEO,GAAE,KAAK,UAAQR,IAAEC,EAAC;AAAA,cAAC;AAAC,cAAAH,GAAE,QAAQ,OAAO,YAAUE,IAAEC,EAAC;AAAA,YAAC;AAAA,UAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAE,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,qBAAoB,OAAM,WAAU;AAAC,cAAIP,KAAE;AAAK,eAAK,QAAQ,KAAK,gBAAgB,WAAU;AAAC,mBAAOA,GAAE,GAAG,YAAY,CAACA,GAAE,OAAO,EAAC,WAAU,mBAAkB,UAASA,GAAE,GAAG,uBAAuBA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,KAAK,GAAEA,GAAE,OAAO,GAAE,SAAQA,GAAE,KAAK,MAAM,OAAM,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAEA,GAAE,GAAG,SAAS,EAAC,WAAU,kBAAiB,OAAMA,GAAE,QAAQ,WAAU,OAAMA,GAAE,KAAK,MAAM,OAAM,UAAS,SAASC,IAAE;AAAC,0BAAU,OAAOA,OAAIA,KAAE,EAAC,KAAIA,IAAE,OAAM,OAAO,UAAU,eAAe,KAAKD,GAAE,KAAK,OAAMC,EAAC,IAAED,GAAE,KAAK,MAAMC,EAAC,IAAEA,GAAC;AAAG,kBAAIC,KAAED,GAAE,KAAIE,KAAEF,GAAE;AAAM,qBAAM,MAAIC,MAAGD,GAAE,QAAM,aAAWA,GAAE,QAAM,OAAK,OAAKA,GAAE,YAAU,aAAWA,GAAE,YAAU,MAAI,MAAI,MAAIE,KAAE,OAAKD,KAAE;AAAA,YAAG,GAAE,OAAMF,GAAE,QAAQ,oBAAoB,oBAAoB,EAAC,CAAC,CAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE;AAAE,mBAAQC,KAAE,WAAU;AAAC,gBAAIA,KAAED,GAAE,QAAQ,UAAUE,EAAC;AAAE,YAAAF,GAAE,QAAQ,KAAK,kBAAgBC,IAAG,WAAU;AAAC,qBAAOD,GAAE,OAAO,EAAC,WAAU,oBAAkBC,IAAE,UAAS,sBAAoBA,KAAE,OAAKA,GAAE,YAAY,IAAE,UAAS,SAAQD,GAAE,KAAK,MAAMC,EAAC,GAAE,OAAMD,GAAE,QAAQ,oBAAoB,oBAAoB,EAAC,CAAC,EAAE,OAAO;AAAA,YAAC,CAAE;AAAA,UAAC,GAAEE,KAAE,GAAEC,KAAE,KAAK,QAAQ,UAAU,QAAOD,KAAEC,IAAED,KAAI,CAAAD,GAAE;AAAE,eAAK,QAAQ,KAAK,eAAe,WAAU;AAAC,mBAAOD,GAAE,OAAO,EAAC,WAAU,iBAAgB,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,IAAI,GAAE,SAAQA,GAAE,KAAK,KAAK,OAAKA,GAAE,kBAAkB,MAAM,GAAE,OAAMA,GAAE,QAAQ,kCAAkC,aAAa,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,iBAAiB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,mBAAkB,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,MAAM,GAAE,SAAQA,GAAE,KAAK,KAAK,SAAOA,GAAE,kBAAkB,QAAQ,GAAE,OAAMA,GAAE,QAAQ,kCAAkC,eAAe,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,oBAAoB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,sBAAqB,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,SAAS,GAAE,SAAQA,GAAE,KAAK,KAAK,YAAUA,GAAE,kBAAkB,WAAW,GAAE,OAAMA,GAAE,QAAQ,kCAAkC,kBAAkB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,gBAAgB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,MAAM,GAAE,SAAQA,GAAE,KAAK,KAAK,QAAMA,GAAE,kBAAkB,cAAc,GAAE,OAAMA,GAAE,QAAQ,oBAAoB,qBAAqB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,wBAAwB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,0BAAyB,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,aAAa,GAAE,SAAQA,GAAE,KAAK,KAAK,gBAAcA,GAAE,kBAAkB,eAAe,GAAE,OAAMA,GAAE,QAAQ,kCAAkC,sBAAsB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,sBAAsB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,wBAAuB,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,WAAW,GAAE,SAAQA,GAAE,KAAK,KAAK,aAAY,OAAMA,GAAE,QAAQ,kCAAkC,oBAAoB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,oBAAoB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,sBAAqB,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,SAAS,GAAE,SAAQA,GAAE,KAAK,KAAK,WAAU,OAAMA,GAAE,QAAQ,kCAAkC,kBAAkB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,mBAAmB,WAAU;AAAC,gBAAIC,KAAED,GAAE,QAAQ,OAAO,qBAAqB;AAAE,mBAAOA,GAAE,QAAQ,mBAAiB,EAAE,EAAE,KAAKC,GAAE,aAAa,EAAE,MAAM,GAAG,GAAG,SAASA,IAAEC,IAAE;AAAC,cAAAA,KAAEA,GAAE,KAAK,EAAE,QAAQ,UAAS,EAAE,GAAEF,GAAE,oBAAoBE,EAAC,KAAG,OAAKF,GAAE,QAAQ,UAAU,QAAQE,EAAC,KAAGF,GAAE,QAAQ,UAAU,KAAKE,EAAC;AAAA,YAAC,CAAE,GAAEF,GAAE,GAAG,YAAY,CAACA,GAAE,OAAO,EAAC,WAAU,mBAAkB,UAASA,GAAE,GAAG,uBAAuB,+CAA8CA,GAAE,OAAO,GAAE,SAAQA,GAAE,KAAK,KAAK,MAAK,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAEA,GAAE,GAAG,cAAc,EAAC,WAAU,qBAAoB,gBAAeA,GAAE,QAAQ,MAAM,WAAU,OAAMA,GAAE,QAAQ,UAAU,OAAOA,GAAE,gBAAgB,KAAKA,EAAC,CAAC,GAAE,OAAMA,GAAE,KAAK,KAAK,MAAK,UAAS,SAASA,IAAE;AAAC,qBAAM,+BAA6B,EAAE,cAAcA,EAAC,IAAE,OAAKA,KAAE;AAAA,YAAS,GAAE,OAAMA,GAAE,QAAQ,kCAAkC,iBAAiB,EAAC,CAAC,CAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,mBAAmB,WAAU;AAAC,mBAAOA,GAAE,GAAG,YAAY,CAACA,GAAE,OAAO,EAAC,WAAU,mBAAkB,UAASA,GAAE,GAAG,uBAAuB,+CAA8CA,GAAE,OAAO,GAAE,SAAQA,GAAE,KAAK,KAAK,MAAK,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAEA,GAAE,GAAG,cAAc,EAAC,WAAU,qBAAoB,gBAAeA,GAAE,QAAQ,MAAM,WAAU,OAAMA,GAAE,QAAQ,WAAU,OAAMA,GAAE,KAAK,KAAK,MAAK,OAAMA,GAAE,QAAQ,kCAAkC,iBAAiB,EAAC,CAAC,CAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,uBAAuB,WAAU;AAAC,mBAAOA,GAAE,GAAG,YAAY,CAACA,GAAE,OAAO,EAAC,WAAU,mBAAkB,UAASA,GAAE,GAAG,uBAAuB,mDAAkDA,GAAE,OAAO,GAAE,SAAQA,GAAE,KAAK,KAAK,UAAS,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAEA,GAAE,GAAG,cAAc,EAAC,WAAU,yBAAwB,gBAAeA,GAAE,QAAQ,MAAM,WAAU,OAAMA,GAAE,QAAQ,eAAc,OAAMA,GAAE,KAAK,KAAK,UAAS,OAAMA,GAAE,QAAQ,kCAAkC,qBAAqB,EAAC,CAAC,CAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,gBAAgB,WAAU;AAAC,mBAAOA,GAAE,aAAa,kBAAiBA,GAAE,KAAK,MAAM,QAAO,MAAG,IAAE;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,oBAAoB,WAAU;AAAC,mBAAOA,GAAE,aAAa,mBAAkBA,GAAE,KAAK,MAAM,YAAW,OAAG,IAAE;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,oBAAoB,WAAU;AAAC,mBAAOA,GAAE,aAAa,mBAAkBA,GAAE,KAAK,MAAM,YAAW,MAAG,KAAE;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,aAAa,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,aAAa,GAAE,SAAQA,GAAE,KAAK,MAAM,YAAUA,GAAE,kBAAkB,qBAAqB,GAAE,OAAMA,GAAE,QAAQ,oBAAoB,4BAA4B,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,aAAa,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,WAAW,GAAE,SAAQA,GAAE,KAAK,MAAM,UAAQA,GAAE,kBAAkB,mBAAmB,GAAE,OAAMA,GAAE,QAAQ,oBAAoB,0BAA0B,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE;AAAE,cAAII,KAAE,KAAK,OAAO,EAAC,UAAS,KAAK,GAAG,KAAK,KAAK,QAAQ,MAAM,SAAS,GAAE,SAAQ,KAAK,KAAK,UAAU,OAAK,KAAK,kBAAkB,aAAa,GAAE,OAAM,KAAK,QAAQ,oBAAoB,oBAAoB,EAAC,CAAC,GAAEE,KAAE,KAAK,OAAO,EAAC,UAAS,KAAK,GAAG,KAAK,KAAK,QAAQ,MAAM,WAAW,GAAE,SAAQ,KAAK,KAAK,UAAU,SAAO,KAAK,kBAAkB,eAAe,GAAE,OAAM,KAAK,QAAQ,oBAAoB,sBAAsB,EAAC,CAAC,GAAEC,KAAE,KAAK,OAAO,EAAC,UAAS,KAAK,GAAG,KAAK,KAAK,QAAQ,MAAM,UAAU,GAAE,SAAQ,KAAK,KAAK,UAAU,QAAM,KAAK,kBAAkB,cAAc,GAAE,OAAM,KAAK,QAAQ,oBAAoB,qBAAqB,EAAC,CAAC,GAAEC,KAAE,KAAK,OAAO,EAAC,UAAS,KAAK,GAAG,KAAK,KAAK,QAAQ,MAAM,YAAY,GAAE,SAAQ,KAAK,KAAK,UAAU,UAAQ,KAAK,kBAAkB,aAAa,GAAE,OAAM,KAAK,QAAQ,oBAAoB,oBAAoB,EAAC,CAAC,GAAEC,KAAE,KAAK,OAAO,EAAC,UAAS,KAAK,GAAG,KAAK,KAAK,QAAQ,MAAM,OAAO,GAAE,SAAQ,KAAK,KAAK,UAAU,UAAQ,KAAK,kBAAkB,SAAS,GAAE,OAAM,KAAK,QAAQ,oBAAoB,gBAAgB,EAAC,CAAC,GAAEC,KAAE,KAAK,OAAO,EAAC,UAAS,KAAK,GAAG,KAAK,KAAK,QAAQ,MAAM,MAAM,GAAE,SAAQ,KAAK,KAAK,UAAU,SAAO,KAAK,kBAAkB,QAAQ,GAAE,OAAM,KAAK,QAAQ,oBAAoB,eAAe,EAAC,CAAC;AAAE,eAAK,QAAQ,KAAK,sBAAqB,EAAE,OAAON,IAAE,QAAQ,CAAC,GAAE,KAAK,QAAQ,KAAK,wBAAuB,EAAE,OAAOE,IAAE,QAAQ,CAAC,GAAE,KAAK,QAAQ,KAAK,uBAAsB,EAAE,OAAOC,IAAE,QAAQ,CAAC,GAAE,KAAK,QAAQ,KAAK,sBAAqB,EAAE,OAAOC,IAAE,QAAQ,CAAC,GAAE,KAAK,QAAQ,KAAK,kBAAiB,EAAE,OAAOC,IAAE,QAAQ,CAAC,GAAE,KAAK,QAAQ,KAAK,iBAAgB,EAAE,OAAOC,IAAE,QAAQ,CAAC,GAAE,KAAK,QAAQ,KAAK,oBAAoB,WAAU;AAAC,mBAAOV,GAAE,GAAG,YAAY,CAACA,GAAE,OAAO,EAAC,WAAU,mBAAkB,UAASA,GAAE,GAAG,uBAAuBA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,SAAS,GAAEA,GAAE,OAAO,GAAE,SAAQA,GAAE,KAAK,UAAU,WAAU,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAEA,GAAE,GAAG,SAAS,CAACA,GAAE,GAAG,YAAY,EAAC,WAAU,cAAa,UAAS,CAACI,IAAEE,IAAEC,IAAEC,EAAC,EAAC,CAAC,GAAER,GAAE,GAAG,YAAY,EAAC,WAAU,aAAY,UAAS,CAACS,IAAEC,EAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,iBAAiB,WAAU;AAAC,mBAAOV,GAAE,GAAG,YAAY,CAACA,GAAE,OAAO,EAAC,WAAU,mBAAkB,UAASA,GAAE,GAAG,uBAAuBA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,UAAU,GAAEA,GAAE,OAAO,GAAE,SAAQA,GAAE,KAAK,KAAK,QAAO,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAEA,GAAE,GAAG,cAAc,EAAC,OAAMA,GAAE,QAAQ,aAAY,gBAAeA,GAAE,QAAQ,MAAM,WAAU,WAAU,wBAAuB,OAAMA,GAAE,KAAK,KAAK,QAAO,OAAMA,GAAE,QAAQ,oBAAoB,mBAAmB,EAAC,CAAC,CAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,gBAAgB,WAAU;AAAC,mBAAOA,GAAE,GAAG,YAAY,CAACA,GAAE,OAAO,EAAC,WAAU,mBAAkB,UAASA,GAAE,GAAG,uBAAuBA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,KAAK,GAAEA,GAAE,OAAO,GAAE,SAAQA,GAAE,KAAK,MAAM,OAAM,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAEA,GAAE,GAAG,SAAS,EAAC,OAAMA,GAAE,KAAK,MAAM,OAAM,WAAU,cAAa,OAAM,CAAC,uCAAsC,oGAAmG,yDAAwD,2DAA0D,UAAS,iDAAiD,EAAE,KAAK,EAAE,EAAC,CAAC,CAAC,GAAE,EAAC,UAAS,SAASC,IAAE;AAAC,cAAAA,GAAE,KAAK,qCAAqC,EAAE,IAAI,EAAC,OAAMD,GAAE,QAAQ,mBAAmB,MAAI,MAAK,QAAOA,GAAE,QAAQ,mBAAmB,MAAI,KAAI,CAAC,EAAE,GAAG,aAAYA,GAAE,QAAQ,oBAAoB,oBAAoB,CAAC,EAAE,GAAG,aAAYA,GAAE,iBAAiB,KAAKA,EAAC,CAAC;AAAA,YAAC,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,eAAe,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,IAAI,GAAE,SAAQA,GAAE,KAAK,KAAK,OAAKA,GAAE,kBAAkB,iBAAiB,GAAE,OAAMA,GAAE,QAAQ,oBAAoB,iBAAiB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,kBAAkB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,OAAO,GAAE,SAAQA,GAAE,KAAK,MAAM,OAAM,OAAMA,GAAE,QAAQ,oBAAoB,kBAAkB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,gBAAgB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,KAAK,GAAE,SAAQA,GAAE,KAAK,MAAM,OAAM,OAAMA,GAAE,QAAQ,oBAAoB,kBAAkB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,aAAa,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,KAAK,GAAE,SAAQA,GAAE,KAAK,GAAG,SAAOA,GAAE,kBAAkB,sBAAsB,GAAE,OAAMA,GAAE,QAAQ,oBAAoB,6BAA6B,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,qBAAqB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,qCAAoC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,SAAS,GAAE,SAAQA,GAAE,KAAK,QAAQ,YAAW,OAAMA,GAAE,QAAQ,oBAAoB,mBAAmB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,mBAAmB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,mCAAkC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,IAAI,GAAE,SAAQA,GAAE,KAAK,QAAQ,UAAS,OAAMA,GAAE,QAAQ,oBAAoB,iBAAiB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,eAAe,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,IAAI,GAAE,SAAQA,GAAE,KAAK,QAAQ,OAAKA,GAAE,kBAAkB,MAAM,GAAE,OAAMA,GAAE,QAAQ,oBAAoB,aAAa,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,eAAe,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,IAAI,GAAE,SAAQA,GAAE,KAAK,QAAQ,OAAKA,GAAE,kBAAkB,MAAM,GAAE,OAAMA,GAAE,QAAQ,oBAAoB,aAAa,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,eAAe,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,QAAQ,GAAE,SAAQA,GAAE,KAAK,QAAQ,MAAK,OAAMA,GAAE,QAAQ,oBAAoB,iBAAiB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,0BAAyB,OAAM,WAAU;AAAC,cAAIA,KAAE;AAAK,eAAK,QAAQ,KAAK,qBAAqB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAAS,8CAA6C,SAAQA,GAAE,KAAK,MAAM,YAAW,OAAMA,GAAE,QAAQ,oBAAoB,iBAAgB,GAAG,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,qBAAqB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAAS,6CAA4C,SAAQA,GAAE,KAAK,MAAM,YAAW,OAAMA,GAAE,QAAQ,oBAAoB,iBAAgB,KAAK,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,wBAAwB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAAS,6CAA4C,SAAQA,GAAE,KAAK,MAAM,eAAc,OAAMA,GAAE,QAAQ,oBAAoB,iBAAgB,MAAM,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,qBAAqB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,QAAQ,GAAE,SAAQA,GAAE,KAAK,MAAM,YAAW,OAAMA,GAAE,QAAQ,oBAAoB,iBAAgB,GAAG,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,oBAAoB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,SAAS,GAAE,SAAQA,GAAE,KAAK,MAAM,WAAU,OAAMA,GAAE,QAAQ,oBAAoB,kBAAiB,MAAM,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,qBAAqB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,UAAU,GAAE,SAAQA,GAAE,KAAK,MAAM,YAAW,OAAMA,GAAE,QAAQ,oBAAoB,kBAAiB,OAAO,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,oBAAoB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,QAAQ,GAAE,SAAQA,GAAE,KAAK,MAAM,WAAU,OAAMA,GAAE,QAAQ,oBAAoB,kBAAiB,MAAM,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,sBAAsB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,KAAK,GAAE,SAAQA,GAAE,KAAK,MAAM,QAAO,OAAMA,GAAE,QAAQ,oBAAoB,oBAAoB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,yBAAwB,OAAM,WAAU;AAAC,cAAIA,KAAE;AAAK,eAAK,QAAQ,KAAK,yBAAyB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,IAAI,GAAE,SAAQA,GAAE,KAAK,KAAK,MAAK,OAAMA,GAAE,QAAQ,oBAAoB,iBAAiB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,iBAAiB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,MAAM,GAAE,SAAQA,GAAE,KAAK,KAAK,QAAO,OAAMA,GAAE,QAAQ,oBAAoB,eAAe,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,0BAAyB,OAAM,WAAU;AAAC,cAAIA,KAAE;AAAK,eAAK,QAAQ,KAAK,mBAAmB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,UAAS,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,QAAQ,GAAE,SAAQA,GAAE,KAAK,MAAM,aAAY,OAAMA,GAAE,QAAQ,oBAAoB,iBAAgB,KAAK,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,qBAAqB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,UAAS,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,QAAQ,GAAE,SAAQA,GAAE,KAAK,MAAM,aAAY,OAAMA,GAAE,QAAQ,oBAAoB,iBAAgB,QAAQ,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,qBAAqB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,UAAS,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,SAAS,GAAE,SAAQA,GAAE,KAAK,MAAM,YAAW,OAAMA,GAAE,QAAQ,oBAAoB,iBAAgB,MAAM,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,sBAAsB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,UAAS,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,QAAQ,GAAE,SAAQA,GAAE,KAAK,MAAM,aAAY,OAAMA,GAAE,QAAQ,oBAAoB,iBAAgB,OAAO,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,oBAAoB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,UAAS,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,SAAS,GAAE,SAAQA,GAAE,KAAK,MAAM,QAAO,OAAMA,GAAE,QAAQ,oBAAoB,kBAAkB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,oBAAoB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,UAAS,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,SAAS,GAAE,SAAQA,GAAE,KAAK,MAAM,QAAO,OAAMA,GAAE,QAAQ,oBAAoB,kBAAkB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,KAAK,sBAAsB,WAAU;AAAC,mBAAOA,GAAE,OAAO,EAAC,WAAU,UAAS,UAASA,GAAE,GAAG,KAAKA,GAAE,QAAQ,MAAM,KAAK,GAAE,SAAQA,GAAE,KAAK,MAAM,UAAS,OAAMA,GAAE,QAAQ,oBAAoB,oBAAoB,EAAC,CAAC,EAAE,OAAO;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,SAASA,IAAEC,IAAE;AAAC,mBAAQC,KAAE,GAAEC,KAAEF,GAAE,QAAOC,KAAEC,IAAED,MAAI;AAAC,qBAAQE,KAAEH,GAAEC,EAAC,GAAEG,KAAE,MAAM,QAAQD,EAAC,IAAEA,GAAE,CAAC,IAAEA,IAAEE,KAAE,MAAM,QAAQF,EAAC,IAAE,MAAIA,GAAE,SAAO,CAACA,GAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,IAAE,CAACA,EAAC,GAAEG,KAAE,KAAK,GAAG,YAAY,EAAC,WAAU,UAAQF,GAAC,CAAC,EAAE,OAAO,GAAEG,KAAE,GAAEC,KAAEH,GAAE,QAAOE,KAAEC,IAAED,MAAI;AAAC,kBAAIE,KAAE,KAAK,QAAQ,KAAK,YAAUJ,GAAEE,EAAC,CAAC;AAAE,cAAAE,MAAGH,GAAE,OAAO,cAAY,OAAOG,KAAEA,GAAE,KAAK,OAAO,IAAEA,EAAC;AAAA,YAAC;AAAC,YAAAH,GAAE,SAASP,EAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,sBAAqB,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAED,MAAG,KAAK,UAASE,KAAE,KAAK,QAAQ,OAAO,qBAAqB;AAAE,cAAG,KAAK,gBAAgBD,IAAE,EAAC,kBAAiB,WAAU;AAAC,mBAAM,WAASC,GAAE,WAAW;AAAA,UAAC,GAAE,oBAAmB,WAAU;AAAC,mBAAM,aAAWA,GAAE,aAAa;AAAA,UAAC,GAAE,uBAAsB,WAAU;AAAC,mBAAM,gBAAcA,GAAE,gBAAgB;AAAA,UAAC,GAAE,uBAAsB,WAAU;AAAC,mBAAM,gBAAcA,GAAE,gBAAgB;AAAA,UAAC,GAAE,yBAAwB,WAAU;AAAC,mBAAM,kBAAgBA,GAAE,kBAAkB;AAAA,UAAC,GAAE,2BAA0B,WAAU;AAAC,mBAAM,oBAAkBA,GAAE,oBAAoB;AAAA,UAAC,EAAC,CAAC,GAAEA,GAAE,aAAa,GAAE;AAAC,gBAAIC,KAAED,GAAE,aAAa,EAAE,MAAM,GAAG,EAAE,IAAK,SAASF,IAAE;AAAC,qBAAOA,GAAE,QAAQ,WAAU,EAAE,EAAE,QAAQ,QAAO,EAAE,EAAE,QAAQ,QAAO,EAAE;AAAA,YAAC,CAAE,GAAEI,KAAE,EAAE,KAAKD,IAAE,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAAE,YAAAF,GAAE,KAAK,sBAAsB,EAAE,KAAM,SAASD,IAAEC,IAAE;AAAC,kBAAIC,KAAE,EAAE,EAAED,EAAC,GAAEE,KAAED,GAAE,KAAK,OAAO,IAAE,MAAIE,KAAE;AAAG,cAAAF,GAAE,YAAY,WAAUC,EAAC;AAAA,YAAC,CAAE,GAAEF,GAAE,KAAK,wBAAwB,EAAE,KAAKG,EAAC,EAAE,IAAI,eAAcA,EAAC;AAAA,UAAC;AAAC,cAAGF,GAAE,WAAW,GAAE;AAAC,gBAAII,KAAEJ,GAAE,WAAW;AAAE,YAAAD,GAAE,KAAK,sBAAsB,EAAE,KAAM,SAASD,IAAEC,IAAE;AAAC,kBAAIC,KAAE,EAAE,EAAED,EAAC,GAAEE,KAAED,GAAE,KAAK,OAAO,IAAE,MAAII,KAAE;AAAG,cAAAJ,GAAE,YAAY,WAAUC,EAAC;AAAA,YAAC,CAAE,GAAEF,GAAE,KAAK,wBAAwB,EAAE,KAAKK,EAAC;AAAE,gBAAIC,KAAEL,GAAE,gBAAgB;AAAE,YAAAD,GAAE,KAAK,0BAA0B,EAAE,KAAM,SAASD,IAAEC,IAAE;AAAC,kBAAIC,KAAE,EAAE,EAAED,EAAC,GAAEE,KAAED,GAAE,KAAK,OAAO,IAAE,MAAIK,KAAE;AAAG,cAAAL,GAAE,YAAY,WAAUC,EAAC;AAAA,YAAC,CAAE,GAAEF,GAAE,KAAK,4BAA4B,EAAE,KAAKM,EAAC;AAAA,UAAC;AAAC,cAAGL,GAAE,aAAa,GAAE;AAAC,gBAAIM,KAAEN,GAAE,aAAa;AAAE,YAAAD,GAAE,KAAK,yBAAyB,EAAE,KAAM,SAASD,IAAEC,IAAE;AAAC,kBAAIC,KAAE,EAAE,EAAED,EAAC,GAAEE,KAAE,EAAE,EAAEF,EAAC,EAAE,KAAK,OAAO,IAAE,MAAIO,KAAE;AAAG,cAAAN,GAAE,YAAY,WAAUC,EAAC;AAAA,YAAC,CAAE,GAAEF,GAAE,KAAK,2BAA2B,EAAE,KAAKO,EAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,mBAAkB,OAAM,SAASR,IAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,YAAE,EAAE,KAAKD,IAAG,SAASA,IAAEE,IAAE;AAAC,YAAAD,GAAE,GAAG,gBAAgBF,GAAE,KAAKC,EAAC,GAAEE,GAAE,CAAC;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,SAASH,IAAE;AAAC,cAAIC,IAAEC,KAAE,EAAE,EAAEF,GAAE,OAAO,UAAU,GAAEG,KAAED,GAAE,KAAK,GAAEE,KAAEF,GAAE,KAAK,qCAAqC,GAAEI,KAAEJ,GAAE,KAAK,oCAAoC,GAAEK,KAAEL,GAAE,KAAK,sCAAsC;AAAE,cAAG,WAASF,GAAE,SAAQ;AAAC,gBAAIQ,KAAE,EAAE,EAAER,GAAE,MAAM,EAAE,OAAO;AAAE,YAAAC,KAAE,EAAC,GAAED,GAAE,QAAMQ,GAAE,MAAK,GAAER,GAAE,QAAMQ,GAAE,IAAG;AAAA,UAAC,MAAM,CAAAP,KAAE,EAAC,GAAED,GAAE,SAAQ,GAAEA,GAAE,QAAO;AAAE,cAAIS,KAAE,KAAK,KAAKR,GAAE,IAAE,EAAE,KAAG,GAAES,KAAE,KAAK,KAAKT,GAAE,IAAE,EAAE,KAAG;AAAE,UAAAK,GAAE,IAAI,EAAC,OAAMG,KAAE,MAAK,QAAOC,KAAE,KAAI,CAAC,GAAEN,GAAE,KAAK,SAAQK,KAAE,MAAIC,EAAC,GAAED,KAAE,KAAGA,KAAE,KAAK,QAAQ,mBAAmB,OAAKF,GAAE,IAAI,EAAC,OAAME,KAAE,IAAE,KAAI,CAAC,GAAEC,KAAE,KAAGA,KAAE,KAAK,QAAQ,mBAAmB,OAAKH,GAAE,IAAI,EAAC,QAAOG,KAAE,IAAE,KAAI,CAAC,GAAEP,GAAE,KAAKM,KAAE,QAAMC,EAAC;AAAA,QAAC,EAAC,CAAC,GAAET,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,UAAQ,EAAE,EAAE,MAAM,GAAE,KAAK,YAAU,EAAE,EAAE,QAAQ,GAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,QAAMA,GAAE,WAAW,MAAK,KAAK,UAAQA,GAAE,WAAW,QAAO,KAAK,WAASA,GAAE,WAAW,SAAQ,KAAK,YAAUA,GAAE,WAAW,UAAS,KAAK,aAAWA,GAAE,WAAW,WAAU,KAAK,UAAQA,GAAE,SAAQ,KAAK,cAAY,OAAG,KAAK,eAAa,KAAK,aAAa,KAAK,IAAI;AAAA,QAAC,IAAGA,KAAE,CAAC,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,iBAAM,CAAC,KAAK,QAAQ;AAAA,QAAO,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,QAAQ,UAAQ,KAAK,QAAQ,WAAS,CAAC,GAAE,KAAK,QAAQ,QAAQ,SAAO,KAAK,QAAQ,OAAO,iBAAgB,KAAK,UAAS,KAAK,QAAQ,OAAO,IAAE,KAAK,SAAS,KAAK,GAAE,KAAK,QAAQ,oBAAkB,KAAK,SAAS,SAAS,KAAK,QAAQ,gBAAgB,GAAE,KAAK,gBAAgB,KAAE,GAAE,KAAK,MAAM,GAAG,yDAAyD,WAAU;AAAC,YAAAA,GAAE,QAAQ,OAAO,4BAA4B;AAAA,UAAC,CAAE,GAAE,KAAK,QAAQ,OAAO,4BAA4B,GAAE,KAAK,QAAQ,oBAAkB,KAAK,QAAQ,GAAG,iBAAgB,KAAK,YAAY;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,SAAS,SAAS,EAAE,OAAO,GAAE,KAAK,QAAQ,oBAAkB,KAAK,QAAQ,IAAI,iBAAgB,KAAK,YAAY;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,WAAU;AAAC,cAAG,KAAK,QAAQ,SAAS,YAAY,EAAE,QAAM;AAAG,cAAIA,KAAE,KAAK,QAAQ,YAAY,GAAEC,KAAE,KAAK,QAAQ,MAAM,GAAEC,KAAE,KAAK,SAAS,OAAO,GAAEC,KAAE,KAAK,WAAW,OAAO,GAAEC,KAAE;AAAE,eAAK,QAAQ,mBAAiBA,KAAE,EAAE,EAAE,KAAK,QAAQ,cAAc,EAAE,YAAY;AAAG,cAAIE,KAAE,KAAK,UAAU,UAAU,GAAEC,KAAE,KAAK,QAAQ,OAAO,EAAE,KAAIC,KAAED,KAAEH,IAAEK,KAAEF,KAAEP,KAAEI,KAAEF,KAAEC;AAAE,WAAC,KAAK,eAAaG,KAAEE,MAAGF,KAAEG,KAAEP,MAAG,KAAK,cAAY,MAAG,KAAK,UAAU,IAAI,EAAC,WAAU,KAAK,SAAS,YAAY,EAAC,CAAC,GAAE,KAAK,SAAS,IAAI,EAAC,UAAS,SAAQ,KAAIE,IAAE,OAAMH,IAAE,QAAO,IAAG,CAAC,KAAG,KAAK,gBAAcK,KAAEE,MAAGF,KAAEG,QAAK,KAAK,cAAY,OAAG,KAAK,SAAS,IAAI,EAAC,UAAS,YAAW,KAAI,GAAE,OAAM,QAAO,QAAO,OAAM,CAAC,GAAE,KAAK,UAAU,IAAI,EAAC,WAAU,GAAE,CAAC;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,mBAAkB,OAAM,SAAST,IAAE;AAAC,UAAAA,KAAE,KAAK,SAAS,UAAU,KAAK,OAAO,IAAE,KAAK,QAAQ,oBAAkB,KAAK,SAAS,SAAS,KAAK,QAAQ,gBAAgB,GAAE,KAAK,QAAQ,oBAAkB,KAAK,aAAa;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,SAASA,IAAE;AAAC,eAAK,GAAG,gBAAgB,KAAK,SAAS,KAAK,iBAAiB,GAAEA,EAAC,GAAE,KAAK,gBAAgBA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,kBAAiB,OAAM,SAASA,IAAE;AAAC,eAAK,GAAG,gBAAgB,KAAK,SAAS,KAAK,eAAe,GAAEA,EAAC,GAAEA,KAAE,KAAK,WAAW,IAAE,KAAK,SAAS;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAE,KAAK,SAAS,KAAK,QAAQ;AAAE,UAAAD,OAAIC,KAAEA,GAAE,IAAI,qBAAqB,IAAG,KAAK,GAAG,UAAUA,IAAE,IAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE,KAAK,SAAS,KAAK,QAAQ;AAAE,UAAAD,OAAIC,KAAEA,GAAE,IAAI,qBAAqB,IAAG,KAAK,GAAG,UAAUA,IAAE,KAAE;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,oDAAmD,KAAG,gEAA+D,KAAG,qCAAoC,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,QAAM,EAAE,EAAE,SAAS,IAAI,GAAE,KAAK,UAAQA,GAAE,WAAW,QAAO,KAAK,UAAQA,GAAE,SAAQ,KAAK,OAAK,KAAK,QAAQ,UAASA,GAAE,KAAK,wBAAuB,KAAK,QAAQ,SAAS,KAAK,iBAAiB,CAAC;AAAA,QAAC,IAAGA,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE,KAAK,QAAQ,gBAAc,KAAK,QAAM,KAAK,QAAQ,WAAUC,KAAE,CAAC,4CAA2C,oCAAoC,OAAO,KAAK,QAAQ,IAAG,4BAA4B,EAAE,OAAO,KAAK,KAAK,KAAK,eAAc,UAAU,GAAE,mCAAmC,OAAO,KAAK,QAAQ,IAAG,kFAAkF,GAAE,UAAS,4CAA2C,oCAAoC,OAAO,KAAK,QAAQ,IAAG,4BAA4B,EAAE,OAAO,KAAK,KAAK,KAAK,KAAI,UAAU,GAAE,mCAAmC,OAAO,KAAK,QAAQ,IAAG,iGAAiG,GAAE,UAAS,KAAK,QAAQ,oBAAkB,KAAG,EAAE,EAAE,aAAa,EAAE,OAAO,KAAK,GAAG,SAAS,EAAC,WAAU,kCAAiC,MAAK,KAAK,KAAK,KAAK,iBAAgB,SAAQ,KAAE,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAEC,KAAE,wCAAwC,OAAO,2DAA0D,WAAW,EAAE,OAAO,KAAK,KAAK,KAAK,QAAO,aAAa;AAAE,eAAK,UAAQ,KAAK,GAAG,OAAO,EAAC,WAAU,eAAc,OAAM,KAAK,KAAK,KAAK,QAAO,MAAK,KAAK,QAAQ,aAAY,MAAKD,IAAE,QAAOC,GAAC,CAAC,EAAE,OAAO,EAAE,SAASF,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,GAAG,WAAW,KAAK,OAAO,GAAE,KAAK,QAAQ,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASA,IAAEC,IAAE;AAAC,UAAAD,GAAE,GAAG,YAAY,SAASA,IAAE;AAAC,YAAAA,GAAE,YAAU,GAAG,KAAK,UAAQA,GAAE,eAAe,GAAEC,GAAE,QAAQ,OAAO;AAAA,UAAE,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASD,IAAE;AAAC,iBAAO,GAAG,KAAKA,EAAC,IAAE,cAAYA,KAAE,GAAG,KAAKA,EAAC,IAAE,WAASA,KAAE,GAAG,KAAKA,EAAC,IAAEA,KAAE,YAAUA;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,kBAAiB,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAE;AAAK,UAAAD,GAAE,GAAG,QAAQ,SAASA,IAAE;AAAC,YAAAA,GAAE,OAAO,QAAM,MAAIA,GAAE,OAAO,QAAM,KAAGC,GAAE,aAAaD,GAAE,OAAO,KAAK;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASA,IAAEC,IAAEC,IAAE;AAAC,eAAK,GAAG,UAAUF,IAAEC,GAAE,IAAI,KAAGC,GAAE,IAAI,CAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,kBAAiB,OAAM,SAASF,IAAE;AAAC,cAAIC,KAAE;AAAK,iBAAO,EAAE,EAAE,SAAU,SAASC,IAAE;AAAC,gBAAIC,KAAEF,GAAE,QAAQ,KAAK,iBAAiB,GAAEG,KAAEH,GAAE,QAAQ,KAAK,gBAAgB,GAAEI,KAAEJ,GAAE,QAAQ,KAAK,gBAAgB,GAAEK,KAAEL,GAAE,QAAQ,KAAK,sDAAsD;AAAE,YAAAA,GAAE,GAAG,cAAcA,GAAE,SAAS,WAAU;AAAC,cAAAA,GAAE,QAAQ,aAAa,cAAc,GAAE,CAACD,GAAE,OAAK,EAAE,WAAWA,GAAE,IAAI,MAAIA,GAAE,MAAIC,GAAE,aAAaD,GAAE,IAAI,IAAGG,GAAE,GAAG,8BAA8B,WAAU;AAAC,oBAAID,KAAEC,GAAE,IAAI,GAAEG,KAAE,SAAS,cAAc,KAAK;AAAE,gBAAAA,GAAE,YAAUJ,IAAEA,KAAEI,GAAE,WAAUN,GAAE,OAAKE,IAAED,GAAE,cAAcI,IAAEF,IAAEC,EAAC;AAAA,cAAC,CAAE,EAAE,IAAIJ,GAAE,IAAI,GAAEI,GAAE,GAAG,8BAA8B,WAAU;AAAC,gBAAAJ,GAAE,QAAMG,GAAE,IAAIC,GAAE,IAAI,CAAC,GAAEH,GAAE,cAAcI,IAAEF,IAAEC,EAAC;AAAA,cAAC,CAAE,EAAE,IAAIJ,GAAE,GAAG,GAAE,EAAE,kBAAgBI,GAAE,QAAQ,OAAO,GAAEH,GAAE,cAAcI,IAAEF,IAAEC,EAAC,GAAEH,GAAE,aAAaG,IAAEC,EAAC,GAAEJ,GAAE,aAAaE,IAAEE,EAAC,GAAEJ,GAAE,eAAeG,EAAC;AAAE,kBAAIG,KAAE,WAASP,GAAE,cAAYA,GAAE,cAAYC,GAAE,QAAQ,QAAQ;AAAgB,cAAAK,GAAE,KAAK,WAAUC,EAAC,GAAEF,GAAE,IAAI,SAAS,SAASA,IAAE;AAAC,gBAAAA,GAAE,eAAe,GAAEH,GAAE,QAAQ,EAAC,OAAMF,GAAE,OAAM,KAAII,GAAE,IAAI,GAAE,MAAKD,GAAE,IAAI,GAAE,aAAYG,GAAE,GAAG,UAAU,EAAC,CAAC,GAAEL,GAAE,GAAG,WAAWA,GAAE,OAAO;AAAA,cAAC,CAAE;AAAA,YAAC,CAAE,GAAEA,GAAE,GAAG,eAAeA,GAAE,SAAS,WAAU;AAAC,cAAAE,GAAE,IAAI,GAAEC,GAAE,IAAI,GAAEC,GAAE,IAAI,GAAE,cAAYH,GAAE,MAAM,KAAGA,GAAE,OAAO;AAAA,YAAC,CAAE,GAAED,GAAE,GAAG,WAAWA,GAAE,OAAO;AAAA,UAAC,CAAE,EAAE,QAAQ;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAID,KAAE,MAAKC,KAAE,KAAK,QAAQ,OAAO,oBAAoB;AAAE,eAAK,QAAQ,OAAO,kBAAkB,GAAE,KAAK,eAAeA,EAAC,EAAE,KAAM,SAASA,IAAE;AAAC,YAAAD,GAAE,QAAQ,OAAO,qBAAqB,GAAEA,GAAE,QAAQ,OAAO,qBAAoBC,EAAC;AAAA,UAAC,CAAE,EAAE,KAAM,WAAU;AAAC,YAAAD,GAAE,QAAQ,OAAO,qBAAqB;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,UAAQA,GAAE,SAAQ,KAAK,SAAO,EAAC,2EAA0E,WAAU;AAAC,YAAAC,GAAE,OAAO;AAAA,UAAC,GAAE,8CAA6C,WAAU;AAAC,YAAAA,GAAE,KAAK;AAAA,UAAC,GAAE,mBAAkB,SAASF,IAAEC,IAAE;AAAC,YAAAA,GAAE,iBAAeA,GAAE,cAAc,iBAAeC,GAAE,SAAS,CAAC,EAAE,SAASD,GAAE,cAAc,aAAa,KAAGC,GAAE,KAAK;AAAA,UAAC,EAAC;AAAA,QAAC,IAAGD,KAAE,CAAC,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,iBAAM,CAAC,EAAE,QAAQ,KAAK,QAAQ,QAAQ,IAAI;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,eAAK,WAAS,KAAK,GAAG,QAAQ,EAAC,WAAU,qBAAoB,UAAS,SAASD,IAAE;AAAC,YAAAA,GAAE,KAAK,wCAAwC,EAAE,QAAQ,4CAA4C;AAAA,UAAC,EAAC,CAAC,EAAE,OAAO,EAAE,SAAS,KAAK,QAAQ,SAAS;AAAE,cAAIA,KAAE,KAAK,SAAS,KAAK,wCAAwC;AAAE,eAAK,QAAQ,OAAO,iBAAgBA,IAAE,KAAK,QAAQ,QAAQ,IAAI,GAAE,KAAK,SAAS,GAAG,aAAa,SAASA,IAAE;AAAC,YAAAA,GAAE,eAAe;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,SAAS,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,cAAG,KAAK,QAAQ,OAAO,iBAAiB,GAAE;AAAC,gBAAIA,KAAE,KAAK,QAAQ,OAAO,qBAAqB;AAAE,gBAAGA,GAAE,YAAY,KAAGA,GAAE,WAAW,GAAE;AAAC,kBAAIC,KAAE,GAAG,SAASD,GAAE,IAAG,GAAG,QAAQ,GAAEE,KAAE,EAAE,EAAED,EAAC,EAAE,KAAK,MAAM;AAAE,mBAAK,SAAS,KAAK,GAAG,EAAE,KAAK,QAAOC,EAAC,EAAE,KAAKA,EAAC;AAAE,kBAAIC,KAAE,GAAG,mBAAmBF,EAAC,GAAEG,KAAE,EAAE,EAAE,KAAK,QAAQ,SAAS,EAAE,OAAO;AAAE,cAAAD,GAAE,OAAKC,GAAE,KAAID,GAAE,QAAMC,GAAE,MAAK,KAAK,SAAS,IAAI,EAAC,SAAQ,SAAQ,MAAKD,GAAE,MAAK,KAAIA,GAAE,IAAG,CAAC;AAAA,YAAC,MAAM,MAAK,KAAK;AAAA,UAAC,MAAM,MAAK,KAAK;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,SAAS,KAAK;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGH,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,QAAM,EAAE,EAAE,SAAS,IAAI,GAAE,KAAK,UAAQA,GAAE,WAAW,QAAO,KAAK,UAAQA,GAAE,SAAQ,KAAK,OAAK,KAAK,QAAQ;AAAA,QAAQ,IAAGA,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE;AAAG,cAAG,KAAK,QAAQ,sBAAqB;AAAC,gBAAIC,KAAE,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ,oBAAoB,IAAE,KAAK,IAAI,IAAI,CAAC,GAAEC,KAAE,KAAG,KAAK,QAAQ,uBAAqB,KAAK,IAAI,MAAKD,EAAC,GAAG,QAAQ,CAAC,IAAE,MAAI,SAASA,EAAC,IAAE;AAAI,YAAAD,KAAE,UAAU,OAAO,KAAK,KAAK,MAAM,kBAAgB,QAAME,IAAE,UAAU;AAAA,UAAC;AAAC,cAAIC,KAAE,KAAK,QAAQ,gBAAc,KAAK,QAAM,KAAK,QAAQ,WAAUC,KAAE,CAAC,yEAAwE,wCAAsC,KAAK,QAAQ,KAAG,+BAA6B,KAAK,KAAK,MAAM,kBAAgB,YAAW,uCAAqC,KAAK,QAAQ,KAAG,8EAA6E,uCAAqC,KAAK,QAAQ,uBAAqB,2BAA0BJ,IAAE,UAAS,iDAAgD,uCAAqC,KAAK,QAAQ,KAAG,+BAA6B,KAAK,KAAK,MAAM,MAAI,YAAW,sCAAoC,KAAK,QAAQ,KAAG,oFAAmF,QAAQ,EAAE,KAAK,EAAE,GAAEK,KAAE,wCAAwC,OAAO,4DAA2D,WAAW,EAAE,OAAO,KAAK,KAAK,MAAM,QAAO,aAAa;AAAE,eAAK,UAAQ,KAAK,GAAG,OAAO,EAAC,OAAM,KAAK,KAAK,MAAM,QAAO,MAAK,KAAK,QAAQ,aAAY,MAAKD,IAAE,QAAOC,GAAC,CAAC,EAAE,OAAO,EAAE,SAASF,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,GAAG,WAAW,KAAK,OAAO,GAAE,KAAK,QAAQ,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASH,IAAEC,IAAE;AAAC,UAAAD,GAAE,GAAG,YAAY,SAASA,IAAE;AAAC,YAAAA,GAAE,YAAU,GAAG,KAAK,UAAQA,GAAE,eAAe,GAAEC,GAAE,QAAQ,OAAO;AAAA,UAAE,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,QAAQ,OAAO,kBAAkB,GAAE,KAAK,gBAAgB,EAAE,KAAM,SAASC,IAAE;AAAC,YAAAD,GAAE,GAAG,WAAWA,GAAE,OAAO,GAAEA,GAAE,QAAQ,OAAO,qBAAqB,GAAE,YAAU,OAAOC,KAAED,GAAE,QAAQ,UAAU,oBAAkBA,GAAE,QAAQ,aAAa,qBAAoBC,EAAC,IAAED,GAAE,QAAQ,OAAO,sBAAqBC,EAAC,IAAED,GAAE,QAAQ,OAAO,iCAAgCC,EAAC;AAAA,UAAC,CAAE,EAAE,KAAM,WAAU;AAAC,YAAAD,GAAE,QAAQ,OAAO,qBAAqB;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,mBAAkB,OAAM,WAAU;AAAC,cAAIA,KAAE;AAAK,iBAAO,EAAE,EAAE,SAAU,SAASC,IAAE;AAAC,gBAAIC,KAAEF,GAAE,QAAQ,KAAK,mBAAmB,GAAEG,KAAEH,GAAE,QAAQ,KAAK,iBAAiB,GAAEI,KAAEJ,GAAE,QAAQ,KAAK,iBAAiB;AAAE,YAAAA,GAAE,GAAG,cAAcA,GAAE,SAAS,WAAU;AAAC,cAAAA,GAAE,QAAQ,aAAa,cAAc,GAAEE,GAAE,YAAYA,GAAE,MAAM,EAAE,GAAG,UAAU,SAASF,IAAE;AAAC,gBAAAC,GAAE,QAAQD,GAAE,OAAO,SAAOA,GAAE,OAAO,KAAK;AAAA,cAAC,CAAE,EAAE,IAAI,EAAE,CAAC,GAAEG,GAAE,GAAG,8BAA8B,WAAU;AAAC,gBAAAH,GAAE,GAAG,UAAUI,IAAED,GAAE,IAAI,CAAC;AAAA,cAAC,CAAE,EAAE,IAAI,EAAE,GAAE,EAAE,kBAAgBA,GAAE,QAAQ,OAAO,GAAEC,GAAE,GAAG,SAAS,SAASJ,IAAE;AAAC,gBAAAA,GAAE,eAAe,GAAEC,GAAE,QAAQE,GAAE,IAAI,CAAC;AAAA,cAAC,CAAE,GAAEH,GAAE,aAAaG,IAAEC,EAAC;AAAA,YAAC,CAAE,GAAEJ,GAAE,GAAG,eAAeA,GAAE,SAAS,WAAU;AAAC,cAAAE,GAAE,IAAI,GAAEC,GAAE,IAAI,GAAEC,GAAE,IAAI,GAAE,cAAYH,GAAE,MAAM,KAAGA,GAAE,OAAO;AAAA,YAAC,CAAE,GAAED,GAAE,GAAG,WAAWA,GAAE,OAAO;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,WAASA,GAAE,WAAW,SAAS,CAAC,GAAE,KAAK,UAAQA,GAAE,SAAQ,KAAK,SAAO,EAAC,8CAA6C,WAAU;AAAC,YAAAC,GAAE,KAAK;AAAA,UAAC,GAAE,mBAAkB,SAASF,IAAEC,IAAE;AAAC,YAAAA,GAAE,iBAAeA,GAAE,cAAc,iBAAeC,GAAE,SAAS,CAAC,EAAE,SAASD,GAAE,cAAc,aAAa,KAAGC,GAAE,KAAK;AAAA,UAAC,EAAC;AAAA,QAAC,GAAED,KAAE,CAAC,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,iBAAM,CAAC,EAAE,QAAQ,KAAK,QAAQ,QAAQ,KAAK;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,eAAK,WAAS,KAAK,GAAG,QAAQ,EAAC,WAAU,qBAAoB,CAAC,EAAE,OAAO,EAAE,SAAS,KAAK,QAAQ,SAAS;AAAE,cAAID,KAAE,KAAK,SAAS,KAAK,wCAAwC;AAAE,eAAK,QAAQ,OAAO,iBAAgBA,IAAE,KAAK,QAAQ,QAAQ,KAAK,GAAE,KAAK,SAAS,GAAG,aAAa,SAASA,IAAE;AAAC,YAAAA,GAAE,eAAe;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,SAAS,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASA,IAAEC,IAAE;AAAC,cAAG,GAAG,MAAMD,EAAC,GAAE;AAAC,gBAAIE,KAAE,EAAE,EAAEF,EAAC,EAAE,OAAO,GAAEG,KAAE,EAAE,EAAE,KAAK,QAAQ,SAAS,EAAE,OAAO,GAAEC,KAAE,CAAC;AAAE,iBAAK,QAAQ,cAAYA,GAAE,OAAKH,GAAE,QAAM,IAAGG,GAAE,MAAIH,GAAE,SAAOG,KAAEF,IAAEE,GAAE,OAAKD,GAAE,KAAIC,GAAE,QAAMD,GAAE,MAAK,KAAK,SAAS,IAAI,EAAC,SAAQ,SAAQ,MAAKC,GAAE,MAAK,KAAIA,GAAE,IAAG,CAAC;AAAA,UAAC,MAAM,MAAK,KAAK;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,SAAS,KAAK;AAAA,QAAC,EAAC,CAAC,GAAEH,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,UAAQA,GAAE,SAAQ,KAAK,SAAO,EAAC,wBAAuB,SAASD,IAAEC,IAAE;AAAC,YAAAC,GAAE,OAAOD,GAAE,MAAM;AAAA,UAAC,GAAE,wDAAuD,WAAU;AAAC,YAAAC,GAAE,OAAO;AAAA,UAAC,GAAE,8CAA6C,WAAU;AAAC,YAAAA,GAAE,KAAK;AAAA,UAAC,GAAE,mBAAkB,SAASF,IAAEC,IAAE;AAAC,YAAAA,GAAE,iBAAeA,GAAE,cAAc,iBAAeC,GAAE,SAAS,CAAC,EAAE,SAASD,GAAE,cAAc,aAAa,KAAGC,GAAE,KAAK;AAAA,UAAC,EAAC;AAAA,QAAC,GAAED,KAAE,CAAC,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,iBAAM,CAAC,EAAE,QAAQ,KAAK,QAAQ,QAAQ,KAAK;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,eAAK,WAAS,KAAK,GAAG,QAAQ,EAAC,WAAU,qBAAoB,CAAC,EAAE,OAAO,EAAE,SAAS,KAAK,QAAQ,SAAS;AAAE,cAAID,KAAE,KAAK,SAAS,KAAK,wCAAwC;AAAE,eAAK,QAAQ,OAAO,iBAAgBA,IAAE,KAAK,QAAQ,QAAQ,KAAK,GAAE,EAAE,QAAM,SAAS,YAAY,4BAA2B,OAAG,KAAE,GAAE,KAAK,SAAS,GAAG,aAAa,SAASA,IAAE;AAAC,YAAAA,GAAE,eAAe;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,SAAS,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASA,IAAE;AAAC,cAAG,KAAK,QAAQ,WAAW,EAAE,QAAM;AAAG,cAAIC,KAAE,GAAG,OAAOD,EAAC,KAAG,GAAG,OAAO,QAAMA,KAAE,SAAOA,GAAE,aAAa;AAAE,cAAGC,IAAE;AAAC,gBAAIC,KAAE,GAAG,mBAAmBF,EAAC,GAAEG,KAAE,EAAE,EAAE,KAAK,QAAQ,SAAS,EAAE,OAAO;AAAE,YAAAD,GAAE,OAAKC,GAAE,KAAID,GAAE,QAAMC,GAAE,MAAK,KAAK,SAAS,IAAI,EAAC,SAAQ,SAAQ,MAAKD,GAAE,MAAK,KAAIA,GAAE,IAAG,CAAC;AAAA,UAAC,MAAM,MAAK,KAAK;AAAE,iBAAOD;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,SAAS,KAAK;AAAA,QAAC,EAAC,CAAC,GAAEA,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,QAAM,EAAE,EAAE,SAAS,IAAI,GAAE,KAAK,UAAQA,GAAE,WAAW,QAAO,KAAK,UAAQA,GAAE,SAAQ,KAAK,OAAK,KAAK,QAAQ;AAAA,QAAQ,IAAGA,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE,KAAK,QAAQ,gBAAc,KAAK,QAAM,KAAK,QAAQ,WAAUC,KAAE,CAAC,sDAAqD,qCAAqC,OAAO,KAAK,QAAQ,IAAG,4BAA4B,EAAE,OAAO,KAAK,KAAK,MAAM,KAAI,6BAA6B,EAAE,OAAO,KAAK,KAAK,MAAM,WAAU,kBAAkB,GAAE,oCAAoC,OAAO,KAAK,QAAQ,IAAG,kFAAkF,GAAE,QAAQ,EAAE,KAAK,EAAE,GAAEC,KAAE,wCAAwC,OAAO,4DAA2D,WAAW,EAAE,OAAO,KAAK,KAAK,MAAM,QAAO,aAAa;AAAE,eAAK,UAAQ,KAAK,GAAG,OAAO,EAAC,OAAM,KAAK,KAAK,MAAM,QAAO,MAAK,KAAK,QAAQ,aAAY,MAAKD,IAAE,QAAOC,GAAC,CAAC,EAAE,OAAO,EAAE,SAASF,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,GAAG,WAAW,KAAK,OAAO,GAAE,KAAK,QAAQ,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASA,IAAEC,IAAE;AAAC,UAAAD,GAAE,GAAG,YAAY,SAASA,IAAE;AAAC,YAAAA,GAAE,YAAU,GAAG,KAAK,UAAQA,GAAE,eAAe,GAAEC,GAAE,QAAQ,OAAO;AAAA,UAAE,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,mBAAkB,OAAM,SAASD,IAAE;AAAC,cAAIC,IAAEC,KAAEF,GAAE,MAAM,qIAAqI,GAAEG,KAAEH,GAAE,MAAM,iEAAiE,GAAEI,KAAEJ,GAAE,MAAM,2DAA2D,GAAEM,KAAEN,GAAE,MAAM,iCAAiC,GAAEO,KAAEP,GAAE,MAAM,mDAAmD,GAAEQ,KAAER,GAAE,MAAM,gEAAgE,GAAES,KAAET,GAAE,MAAM,6CAA6C,GAAEU,KAAEV,GAAE,MAAM,iIAAiI,GAAEW,KAAEX,GAAE,MAAM,2BAA2B,GAAEY,KAAEZ,GAAE,MAAM,2DAA2D,GAAEa,KAAEb,GAAE,MAAM,gBAAgB,GAAEc,KAAEd,GAAE,MAAM,gBAAgB,GAAEe,KAAEf,GAAE,MAAM,aAAa,GAAEgB,KAAEhB,GAAE,MAAM,yDAAyD;AAAE,cAAGE,MAAG,OAAKA,GAAE,CAAC,EAAE,QAAO;AAAC,gBAAIe,KAAEf,GAAE,CAAC,GAAEgB,KAAE;AAAE,gBAAG,WAAShB,GAAE,CAAC,GAAE;AAAC,kBAAIiB,KAAEjB,GAAE,CAAC,EAAE,MAAM,qCAAqC;AAAE,kBAAGiB,GAAE,UAAQC,KAAE,CAAC,MAAK,IAAG,CAAC,GAAEC,KAAE,GAAEC,KAAEF,GAAE,QAAOC,KAAEC,IAAED,KAAI,CAAAH,MAAG,WAASC,GAAEE,KAAE,CAAC,IAAED,GAAEC,EAAC,IAAE,SAASF,GAAEE,KAAE,CAAC,GAAE,EAAE,IAAE;AAAA,kBAAO,CAAAH,KAAE,SAAShB,GAAE,CAAC,GAAE,EAAE;AAAA,YAAC;AAAC,YAAAD,KAAE,EAAE,EAAE,UAAU,EAAE,KAAK,eAAc,CAAC,EAAE,KAAK,OAAM,6BAA2BgB,MAAGC,KAAE,IAAE,YAAUA,KAAE,GAAG,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,UAAS,KAAK;AAAA,UAAC,WAASf,MAAGA,GAAE,CAAC,EAAE,OAAO,CAAAF,KAAE,EAAE,EAAE,UAAU,EAAE,KAAK,eAAc,CAAC,EAAE,KAAK,OAAM,qCAAmCE,GAAE,CAAC,IAAE,UAAU,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,UAAS,KAAK;AAAA,mBAAUC,MAAGA,GAAE,CAAC,EAAE,OAAO,CAAAH,KAAE,EAAE,EAAE,UAAU,EAAE,KAAK,eAAc,CAAC,EAAE,KAAK,OAAM,6BAA2BG,GAAE,CAAC,IAAE,SAAS,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,UAAS,KAAK,EAAE,KAAK,aAAY,IAAI,EAAE,KAAK,qBAAoB,MAAM;AAAA,mBAAUE,MAAGA,GAAE,CAAC,EAAE,OAAO,CAAAL,KAAE,EAAE,EAAE,UAAU,EAAE,KAAK,eAAc,CAAC,EAAE,KAAK,OAAMK,GAAE,CAAC,IAAE,eAAe,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,UAAS,KAAK,EAAE,KAAK,SAAQ,YAAY;AAAA,mBAAUC,MAAGA,GAAE,CAAC,EAAE,OAAO,CAAAN,KAAE,EAAE,EAAE,mEAAmE,EAAE,KAAK,eAAc,CAAC,EAAE,KAAK,OAAM,8BAA4BM,GAAE,CAAC,CAAC,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,UAAS,KAAK;AAAA,mBAAUC,MAAGA,GAAE,CAAC,EAAE,OAAO,CAAAP,KAAE,EAAE,EAAE,UAAU,EAAE,KAAK,eAAc,CAAC,EAAE,KAAK,OAAM,uCAAqCO,GAAE,CAAC,CAAC,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,UAAS,KAAK;AAAA,mBAAUC,MAAGA,GAAE,CAAC,EAAE,OAAO,CAAAR,KAAE,EAAE,EAAE,mEAAmE,EAAE,KAAK,eAAc,CAAC,EAAE,KAAK,UAAS,KAAK,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,OAAM,8BAA4BQ,GAAE,CAAC,CAAC;AAAA,mBAAUC,MAAGA,GAAE,CAAC,EAAE,QAAO;AAAC,gBAAIa,KAAE;AAAE,4BAAcb,GAAE,CAAC,MAAIa,KAAEb,GAAE,CAAC;AAAG,gBAAIc,KAAE;AAAE,4BAAcd,GAAE,CAAC,MAAIc,KAAEd,GAAE,CAAC;AAAG,gBAAIe,KAAE;AAAE,4BAAcf,GAAE,CAAC,MAAIe,KAAEf,GAAE,CAAC;AAAG,gBAAIgB,KAAE;AAAE,4BAAchB,GAAE,CAAC,MAAIgB,KAAEhB,GAAE,CAAC;AAAG,gBAAIiB,KAAE;AAAE,4BAAcjB,GAAE,CAAC,MAAIiB,KAAEjB,GAAE,CAAC,IAAGT,KAAE,EAAE,EAAE,iFAAiF,EAAE,KAAK,eAAc,CAAC,EAAE,KAAK,OAAM,OAAKS,GAAE,CAAC,IAAE,mBAAiBA,GAAE,CAAC,IAAE,WAASe,KAAE,eAAaC,KAAE,YAAUC,MAAGJ,KAAE,IAAE,YAAUA,KAAE,OAAKC,KAAE,IAAE,UAAQN,KAAE,GAAG,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,UAAS,KAAK;AAAA,UAAC,WAASP,MAAGA,GAAE,CAAC,EAAE,UAAQC,MAAGA,GAAE,CAAC,EAAE,QAAO;AAAC,gBAAIgB,KAAEjB,MAAGA,GAAE,CAAC,EAAE,SAAOA,GAAE,CAAC,IAAEC,GAAE,CAAC;AAAE,YAAAX,KAAE,EAAE,EAAE,mEAAmE,EAAE,KAAK,eAAc,CAAC,EAAE,KAAK,UAAS,KAAK,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,OAAM,iDAA+C2B,KAAE,aAAa;AAAA,UAAC,WAASf,MAAGC,MAAGC,GAAE,CAAAd,KAAE,EAAE,EAAE,kBAAkB,EAAE,KAAK,OAAMD,EAAC,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,UAAS,KAAK;AAAA,eAAM;AAAC,gBAAG,CAACgB,MAAG,CAACA,GAAE,CAAC,EAAE,OAAO,QAAM;AAAG,YAAAf,KAAE,EAAE,EAAE,UAAU,EAAE,KAAK,eAAc,CAAC,EAAE,KAAK,OAAM,qDAAmD,mBAAmBe,GAAE,CAAC,CAAC,IAAE,wBAAwB,EAAE,KAAK,SAAQ,KAAK,EAAE,KAAK,UAAS,KAAK,EAAE,KAAK,aAAY,IAAI,EAAE,KAAK,qBAAoB,MAAM;AAAA,UAAC;AAAC,iBAAOf,GAAE,SAAS,iBAAiB,GAAEA,GAAE,CAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAID,KAAE,MAAKC,KAAE,KAAK,QAAQ,OAAO,wBAAwB;AAAE,eAAK,QAAQ,OAAO,kBAAkB,GAAE,KAAK,gBAAgBA,EAAC,EAAE,KAAM,SAASA,IAAE;AAAC,YAAAD,GAAE,GAAG,WAAWA,GAAE,OAAO,GAAEA,GAAE,QAAQ,OAAO,qBAAqB;AAAE,gBAAIE,KAAEF,GAAE,gBAAgBC,EAAC;AAAE,YAAAC,MAAGF,GAAE,QAAQ,OAAO,qBAAoBE,EAAC;AAAA,UAAC,CAAE,EAAE,KAAM,WAAU;AAAC,YAAAF,GAAE,QAAQ,OAAO,qBAAqB;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,mBAAkB,OAAM,WAAU;AAAC,cAAIA,KAAE;AAAK,iBAAO,EAAE,EAAE,SAAU,SAASC,IAAE;AAAC,gBAAIC,KAAEF,GAAE,QAAQ,KAAK,iBAAiB,GAAEG,KAAEH,GAAE,QAAQ,KAAK,iBAAiB;AAAE,YAAAA,GAAE,GAAG,cAAcA,GAAE,SAAS,WAAU;AAAC,cAAAA,GAAE,QAAQ,aAAa,cAAc,GAAEE,GAAE,GAAG,8BAA8B,WAAU;AAAC,gBAAAF,GAAE,GAAG,UAAUG,IAAED,GAAE,IAAI,CAAC;AAAA,cAAC,CAAE,GAAE,EAAE,kBAAgBA,GAAE,QAAQ,OAAO,GAAEC,GAAE,GAAG,SAAS,SAASH,IAAE;AAAC,gBAAAA,GAAE,eAAe,GAAEC,GAAE,QAAQC,GAAE,IAAI,CAAC;AAAA,cAAC,CAAE,GAAEF,GAAE,aAAaE,IAAEC,EAAC;AAAA,YAAC,CAAE,GAAEH,GAAE,GAAG,eAAeA,GAAE,SAAS,WAAU;AAAC,cAAAE,GAAE,IAAI,GAAEC,GAAE,IAAI,GAAE,cAAYF,GAAE,MAAM,KAAGA,GAAE,OAAO;AAAA,YAAC,CAAE,GAAED,GAAE,GAAG,WAAWA,GAAE,OAAO;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,QAAM,EAAE,EAAE,SAAS,IAAI,GAAE,KAAK,UAAQA,GAAE,WAAW,QAAO,KAAK,UAAQA,GAAE,SAAQ,KAAK,OAAK,KAAK,QAAQ;AAAA,QAAQ,GAAEA,KAAE,CAAC,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE,KAAK,QAAQ,gBAAc,KAAK,QAAM,KAAK,QAAQ,WAAUC,KAAE,CAAC,2BAA0B,sGAAqG,+GAA8G,kHAAiH,MAAM,EAAE,KAAK,EAAE;AAAE,eAAK,UAAQ,KAAK,GAAG,OAAO,EAAC,OAAM,KAAK,KAAK,QAAQ,MAAK,MAAK,KAAK,QAAQ,aAAY,MAAK,KAAK,mBAAmB,GAAE,QAAOA,IAAE,UAAS,SAASD,IAAE;AAAC,YAAAA,GAAE,KAAK,8BAA8B,EAAE,IAAI,EAAC,cAAa,KAAI,UAAS,SAAQ,CAAC;AAAA,UAAC,EAAC,CAAC,EAAE,OAAO,EAAE,SAASA,EAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,GAAG,WAAW,KAAK,OAAO,GAAE,KAAK,QAAQ,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,sBAAqB,OAAM,WAAU;AAAC,cAAIA,KAAE,MAAKC,KAAE,KAAK,QAAQ,OAAO,EAAE,QAAM,QAAM,IAAI;AAAE,iBAAO,OAAO,KAAKA,EAAC,EAAE,IAAK,SAASC,IAAE;AAAC,gBAAIC,KAAEF,GAAEC,EAAC,GAAEE,KAAE,EAAE,EAAE,+CAA+C;AAAE,mBAAOA,GAAE,OAAO,EAAE,EAAE,iBAAeF,KAAE,gBAAgB,EAAE,IAAI,EAAC,OAAM,KAAI,gBAAe,GAAE,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,eAAe,EAAE,KAAKF,GAAE,QAAQ,KAAK,UAAQG,EAAC,KAAGA,EAAC,CAAC,GAAEC,GAAE,KAAK;AAAA,UAAC,CAAE,EAAE,KAAK,EAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,kBAAiB,OAAM,WAAU;AAAC,cAAIJ,KAAE;AAAK,iBAAO,EAAE,EAAE,SAAU,SAASC,IAAE;AAAC,YAAAD,GAAE,GAAG,cAAcA,GAAE,SAAS,WAAU;AAAC,cAAAA,GAAE,QAAQ,aAAa,cAAc,GAAEC,GAAE,QAAQ;AAAA,YAAC,CAAE,GAAED,GAAE,GAAG,WAAWA,GAAE,OAAO;AAAA,UAAC,CAAE,EAAE,QAAQ;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAIA,KAAE;AAAK,eAAK,QAAQ,OAAO,kBAAkB,GAAE,KAAK,eAAe,EAAE,KAAM,WAAU;AAAC,YAAAA,GAAE,QAAQ,OAAO,qBAAqB;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,CAAC,GAAEC,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,UAAQA,GAAE,SAAQ,KAAK,UAAQ,MAAG,KAAK,gBAAc,OAAG,KAAK,QAAM,MAAK,KAAK,QAAM,MAAK,KAAK,SAAO,EAAC,0BAAyB,SAASD,IAAE;AAAC,YAAAE,GAAE,QAAQ,YAAUF,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEE,GAAE,gBAAc,MAAGA,GAAE,OAAO,IAAE;AAAA,UAAE,GAAE,wBAAuB,SAASF,IAAEC,IAAE;AAAC,YAAAC,GAAE,QAAMD,GAAE,OAAMC,GAAE,QAAMD,GAAE;AAAA,UAAK,GAAE,yDAAwD,SAASD,IAAEC,IAAE;AAAC,gBAAGC,GAAE,QAAQ,WAAS,CAACA,GAAE,eAAc;AAAC,kBAAG,WAASD,GAAE,MAAK;AAAC,oBAAIE,KAAED,GAAE,QAAQ,OAAO,qBAAqB,EAAE,aAAa,GAAEE,KAAE,EAAE,SAAS,EAAE,KAAKD,GAAE,eAAe,CAAC,CAAC;AAAE,gBAAAD,GAAE,QAAME,GAAE,MAAKF,GAAE,QAAME,GAAE;AAAA,cAAG,MAAM,CAAAF,GAAE,QAAMD,GAAE,OAAMC,GAAE,QAAMD,GAAE;AAAM,cAAAC,GAAE,OAAO;AAAA,YAAC;AAAC,YAAAA,GAAE,gBAAc;AAAA,UAAE,GAAE,gFAA+E,WAAU;AAAC,YAAAA,GAAE,KAAK;AAAA,UAAC,GAAE,uBAAsB,WAAU;AAAC,YAAAA,GAAE,SAAS,GAAG,gBAAgB,KAAGA,GAAE,KAAK;AAAA,UAAC,EAAC;AAAA,QAAC,IAAGD,KAAE,CAAC,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,iBAAO,KAAK,QAAQ,WAAS,CAAC,EAAE,QAAQ,KAAK,QAAQ,QAAQ,GAAG;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,WAAS,KAAK,GAAG,QAAQ,EAAC,WAAU,mBAAkB,CAAC,EAAE,OAAO,EAAE,SAAS,KAAK,QAAQ,SAAS;AAAE,cAAIC,KAAE,KAAK,SAAS,KAAK,kBAAkB;AAAE,eAAK,QAAQ,OAAO,iBAAgBA,IAAE,KAAK,QAAQ,QAAQ,GAAG,GAAE,KAAK,SAAS,GAAG,aAAa,WAAU;AAAC,YAAAD,GAAE,UAAQ;AAAA,UAAE,CAAE,GAAE,KAAK,SAAS,GAAG,WAAW,WAAU;AAAC,YAAAA,GAAE,UAAQ;AAAA,UAAE,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,SAAS,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAE,KAAK,QAAQ,OAAO,qBAAqB;AAAE,cAAG,CAACA,GAAE,SAAOA,GAAE,MAAM,YAAY,KAAG,CAACD,GAAE,MAAK,KAAK;AAAA,eAAM;AAAC,gBAAIE,KAAE,EAAC,MAAK,KAAK,OAAM,KAAI,KAAK,MAAK,GAAEC,KAAE,EAAE,EAAE,KAAK,QAAQ,SAAS,EAAE,OAAO;AAAE,YAAAD,GAAE,OAAKC,GAAE,KAAID,GAAE,QAAMC,GAAE,MAAK,KAAK,SAAS,IAAI,EAAC,SAAQ,SAAQ,MAAK,KAAK,IAAID,GAAE,MAAK,CAAC,IAAE,IAAG,KAAIA,GAAE,MAAI,EAAC,CAAC,GAAE,KAAK,QAAQ,OAAO,8BAA6B,KAAK,QAAQ;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,kBAAiB,OAAM,SAASF,IAAE;AAAC,eAAK,GAAG,gBAAgB,KAAK,SAAS,KAAK,eAAe,GAAEA,EAAC,GAAEA,MAAG,KAAK,KAAK;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,WAAS,KAAK,SAAS,KAAK;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,cAAIC,KAAE;AAAK,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,KAAG,EAAE,EAAE,WAAW,IAAG,KAAK,YAAUA,GAAE,WAAW,UAAS,KAAK,UAAQA,GAAE,SAAQ,KAAK,OAAK,KAAK,QAAQ,QAAM,CAAC,GAAE,KAAK,YAAU,KAAK,QAAQ,iBAAe,UAAS,KAAK,QAAM,MAAM,QAAQ,KAAK,IAAI,IAAE,KAAK,OAAK,CAAC,KAAK,IAAI,GAAE,KAAK,SAAO,EAAC,oBAAmB,SAASD,IAAEC,IAAE;AAAC,YAAAA,GAAE,mBAAmB,KAAGC,GAAE,YAAYD,EAAC;AAAA,UAAC,GAAE,sBAAqB,SAASD,IAAEC,IAAE;AAAC,YAAAC,GAAE,cAAcD,EAAC;AAAA,UAAC,GAAE,8DAA6D,WAAU;AAAC,YAAAC,GAAE,KAAK;AAAA,UAAC,EAAC;AAAA,QAAC,GAAED,KAAE,CAAC,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,iBAAO,KAAK,MAAM,SAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,gBAAc,MAAK,KAAK,eAAa,MAAK,KAAK,WAAS,KAAK,GAAG,QAAQ,EAAC,WAAU,qBAAoB,WAAU,MAAG,WAAU,GAAE,CAAC,EAAE,OAAO,EAAE,SAAS,KAAK,QAAQ,SAAS,GAAE,KAAK,SAAS,KAAK,GAAE,KAAK,WAAS,KAAK,SAAS,KAAK,wCAAwC,GAAE,KAAK,SAAS,GAAG,SAAQ,mBAAmB,SAASC,IAAE;AAAC,YAAAD,GAAE,SAAS,KAAK,SAAS,EAAE,YAAY,QAAQ,GAAE,EAAE,EAAEC,GAAE,aAAa,EAAE,SAAS,QAAQ,GAAED,GAAE,QAAQ;AAAA,UAAC,CAAE,GAAE,KAAK,SAAS,GAAG,aAAa,SAASA,IAAE;AAAC,YAAAA,GAAE,eAAe;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,eAAK,SAAS,OAAO;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAASA,IAAE;AAAC,eAAK,SAAS,KAAK,SAAS,EAAE,YAAY,QAAQ,GAAEA,GAAE,SAAS,QAAQ,GAAE,KAAK,SAAS,CAAC,EAAE,YAAUA,GAAE,CAAC,EAAE,YAAU,KAAK,SAAS,YAAY,IAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,WAAU;AAAC,cAAIA,KAAE,KAAK,SAAS,KAAK,wBAAwB,GAAEC,KAAED,GAAE,KAAK;AAAE,cAAGC,GAAE,OAAO,MAAK,WAAWA,EAAC;AAAA,eAAM;AAAC,gBAAIC,KAAEF,GAAE,OAAO,EAAE,KAAK;AAAE,YAAAE,GAAE,WAASA,KAAE,KAAK,SAAS,KAAK,kBAAkB,EAAE,MAAM,IAAG,KAAK,WAAWA,GAAE,KAAK,iBAAiB,EAAE,MAAM,CAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,cAAIF,KAAE,KAAK,SAAS,KAAK,wBAAwB,GAAEC,KAAED,GAAE,KAAK;AAAE,cAAGC,GAAE,OAAO,MAAK,WAAWA,EAAC;AAAA,eAAM;AAAC,gBAAIC,KAAEF,GAAE,OAAO,EAAE,KAAK;AAAE,YAAAE,GAAE,WAASA,KAAE,KAAK,SAAS,KAAK,kBAAkB,EAAE,KAAK,IAAG,KAAK,WAAWA,GAAE,KAAK,iBAAiB,EAAE,KAAK,CAAC;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,cAAIF,KAAE,KAAK,SAAS,KAAK,wBAAwB;AAAE,cAAGA,GAAE,QAAO;AAAC,gBAAIC,KAAE,KAAK,aAAaD,EAAC;AAAE,gBAAG,SAAO,KAAK,gBAAc,MAAI,KAAK,aAAa,OAAO,MAAK,cAAc,KAAG,KAAK,cAAc;AAAA,qBAAW,SAAO,KAAK,gBAAc,KAAK,aAAa,SAAO,KAAG,CAAC,KAAK,cAAc,YAAY,GAAE;AAAC,kBAAIE,KAAE,KAAK,cAAc,KAAG,KAAK,cAAc,KAAG,KAAK,aAAa;AAAO,cAAAA,KAAE,MAAI,KAAK,cAAc,MAAIA;AAAA,YAAE;AAAC,gBAAG,KAAK,cAAc,WAAWD,EAAC,GAAE,WAAS,KAAK,QAAQ,YAAW;AAAC,kBAAIE,KAAE,SAAS,eAAe,EAAE;AAAE,gBAAE,EAAEF,EAAC,EAAE,MAAME,EAAC,GAAE,GAAG,qBAAqBA,EAAC,EAAE,OAAO;AAAA,YAAC,MAAM,IAAG,oBAAoBF,EAAC,EAAE,OAAO;AAAE,iBAAK,gBAAc,MAAK,KAAK,KAAK,GAAE,KAAK,QAAQ,OAAO,cAAc,GAAE,KAAK,QAAQ,aAAa,UAAS,KAAK,UAAU,KAAK,GAAE,KAAK,SAAS;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE,KAAK,MAAMD,GAAE,KAAK,OAAO,CAAC,GAAEE,KAAEF,GAAE,KAAK,MAAM,GAAEG,KAAEF,GAAE,UAAQA,GAAE,QAAQC,EAAC,IAAEA;AAAE,iBAAM,YAAU,OAAOC,OAAIA,KAAE,GAAG,WAAWA,EAAC,IAAGA;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,uBAAsB,OAAM,SAASH,IAAEC,IAAE;AAAC,cAAIC,KAAE,KAAK,MAAMF,EAAC;AAAE,iBAAOC,GAAE,IAAK,SAASA,IAAEE,IAAE;AAAC,gBAAIC,KAAE,EAAE,EAAE,oCAAoC;AAAE,mBAAOA,GAAE,OAAOF,GAAE,WAASA,GAAE,SAASD,EAAC,IAAEA,KAAE,EAAE,GAAEG,GAAE,KAAK,EAAC,OAAMJ,IAAE,MAAKC,GAAC,CAAC,GAAE,MAAID,MAAG,MAAIG,MAAGC,GAAE,SAAS,QAAQ,GAAEA;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASJ,IAAE;AAAC,eAAK,SAAS,GAAG,UAAU,MAAIA,GAAE,YAAU,GAAG,KAAK,SAAOA,GAAE,eAAe,GAAE,KAAK,QAAQ,KAAGA,GAAE,YAAU,GAAG,KAAK,MAAIA,GAAE,eAAe,GAAE,KAAK,OAAO,KAAGA,GAAE,YAAU,GAAG,KAAK,SAAOA,GAAE,eAAe,GAAE,KAAK,SAAS;AAAA,QAAG,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASA,IAAEC,IAAEC,IAAE;AAAC,cAAIC,KAAE,KAAK,MAAMH,EAAC;AAAE,cAAGG,MAAGA,GAAE,MAAM,KAAKF,EAAC,KAAGE,GAAE,QAAO;AAAC,gBAAIC,KAAED,GAAE,MAAM,KAAKF,EAAC;AAAE,iBAAK,eAAaG,GAAE,CAAC,GAAED,GAAE,OAAOC,GAAE,CAAC,GAAEF,EAAC;AAAA,UAAC,MAAM,CAAAA,GAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASF,IAAEC,IAAE;AAAC,cAAIC,KAAE,MAAKC,KAAE,EAAE,EAAE,iDAA+CH,KAAE,UAAU;AAAE,iBAAO,KAAK,cAAcA,IAAEC,IAAG,SAASA,IAAE;AAAC,aAACA,KAAEA,MAAG,CAAC,GAAG,WAASE,GAAE,KAAKD,GAAE,oBAAoBF,IAAEC,EAAC,CAAC,GAAEC,GAAE,KAAK;AAAA,UAAE,CAAE,GAAEC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASH,IAAE;AAAC,cAAIC,KAAE;AAAK,cAAG,CAAC,EAAE,SAAS,CAAC,GAAG,KAAK,OAAM,GAAG,KAAK,IAAG,GAAG,KAAK,IAAI,GAAED,GAAE,OAAO,GAAE;AAAC,gBAAIE,IAAEC,IAAEC,KAAE,KAAK,QAAQ,OAAO,qBAAqB;AAAE,gBAAG,YAAU,KAAK,QAAQ,UAAS;AAAC,kBAAGF,KAAEE,GAAE,cAAcA,EAAC,GAAED,KAAED,GAAE,SAAS,GAAE,KAAK,MAAM,QAAS,SAASF,IAAE;AAAC,oBAAGA,GAAE,MAAM,KAAKG,EAAC,EAAE,QAAOD,KAAEE,GAAE,mBAAmBJ,GAAE,KAAK,GAAE;AAAA,cAAE,CAAE,GAAE,CAACE,GAAE,QAAO,KAAK,KAAK,KAAK;AAAE,cAAAC,KAAED,GAAE,SAAS;AAAA,YAAC,MAAM,CAAAA,KAAEE,GAAE,aAAa,GAAED,KAAED,GAAE,SAAS;AAAE,gBAAG,KAAK,MAAM,UAAQC,IAAE;AAAC,mBAAK,SAAS,MAAM;AAAE,kBAAIG,KAAE,EAAE,SAAS,EAAE,KAAKJ,GAAE,eAAe,CAAC,CAAC,GAAEK,KAAE,EAAE,EAAE,KAAK,QAAQ,SAAS,EAAE,OAAO;AAAE,cAAAD,OAAIA,GAAE,OAAKC,GAAE,KAAID,GAAE,QAAMC,GAAE,MAAK,KAAK,SAAS,KAAK,GAAE,KAAK,gBAAcL,IAAE,KAAK,MAAM,QAAS,SAASF,IAAEE,IAAE;AAAC,gBAAAF,GAAE,MAAM,KAAKG,EAAC,KAAGF,GAAE,YAAYC,IAAEC,EAAC,EAAE,SAASF,GAAE,QAAQ;AAAA,cAAC,CAAE,GAAE,KAAK,SAAS,KAAK,iBAAiB,EAAE,MAAM,EAAE,SAAS,QAAQ,GAAE,UAAQ,KAAK,YAAU,KAAK,SAAS,IAAI,EAAC,MAAKK,GAAE,MAAK,KAAIA,GAAE,MAAI,KAAK,SAAS,YAAY,IAAE,EAAC,CAAC,IAAE,KAAK,SAAS,IAAI,EAAC,MAAKA,GAAE,MAAK,KAAIA,GAAE,MAAIA,GAAE,SAAO,EAAC,CAAC;AAAA,YAAE,MAAM,MAAK,KAAK;AAAA,UAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,SAAS,KAAK;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,SAAS,KAAK;AAAA,QAAC,EAAC,CAAC,GAAEL,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,QAAE,EAAE,aAAW,EAAE,EAAE,OAAO,EAAE,EAAE,YAAW,EAAC,SAAQ,SAAQ,SAAQ,CAAC,GAAE,KAAI,IAAG,OAAM,IAAG,OAAM,GAAE,SAAQ,EAAC,UAAS,EAAE,EAAE,WAAW,KAAK,OAAO,GAAE,SAAQ,MAAG,SAAQ,EAAC,QAAO,IAAG,WAAU,IAAG,UAAS,IAAG,UAAS,IAAG,WAAU,IAAG,YAAW,IAAG,QAAO,IAAG,aAAY,IAAG,UAAS,IAAG,UAAS,IAAG,aAAY,IAAG,aAAY,IAAG,SAAQ,IAAG,SAAQ,IAAG,YAAW,IAAG,aAAY,IAAG,aAAY,IAAG,cAAa,IAAG,cAAa,IAAG,aAAY,IAAG,YAAW,IAAG,YAAW,GAAE,GAAE,SAAQ,CAAC,GAAE,MAAK,SAAQ,kBAAiB,OAAG,iBAAgB,OAAM,gBAAe,IAAG,oBAAmB,OAAG,SAAQ,CAAC,CAAC,SAAQ,CAAC,OAAO,CAAC,GAAE,CAAC,QAAO,CAAC,QAAO,aAAY,OAAO,CAAC,GAAE,CAAC,YAAW,CAAC,UAAU,CAAC,GAAE,CAAC,SAAQ,CAAC,OAAO,CAAC,GAAE,CAAC,QAAO,CAAC,MAAK,MAAK,WAAW,CAAC,GAAE,CAAC,SAAQ,CAAC,OAAO,CAAC,GAAE,CAAC,UAAS,CAAC,QAAO,WAAU,OAAO,CAAC,GAAE,CAAC,QAAO,CAAC,cAAa,YAAW,MAAM,CAAC,CAAC,GAAE,YAAW,MAAG,SAAQ,EAAC,OAAM,CAAC,CAAC,UAAS,CAAC,cAAa,cAAa,iBAAgB,YAAY,CAAC,GAAE,CAAC,SAAQ,CAAC,aAAY,cAAa,WAAW,CAAC,GAAE,CAAC,UAAS,CAAC,aAAa,CAAC,CAAC,GAAE,MAAK,CAAC,CAAC,QAAO,CAAC,kBAAiB,QAAQ,CAAC,CAAC,GAAE,OAAM,CAAC,CAAC,OAAM,CAAC,cAAa,YAAW,cAAa,aAAa,CAAC,GAAE,CAAC,UAAS,CAAC,aAAY,aAAY,aAAa,CAAC,CAAC,GAAE,KAAI,CAAC,CAAC,SAAQ,CAAC,OAAO,CAAC,GAAE,CAAC,QAAO,CAAC,QAAO,aAAY,OAAO,CAAC,GAAE,CAAC,QAAO,CAAC,MAAK,WAAW,CAAC,GAAE,CAAC,SAAQ,CAAC,OAAO,CAAC,GAAE,CAAC,UAAS,CAAC,QAAO,SAAS,CAAC,GAAE,CAAC,QAAO,CAAC,cAAa,UAAU,CAAC,CAAC,EAAC,GAAE,mBAAkB,OAAG,iBAAgB,OAAG,SAAQ,OAAG,qBAAoB,OAAG,OAAM,MAAK,QAAO,MAAK,iBAAgB,MAAG,OAAM,OAAG,YAAW,OAAG,SAAQ,GAAE,cAAa,OAAG,WAAU,MAAG,kBAAiB,MAAG,SAAQ,QAAO,WAAU,MAAK,eAAc,GAAE,yBAAwB,GAAE,YAAW,MAAG,gBAAe,OAAG,aAAY,MAAK,oBAAmB,OAAG,sBAAqB,OAAG,cAAa,KAAI,2BAA0B,OAAG,UAAS,QAAO,YAAW,SAAQ,eAAc,UAAS,WAAU,CAAC,KAAI,cAAa,OAAM,MAAK,MAAK,MAAK,MAAK,MAAK,IAAI,GAAE,WAAU,CAAC,SAAQ,eAAc,iBAAgB,eAAc,kBAAiB,aAAY,UAAS,iBAAgB,UAAS,mBAAkB,SAAS,GAAE,sBAAqB,CAAC,GAAE,iBAAgB,MAAG,WAAU,CAAC,KAAI,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,IAAI,GAAE,eAAc,CAAC,MAAK,IAAI,GAAE,QAAO,CAAC,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,GAAE,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,GAAE,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,GAAE,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,GAAE,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,GAAE,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,GAAE,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,GAAE,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,CAAC,GAAE,YAAW,CAAC,CAAC,SAAQ,WAAU,aAAY,aAAY,cAAa,WAAU,aAAY,OAAO,GAAE,CAAC,OAAM,eAAc,UAAS,SAAQ,QAAO,QAAO,mBAAkB,SAAS,GAAE,CAAC,UAAS,SAAQ,aAAY,SAAQ,cAAa,iBAAgB,WAAU,UAAU,GAAE,CAAC,cAAa,gBAAe,gBAAe,UAAS,UAAS,UAAS,eAAc,aAAa,GAAE,CAAC,SAAQ,SAAQ,aAAY,WAAU,eAAc,UAAS,mBAAkB,MAAM,GAAE,CAAC,iBAAgB,aAAY,gBAAe,oBAAmB,cAAa,eAAc,kBAAiB,UAAU,GAAE,CAAC,WAAU,WAAU,eAAc,gBAAe,QAAO,eAAc,aAAY,QAAQ,GAAE,CAAC,YAAW,YAAW,SAAQ,WAAU,SAAQ,iBAAgB,aAAY,QAAQ,CAAC,GAAE,aAAY,EAAC,WAAU,WAAU,WAAU,UAAS,GAAE,aAAY,CAAC,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,KAAK,GAAE,gBAAe,wBAAuB,oBAAmB,EAAC,KAAI,IAAG,KAAI,GAAE,GAAE,eAAc,OAAG,aAAY,OAAG,sBAAqB,MAAK,sBAAqB,WAAU,4BAA2B,MAAG,WAAU,EAAC,iBAAgB,MAAK,QAAO,MAAK,gBAAe,MAAK,UAAS,MAAK,kBAAiB,MAAK,eAAc,MAAK,SAAQ,MAAK,SAAQ,MAAK,mBAAkB,MAAK,eAAc,MAAK,oBAAmB,MAAK,QAAO,MAAK,WAAU,MAAK,SAAQ,MAAK,aAAY,MAAK,WAAU,MAAK,SAAQ,MAAK,UAAS,KAAI,GAAE,YAAW,EAAC,MAAK,aAAY,UAAS,MAAG,aAAY,KAAE,GAAE,gBAAe,MAAG,qBAAoB,2IAA0I,sBAAqB,MAAG,4BAA2B,CAAC,GAAE,gCAA+B,CAAC,mBAAkB,4BAA2B,oBAAmB,WAAU,iBAAgB,oBAAmB,uBAAsB,oBAAmB,kBAAiB,UAAU,GAAE,QAAO,EAAC,IAAG,EAAC,KAAI,UAAS,OAAM,mBAAkB,UAAS,QAAO,UAAS,QAAO,KAAI,OAAM,aAAY,SAAQ,UAAS,QAAO,UAAS,UAAS,UAAS,aAAY,gBAAe,iBAAgB,kBAAiB,gBAAe,gBAAe,eAAc,gBAAe,iBAAgB,gBAAe,gBAAe,gBAAe,eAAc,mBAAkB,uBAAsB,mBAAkB,qBAAoB,oBAAmB,WAAU,qBAAoB,UAAS,aAAY,cAAa,aAAY,YAAW,aAAY,YAAW,aAAY,YAAW,aAAY,YAAW,aAAY,YAAW,aAAY,YAAW,cAAa,wBAAuB,UAAS,kBAAiB,GAAE,KAAI,EAAC,KAAI,UAAS,OAAM,mBAAkB,SAAQ,QAAO,eAAc,QAAO,KAAI,OAAM,aAAY,SAAQ,SAAQ,QAAO,SAAQ,UAAS,SAAQ,aAAY,eAAc,iBAAgB,iBAAgB,gBAAe,eAAc,eAAc,eAAc,iBAAgB,eAAc,gBAAe,eAAc,eAAc,kBAAiB,uBAAsB,kBAAiB,qBAAoB,mBAAkB,WAAU,oBAAmB,UAAS,YAAW,cAAa,YAAW,YAAW,YAAW,YAAW,YAAW,YAAW,YAAW,YAAW,YAAW,YAAW,YAAW,YAAW,aAAY,wBAAuB,SAAQ,kBAAiB,EAAC,GAAE,OAAM,EAAC,OAAM,mBAAkB,aAAY,0BAAyB,cAAa,2BAA0B,WAAU,wBAAuB,YAAW,yBAAwB,UAAS,uBAAsB,WAAU,wBAAuB,UAAS,uBAAsB,UAAS,uBAAsB,WAAU,wBAAuB,WAAU,wBAAuB,QAAO,0BAAyB,SAAQ,2BAA0B,WAAU,wBAAuB,MAAK,kBAAiB,OAAM,mBAAkB,QAAO,oBAAmB,OAAM,mBAAkB,MAAK,kBAAiB,QAAO,oBAAmB,WAAU,wBAAuB,YAAW,yBAAwB,MAAK,kBAAiB,OAAM,mBAAkB,QAAO,oBAAmB,MAAK,kBAAiB,QAAO,0BAAyB,OAAM,mBAAkB,WAAU,wBAAuB,OAAM,mBAAkB,aAAY,yBAAwB,QAAO,oBAAmB,SAAQ,qBAAoB,UAAS,sBAAqB,MAAK,kBAAiB,UAAS,sBAAqB,QAAO,oBAAmB,eAAc,2BAA0B,WAAU,uBAAsB,aAAY,yBAAwB,OAAM,mBAAkB,YAAW,yBAAwB,OAAM,mBAAkB,WAAU,uBAAsB,MAAK,kBAAiB,eAAc,2BAA0B,OAAM,kBAAiB,EAAC,EAAC,CAAC;AAAE,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAC,SAASJ,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,SAAOC,IAAE,KAAK,WAASC,IAAE,KAAK,UAAQC,IAAE,KAAK,WAASC;AAAA,QAAC,IAAGH,KAAE,CAAC,EAAC,KAAI,UAAS,OAAM,SAASD,IAAE;AAAC,cAAIC,KAAE,EAAE,EAAE,KAAK,MAAM;AAAE,cAAG,KAAK,WAAS,KAAK,QAAQ,YAAUA,GAAE,KAAK,KAAK,QAAQ,QAAQ,GAAE,KAAK,WAAS,KAAK,QAAQ,aAAWA,GAAE,SAAS,KAAK,QAAQ,SAAS,GAAE,KAAK,WAAS,KAAK,QAAQ,QAAM,EAAE,EAAE,KAAK,KAAK,QAAQ,MAAM,SAASD,IAAEE,IAAE;AAAC,YAAAD,GAAE,KAAK,UAAQD,IAAEE,EAAC;AAAA,UAAC,CAAE,GAAE,KAAK,WAAS,KAAK,QAAQ,SAAOD,GAAE,GAAG,SAAQ,KAAK,QAAQ,KAAK,GAAE,KAAK,UAAS;AAAC,gBAAIC,KAAED,GAAE,KAAK,0BAA0B;AAAE,iBAAK,SAAS,QAAS,SAASD,IAAE;AAAC,cAAAA,GAAE,OAAOE,GAAE,SAAOA,KAAED,EAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAC,iBAAO,KAAK,YAAU,KAAK,SAASA,IAAE,KAAK,OAAO,GAAE,KAAK,WAAS,KAAK,QAAQ,YAAU,KAAK,QAAQ,SAASA,EAAC,GAAED,MAAGA,GAAE,OAAOC,EAAC,GAAEA;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,YAAM,KAAG,SAASF,IAAEC,IAAE;AAAC,eAAO,WAAU;AAAC,cAAIC,KAAE,aAAW,GAAG,UAAU,CAAC,CAAC,IAAE,UAAU,CAAC,IAAE,UAAU,CAAC,GAAEC,KAAE,MAAM,QAAQ,UAAU,CAAC,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,iBAAOD,MAAGA,GAAE,aAAWC,KAAED,GAAE,WAAU,IAAI,GAAGF,IAAEG,IAAED,IAAED,EAAC;AAAA,QAAC;AAAA,MAAC;AAAE,eAAS,GAAGD,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,YAAM,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAEC,IAAE;AAAC,cAAG,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,QAAMC,IAAE,KAAK,UAAQ,EAAE,EAAE,OAAO,CAAC,GAAE,EAAC,OAAM,IAAG,QAAOC,GAAE,WAAU,SAAQ,eAAc,WAAU,SAAQ,GAAEA,EAAC,GAAE,KAAK,WAAS,EAAE,EAAE,CAAC,8BAA6B,0CAAyC,4CAA2C,QAAQ,EAAE,KAAK,EAAE,CAAC,GAAE,aAAW,KAAK,QAAQ,SAAQ;AAAC,gBAAIC,KAAE,KAAK,KAAK,KAAK,IAAI,GAAEC,KAAE,KAAK,KAAK,KAAK,IAAI,GAAEE,KAAE,KAAK,OAAO,KAAK,IAAI;AAAE,iBAAK,QAAQ,QAAQ,MAAM,GAAG,EAAE,QAAS,SAASN,IAAE;AAAC,0BAAUA,MAAGC,GAAE,IAAI,uBAAuB,GAAEA,GAAE,GAAG,cAAaE,EAAC,EAAE,GAAG,cAAaC,EAAC,KAAG,YAAUJ,KAAEC,GAAE,GAAG,SAAQK,EAAC,IAAE,YAAUN,MAAGC,GAAE,GAAG,SAAQE,EAAC,EAAE,GAAG,QAAOC,EAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAA,QAAC,IAAGH,KAAE,CAAC,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAID,KAAE,KAAK,OAAMC,KAAED,GAAE,OAAO,GAAEE,KAAE,EAAE,EAAE,KAAK,QAAQ,MAAM,EAAE,OAAO;AAAE,UAAAD,GAAE,OAAKC,GAAE,KAAID,GAAE,QAAMC,GAAE;AAAK,cAAIC,KAAE,KAAK,UAASC,KAAE,KAAK,QAAQ,SAAOJ,GAAE,KAAK,OAAO,KAAGA,GAAE,KAAK,OAAO,GAAEM,KAAE,KAAK,QAAQ,aAAWN,GAAE,KAAK,WAAW;AAAE,UAAAG,GAAE,SAASG,EAAC,GAAEH,GAAE,KAAK,uBAAuB,EAAE,KAAKC,EAAC,GAAED,GAAE,SAAS,KAAK,QAAQ,MAAM;AAAE,cAAII,KAAEP,GAAE,WAAW,GAAEQ,KAAER,GAAE,YAAY,GAAES,KAAEN,GAAE,WAAW,GAAEO,KAAEP,GAAE,YAAY;AAAE,uBAAWG,KAAEH,GAAE,IAAI,EAAC,KAAIF,GAAE,MAAIO,IAAE,MAAKP,GAAE,QAAMM,KAAE,IAAEE,KAAE,GAAE,CAAC,IAAE,UAAQH,KAAEH,GAAE,IAAI,EAAC,KAAIF,GAAE,MAAIS,IAAE,MAAKT,GAAE,QAAMM,KAAE,IAAEE,KAAE,GAAE,CAAC,IAAE,WAASH,KAAEH,GAAE,IAAI,EAAC,KAAIF,GAAE,OAAKO,KAAE,IAAEE,KAAE,IAAG,MAAKT,GAAE,OAAKQ,GAAC,CAAC,IAAE,YAAUH,MAAGH,GAAE,IAAI,EAAC,KAAIF,GAAE,OAAKO,KAAE,IAAEE,KAAE,IAAG,MAAKT,GAAE,OAAKM,GAAC,CAAC,GAAEJ,GAAE,SAAS,IAAI;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAIH,KAAE;AAAK,eAAK,SAAS,YAAY,IAAI,GAAE,WAAY,WAAU;AAAC,YAAAA,GAAE,SAAS,OAAO;AAAA,UAAC,GAAG,GAAG;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,eAAK,SAAS,SAAS,IAAI,IAAE,KAAK,KAAK,IAAE,KAAK,KAAK;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGF,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,UAAI,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAEC,IAAE;AAAC,WAAC,SAASF,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,UAAQC,IAAE,KAAK,UAAQ,EAAE,EAAE,OAAO,CAAC,GAAE,EAAC,QAAOC,GAAE,UAAS,GAAEA,EAAC,GAAE,KAAK,SAAS;AAAA,QAAC,GAAED,KAAE,CAAC,EAAC,KAAI,YAAW,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,QAAQ,GAAG,SAAS,SAASC,IAAE;AAAC,YAAAD,GAAE,OAAO,GAAEC,GAAE,yBAAyB;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,WAAU;AAAC,cAAID,KAAE,EAAE,EAAE,sBAAsB;AAAE,UAAAA,GAAE,KAAK,kBAAkB,EAAE,YAAY,QAAQ,GAAEA,GAAE,YAAY,MAAM;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,QAAQ,SAAS,QAAQ,GAAE,KAAK,QAAQ,OAAO,EAAE,SAAS,MAAM;AAAE,cAAIA,KAAE,KAAK,QAAQ,KAAK,GAAEC,KAAED,GAAE,OAAO,GAAEE,KAAEF,GAAE,WAAW,GAAEG,KAAE,EAAE,EAAE,MAAM,EAAE,MAAM,GAAEC,KAAE,WAAW,EAAE,EAAE,KAAK,QAAQ,MAAM,EAAE,IAAI,cAAc,CAAC;AAAE,UAAAH,GAAE,OAAKC,KAAEC,KAAEC,KAAEJ,GAAE,IAAI,eAAcG,KAAEC,MAAGH,GAAE,OAAKC,GAAE,IAAEF,GAAE,IAAI,eAAc,EAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,QAAQ,YAAY,QAAQ,GAAE,KAAK,QAAQ,OAAO,EAAE,YAAY,MAAM;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,cAAIA,KAAE,KAAK,QAAQ,OAAO,EAAE,SAAS,MAAM;AAAE,eAAK,MAAM,GAAEA,KAAE,KAAK,KAAK,IAAE,KAAK,KAAK;AAAA,QAAC,EAAC,CAAC,GAAEC,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,QAAE,EAAE,QAAQ,EAAE,GAAG,4BAA4B,SAASF,IAAE;AAAC,UAAE,EAAEA,GAAE,MAAM,EAAE,QAAQ,iBAAiB,EAAE,WAAS,EAAE,EAAE,uCAAuC,EAAE,YAAY,QAAQ,GAAE,EAAE,EAAE,sBAAsB,EAAE,YAAY,MAAM;AAAA,MAAE,CAAE,GAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,4BAA4B,SAASA,IAAE;AAAC,UAAE,EAAEA,GAAE,MAAM,EAAE,QAAQ,qBAAqB,EAAE,OAAO,EAAE,YAAY,MAAM,GAAE,EAAE,EAAEA,GAAE,MAAM,EAAE,QAAQ,qBAAqB,EAAE,OAAO,EAAE,KAAK,kBAAkB,EAAE,YAAY,QAAQ;AAAA,MAAC,CAAE;AAAE,YAAM,KAAG;AAAG,eAAS,GAAGA,IAAE;AAAC,eAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAE,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,GAAGA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAE,GAAGG,GAAE,GAAG,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,GAAGH,IAAE;AAAC,YAAIC,KAAE,SAASD,IAAEC,IAAE;AAAC,cAAG,YAAU,GAAGD,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,cAAIE,KAAEF,GAAE,OAAO,WAAW;AAAE,cAAG,WAASE,IAAE;AAAC,gBAAIC,KAAED,GAAE,KAAKF,IAAEC,MAAG,SAAS;AAAE,gBAAG,YAAU,GAAGE,EAAC,EAAE,QAAOA;AAAE,kBAAM,IAAI,UAAU,8CAA8C;AAAA,UAAC;AAAC,kBAAO,aAAWF,KAAE,SAAO,QAAQD,EAAC;AAAA,QAAC,EAAEA,IAAE,QAAQ;AAAE,eAAM,YAAU,GAAGC,EAAC,IAAEA,KAAEA,KAAE;AAAA,MAAE;AAAC,YAAM,KAAG,WAAU;AAAC,eAAOD,KAAE,SAASA,GAAEC,IAAE;AAAC,WAAC,SAASD,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAAC,EAAE,MAAKD,EAAC,GAAE,KAAK,SAAOC,IAAE,KAAK,YAAU,EAAE,EAAE,yCAAyC;AAAA,QAAC,IAAGA,KAAE,CAAC,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,UAAU,SAAS,SAAS,IAAI,EAAE,KAAK,GAAE,KAAK,OAAO,SAAS,MAAM,EAAE,KAAK,GAAE,KAAK,OAAO,QAAQ,iBAAiB,GAAE,KAAK,OAAO,IAAI,SAAQ,QAAQ,EAAE,GAAG,SAAQ,UAAS,KAAK,KAAK,KAAK,IAAI,CAAC,GAAE,KAAK,OAAO,GAAG,WAAW,SAASC,IAAE;AAAC,mBAAKA,GAAE,UAAQA,GAAE,eAAe,GAAED,GAAE,KAAK;AAAA,UAAE,CAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,OAAO,YAAY,MAAM,EAAE,KAAK,GAAE,KAAK,UAAU,KAAK,GAAE,KAAK,OAAO,QAAQ,iBAAiB,GAAE,KAAK,OAAO,IAAI,SAAS;AAAA,QAAC,EAAC,CAAC,MAAI,GAAGA,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEA;AAAE,YAAIA,IAAEC,IAAEC;AAAA,MAAC,EAAE;AAAE,UAAI,KAAG,GAAG,4CAA4C,GAAE,KAAG,GAAG,iDAAiD,GAAE,KAAG,GAAG,uCAAuC,GAAE,KAAG,GAAG,kEAAkE,GAAE,KAAG,GAAG,+FAA+F,GAAE,KAAG,GAAG,CAAC,iFAAgF,8CAA6C,oDAAmD,qCAAoC,qCAAoC,qCAAoC,UAAS,QAAQ,EAAE,KAAK,EAAE,CAAC,GAAE,KAAG,GAAG,+CAA+C,GAAE,KAAG,GAAG,CAAC,iGAAgG,+EAA+E,EAAE,KAAK,EAAE,CAAC,GAAE,KAAG,GAAG,oCAAoC,GAAE,KAAG,GAAG,kEAAkE,SAASF,IAAEC,IAAE;AAAC,QAAAA,MAAGA,GAAE,YAAUD,GAAE,KAAK,EAAC,cAAaC,GAAE,QAAO,CAAC,GAAED,GAAE,KAAK,iBAAgB,IAAI,GAAGA,IAAE,EAAC,OAAMC,GAAE,SAAQ,WAAUA,GAAE,UAAS,CAAC,CAAC,EAAE,GAAG,SAAS,SAASD,IAAE;AAAC,YAAE,EAAEA,GAAE,aAAa,EAAE,KAAK,eAAe,EAAE,KAAK;AAAA,QAAC,CAAE,IAAGC,GAAE,YAAUD,GAAE,KAAKC,GAAE,QAAQ,GAAEA,MAAGA,GAAE,QAAM,eAAaA,GAAE,KAAK,UAAQD,GAAE,KAAK,kBAAiB,IAAI,GAAGA,IAAE,EAAC,WAAUC,GAAE,UAAS,CAAC,CAAC,GAAEA,MAAGA,GAAE,sBAAoBD,GAAE,SAAS,oBAAoB;AAAA,MAAC,CAAE,GAAE,KAAG,GAAG,sDAAsD,SAASA,IAAEC,IAAE;AAAC,YAAIC,KAAE,MAAM,QAAQD,GAAE,KAAK,IAAEA,GAAE,MAAM,IAAK,SAASD,IAAE;AAAC,cAAIE,KAAE,YAAU,OAAOF,KAAEA,KAAEA,GAAE,SAAO,IAAGG,KAAEF,GAAE,WAASA,GAAE,SAASD,EAAC,IAAEA,IAAEI,KAAE,EAAE,EAAE,wDAAsDF,KAAE,mCAAiCA,KAAE,QAAQ;AAAE,iBAAOE,GAAE,KAAKD,EAAC,EAAE,KAAK,QAAOH,EAAC,GAAEI;AAAA,QAAC,CAAE,IAAEH,GAAE;AAAM,QAAAD,GAAE,KAAKE,EAAC,EAAE,KAAK,EAAC,cAAaD,GAAE,MAAK,CAAC,GAAED,GAAE,GAAG,SAAQ,yBAAyB,SAASA,IAAE;AAAC,cAAIE,KAAE,EAAE,EAAE,IAAI,GAAEC,KAAED,GAAE,KAAK,MAAM,GAAEE,KAAEF,GAAE,KAAK,OAAO;AAAE,UAAAC,GAAE,QAAMA,GAAE,MAAMD,EAAC,IAAED,GAAE,aAAWA,GAAE,UAAUD,IAAEG,IAAEC,EAAC;AAAA,QAAC,CAAE,GAAEH,MAAGA,GAAE,sBAAoBD,GAAE,SAAS,oBAAoB;AAAA,MAAC,CAAE,GAAE,KAAG,GAAG,iEAAiE,SAASA,IAAEC,IAAE;AAAC,YAAIC,KAAE,MAAM,QAAQD,GAAE,KAAK,IAAEA,GAAE,MAAM,IAAK,SAASD,IAAE;AAAC,cAAIE,KAAE,YAAU,OAAOF,KAAEA,KAAEA,GAAE,SAAO,IAAGG,KAAEF,GAAE,WAASA,GAAE,SAASD,EAAC,IAAEA,IAAEI,KAAE,EAAE,EAAE,wDAAsDF,KAAE,mCAAiCF,KAAE,QAAQ;AAAE,iBAAOI,GAAE,KAAK,CAAC,GAAGH,GAAE,cAAc,GAAE,KAAIE,EAAC,CAAC,EAAE,KAAK,QAAOH,EAAC,GAAEI;AAAA,QAAC,CAAE,IAAEH,GAAE;AAAM,QAAAD,GAAE,KAAKE,EAAC,EAAE,KAAK,EAAC,cAAaD,GAAE,MAAK,CAAC,GAAED,GAAE,GAAG,SAAQ,yBAAyB,SAASA,IAAE;AAAC,cAAIE,KAAE,EAAE,EAAE,IAAI,GAAEC,KAAED,GAAE,KAAK,MAAM,GAAEE,KAAEF,GAAE,KAAK,OAAO;AAAE,UAAAC,GAAE,QAAMA,GAAE,MAAMD,EAAC,IAAED,GAAE,aAAWA,GAAE,UAAUD,IAAEG,IAAEC,EAAC;AAAA,QAAC,CAAE,GAAEH,MAAGA,GAAE,sBAAoBD,GAAE,SAAS,oBAAoB;AAAA,MAAC,CAAE,GAAE,KAAG,SAASA,IAAEC,IAAE;AAAC,eAAOD,KAAE,MAAI,GAAGC,GAAE,MAAM,OAAM,MAAM;AAAA,MAAC,GAAE,KAAG,SAASD,IAAEC,IAAE;AAAC,eAAO,GAAG,CAAC,GAAG,EAAC,WAAU,mBAAkB,UAASD,GAAE,QAAM,MAAI,GAAG,iBAAiB,GAAE,SAAQA,GAAE,SAAQ,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAE,GAAG,EAAC,WAAUA,GAAE,WAAU,OAAMA,GAAE,OAAM,UAASA,GAAE,UAAS,WAAUA,GAAE,UAAS,CAAC,CAAC,GAAE,EAAC,UAASC,GAAC,CAAC,EAAE,OAAO;AAAA,MAAC,GAAE,KAAG,SAASD,IAAEC,IAAE;AAAC,eAAO,GAAG,CAAC,GAAG,EAAC,WAAU,mBAAkB,UAASD,GAAE,QAAM,MAAI,GAAG,iBAAiB,GAAE,SAAQA,GAAE,SAAQ,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAE,GAAG,EAAC,WAAUA,GAAE,WAAU,gBAAeA,GAAE,gBAAe,OAAMA,GAAE,OAAM,UAASA,GAAE,UAAS,WAAUA,GAAE,UAAS,CAAC,CAAC,GAAE,EAAC,UAASC,GAAC,CAAC,EAAE,OAAO;AAAA,MAAC,GAAE,KAAG,SAASD,IAAE;AAAC,eAAO,GAAG,CAAC,GAAG,EAAC,WAAU,mBAAkB,UAASA,GAAE,QAAM,MAAI,GAAG,iBAAiB,GAAE,SAAQA,GAAE,SAAQ,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAE,GAAG,CAAC,GAAG,EAAC,WAAU,cAAa,UAASA,GAAE,MAAM,CAAC,EAAC,CAAC,GAAE,GAAG,EAAC,WAAU,aAAY,UAASA,GAAE,MAAM,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO;AAAA,MAAC,GAAE,KAAG,SAASA,IAAE;AAAC,eAAO,GAAG,CAAC,GAAG,EAAC,WAAU,mBAAkB,UAASA,GAAE,QAAM,MAAI,GAAG,iBAAiB,GAAE,SAAQA,GAAE,SAAQ,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAE,GAAG,EAAC,WAAU,cAAa,OAAM,CAAC,uCAAsC,oGAAmG,yDAAwD,2DAA0D,UAAS,iDAAiD,EAAE,KAAK,EAAE,EAAC,CAAC,CAAC,GAAE,EAAC,UAAS,SAASC,IAAE;AAAC,UAAAA,GAAE,KAAK,qCAAqC,EAAE,IAAI,EAAC,OAAMD,GAAE,MAAI,MAAK,QAAOA,GAAE,MAAI,KAAI,CAAC,EAAE,GAAG,WAAUA,GAAE,SAAS,EAAE,GAAG,aAAa,SAASC,IAAE;AAAC,aAAC,SAASD,IAAEC,IAAEC,IAAE;AAAC,kBAAIC,IAAEC,KAAE,EAAE,EAAEJ,GAAE,OAAO,UAAU,GAAEM,KAAEF,GAAE,KAAK,GAAEG,KAAEH,GAAE,KAAK,qCAAqC,GAAEI,KAAEJ,GAAE,KAAK,oCAAoC,GAAEK,KAAEL,GAAE,KAAK,sCAAsC;AAAE,kBAAG,WAASJ,GAAE,SAAQ;AAAC,oBAAIU,KAAE,EAAE,EAAEV,GAAE,MAAM,EAAE,OAAO;AAAE,gBAAAG,KAAE,EAAC,GAAEH,GAAE,QAAMU,GAAE,MAAK,GAAEV,GAAE,QAAMU,GAAE,IAAG;AAAA,cAAC,MAAM,CAAAP,KAAE,EAAC,GAAEH,GAAE,SAAQ,GAAEA,GAAE,QAAO;AAAE,kBAAIW,KAAE,KAAK,KAAKR,GAAE,IAAE,EAAE,KAAG,GAAES,KAAE,KAAK,KAAKT,GAAE,IAAE,EAAE,KAAG;AAAE,cAAAK,GAAE,IAAI,EAAC,OAAMG,KAAE,MAAK,QAAOC,KAAE,KAAI,CAAC,GAAEL,GAAE,KAAK,SAAQI,KAAE,MAAIC,EAAC,GAAED,KAAE,KAAGA,KAAEV,MAAGQ,GAAE,IAAI,EAAC,OAAME,KAAE,IAAE,KAAI,CAAC,GAAEC,KAAE,KAAGA,KAAEV,MAAGO,GAAE,IAAI,EAAC,QAAOG,KAAE,IAAE,KAAI,CAAC,GAAEN,GAAE,KAAKK,KAAE,QAAMC,EAAC;AAAA,YAAC,EAAEX,IAAED,GAAE,KAAIA,GAAE,GAAG;AAAA,UAAC,CAAE;AAAA,QAAC,EAAC,CAAC,EAAE,OAAO;AAAA,MAAC,GAAE,KAAG,GAAG,0CAA0C,SAASA,IAAEC,IAAE;AAAC,iBAAQC,KAAE,CAAC,GAAEC,KAAE,GAAEC,KAAEH,GAAE,OAAO,QAAOE,KAAEC,IAAED,MAAI;AAAC,mBAAQG,KAAEL,GAAE,WAAUM,KAAEN,GAAE,OAAOE,EAAC,GAAEK,KAAEP,GAAE,WAAWE,EAAC,GAAEM,KAAE,CAAC,GAAEC,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAEC,IAAED,MAAI;AAAC,gBAAIE,KAAEL,GAAEG,EAAC,GAAEG,KAAEL,GAAEE,EAAC;AAAE,YAAAD,GAAE,KAAK,CAAC,yDAAwD,4BAA2BG,IAAE,MAAK,gBAAeN,IAAE,MAAK,gBAAeM,IAAE,MAAK,gBAAeC,IAAE,MAAK,gBAAeA,IAAE,MAAK,8CAA8C,EAAE,KAAK,EAAE,CAAC;AAAA,UAAC;AAAC,UAAAX,GAAE,KAAK,iCAA+BO,GAAE,KAAK,EAAE,IAAE,QAAQ;AAAA,QAAC;AAAC,QAAAT,GAAE,KAAKE,GAAE,KAAK,EAAE,CAAC,GAAEF,GAAE,KAAK,iBAAiB,EAAE,KAAM,WAAU;AAAC,YAAE,EAAE,IAAI,EAAE,KAAK,iBAAgB,IAAI,GAAG,EAAE,EAAE,IAAI,GAAE,EAAC,WAAUC,GAAE,UAAS,CAAC,CAAC;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE,GAAE,KAAG,SAASD,IAAEC,IAAE;AAAC,eAAO,GAAG,EAAC,WAAU,cAAa,UAAS,CAAC,GAAG,EAAC,WAAU,6BAA4B,UAASD,GAAE,OAAM,SAAQA,GAAE,KAAK,MAAM,QAAO,OAAMA,GAAE,cAAa,UAAS,SAASA,IAAE;AAAC,cAAIE,KAAEF,GAAE,KAAK,oBAAoB;AAAE,0BAAcC,OAAIC,GAAE,IAAI,oBAAmB,SAAS,GAAEF,GAAE,KAAK,kBAAiB,SAAS;AAAA,QAAE,EAAC,CAAC,GAAE,GAAG,EAAC,WAAU,mBAAkB,UAAS,GAAG,iBAAiB,GAAE,SAAQA,GAAE,KAAK,MAAM,MAAK,MAAK,EAAC,QAAO,WAAU,EAAC,CAAC,GAAE,GAAG,EAAC,OAAM,CAAC,SAAQ,qDAAoD,qCAAmCA,GAAE,KAAK,MAAM,aAAW,UAAS,SAAQ,2HAA0HA,GAAE,KAAK,MAAM,aAAY,aAAY,UAAS,0DAAyD,wBAAuB,uHAAsH,uGAAsGA,GAAE,KAAK,MAAM,UAAS,aAAY,UAAS,UAAS,qDAAoD,qCAAmCA,GAAE,KAAK,MAAM,aAAW,UAAS,SAAQ,4HAA2HA,GAAE,KAAK,MAAM,gBAAe,aAAY,UAAS,0DAAyD,wBAAuB,uHAAsH,uGAAsGA,GAAE,KAAK,MAAM,UAAS,aAAY,UAAS,UAAS,QAAQ,EAAE,KAAK,EAAE,GAAE,UAAS,SAASE,IAAE;AAAC,UAAAA,GAAE,KAAK,cAAc,EAAE,KAAM,WAAU;AAAC,gBAAID,KAAE,EAAE,EAAE,IAAI;AAAE,YAAAA,GAAE,OAAO,GAAG,EAAC,QAAOD,GAAE,QAAO,WAAUC,GAAE,KAAK,OAAO,EAAC,CAAC,EAAE,OAAO,CAAC;AAAA,UAAC,CAAE,GAAE,WAASA,MAAGC,GAAE,KAAK,uBAAuB,EAAE,KAAK,GAAEA,GAAE,IAAI,EAAC,aAAY,QAAO,CAAC,KAAG,WAASD,OAAIC,GAAE,KAAK,uBAAuB,EAAE,KAAK,GAAEA,GAAE,IAAI,EAAC,aAAY,QAAO,CAAC;AAAA,QAAE,GAAE,OAAM,SAASA,IAAE;AAAC,cAAIC,KAAE,EAAE,EAAED,GAAE,MAAM,GAAEE,KAAED,GAAE,KAAK,OAAO,GAAEG,KAAEH,GAAE,KAAK,OAAO,GAAEI,KAAE,SAAS,eAAe,UAAU,EAAE,OAAMC,KAAE,SAAS,eAAe,UAAU,EAAE;AAAM,cAAG,SAAOF,KAAEJ,GAAE,gBAAgB,IAAE,kBAAgBI,KAAEA,KAAEE,KAAE,kBAAgBF,OAAIA,KAAEC,KAAGH,MAAGE,IAAE;AAAC,gBAAIG,KAAE,gBAAcL,KAAE,qBAAmB,SAAQM,KAAEP,GAAE,QAAQ,aAAa,EAAE,KAAK,oBAAoB,GAAEQ,KAAER,GAAE,QAAQ,aAAa,EAAE,KAAK,4BAA4B;AAAE,YAAAO,GAAE,IAAID,IAAEH,EAAC,GAAEK,GAAE,KAAK,UAAQP,IAAEE,EAAC,GAAE,WAASL,KAAED,GAAE,UAAU,aAAYM,EAAC,IAAE,WAASL,KAAED,GAAE,UAAU,aAAYM,EAAC,IAAEN,GAAE,UAAUI,IAAEE,EAAC;AAAA,UAAC;AAAA,QAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAE,OAAO;AAAA,MAAC,GAAE,KAAG,GAAG,kFAAkF,SAASN,IAAEC,IAAE;AAAC,QAAAA,GAAE,QAAMD,GAAE,SAAS,MAAM,GAAEA,GAAE,KAAK,EAAC,cAAaC,GAAE,MAAK,CAAC,GAAED,GAAE,KAAK,CAAC,oCAAmCC,GAAE,QAAM,mLAAiLA,GAAE,QAAM,gBAAc,IAAG,kCAAgCA,GAAE,OAAK,UAASA,GAAE,SAAO,oCAAkCA,GAAE,SAAO,WAAS,IAAG,QAAQ,EAAE,KAAK,EAAE,CAAC,GAAED,GAAE,KAAK,SAAQ,IAAI,GAAGA,IAAEC,EAAC,CAAC;AAAA,MAAC,CAAE,GAAE,KAAG,SAASD,IAAE;AAAC,YAAIC,KAAE,oEAAkED,GAAE,KAAG,+BAA6BA,GAAE,KAAK,MAAM,MAAI,gCAA8BA,GAAE,KAAK,MAAM,YAAU,sDAAoDA,GAAE,KAAG,2DAA0DE,KAAE,CAAC,sGAAqGF,GAAE,KAAK,MAAM,QAAO,WAAW,EAAE,KAAK,EAAE;AAAE,eAAO,GAAG,EAAC,OAAMA,GAAE,KAAK,MAAM,QAAO,MAAKA,GAAE,MAAK,MAAKC,IAAE,QAAOC,GAAC,CAAC,EAAE,OAAO;AAAA,MAAC,GAAE,KAAG,SAASF,IAAE;AAAC,YAAIC,KAAE,kGAAgGD,GAAE,KAAG,+BAA6BA,GAAE,KAAK,MAAM,kBAAgB,+CAA6CA,GAAE,KAAG,+GAA6GA,GAAE,kBAAgB,0EAAwEA,GAAE,KAAG,+BAA6BA,GAAE,KAAK,MAAM,MAAI,8CAA4CA,GAAE,KAAG,2DAA0DE,KAAE,CAAC,qHAAoHF,GAAE,KAAK,MAAM,QAAO,WAAW,EAAE,KAAK,EAAE;AAAE,eAAO,GAAG,EAAC,OAAMA,GAAE,KAAK,MAAM,QAAO,MAAKA,GAAE,MAAK,MAAKC,IAAE,QAAOC,GAAC,CAAC,EAAE,OAAO;AAAA,MAAC,GAAE,KAAG,SAASF,IAAE;AAAC,YAAIC,KAAE,mEAAiED,GAAE,KAAG,+BAA6BA,GAAE,KAAK,KAAK,gBAAc,6CAA2CA,GAAE,KAAG,0HAAwHA,GAAE,KAAG,+BAA6BA,GAAE,KAAK,KAAK,MAAI,6CAA2CA,GAAE,KAAG,4EAA0EA,GAAE,oBAAkB,KAAG,2DAAyDA,GAAE,KAAG,sCAAoCA,GAAE,KAAG,gCAA8BA,GAAE,KAAK,KAAK,kBAAgB,mBAAkBE,KAAE,CAAC,qGAAoGF,GAAE,KAAK,KAAK,QAAO,WAAW,EAAE,KAAK,EAAE;AAAE,eAAO,GAAG,EAAC,WAAU,eAAc,OAAMA,GAAE,KAAK,KAAK,QAAO,MAAKA,GAAE,MAAK,MAAKC,IAAE,QAAOC,GAAC,CAAC,EAAE,OAAO;AAAA,MAAC,GAAE,KAAG,GAAG,CAAC,qCAAoC,0CAAyC,+DAA8D,QAAQ,EAAE,KAAK,EAAE,GAAG,SAASF,IAAEC,IAAE;AAAC,YAAIC,KAAE,WAASD,GAAE,YAAUA,GAAE,YAAU;AAAS,QAAAD,GAAE,SAASE,EAAC,EAAE,KAAK,GAAED,GAAE,aAAWD,GAAE,KAAK,qBAAqB,EAAE,KAAK;AAAA,MAAC,CAAE,GAAE,KAAG,GAAG,gCAAgC,SAASA,IAAEC,IAAE;AAAC,QAAAD,GAAE,KAAK,CAAC,YAAUC,GAAE,KAAG,gBAAcA,GAAE,KAAG,MAAI,MAAI,KAAI,4CAA0CA,GAAE,KAAG,eAAaA,GAAE,KAAG,MAAI,KAAIA,GAAE,UAAQ,aAAW,IAAG,qBAAmBA,GAAE,UAAQ,SAAO,WAAS,OAAMA,GAAE,OAAKA,GAAE,OAAK,IAAG,UAAU,EAAE,KAAK,EAAE,CAAC;AAAA,MAAC,CAAE,GAAE,KAAG,SAASD,IAAEC,IAAE;AAAC,eAAOD,GAAE,MAAM,IAAI,IAAEA,KAAE,OAAKC,KAAEA,MAAG,OAAK,aAAWD,KAAE,SAAOC,KAAE;AAAA,MAAG;AAAE,aAAO,EAAE,EAAE,aAAW,EAAE,EAAE,OAAO,EAAE,EAAE,YAAW,EAAC,aAAY,SAASD,IAAE;AAAC,eAAM,EAAC,QAAO,IAAG,SAAQ,IAAG,aAAY,IAAG,SAAQ,IAAG,UAAS,IAAG,WAAU,IAAG,WAAU,IAAG,aAAY,IAAG,aAAY,IAAG,QAAO,IAAG,UAAS,IAAG,eAAc,IAAG,gBAAe,IAAG,wBAAuB,IAAG,qBAAoB,IAAG,yBAAwB,IAAG,qBAAoB,IAAG,qBAAoB,IAAG,SAAQ,IAAG,QAAO,IAAG,aAAY,IAAG,aAAY,IAAG,YAAW,IAAG,SAAQ,IAAG,UAAS,IAAG,MAAK,IAAG,SAAQA,IAAE,WAAU,SAASA,IAAEC,IAAE;AAAC,UAAAD,GAAE,YAAY,YAAW,CAACC,EAAC,GAAED,GAAE,KAAK,YAAW,CAACC,EAAC;AAAA,QAAC,GAAE,iBAAgB,SAASD,IAAEC,IAAE;AAAC,UAAAD,GAAE,YAAY,UAASC,EAAC;AAAA,QAAC,GAAE,OAAM,SAASD,IAAEC,IAAE;AAAC,UAAAD,GAAE,KAAK,UAAU,EAAE,YAAY,SAAS,GAAEA,GAAE,KAAK,kBAAgBC,KAAE,IAAI,EAAE,SAAS,SAAS;AAAA,QAAC,GAAE,eAAc,SAASD,IAAEC,IAAE;AAAC,UAAAD,GAAE,IAAI,mBAAkBC,EAAC;AAAA,QAAC,GAAE,gBAAe,SAASD,IAAEC,IAAE;AAAC,UAAAD,GAAE,IAAI,mBAAkBC,EAAC;AAAA,QAAC,GAAE,YAAW,SAASD,IAAE;AAAC,UAAAA,GAAE,KAAK,OAAO,EAAE,KAAK;AAAA,QAAC,GAAE,YAAW,SAASA,IAAE;AAAC,UAAAA,GAAE,KAAK,OAAO,EAAE,KAAK;AAAA,QAAC,GAAE,mBAAkB,SAASA,IAAE;AAAC,iBAAOA,GAAE,KAAK,uBAAuB;AAAA,QAAC,GAAE,eAAc,SAASA,IAAE;AAAC,iBAAOA,GAAE,KAAK,kBAAkB;AAAA,QAAC,GAAE,cAAa,SAASC,IAAE;AAAC,cAAIC,MAAGF,GAAE,UAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,GAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAE,aAAWA,GAAE,kBAAgB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAE,GAAG,CAAC,CAAC,GAAE,GAAG,GAAE,GAAG,CAAC,CAAC,IAAE,GAAG,CAAC,GAAG,GAAE,GAAG,CAAC,GAAG,GAAE,GAAG,CAAC,CAAC,GAAE,GAAG,CAAC,CAAC,GAAG,OAAO;AAAE,iBAAOE,GAAE,YAAYD,EAAC,GAAE,EAAC,MAAKA,IAAE,QAAOC,IAAE,SAAQA,GAAE,KAAK,eAAe,GAAE,aAAYA,GAAE,KAAK,oBAAoB,GAAE,UAASA,GAAE,KAAK,gBAAgB,GAAE,SAAQA,GAAE,KAAK,eAAe,GAAE,WAAUA,GAAE,KAAK,iBAAiB,EAAC;AAAA,QAAC,GAAE,cAAa,SAASF,IAAEC,IAAE;AAAC,UAAAD,GAAE,KAAKC,GAAE,SAAS,KAAK,CAAC,GAAEA,GAAE,OAAO,OAAO,GAAED,GAAE,IAAI,YAAY,GAAEA,GAAE,KAAK;AAAA,QAAC,EAAC;AAAA,MAAC,GAAE,WAAU,OAAM,CAAC,GAAE,CAAC;AAAA,IAAC,GAAG,CAAE;AAAA;AAAA;", "names": ["t", "e", "o", "n", "i", "r", "a", "s", "l", "c", "u", "d", "f", "h", "p", "m", "v", "g", "b", "y", "k", "w", "C", "S", "x", "T", "E", "P", "N"]}