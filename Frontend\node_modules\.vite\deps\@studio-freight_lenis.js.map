{"version": 3, "sources": ["../../@studio-freight/lenis/src/maths.js", "../../@studio-freight/lenis/src/animate.js", "../../@studio-freight/lenis/src/dimensions.js", "../../@studio-freight/lenis/src/debounce.js", "../../@studio-freight/lenis/src/emitter.js", "../../@studio-freight/lenis/src/virtual-scroll.js", "../../@studio-freight/src/index.ts"], "sourcesContent": ["// Clamp a value between a minimum and maximum value\r\nexport function clamp(min, input, max) {\r\n  return Math.max(min, Math.min(input, max))\r\n}\r\n\r\n// Truncate a floating-point number to a specified number of decimal places\r\nexport function truncate(value, decimals = 0) {\r\n  return parseFloat(value.toFixed(decimals))\r\n}\r\n\r\n// Linearly interpolate between two values using an amount (0 <= t <= 1)\r\nexport function lerp(x, y, t) {\r\n  return (1 - t) * x + t * y\r\n}\r\n\r\n// http://www.rorydriscoll.com/2016/03/07/frame-rate-independent-damping-using-lerp/\r\nexport function damp(x, y, lambda, dt) {\r\n  return lerp(x, y, 1 - Math.exp(-lambda * dt))\r\n}\r\n\r\n// Calculate the modulo of the dividend and divisor while keeping the result within the same sign as the divisor\r\n// https://anguscroll.com/just/just-modulo\r\nexport function modulo(n, d) {\r\n  return ((n % d) + d) % d\r\n}\r\n", "import { clamp, damp } from './maths'\r\n\r\n// Animate class to handle value animations with lerping or easing\r\nexport class Animate {\r\n  // Advance the animation by the given delta time\r\n  advance(deltaTime) {\r\n    if (!this.isRunning) return\r\n\r\n    let completed = false\r\n\r\n    if (this.lerp) {\r\n      this.value = damp(this.value, this.to, this.lerp * 60, deltaTime)\r\n      if (Math.round(this.value) === this.to) {\r\n        this.value = this.to\r\n        completed = true\r\n      }\r\n    } else {\r\n      this.currentTime += deltaTime\r\n      const linearProgress = clamp(0, this.currentTime / this.duration, 1)\r\n\r\n      completed = linearProgress >= 1\r\n      const easedProgress = completed ? 1 : this.easing(linearProgress)\r\n      this.value = this.from + (this.to - this.from) * easedProgress\r\n    }\r\n\r\n    // Call the onUpdate callback with the current value and completed status\r\n    this.onUpdate?.(this.value, completed)\r\n\r\n    if (completed) {\r\n      this.stop()\r\n    }\r\n  }\r\n\r\n  // Stop the animation\r\n  stop() {\r\n    this.isRunning = false\r\n  }\r\n\r\n  // Set up the animation from a starting value to an ending value\r\n  // with optional parameters for lerping, duration, easing, and onUpdate callback\r\n  fromTo(\r\n    from,\r\n    to,\r\n    { lerp = 0.1, duration = 1, easing = (t) => t, onStart, onUpdate }\r\n  ) {\r\n    this.from = this.value = from\r\n    this.to = to\r\n    this.lerp = lerp\r\n    this.duration = duration\r\n    this.easing = easing\r\n    this.currentTime = 0\r\n    this.isRunning = true\r\n\r\n    onStart?.()\r\n    this.onUpdate = onUpdate\r\n  }\r\n}\r\n", "import { debounce } from './debounce'\r\n\r\nexport class Dimensions {\r\n  constructor({\r\n    wrapper,\r\n    content,\r\n    autoResize = true,\r\n    debounce: debounceValue = 250,\r\n  } = {}) {\r\n    this.wrapper = wrapper\r\n    this.content = content\r\n\r\n    if (autoResize) {\r\n      this.debouncedResize = debounce(this.resize, debounceValue)\r\n\r\n      if (this.wrapper === window) {\r\n        window.addEventListener('resize', this.debouncedResize, false)\r\n      } else {\r\n        this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize)\r\n        this.wrapperResizeObserver.observe(this.wrapper)\r\n      }\r\n\r\n      this.contentResizeObserver = new ResizeObserver(this.debouncedResize)\r\n      this.contentResizeObserver.observe(this.content)\r\n    }\r\n\r\n    this.resize()\r\n  }\r\n\r\n  destroy() {\r\n    this.wrapperResizeObserver?.disconnect()\r\n    this.contentResizeObserver?.disconnect()\r\n    window.removeEventListener('resize', this.debouncedResize, false)\r\n  }\r\n\r\n  resize = () => {\r\n    this.onWrapperResize()\r\n    this.onContentResize()\r\n  }\r\n\r\n  onWrapperResize = () => {\r\n    if (this.wrapper === window) {\r\n      this.width = window.innerWidth\r\n      this.height = window.innerHeight\r\n    } else {\r\n      this.width = this.wrapper.clientWidth\r\n      this.height = this.wrapper.clientHeight\r\n    }\r\n  }\r\n\r\n  onContentResize = () => {\r\n    if (this.wrapper === window) {\r\n      this.scrollHeight = this.content.scrollHeight\r\n      this.scrollWidth = this.content.scrollWidth\r\n    } else {\r\n      this.scrollHeight = this.wrapper.scrollHeight\r\n      this.scrollWidth = this.wrapper.scrollWidth\r\n    }\r\n  }\r\n\r\n  get limit() {\r\n    return {\r\n      x: this.scrollWidth - this.width,\r\n      y: this.scrollHeight - this.height,\r\n    }\r\n  }\r\n}\r\n", "export function debounce(callback, delay) {\r\n  let timer\r\n  return function () {\r\n    let args = arguments\r\n    let context = this\r\n    clearTimeout(timer)\r\n    timer = setTimeout(function () {\r\n      callback.apply(context, args)\r\n    }, delay)\r\n  }\r\n}\r\n", "export class Emitter {\r\n  constructor() {\r\n    this.events = {}\r\n  }\r\n\r\n  emit(event, ...args) {\r\n    let callbacks = this.events[event] || []\r\n    for (let i = 0, length = callbacks.length; i < length; i++) {\r\n      callbacks[i](...args)\r\n    }\r\n  }\r\n\r\n  on(event, cb) {\r\n    // Add the callback to the event's callback list, or create a new list with the callback\r\n    this.events[event]?.push(cb) || (this.events[event] = [cb])\r\n\r\n    // Return an unsubscribe function\r\n    return () => {\r\n      this.events[event] = this.events[event]?.filter((i) => cb !== i)\r\n    }\r\n  }\r\n\r\n  off(event, callback) {\r\n    this.events[event] = this.events[event]?.filter((i) => callback !== i)\r\n  }\r\n\r\n  destroy() {\r\n    this.events = {}\r\n  }\r\n}\r\n", "import { Emitter } from './emitter'\r\n\r\nconst LINE_HEIGHT = 100 / 6\r\n\r\nexport class VirtualScroll {\r\n  constructor(element, { wheelMultiplier = 1, touchMultiplier = 1 }) {\r\n    this.element = element\r\n    this.wheelMultiplier = wheelMultiplier\r\n    this.touchMultiplier = touchMultiplier\r\n\r\n    this.touchStart = {\r\n      x: null,\r\n      y: null,\r\n    }\r\n\r\n    this.emitter = new Emitter()\r\n    window.addEventListener('resize', this.onWindowResize, false)\r\n    this.onWindowResize()\r\n\r\n    this.element.addEventListener('wheel', this.onWheel, { passive: false })\r\n    this.element.addEventListener('touchstart', this.onTouchStart, {\r\n      passive: false,\r\n    })\r\n    this.element.addEventListener('touchmove', this.onTouchMove, {\r\n      passive: false,\r\n    })\r\n    this.element.addEventListener('touchend', this.onTouchEnd, {\r\n      passive: false,\r\n    })\r\n  }\r\n\r\n  // Add an event listener for the given event and callback\r\n  on(event, callback) {\r\n    return this.emitter.on(event, callback)\r\n  }\r\n\r\n  // Remove all event listeners and clean up\r\n  destroy() {\r\n    this.emitter.destroy()\r\n\r\n    window.removeEventListener('resize', this.onWindowResize, false)\r\n\r\n    this.element.removeEventListener('wheel', this.onWheel, {\r\n      passive: false,\r\n    })\r\n    this.element.removeEventListener('touchstart', this.onTouchStart, {\r\n      passive: false,\r\n    })\r\n    this.element.removeEventListener('touchmove', this.onTouchMove, {\r\n      passive: false,\r\n    })\r\n    this.element.removeEventListener('touchend', this.onTouchEnd, {\r\n      passive: false,\r\n    })\r\n  }\r\n\r\n  // Event handler for 'touchstart' event\r\n  onTouchStart = (event) => {\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: 0,\r\n      y: 0,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX: 0,\r\n      deltaY: 0,\r\n      event,\r\n    })\r\n  }\r\n\r\n  // Event handler for 'touchmove' event\r\n  onTouchMove = (event) => {\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    const deltaX = -(clientX - this.touchStart.x) * this.touchMultiplier\r\n    const deltaY = -(clientY - this.touchStart.y) * this.touchMultiplier\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: deltaX,\r\n      y: deltaY,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX,\r\n      deltaY,\r\n      event,\r\n    })\r\n  }\r\n\r\n  onTouchEnd = (event) => {\r\n    this.emitter.emit('scroll', {\r\n      deltaX: this.lastDelta.x,\r\n      deltaY: this.lastDelta.y,\r\n      event,\r\n    })\r\n  }\r\n\r\n  // Event handler for 'wheel' event\r\n  onWheel = (event) => {\r\n    let { deltaX, deltaY, deltaMode } = event\r\n\r\n    const multiplierX =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.windowWidth : 1\r\n    const multiplierY =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.windowHeight : 1\r\n\r\n    deltaX *= multiplierX\r\n    deltaY *= multiplierY\r\n\r\n    deltaX *= this.wheelMultiplier\r\n    deltaY *= this.wheelMultiplier\r\n\r\n    this.emitter.emit('scroll', { deltaX, deltaY, event })\r\n  }\r\n\r\n  onWindowResize = () => {\r\n    this.windowWidth = window.innerWidth\r\n    this.windowHeight = window.innerHeight\r\n  }\r\n}\r\n", "import { version } from '../package.json'\r\nimport { Animate } from './animate'\r\nimport { Dimensions } from './dimensions'\r\nimport { Emitter } from './emitter'\r\nimport { clamp, modulo } from './maths'\r\nimport { VirtualScroll } from './virtual-scroll'\r\n\r\n// Technical explanation\r\n// - listen to 'wheel' events\r\n// - prevent 'wheel' event to prevent scroll\r\n// - normalize wheel delta\r\n// - add delta to targetScroll\r\n// - animate scroll to targetScroll (smooth context)\r\n// - if animation is not running, listen to 'scroll' events (native context)\r\n\r\ntype EasingFunction = (t: number) => number\r\ntype Orientation = 'vertical' | 'horizontal'\r\ntype GestureOrientation = 'vertical' | 'horizontal' | 'both'\r\n\r\nexport type LenisOptions = {\r\n  wrapper?: Window | HTMLElement\r\n  content?: HTMLElement\r\n  wheelEventsTarget?: Window | HTMLElement\r\n  eventsTarget?: Window | HTMLElement\r\n  smoothWheel?: boolean\r\n  syncTouch?: boolean\r\n  syncTouchLerp?: number\r\n  touchInertiaMultiplier?: number\r\n  duration?: number\r\n  easing?: EasingFunction\r\n  lerp?: number\r\n  infinite?: boolean\r\n  orientation?: Orientation\r\n  gestureOrientation?: GestureOrientation\r\n  touchMultiplier?: number\r\n  wheelMultiplier?: number\r\n  autoResize?: boolean\r\n  __experimental__naiveDimensions?: boolean\r\n}\r\n\r\nexport default class Lenis {\r\n  __isSmooth: boolean = false // true if scroll should be animated\r\n  __isScrolling: boolean = false // true when scroll is animating\r\n  __isStopped: boolean = false // true if user should not be able to scroll - enable/disable programmatically\r\n  __isLocked: boolean = false // same as isStopped but enabled/disabled when scroll reaches target\r\n\r\n  constructor({\r\n    wrapper = window,\r\n    content = document.documentElement,\r\n    wheelEventsTarget = wrapper, // deprecated\r\n    eventsTarget = wheelEventsTarget,\r\n    smoothWheel = true,\r\n    syncTouch = false,\r\n    syncTouchLerp = 0.075,\r\n    touchInertiaMultiplier = 35,\r\n    duration, // in seconds\r\n    easing = (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),\r\n    lerp = !duration && 0.1,\r\n    infinite = false,\r\n    orientation = 'vertical', // vertical, horizontal\r\n    gestureOrientation = 'vertical', // vertical, horizontal, both\r\n    touchMultiplier = 1,\r\n    wheelMultiplier = 1,\r\n    autoResize = true,\r\n    __experimental__naiveDimensions = false,\r\n  }: LenisOptions = {}) {\r\n    window.lenisVersion = version\r\n\r\n    // if wrapper is html or body, fallback to window\r\n    if (wrapper === document.documentElement || wrapper === document.body) {\r\n      wrapper = window\r\n    }\r\n\r\n    this.options = {\r\n      wrapper,\r\n      content,\r\n      wheelEventsTarget,\r\n      eventsTarget,\r\n      smoothWheel,\r\n      syncTouch,\r\n      syncTouchLerp,\r\n      touchInertiaMultiplier,\r\n      duration,\r\n      easing,\r\n      lerp,\r\n      infinite,\r\n      gestureOrientation,\r\n      orientation,\r\n      touchMultiplier,\r\n      wheelMultiplier,\r\n      autoResize,\r\n      __experimental__naiveDimensions,\r\n    }\r\n\r\n    this.animate = new Animate()\r\n    this.emitter = new Emitter()\r\n    this.dimensions = new Dimensions({ wrapper, content, autoResize })\r\n    this.toggleClassName('lenis', true)\r\n\r\n    this.velocity = 0\r\n    this.isLocked = false\r\n    this.isStopped = false\r\n    this.isSmooth = syncTouch || smoothWheel\r\n    this.isScrolling = false\r\n    this.targetScroll = this.animatedScroll = this.actualScroll\r\n\r\n    this.options.wrapper.addEventListener('scroll', this.onNativeScroll, false)\r\n\r\n    this.virtualScroll = new VirtualScroll(eventsTarget, {\r\n      touchMultiplier,\r\n      wheelMultiplier,\r\n    })\r\n    this.virtualScroll.on('scroll', this.onVirtualScroll)\r\n  }\r\n\r\n  destroy() {\r\n    this.emitter.destroy()\r\n\r\n    this.options.wrapper.removeEventListener(\r\n      'scroll',\r\n      this.onNativeScroll,\r\n      false\r\n    )\r\n\r\n    this.virtualScroll.destroy()\r\n    this.dimensions.destroy()\r\n\r\n    this.toggleClassName('lenis', false)\r\n    this.toggleClassName('lenis-smooth', false)\r\n    this.toggleClassName('lenis-scrolling', false)\r\n    this.toggleClassName('lenis-stopped', false)\r\n    this.toggleClassName('lenis-locked', false)\r\n  }\r\n\r\n  on(event: string, callback: Function) {\r\n    return this.emitter.on(event, callback)\r\n  }\r\n\r\n  off(event: string, callback: Function) {\r\n    return this.emitter.off(event, callback)\r\n  }\r\n\r\n  private setScroll(scroll) {\r\n    // apply scroll value immediately\r\n    if (this.isHorizontal) {\r\n      this.rootElement.scrollLeft = scroll\r\n    } else {\r\n      this.rootElement.scrollTop = scroll\r\n    }\r\n  }\r\n\r\n  private onVirtualScroll = ({ deltaX, deltaY, event }) => {\r\n    // keep zoom feature\r\n    if (event.ctrlKey) return\r\n\r\n    const isTouch = event.type.includes('touch')\r\n    const isWheel = event.type.includes('wheel')\r\n\r\n    const isTapToStop =\r\n      this.options.syncTouch &&\r\n      isTouch &&\r\n      event.type === 'touchstart' &&\r\n      !this.isStopped &&\r\n      !this.isLocked\r\n\r\n    if (isTapToStop) {\r\n      this.reset()\r\n      return\r\n    }\r\n\r\n    const isClick = deltaX === 0 && deltaY === 0 // click event\r\n\r\n    // const isPullToRefresh =\r\n    //   this.options.gestureOrientation === 'vertical' &&\r\n    //   this.scroll === 0 &&\r\n    //   !this.options.infinite &&\r\n    //   deltaY <= 5 // touch pull to refresh, not reliable yet\r\n\r\n    const isUnknownGesture =\r\n      (this.options.gestureOrientation === 'vertical' && deltaY === 0) ||\r\n      (this.options.gestureOrientation === 'horizontal' && deltaX === 0)\r\n\r\n    if (isClick || isUnknownGesture) {\r\n      // console.log('prevent')\r\n      return\r\n    }\r\n\r\n    // catch if scrolling on nested scroll elements\r\n    let composedPath = event.composedPath()\r\n    composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement)) // remove parents elements\r\n\r\n    if (\r\n      !!composedPath.find(\r\n        (node) =>\r\n          node.hasAttribute?.('data-lenis-prevent') ||\r\n          (isTouch && node.hasAttribute?.('data-lenis-prevent-touch')) ||\r\n          (isWheel && node.hasAttribute?.('data-lenis-prevent-wheel')) ||\r\n          (node.classList?.contains('lenis') &&\r\n            !node.classList?.contains('lenis-stopped')) // nested lenis instance\r\n      )\r\n    )\r\n      return\r\n\r\n    if (this.isStopped || this.isLocked) {\r\n      event.preventDefault() // this will stop forwarding the event to the parent, this is problematic\r\n      return\r\n    }\r\n\r\n    this.isSmooth =\r\n      (this.options.syncTouch && isTouch) ||\r\n      (this.options.smoothWheel && isWheel)\r\n\r\n    if (!this.isSmooth) {\r\n      this.isScrolling = false\r\n      this.animate.stop()\r\n      return\r\n    }\r\n\r\n    event.preventDefault()\r\n\r\n    let delta = deltaY\r\n    if (this.options.gestureOrientation === 'both') {\r\n      delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX\r\n    } else if (this.options.gestureOrientation === 'horizontal') {\r\n      delta = deltaX\r\n    }\r\n\r\n    const syncTouch = isTouch && this.options.syncTouch\r\n    const isTouchEnd = isTouch && event.type === 'touchend'\r\n\r\n    const hasTouchInertia = isTouchEnd && Math.abs(delta) > 5\r\n\r\n    if (hasTouchInertia) {\r\n      delta = this.velocity * this.options.touchInertiaMultiplier\r\n    }\r\n\r\n    this.scrollTo(this.targetScroll + delta, {\r\n      programmatic: false,\r\n      ...(syncTouch\r\n        ? {\r\n            lerp: hasTouchInertia ? this.options.syncTouchLerp : 1,\r\n          }\r\n        : {\r\n            lerp: this.options.lerp,\r\n            duration: this.options.duration,\r\n            easing: this.options.easing,\r\n          }),\r\n    })\r\n  }\r\n\r\n  resize() {\r\n    this.dimensions.resize()\r\n  }\r\n\r\n  private emit() {\r\n    this.emitter.emit('scroll', this)\r\n  }\r\n\r\n  private onNativeScroll = () => {\r\n    if (this.__preventNextScrollEvent) return\r\n\r\n    if (!this.isScrolling) {\r\n      const lastScroll = this.animatedScroll\r\n      this.animatedScroll = this.targetScroll = this.actualScroll\r\n      this.velocity = 0\r\n      this.direction = Math.sign(this.animatedScroll - lastScroll)\r\n      this.emit()\r\n    }\r\n  }\r\n\r\n  private reset() {\r\n    this.isLocked = false\r\n    this.isScrolling = false\r\n    this.animatedScroll = this.targetScroll = this.actualScroll\r\n    this.velocity = 0\r\n    this.animate.stop()\r\n  }\r\n\r\n  start() {\r\n    if (!this.isStopped) return\r\n    this.isStopped = false\r\n\r\n    this.reset()\r\n  }\r\n\r\n  stop() {\r\n    if (this.isStopped) return\r\n    this.isStopped = true\r\n    this.animate.stop()\r\n\r\n    this.reset()\r\n  }\r\n\r\n  raf(time: number) {\r\n    const deltaTime = time - (this.time || time)\r\n    this.time = time\r\n\r\n    this.animate.advance(deltaTime * 0.001)\r\n  }\r\n\r\n  scrollTo(\r\n    target: number | string | HTMLElement,\r\n    {\r\n      offset = 0,\r\n      immediate = false,\r\n      lock = false,\r\n      duration = this.options.duration,\r\n      easing = this.options.easing,\r\n      lerp = !duration && this.options.lerp,\r\n      onComplete,\r\n      force = false, // scroll even if stopped\r\n      programmatic = true, // called from outside of the class\r\n    }: {\r\n      offset?: number\r\n      immediate?: boolean\r\n      lock?: boolean\r\n      duration?: number\r\n      easing?: EasingFunction\r\n      lerp?: number\r\n      onComplete?: (lenis: Lenis) => void\r\n      force?: boolean\r\n      programmatic?: boolean\r\n    } = {}\r\n  ) {\r\n    if ((this.isStopped || this.isLocked) && !force) return\r\n\r\n    // keywords\r\n    if (['top', 'left', 'start'].includes(target)) {\r\n      target = 0\r\n    } else if (['bottom', 'right', 'end'].includes(target)) {\r\n      target = this.limit\r\n    } else {\r\n      let node\r\n\r\n      if (typeof target === 'string') {\r\n        // CSS selector\r\n        node = document.querySelector(target)\r\n      } else if (target?.nodeType) {\r\n        // Node element\r\n        node = target\r\n      }\r\n\r\n      if (node) {\r\n        if (this.options.wrapper !== window) {\r\n          // nested scroll offset correction\r\n          const wrapperRect = this.options.wrapper.getBoundingClientRect()\r\n          offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top\r\n        }\r\n\r\n        const rect = node.getBoundingClientRect()\r\n\r\n        target =\r\n          (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll\r\n      }\r\n    }\r\n\r\n    if (typeof target !== 'number') return\r\n\r\n    target += offset\r\n    target = Math.round(target)\r\n\r\n    if (this.options.infinite) {\r\n      if (programmatic) {\r\n        this.targetScroll = this.animatedScroll = this.scroll\r\n      }\r\n    } else {\r\n      target = clamp(0, target, this.limit)\r\n    }\r\n\r\n    if (immediate) {\r\n      this.animatedScroll = this.targetScroll = target\r\n      this.setScroll(this.scroll)\r\n      this.reset()\r\n      onComplete?.(this)\r\n      return\r\n    }\r\n\r\n    if (!programmatic) {\r\n      if (target === this.targetScroll) return\r\n\r\n      this.targetScroll = target\r\n    }\r\n\r\n    this.animate.fromTo(this.animatedScroll, target, {\r\n      duration,\r\n      easing,\r\n      lerp,\r\n      onStart: () => {\r\n        // started\r\n        if (lock) this.isLocked = true\r\n        this.isScrolling = true\r\n      },\r\n      onUpdate: (value: number, completed: boolean) => {\r\n        this.isScrolling = true\r\n\r\n        // updated\r\n        this.velocity = value - this.animatedScroll\r\n        this.direction = Math.sign(this.velocity)\r\n\r\n        this.animatedScroll = value\r\n        this.setScroll(this.scroll)\r\n\r\n        if (programmatic) {\r\n          // wheel during programmatic should stop it\r\n          this.targetScroll = value\r\n        }\r\n\r\n        if (!completed) this.emit()\r\n\r\n        if (completed) {\r\n          this.reset()\r\n          this.emit()\r\n          onComplete?.(this)\r\n\r\n          // avoid emitting event twice\r\n          this.__preventNextScrollEvent = true\r\n          requestAnimationFrame(() => {\r\n            delete this.__preventNextScrollEvent\r\n          })\r\n        }\r\n      },\r\n    })\r\n  }\r\n\r\n  get rootElement() {\r\n    return this.options.wrapper === window\r\n      ? document.documentElement\r\n      : this.options.wrapper\r\n  }\r\n\r\n  get limit() {\r\n    if (this.options.__experimental__naiveDimensions) {\r\n      if (this.isHorizontal) {\r\n        return this.rootElement.scrollWidth - this.rootElement.clientWidth\r\n      } else {\r\n        return this.rootElement.scrollHeight - this.rootElement.clientHeight\r\n      }\r\n    } else {\r\n      return this.dimensions.limit[this.isHorizontal ? 'x' : 'y']\r\n    }\r\n  }\r\n\r\n  get isHorizontal() {\r\n    return this.options.orientation === 'horizontal'\r\n  }\r\n\r\n  get actualScroll() {\r\n    // value browser takes into account\r\n    return this.isHorizontal\r\n      ? this.rootElement.scrollLeft\r\n      : this.rootElement.scrollTop\r\n  }\r\n\r\n  get scroll() {\r\n    return this.options.infinite\r\n      ? modulo(this.animatedScroll, this.limit)\r\n      : this.animatedScroll\r\n  }\r\n\r\n  get progress() {\r\n    // avoid progress to be NaN\r\n    return this.limit === 0 ? 1 : this.scroll / this.limit\r\n  }\r\n\r\n  get isSmooth() {\r\n    return this.__isSmooth\r\n  }\r\n\r\n  private set isSmooth(value: boolean) {\r\n    if (this.__isSmooth !== value) {\r\n      this.__isSmooth = value\r\n      this.toggleClassName('lenis-smooth', value)\r\n    }\r\n  }\r\n\r\n  get isScrolling() {\r\n    return this.__isScrolling\r\n  }\r\n\r\n  private set isScrolling(value: boolean) {\r\n    if (this.__isScrolling !== value) {\r\n      this.__isScrolling = value\r\n      this.toggleClassName('lenis-scrolling', value)\r\n    }\r\n  }\r\n\r\n  get isStopped() {\r\n    return this.__isStopped\r\n  }\r\n\r\n  private set isStopped(value: boolean) {\r\n    if (this.__isStopped !== value) {\r\n      this.__isStopped = value\r\n      this.toggleClassName('lenis-stopped', value)\r\n    }\r\n  }\r\n\r\n  get isLocked() {\r\n    return this.__isLocked\r\n  }\r\n\r\n  private set isLocked(value: boolean) {\r\n    if (this.__isLocked !== value) {\r\n      this.__isLocked = value\r\n      this.toggleClassName('lenis-locked', value)\r\n    }\r\n  }\r\n\r\n  get className() {\r\n    let className = 'lenis'\r\n    if (this.isStopped) className += ' lenis-stopped'\r\n    if (this.isLocked) className += ' lenis-locked'\r\n    if (this.isScrolling) className += ' lenis-scrolling'\r\n    if (this.isSmooth) className += ' lenis-smooth'\r\n    return className\r\n  }\r\n\r\n  private toggleClassName(name: string, value: boolean) {\r\n    this.rootElement.classList.toggle(name, value)\r\n    this.emitter.emit('className change', this)\r\n  }\r\n}\r\n"], "mappings": ";;;;;AACO,SAASA,EAAMC,IAAKC,IAAOC,GAAAA;AAChC,SAAOC,KAAKD,IAAIF,IAAKG,KAAKH,IAAIC,IAAOC,CAAAA,CAAAA;AACvC;ACAO,IAAME,UAAN,MAAMA;EAEX,QAAQC,IAAAA;ADJH;ACKH,QAAA,CAAKC,KAAKC,UAAW;AAErB,QAAIC,IAAAA;AAEJ,QAAIF,KAAKG,KACPH,MAAKI,SDKUC,ICLGL,KAAKI,ODKLE,ICLYN,KAAKO,IDKdC,ICL8B,KAAZR,KAAKG,MDKfM,ICL0BV,IDAtD,SAAcM,IAAGC,IAAGI,IAAAA;AACzB,cAAQ,IAAIA,MAAKL,KAAIK,KAAIJ;IAC3B,EAIcD,GAAGC,GAAG,IAAIT,KAAKc,IAAAA,CAAKH,IAASC,CAAAA,CAAAA,ICLjCZ,KAAKe,MAAMZ,KAAKI,KAAAA,MAAWJ,KAAKO,OAClCP,KAAKI,QAAQJ,KAAKO,IAClBL,IAAAA;SAEG;AACLF,WAAKa,eAAed;AACpB,YAAMe,KAAiBrB,EAAM,GAAGO,KAAKa,cAAcb,KAAKe,UAAU,CAAA;AAElEb,UAAYY,MAAkB;AAC9B,YAAME,KAAgBd,IAAY,IAAIF,KAAKiB,OAAOH,EAAAA;AAClDd,WAAKI,QAAQJ,KAAKkB,QAAQlB,KAAKO,KAAKP,KAAKkB,QAAQF;IAClD;ADPE,QAAcX,GAAGC,GAAGE,GAAQC;ACU/BT,eAAKmB,aAALnB,8BAAgBA,KAAKI,OAAOF,IAExBA,KACFF,KAAKoB,KAAAA;EAER;EAGD,OAAAA;AACEpB,SAAKC,YAAAA;EACN;EAID,OACEiB,IACAX,IAAAA,EACAJ,MAAEA,IAAO,KAAGY,UAAEA,IAAW,GAACE,QAAEA,IAAUP,CAAAA,OAAMA,IAACW,SAAEA,GAAOF,UAAEA,EAAAA,GAAAA;AAExDnB,SAAKkB,OAAOlB,KAAKI,QAAQc,IACzBlB,KAAKO,KAAKA,IACVP,KAAKG,OAAOA,GACZH,KAAKe,WAAWA,GAChBf,KAAKiB,SAASA,GACdjB,KAAKa,cAAc,GACnBb,KAAKC,YAAAA,MAELoB,0BACArB,KAAKmB,WAAWA;EACjB;AAAA;ACrDI,IAAMG,aAAN,MAAMA;EACX,YAAAC,EAAYC,SACVA,IAAOC,SACPA,IAAOC,YACPA,IAAAA,MACAC,UAAUC,IAAgB,IAAA,IACxB,CAAA,GAAA;AA2BJC,kCAAS,MAAA;AACP7B,WAAK8B,gBAAAA,GACL9B,KAAK+B,gBAAAA;IAAiB;AAGxBD,2CAAkB,MAAA;AACZ9B,WAAKwB,YAAYQ,UACnBhC,KAAKiC,QAAQD,OAAOE,YACpBlC,KAAKmC,SAASH,OAAOI,gBAErBpC,KAAKiC,QAAQjC,KAAKwB,QAAQa,aAC1BrC,KAAKmC,SAASnC,KAAKwB,QAAQc;IAC5B;AAGHP,2CAAkB,MAAA;AACZ/B,WAAKwB,YAAYQ,UACnBhC,KAAKuC,eAAevC,KAAKyB,QAAQc,cACjCvC,KAAKwC,cAAcxC,KAAKyB,QAAQe,gBAEhCxC,KAAKuC,eAAevC,KAAKwB,QAAQe,cACjCvC,KAAKwC,cAAcxC,KAAKwB,QAAQgB;IACjC;AAhDDxC,SAAKwB,UAAUA,IACfxB,KAAKyB,UAAUA,IAEXC,MACF1B,KAAKyC,kBCbJ,yBAAkBC,IAAUC,IAAAA;AACjC,UAAIC;AACJ,aAAO,WAAA;AACL,YAAIC,KAAOC,WACPC,IAAU/C;AACdgD,qBAAaJ,EAAAA,GACbA,KAAQK,WAAW,WAAA;AACjBP,UAAAA,GAASQ,MAAMH,GAASF,EAAAA;QACzB,GAAEF,EAAAA;MACJ;IACH,EDGsC3C,KAAK6B,QAAQD,CAAAA,GAEzC5B,KAAKwB,YAAYQ,SACnBA,OAAOmB,iBAAiB,UAAUnD,KAAKyC,iBAAAA,KAAiB,KAExDzC,KAAKoD,wBAAwB,IAAIC,eAAerD,KAAKyC,eAAAA,GACrDzC,KAAKoD,sBAAsBE,QAAQtD,KAAKwB,OAAAA,IAG1CxB,KAAKuD,wBAAwB,IAAIF,eAAerD,KAAKyC,eAAAA,GACrDzC,KAAKuD,sBAAsBD,QAAQtD,KAAKyB,OAAAA,IAG1CzB,KAAK6B,OAAAA;EACN;EAED,UAAA2B;AF5BK;AE6BHxD,eAAKoD,0BAALpD,mBAA4ByD,eAC5BzD,UAAKuD,0BAALvD,mBAA4ByD,cAC5BzB,OAAO0B,oBAAoB,UAAU1D,KAAKyC,iBAAAA,KAAiB;EAC5D;EA2BD,IAAA,QAAIkB;AACF,WAAO,EACLtD,GAAGL,KAAKwC,cAAcxC,KAAKiC,OAC3B3B,GAAGN,KAAKuC,eAAevC,KAAKmC,OAAAA;EAE/B;AAAA;AEjEI,IAAMyB,UAAN,MAAMA;EACX,cAAArC;AACEvB,SAAK6D,SAAS,CAAE;EACjB;EAED,KAAKC,OAAUjB,IAAAA;AACb,QAAIkB,IAAY/D,KAAK6D,OAAOC,EAAAA,KAAU,CAAA;AACtC,aAASE,KAAI,GAAGC,IAASF,EAAUE,QAAQD,KAAIC,GAAQD,KACrDD,GAAUC,EAAAA,EAAAA,GAAMnB,EAAAA;EAEnB;EAED,GAAGiB,IAAOI,IAAAA;AJXL;AIgBH,aAHAlE,UAAK6D,OAAOC,EAAAA,MAAZ9D,mBAAoBmE,KAAKD,SAAQlE,KAAK6D,OAAOC,EAAAA,IAAS,CAACI,EAAAA,IAGhD,MAAA;AJhBJ,UAAAE;AIiBDpE,WAAK6D,OAAOC,EAAAA,KAAS9D,MAAAA,KAAK6D,OAAOC,EAAAA,MAAZ9D,gBAAAA,IAAoBqE,OAAQL,CAAAA,OAAME,OAAOF;IAAE;EAEnE;EAED,IAAIF,IAAOpB,IAAAA;AJrBN;AIsBH1C,SAAK6D,OAAOC,EAAAA,KAAS9D,UAAK6D,OAAOC,EAAAA,MAAZ9D,mBAAoBqE,OAAQL,CAAAA,OAAMtB,OAAasB;EACrE;EAED,UAAAR;AACExD,SAAK6D,SAAS,CAAE;EACjB;AAAA;AC1BH,IAAMS,IAAc,MAAM;AAEnB,IAAMC,gBAAN,MAAMA;EACX,YAAYC,IAAAA,EAASC,iBAAEA,KAAkB,GAACC,iBAAEA,IAAkB,EAAA,GAAA;AAoD9DC,wCAAgBb,CAAAA,OAAAA;AACd,YAAA,EAAMc,SAAEA,IAAOC,SAAEA,EAAAA,IAAYf,GAAMgB,gBAC/BhB,GAAMgB,cAAc,CAAA,IACpBhB;AAEJ9D,WAAK+E,WAAW1E,IAAIuE,IACpB5E,KAAK+E,WAAWzE,IAAIuE,GAEpB7E,KAAKgF,YAAY,EACf3E,GAAG,GACHC,GAAG,EAAA,GAGLN,KAAKiF,QAAQC,KAAK,UAAU,EAC1BC,QAAQ,GACRC,QAAQ,GACRtB,OAAAA,GAAAA,CAAAA;IACA;AAIJuB,uCAAevB,CAAAA,OAAAA;AACb,YAAA,EAAMc,SAAEA,IAAOC,SAAEA,EAAAA,IAAYf,GAAMgB,gBAC/BhB,GAAMgB,cAAc,CAAA,IACpBhB,IAEEqB,IAAAA,EAAWP,KAAU5E,KAAK+E,WAAW1E,KAAKL,KAAK0E,iBAC/CU,IAAAA,EAAWP,IAAU7E,KAAK+E,WAAWzE,KAAKN,KAAK0E;AAErD1E,WAAK+E,WAAW1E,IAAIuE,IACpB5E,KAAK+E,WAAWzE,IAAIuE,GAEpB7E,KAAKgF,YAAY,EACf3E,GAAG8E,GACH7E,GAAG8E,EAAAA,GAGLpF,KAAKiF,QAAQC,KAAK,UAAU,EAC1BC,QAAAA,GACAC,QAAAA,GACAtB,OAAAA,GAAAA,CAAAA;IACA;AAGJwB,sCAAcxB,CAAAA,OAAAA;AACZ9D,WAAKiF,QAAQC,KAAK,UAAU,EAC1BC,QAAQnF,KAAKgF,UAAU3E,GACvB+E,QAAQpF,KAAKgF,UAAU1E,GACvBwD,OAAAA,GAAAA,CAAAA;IACA;AAIJyB,mCAAWzB,CAAAA,OAAAA;AACT,UAAA,EAAIqB,QAAEA,GAAMC,QAAEA,GAAMI,WAAEA,EAAAA,IAAc1B;AAOpCqB,WAJgB,MAAdK,IAAkBlB,IAA4B,MAAdkB,IAAkBxF,KAAKyF,cAAc,GAKvEL,KAHgB,MAAdI,IAAkBlB,IAA4B,MAAdkB,IAAkBxF,KAAK0F,eAAe,GAKxEP,KAAUnF,KAAKyE,iBACfW,KAAUpF,KAAKyE,iBAEfzE,KAAKiF,QAAQC,KAAK,UAAU,EAAEC,QAAAA,GAAQC,QAAAA,GAAQtB,OAAAA,GAAAA,CAAAA;IAAQ;AAGxD6B,0CAAiB,MAAA;AACf3F,WAAKyF,cAAczD,OAAOE,YAC1BlC,KAAK0F,eAAe1D,OAAOI;IAAW;AA3HtCpC,SAAKwE,UAAUA,IACfxE,KAAKyE,kBAAkBA,IACvBzE,KAAK0E,kBAAkBA,GAEvB1E,KAAK+E,aAAa,EAChB1E,GAAG,MACHC,GAAG,KAAA,GAGLN,KAAKiF,UAAU,IAAIrB,WACnB5B,OAAOmB,iBAAiB,UAAUnD,KAAK2F,gBAAAA,KAAgB,GACvD3F,KAAK2F,eAAAA,GAEL3F,KAAKwE,QAAQrB,iBAAiB,SAASnD,KAAKuF,SAAS,EAAEK,SAAAA,MAAS,CAAA,GAChE5F,KAAKwE,QAAQrB,iBAAiB,cAAcnD,KAAK2E,cAAc,EAC7DiB,SAAAA,MAAS,CAAA,GAEX5F,KAAKwE,QAAQrB,iBAAiB,aAAanD,KAAKqF,aAAa,EAC3DO,SAAAA,MAAS,CAAA,GAEX5F,KAAKwE,QAAQrB,iBAAiB,YAAYnD,KAAKsF,YAAY,EACzDM,SAAAA,MAAS,CAAA;EAEZ;EAGD,GAAG9B,IAAOpB,IAAAA;AACR,WAAO1C,KAAKiF,QAAQY,GAAG/B,IAAOpB,EAAAA;EAC/B;EAGD,UAAAc;AACExD,SAAKiF,QAAQzB,QAAAA,GAEbxB,OAAO0B,oBAAoB,UAAU1D,KAAK2F,gBAAAA,KAAgB,GAE1D3F,KAAKwE,QAAQd,oBAAoB,SAAS1D,KAAKuF,SAAS,EACtDK,SAAAA,MAAS,CAAA,GAEX5F,KAAKwE,QAAQd,oBAAoB,cAAc1D,KAAK2E,cAAc,EAChEiB,SAAAA,MAAS,CAAA,GAEX5F,KAAKwE,QAAQd,oBAAoB,aAAa1D,KAAKqF,aAAa,EAC9DO,SAAAA,MAAS,CAAA,GAEX5F,KAAKwE,QAAQd,oBAAoB,YAAY1D,KAAKsF,YAAY,EAC5DM,SAAAA,MAAS,CAAA;EAEZ;AA2EuC;ACzF5B,IAAOE,QAAP,MAAOA;EAMnB,YAAAvE,EAAYC,SACVA,KAAUQ,QAAMP,SAChBA,KAAUsE,SAASC,iBAAeC,mBAClCA,IAAoBzE,IAAO0E,cAC3BA,IAAeD,GAAiBE,aAChCA,IAAAA,MAAkBC,WAClBA,IAAAA,OAAiBC,eACjBA,IAAgB,OAAKC,wBACrBA,IAAyB,IAAEvF,UAC3BA,GAAQE,QACRA,IAAUP,CAAAA,OAAMb,KAAKH,IAAI,GAAG,QAAQG,KAAK0G,IAAI,GAAA,MAAS7F,EAAAA,CAAAA,GAAGP,MACzDA,IAAAA,CAAQY,KAAY,KAAGyF,UACvBA,IAAAA,OAAgBC,aAChBA,IAAc,YAAUC,oBACxBA,IAAqB,YAAUhC,iBAC/BA,IAAkB,GAACD,iBACnBA,IAAkB,GAAC/C,YACnBA,IAAAA,MAAiBiF,iCACjBA,IAAAA,MAAkC,IAClB,CAAA,GAAA;AAxBlB3G,SAAU4G,aAAAA,OACV5G,KAAa6G,gBAAAA,OACb7G,KAAW8G,cAAAA,OACX9G,KAAU+G,aAAAA,OA2GF/G,KAAegH,kBAAG,CAAA,EAAG7B,QAAAA,IAAQC,QAAAA,IAAQtB,OAAAA,GAAAA,MAAAA;AAE3C,UAAIA,GAAMmD,QAAS;AAEnB,YAAMC,KAAUpD,GAAMqD,KAAKC,SAAS,OAAA,GAC9BC,KAAUvD,GAAMqD,KAAKC,SAAS,OAAA;AASpC,UANEpH,KAAKsH,QAAQlB,aACbc,MACe,iBAAfpD,GAAMqD,QAAAA,CACLnH,KAAKuH,aAAAA,CACLvH,KAAKwH,SAIN,QAAA,KADAxH,KAAKyH,MAAAA;AAIP,YAAMC,KAAqB,MAAXvC,MAA2B,MAAXC,IAQ1BuC,KACiC,eAApC3H,KAAKsH,QAAQZ,sBAAgD,MAAXtB,MACd,iBAApCpF,KAAKsH,QAAQZ,sBAAkD,MAAXvB;AAEvD,UAAIuC,MAAWC,GAEb;AAIF,UAAIC,KAAe9D,GAAM8D,aAAAA;AAGzB,UAFAA,KAAeA,GAAaC,MAAM,GAAGD,GAAaE,QAAQ9H,KAAK+H,WAAAA,CAAAA,GAG3DH,GAAaI,KACZC,CAAAA,OAAAA;AAAAA,YAAAA,IAAAA,IAAAA,IAAAA,IAAAA;AACC,gBAAiB,UAAjB7D,KAAA6D,GAAKC,iBAAAA,WAAY9D,KAAAA,SAAAA,GAAA+D,KAAAF,IAAG,oBAAA,MACnBf,OAA+B,UAApBkB,KAAAH,GAAKC,iBAAAA,WAAeE,KAAAA,SAAAA,GAAAD,KAAAF,IAAA,0BAAA,MAC/BZ,OAA+B,UAApBgB,KAAAJ,GAAKC,iBAAAA,WAAeG,KAAAA,SAAAA,GAAAF,KAAAF,IAAA,0BAAA,OACf,UAAAK,KAAhBL,GAAKM,cAAAA,WAAWC,KAAAA,SAAAA,GAAAC,SAAS,OAAA,MAAA,EACT,UAAdC,KAAAT,GAAKM,cAAAA,WAASG,KAAAA,SAAAA,GAAED,SAAS,eAAA;MAAiB,CAAA,EAGjD;AAEF,UAAIzI,KAAKuH,aAAavH,KAAKwH,SAEzB,QAAA,KADA1D,GAAM6E,eAAAA;AAQR,UAJA3I,KAAK4I,WACF5I,KAAKsH,QAAQlB,aAAac,MAC1BlH,KAAKsH,QAAQnB,eAAekB,IAAAA,CAE1BrH,KAAK4I,SAGR,QAFA5I,KAAK6I,cAAAA,OAAc,KACnB7I,KAAK8I,QAAQ1H,KAAAA;AAIf0C,MAAAA,GAAM6E,eAAAA;AAEN,UAAII,KAAQ3D;AAC4B,iBAApCpF,KAAKsH,QAAQZ,qBACfqC,KAAQlJ,KAAKmJ,IAAI5D,EAAAA,IAAUvF,KAAKmJ,IAAI7D,EAAAA,IAAUC,KAASD,KACV,iBAApCnF,KAAKsH,QAAQZ,uBACtBqC,KAAQ5D;AAGV,YAAMiB,KAAYc,MAAWlH,KAAKsH,QAAQlB,WAGpC6C,KAFa/B,MAA0B,eAAfpD,GAAMqD,QAEEtH,KAAKmJ,IAAID,EAAAA,IAAS;AAEpDE,MAAAA,OACFF,KAAQ/I,KAAKkJ,WAAWlJ,KAAKsH,QAAQhB,yBAGvCtG,KAAKmJ,SAASnJ,KAAKoJ,eAAeL,IAAKM,OAAAC,OAAA,EACrCC,cAAAA,MAAc,GACVnD,KACA,EACEjG,MAAM8I,KAAkBjJ,KAAKsH,QAAQjB,gBAAgB,EAAA,IAEvD,EACElG,MAAMH,KAAKsH,QAAQnH,MACnBY,UAAUf,KAAKsH,QAAQvG,UACvBE,QAAQjB,KAAKsH,QAAQrG,OAAAA,CAAAA,CAAAA;IAE3B,GAWIjB,KAAcwJ,iBAAG,MAAA;AACvB,UAAA,CAAIxJ,KAAKyJ,4BAAAA,CAEJzJ,KAAK6I,aAAa;AACrB,cAAMa,KAAa1J,KAAK2J;AACxB3J,aAAK2J,iBAAiB3J,KAAKoJ,eAAepJ,KAAK4J,cAC/C5J,KAAKkJ,WAAW,GAChBlJ,KAAK6J,YAAYhK,KAAKiK,KAAK9J,KAAK2J,iBAAiBD,EAAAA,GACjD1J,KAAKkF,KAAAA;MACN;IAAA,GAzMDlD,OAAO+H,eAAAA,UAGHvI,OAAYuE,SAASC,mBAAmBxE,OAAYuE,SAASiE,SAC/DxI,KAAUQ,SAGZhC,KAAKsH,UAAU,EACb9F,SAAAA,IACAC,SAAAA,IACAwE,mBAAAA,GACAC,cAAAA,GACAC,aAAAA,GACAC,WAAAA,GACAC,eAAAA,GACAC,wBAAAA,GACAvF,UAAAA,GACAE,QAAAA,GACAd,MAAAA,GACAqG,UAAAA,GACAE,oBAAAA,GACAD,aAAAA,GACA/B,iBAAAA,GACAD,iBAAAA,GACA/C,YAAAA,GACAiF,iCAAAA,EAAAA,GAGF3G,KAAK8I,UAAU,IAAIhJ,WACnBE,KAAKiF,UAAU,IAAIrB,WACnB5D,KAAKiK,aAAa,IAAI3I,WAAW,EAAEE,SAAAA,IAASC,SAAAA,IAASC,YAAAA,EAAAA,CAAAA,GACrD1B,KAAKkK,gBAAgB,SAAA,IAAS,GAE9BlK,KAAKkJ,WAAW,GAChBlJ,KAAKwH,WAAAA,OACLxH,KAAKuH,YAAAA,OACLvH,KAAK4I,WAAWxC,KAAaD,GAC7BnG,KAAK6I,cAAAA,OACL7I,KAAKoJ,eAAepJ,KAAK2J,iBAAiB3J,KAAK4J,cAE/C5J,KAAKsH,QAAQ9F,QAAQ2B,iBAAiB,UAAUnD,KAAKwJ,gBAAAA,KAAgB,GAErExJ,KAAKmK,gBAAgB,IAAI5F,cAAc2B,GAAc,EACnDxB,iBAAAA,GACAD,iBAAAA,EAAAA,CAAAA,GAEFzE,KAAKmK,cAActE,GAAG,UAAU7F,KAAKgH,eAAAA;EACtC;EAED,UAAAxD;AACExD,SAAKiF,QAAQzB,QAAAA,GAEbxD,KAAKsH,QAAQ9F,QAAQkC,oBACnB,UACA1D,KAAKwJ,gBAAAA,KACL,GAGFxJ,KAAKmK,cAAc3G,QAAAA,GACnBxD,KAAKiK,WAAWzG,QAAAA,GAEhBxD,KAAKkK,gBAAgB,SAAA,KAAS,GAC9BlK,KAAKkK,gBAAgB,gBAAA,KAAgB,GACrClK,KAAKkK,gBAAgB,mBAAA,KAAmB,GACxClK,KAAKkK,gBAAgB,iBAAA,KAAiB,GACtClK,KAAKkK,gBAAgB,gBAAA,KAAgB;EACtC;EAED,GAAGpG,IAAepB,IAAAA;AAChB,WAAO1C,KAAKiF,QAAQY,GAAG/B,IAAOpB,EAAAA;EAC/B;EAED,IAAIoB,IAAepB,IAAAA;AACjB,WAAO1C,KAAKiF,QAAQmF,IAAItG,IAAOpB,EAAAA;EAChC;EAEO,UAAU2H,IAAAA;AAEZrK,SAAKsK,eACPtK,KAAK+H,YAAYwC,aAAaF,KAE9BrK,KAAK+H,YAAYyC,YAAYH;EAEhC;EAqGD,SAAAxI;AACE7B,SAAKiK,WAAWpI,OAAAA;EACjB;EAEO,OAAAqD;AACNlF,SAAKiF,QAAQC,KAAK,UAAUlF,IAAAA;EAC7B;EAcO,QAAAyH;AACNzH,SAAKwH,WAAAA,OACLxH,KAAK6I,cAAAA,OACL7I,KAAK2J,iBAAiB3J,KAAKoJ,eAAepJ,KAAK4J,cAC/C5J,KAAKkJ,WAAW,GAChBlJ,KAAK8I,QAAQ1H,KAAAA;EACd;EAED,QAAAqJ;AACOzK,SAAKuH,cACVvH,KAAKuH,YAAAA,OAELvH,KAAKyH,MAAAA;EACN;EAED,OAAArG;AACMpB,SAAKuH,cACTvH,KAAKuH,YAAAA,MACLvH,KAAK8I,QAAQ1H,KAAAA,GAEbpB,KAAKyH,MAAAA;EACN;EAED,IAAIiD,IAAAA;AACF,UAAM3K,KAAY2K,MAAQ1K,KAAK0K,QAAQA;AACvC1K,SAAK0K,OAAOA,IAEZ1K,KAAK8I,QAAQ6B,QAAoB,OAAZ5K,EAAAA;EACtB;EAED,SACE6K,IAAAA,EACAC,QACEA,IAAS,GAACC,WACVA,IAAAA,OAAiBC,MACjBA,IAAAA,OAAYhK,UACZA,IAAWf,KAAKsH,QAAQvG,UAAQE,QAChCA,IAASjB,KAAKsH,QAAQrG,QAAMd,MAC5BA,IAAAA,CAAQY,KAAYf,KAAKsH,QAAQnH,MAAI6K,YACrCA,GAAUC,OACVA,IAAAA,OAAa1B,cACbA,IAAAA,KAAe,IAWb,CAAA,GAAA;AAEJ,QAAA,CAAKvJ,KAAKuH,aAAAA,CAAavH,KAAKwH,YAAcyD,GAA1C;AAGA,UAAI,CAAC,OAAO,QAAQ,OAAA,EAAS7D,SAASwD,EAAAA,EACpCA,CAAAA,KAAS;eACA,CAAC,UAAU,SAAS,KAAA,EAAOxD,SAASwD,EAAAA,EAC7CA,CAAAA,KAAS5K,KAAK2D;WACT;AACL,YAAIsE;AAUJ,YARsB,YAAA,OAAX2C,KAET3C,KAAOlC,SAASmF,cAAcN,EAAAA,KACrBA,QAAAA,KAAAA,SAAAA,GAAQO,cAEjBlD,KAAO2C,KAGL3C,IAAM;AACR,cAAIjI,KAAKsH,QAAQ9F,YAAYQ,QAAQ;AAEnC,kBAAMoJ,KAAcpL,KAAKsH,QAAQ9F,QAAQ6J,sBAAAA;AACzCR,iBAAU7K,KAAKsK,eAAec,GAAYE,OAAOF,GAAYG;UAC9D;AAED,gBAAMC,KAAOvD,GAAKoD,sBAAAA;AAElBT,UAAAA,MACG5K,KAAKsK,eAAekB,GAAKF,OAAOE,GAAKD,OAAOvL,KAAK2J;QACrD;MACF;AAED,UAAsB,YAAA,OAAXiB,IAAX;AAaA,YAXAA,MAAUC,GACVD,KAAS/K,KAAKe,MAAMgK,EAAAA,GAEhB5K,KAAKsH,QAAQd,WACX+C,MACFvJ,KAAKoJ,eAAepJ,KAAK2J,iBAAiB3J,KAAKqK,UAGjDO,KAASnL,EAAM,GAAGmL,IAAQ5K,KAAK2D,KAAAA,GAG7BmH,EAKF,QAJA9K,KAAK2J,iBAAiB3J,KAAKoJ,eAAewB,IAC1C5K,KAAKyL,UAAUzL,KAAKqK,MAAAA,GACpBrK,KAAKyH,MAAAA,GAAAA,MACLuD,QAAAA,KAAAA,EAAahL,IAAAA;AAIf,YAAA,CAAKuJ,GAAc;AACjB,cAAIqB,OAAW5K,KAAKoJ,aAAc;AAElCpJ,eAAKoJ,eAAewB;QACrB;AAED5K,aAAK8I,QAAQ4C,OAAO1L,KAAK2J,gBAAgBiB,IAAQ,EAC/C7J,UAAAA,GACAE,QAAAA,GACAd,MAAAA,GACAkB,SAAS,MAAA;AAEH0J,gBAAM/K,KAAKwH,WAAAA,OACfxH,KAAK6I,cAAAA;QAAkB,GAEzB1H,UAAU,CAACf,IAAeF,OAAAA;AACxBF,eAAK6I,cAAAA,MAGL7I,KAAKkJ,WAAW9I,KAAQJ,KAAK2J,gBAC7B3J,KAAK6J,YAAYhK,KAAKiK,KAAK9J,KAAKkJ,QAAAA,GAEhClJ,KAAK2J,iBAAiBvJ,IACtBJ,KAAKyL,UAAUzL,KAAKqK,MAAAA,GAEhBd,MAEFvJ,KAAKoJ,eAAehJ,KAGjBF,MAAWF,KAAKkF,KAAAA,GAEjBhF,OACFF,KAAKyH,MAAAA,GACLzH,KAAKkF,KAAAA,GACL8F,QAAAA,KAAAA,EAAahL,IAAAA,GAGbA,KAAKyJ,2BAAAA,MACLkC,sBAAsB,MAAA;AAAA,mBACb3L,KAAKyJ;UAAwB,CAAA;QAEvC,EAAA,CAAA;MA/DiC;IAhCiB;EAkGxD;EAED,IAAA,cAAI1B;AACF,WAAO/H,KAAKsH,QAAQ9F,YAAYQ,SAC5B+D,SAASC,kBACThG,KAAKsH,QAAQ9F;EAClB;EAED,IAAA,QAAImC;AACF,WAAI3D,KAAKsH,QAAQX,kCACX3G,KAAKsK,eACAtK,KAAK+H,YAAYvF,cAAcxC,KAAK+H,YAAY1F,cAEhDrC,KAAK+H,YAAYxF,eAAevC,KAAK+H,YAAYzF,eAGnDtC,KAAKiK,WAAWtG,MAAM3D,KAAKsK,eAAe,MAAM,GAAA;EAE1D;EAED,IAAA,eAAIA;AACF,WAAoC,iBAA7BtK,KAAKsH,QAAQb;EACrB;EAED,IAAA,eAAImD;AAEF,WAAO5J,KAAKsK,eACRtK,KAAK+H,YAAYwC,aACjBvK,KAAK+H,YAAYyC;EACtB;EAED,IAAA,SAAIH;AACF,WAAOrK,KAAKsH,QAAQd,YNhbDoF,KMibR5L,KAAK2J,gBNjbMkC,KMibU7L,KAAK2D,QNhb9BiI,KAAIC,KAAKA,MAAKA,MMibjB7L,KAAK2J;ANlbN,QAAgBiC,IAAGC;EMmbvB;EAED,IAAA,WAAIC;AAEF,WAAsB,MAAf9L,KAAK2D,QAAc,IAAI3D,KAAKqK,SAASrK,KAAK2D;EAClD;EAED,IAAA,WAAIiF;AACF,WAAO5I,KAAK4G;EACb;EAED,IAAA,SAAqBxG,IAAAA;AACfJ,SAAK4G,eAAexG,OACtBJ,KAAK4G,aAAaxG,IAClBJ,KAAKkK,gBAAgB,gBAAgB9J,EAAAA;EAExC;EAED,IAAA,cAAIyI;AACF,WAAO7I,KAAK6G;EACb;EAED,IAAA,YAAwBzG,IAAAA;AAClBJ,SAAK6G,kBAAkBzG,OACzBJ,KAAK6G,gBAAgBzG,IACrBJ,KAAKkK,gBAAgB,mBAAmB9J,EAAAA;EAE3C;EAED,IAAA,YAAImH;AACF,WAAOvH,KAAK8G;EACb;EAED,IAAA,UAAsB1G,IAAAA;AAChBJ,SAAK8G,gBAAgB1G,OACvBJ,KAAK8G,cAAc1G,IACnBJ,KAAKkK,gBAAgB,iBAAiB9J,EAAAA;EAEzC;EAED,IAAA,WAAIoH;AACF,WAAOxH,KAAK+G;EACb;EAED,IAAA,SAAqB3G,IAAAA;AACfJ,SAAK+G,eAAe3G,OACtBJ,KAAK+G,aAAa3G,IAClBJ,KAAKkK,gBAAgB,gBAAgB9J,EAAAA;EAExC;EAED,IAAA,YAAI2L;AACF,QAAIA,KAAY;AAKhB,WAJI/L,KAAKuH,cAAWwE,MAAa,mBAC7B/L,KAAKwH,aAAUuE,MAAa,kBAC5B/L,KAAK6I,gBAAakD,MAAa,qBAC/B/L,KAAK4I,aAAUmD,MAAa,kBACzBA;EACR;EAEO,gBAAgBC,IAAc5L,IAAAA;AACpCJ,SAAK+H,YAAYQ,UAAU0D,OAAOD,IAAM5L,EAAAA,GACxCJ,KAAKiF,QAAQC,KAAK,oBAAoBlF,IAAAA;EACvC;AAAA;", "names": ["clamp", "min", "input", "max", "Math", "Animate", "deltaTime", "this", "isRunning", "completed", "lerp", "value", "x", "y", "to", "lambda", "dt", "t", "exp", "round", "currentTime", "linearProgress", "duration", "easedProgress", "easing", "from", "onUpdate", "stop", "onStart", "Dimensions", "constructor", "wrapper", "content", "autoResize", "debounce", "debounceValue", "resize", "onWrapperResize", "onContentResize", "window", "width", "innerWidth", "height", "innerHeight", "clientWidth", "clientHeight", "scrollHeight", "scrollWidth", "debouncedResize", "callback", "delay", "timer", "args", "arguments", "context", "clearTimeout", "setTimeout", "apply", "addEventListener", "wrapperResizeObserver", "ResizeObserver", "observe", "contentResizeObserver", "destroy", "disconnect", "removeEventListener", "limit", "Emitter", "events", "event", "callbacks", "i", "length", "cb", "push", "_a", "filter", "LINE_HEIGHT", "VirtualScroll", "element", "wheelMultiplier", "touchMultiplier", "onTouchStart", "clientX", "clientY", "targetTouches", "touchStart", "<PERSON><PERSON><PERSON><PERSON>", "emitter", "emit", "deltaX", "deltaY", "onTouchMove", "onTouchEnd", "onWheel", "deltaMode", "windowWidth", "windowHeight", "onWindowResize", "passive", "on", "<PERSON><PERSON>", "document", "documentElement", "wheelEventsTarget", "eventsTarget", "smoothWheel", "syncTouch", "syncTouchLerp", "touchInertiaMultiplier", "pow", "infinite", "orientation", "gestureOrientation", "__experimental__naiveDimensions", "__isSmooth", "__isScrolling", "__isStopped", "__isLocked", "onVirtualScroll", "ctrl<PERSON>ey", "is<PERSON><PERSON>ch", "type", "includes", "isWheel", "options", "isStopped", "isLocked", "reset", "isClick", "isUnknownGesture", "<PERSON><PERSON><PERSON>", "slice", "indexOf", "rootElement", "find", "node", "hasAttribute", "call", "_b", "_c", "r", "classList", "_d", "contains", "_e", "preventDefault", "isSmooth", "isScrolling", "animate", "delta", "abs", "hasTouchInertia", "velocity", "scrollTo", "targetScroll", "Object", "assign", "programmatic", "onNativeScroll", "__preventNextScrollEvent", "lastScroll", "animatedScroll", "actualScroll", "direction", "sign", "lenisVersion", "body", "dimensions", "toggleClassName", "virtualScroll", "off", "scroll", "isHorizontal", "scrollLeft", "scrollTop", "start", "time", "advance", "target", "offset", "immediate", "lock", "onComplete", "force", "querySelector", "nodeType", "wrapperRect", "getBoundingClientRect", "left", "top", "rect", "setScroll", "fromTo", "requestAnimationFrame", "n", "d", "progress", "className", "name", "toggle"]}