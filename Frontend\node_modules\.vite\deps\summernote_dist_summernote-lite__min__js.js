import {
  require_jquery
} from "./chunk-WB42QNLU.js";
import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// node_modules/summernote/dist/summernote-lite.min.js
var require_summernote_lite_min = __commonJS({
  "node_modules/summernote/dist/summernote-lite.min.js"(exports, module) {
    !function(t, e) {
      if ("object" == typeof exports && "object" == typeof module) module.exports = e(require_jquery());
      else if ("function" == typeof define && define.amd) define(["jquery"], e);
      else {
        var o = "object" == typeof exports ? e(require_jquery()) : e(t.jQuery);
        for (var n in o) ("object" == typeof exports ? exports : t)[n] = o[n];
      }
    }(self, (t) => (() => {
      "use strict";
      var e = { 7e3: (t2, e2, o2) => {
        var n2 = o2(8938), i2 = o2.n(n2);
        i2().summernote = i2().summernote || { lang: {} }, i2().extend(true, i2().summernote.lang, { "en-US": { font: { bold: "Bold", italic: "Italic", underline: "Underline", clear: "Remove Font Style", height: "Line Height", name: "Font Family", strikethrough: "Strikethrough", subscript: "Subscript", superscript: "Superscript", size: "Font Size", sizeunit: "Font Size Unit" }, image: { image: "Picture", insert: "Insert Image", resizeFull: "Resize full", resizeHalf: "Resize half", resizeQuarter: "Resize quarter", resizeNone: "Original size", floatLeft: "Float Left", floatRight: "Float Right", floatNone: "Remove float", shapeRounded: "Shape: Rounded", shapeCircle: "Shape: Circle", shapeThumbnail: "Shape: Thumbnail", shapeNone: "Shape: None", dragImageHere: "Drag image or text here", dropImage: "Drop image or Text", selectFromFiles: "Select from files", maximumFileSize: "Maximum file size", maximumFileSizeError: "Maximum file size exceeded.", url: "Image URL", remove: "Remove Image", original: "Original" }, video: { video: "Video", videoLink: "Video Link", insert: "Insert Video", url: "Video URL", providers: "(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion, Youku, Peertube)" }, link: { link: "Link", insert: "Insert Link", unlink: "Unlink", edit: "Edit", textToDisplay: "Text to display", url: "To what URL should this link go?", openInNewWindow: "Open in new window" }, table: { table: "Table", addRowAbove: "Add row above", addRowBelow: "Add row below", addColLeft: "Add column left", addColRight: "Add column right", delRow: "Delete row", delCol: "Delete column", delTable: "Delete table" }, hr: { insert: "Insert Horizontal Rule" }, style: { style: "Style", p: "Normal", blockquote: "Quote", pre: "Code", h1: "Header 1", h2: "Header 2", h3: "Header 3", h4: "Header 4", h5: "Header 5", h6: "Header 6" }, lists: { unordered: "Unordered list", ordered: "Ordered list" }, options: { help: "Help", fullscreen: "Full Screen", codeview: "Code View" }, paragraph: { paragraph: "Paragraph", outdent: "Outdent", indent: "Indent", left: "Align left", center: "Align center", right: "Align right", justify: "Justify full" }, color: { recent: "Recent Color", more: "More Color", background: "Background Color", foreground: "Text Color", transparent: "Transparent", setTransparent: "Set transparent", reset: "Reset", resetToDefault: "Reset to default", cpSelect: "Select" }, shortcut: { shortcuts: "Keyboard shortcuts", close: "Close", textFormatting: "Text formatting", action: "Action", paragraphFormatting: "Paragraph formatting", documentStyle: "Document Style", extraKeys: "Extra keys" }, help: { escape: "Escape", insertParagraph: "Insert Paragraph", undo: "Undo the last command", redo: "Redo the last command", tab: "Tab", untab: "Untab", bold: "Set a bold style", italic: "Set a italic style", underline: "Set a underline style", strikethrough: "Set a strikethrough style", removeFormat: "Clean a style", justifyLeft: "Set left align", justifyCenter: "Set center align", justifyRight: "Set right align", justifyFull: "Set full align", insertUnorderedList: "Toggle unordered list", insertOrderedList: "Toggle ordered list", outdent: "Outdent on current paragraph", indent: "Indent on current paragraph", formatPara: "Change current block's format as a paragraph(P tag)", formatH1: "Change current block's format as H1", formatH2: "Change current block's format as H2", formatH3: "Change current block's format as H3", formatH4: "Change current block's format as H4", formatH5: "Change current block's format as H5", formatH6: "Change current block's format as H6", insertHorizontalRule: "Insert horizontal rule", "linkDialog.show": "Show Link Dialog" }, history: { undo: "Undo", redo: "Redo" }, specialChar: { specialChar: "SPECIAL CHARACTERS", select: "Select Special characters" }, output: { noSelection: "No Selection Made!" } } });
      }, 8938: (e2) => {
        e2.exports = t;
      } }, o = {};
      function n(t2) {
        var i2 = o[t2];
        if (void 0 !== i2) return i2.exports;
        var r2 = o[t2] = { exports: {} };
        return e[t2](r2, r2.exports, n), r2.exports;
      }
      n.n = (t2) => {
        var e2 = t2 && t2.__esModule ? () => t2.default : () => t2;
        return n.d(e2, { a: e2 }), e2;
      }, n.d = (t2, e2) => {
        for (var o2 in e2) n.o(e2, o2) && !n.o(t2, o2) && Object.defineProperty(t2, o2, { enumerable: true, get: e2[o2] });
      }, n.o = (t2, e2) => Object.prototype.hasOwnProperty.call(t2, e2);
      var i = n(8938), r = n.n(i), a = (n(7e3), ["sans-serif", "serif", "monospace", "cursive", "fantasy"]);
      function s(t2) {
        return -1 === r().inArray(t2.toLowerCase(), a) ? "'".concat(t2, "'") : t2;
      }
      var l, c = navigator.userAgent, u = /MSIE|Trident/i.test(c);
      if (u) {
        var d = /MSIE (\d+[.]\d+)/.exec(c);
        d && (l = parseFloat(d[1])), (d = /Trident\/.*rv:([0-9]{1,}[.0-9]{0,})/.exec(c)) && (l = parseFloat(d[1]));
      }
      var f = /Edge\/\d+/.test(c), h = "ontouchstart" in window || navigator.MaxTouchPoints > 0 || navigator.msMaxTouchPoints > 0, p = u ? "DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted" : "input";
      const m = { isMac: navigator.appVersion.indexOf("Mac") > -1, isMSIE: u, isEdge: f, isFF: !f && /firefox/i.test(c), isPhantom: /PhantomJS/i.test(c), isWebkit: !f && /webkit/i.test(c), isChrome: !f && /chrome/i.test(c), isSafari: !f && /safari/i.test(c) && !/chrome/i.test(c), browserVersion: l, isSupportTouch: h, isFontInstalled: function() {
        var t2 = document.createElement("canvas"), e2 = t2.getContext("2d", { willReadFrequently: true });
        function o2(t3, o3) {
          return e2.clearRect(0, 0, 40, 20), e2.font = "20px " + s(t3) + ', "' + o3 + '"', e2.fillText("mw", 20, 10), e2.getImageData(0, 0, 40, 20).data.join("");
        }
        return t2.width = 40, t2.height = 20, e2.textAlign = "center", e2.fillStyle = "black", e2.textBaseline = "middle", function(t3) {
          var e3 = "Comic Sans MS" === t3 ? "Courier New" : "Comic Sans MS";
          return o2(e3, e3) !== o2(t3, e3);
        };
      }(), isW3CRangeSupport: !!document.createRange, inputEventName: p, genericFontFamilies: a, validFontName: s };
      var v = 0;
      const g = { eq: function(t2) {
        return function(e2) {
          return t2 === e2;
        };
      }, eq2: function(t2, e2) {
        return t2 === e2;
      }, peq2: function(t2) {
        return function(e2, o2) {
          return e2[t2] === o2[t2];
        };
      }, ok: function() {
        return true;
      }, fail: function() {
        return false;
      }, self: function(t2) {
        return t2;
      }, not: function(t2) {
        return function() {
          return !t2.apply(t2, arguments);
        };
      }, and: function(t2, e2) {
        return function(o2) {
          return t2(o2) && e2(o2);
        };
      }, invoke: function(t2, e2) {
        return function() {
          return t2[e2].apply(t2, arguments);
        };
      }, resetUniqueId: function() {
        v = 0;
      }, uniqueId: function(t2) {
        var e2 = ++v + "";
        return t2 ? t2 + e2 : e2;
      }, rect2bnd: function(t2) {
        var e2 = r()(document);
        return { top: t2.top + e2.scrollTop(), left: t2.left + e2.scrollLeft(), width: t2.right - t2.left, height: t2.bottom - t2.top };
      }, invertObject: function(t2) {
        var e2 = {};
        for (var o2 in t2) Object.prototype.hasOwnProperty.call(t2, o2) && (e2[t2[o2]] = o2);
        return e2;
      }, namespaceToCamel: function(t2, e2) {
        return (e2 = e2 || "") + t2.split(".").map(function(t3) {
          return t3.substring(0, 1).toUpperCase() + t3.substring(1);
        }).join("");
      }, debounce: function(t2, e2, o2) {
        var n2;
        return function() {
          var i2 = this, r2 = arguments, a2 = o2 && !n2;
          clearTimeout(n2), n2 = setTimeout(function() {
            n2 = null, o2 || t2.apply(i2, r2);
          }, e2), a2 && t2.apply(i2, r2);
        };
      }, isValidUrl: function(t2) {
        return /[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/gi.test(t2);
      } };
      function b(t2) {
        return t2[0];
      }
      function y(t2) {
        return t2[t2.length - 1];
      }
      function k(t2) {
        return t2.slice(1);
      }
      function w(t2, e2) {
        if (t2 && t2.length && e2) {
          if (t2.indexOf) return -1 !== t2.indexOf(e2);
          if (t2.contains) return t2.contains(e2);
        }
        return false;
      }
      const C = { head: b, last: y, initial: function(t2) {
        return t2.slice(0, t2.length - 1);
      }, tail: k, prev: function(t2, e2) {
        if (t2 && t2.length && e2) {
          var o2 = t2.indexOf(e2);
          return -1 === o2 ? null : t2[o2 - 1];
        }
        return null;
      }, next: function(t2, e2) {
        if (t2 && t2.length && e2) {
          var o2 = t2.indexOf(e2);
          return -1 === o2 ? null : t2[o2 + 1];
        }
        return null;
      }, find: function(t2, e2) {
        for (var o2 = 0, n2 = t2.length; o2 < n2; o2++) {
          var i2 = t2[o2];
          if (e2(i2)) return i2;
        }
      }, contains: w, all: function(t2, e2) {
        for (var o2 = 0, n2 = t2.length; o2 < n2; o2++) if (!e2(t2[o2])) return false;
        return true;
      }, sum: function(t2, e2) {
        return e2 = e2 || g.self, t2.reduce(function(t3, o2) {
          return t3 + e2(o2);
        }, 0);
      }, from: function(t2) {
        for (var e2 = [], o2 = t2.length, n2 = -1; ++n2 < o2; ) e2[n2] = t2[n2];
        return e2;
      }, isEmpty: function(t2) {
        return !t2 || !t2.length;
      }, clusterBy: function(t2, e2) {
        return t2.length ? k(t2).reduce(function(t3, o2) {
          var n2 = y(t3);
          return e2(y(n2), o2) ? n2[n2.length] = o2 : t3[t3.length] = [o2], t3;
        }, [[b(t2)]]) : [];
      }, compact: function(t2) {
        for (var e2 = [], o2 = 0, n2 = t2.length; o2 < n2; o2++) t2[o2] && e2.push(t2[o2]);
        return e2;
      }, unique: function(t2) {
        for (var e2 = [], o2 = 0, n2 = t2.length; o2 < n2; o2++) w(e2, t2[o2]) || e2.push(t2[o2]);
        return e2;
      } };
      var S = String.fromCharCode(160);
      function x(t2) {
        return t2 && r()(t2).hasClass("note-editable");
      }
      function T(t2) {
        return t2 = t2.toUpperCase(), function(e2) {
          return e2 && e2.nodeName.toUpperCase() === t2;
        };
      }
      function E(t2) {
        return t2 && 3 === t2.nodeType;
      }
      function P(t2) {
        return t2 && /^BR|^IMG|^HR|^IFRAME|^BUTTON|^INPUT|^AUDIO|^VIDEO|^EMBED/.test(t2.nodeName.toUpperCase());
      }
      function N(t2) {
        return !x(t2) && (t2 && /^DIV|^P|^LI|^H[1-7]/.test(t2.nodeName.toUpperCase()));
      }
      var $ = T("PRE"), I = T("LI");
      var R = T("TABLE"), A = T("DATA");
      function L(t2) {
        return !(B(t2) || F(t2) || D(t2) || N(t2) || R(t2) || j(t2) || A(t2));
      }
      function F(t2) {
        return t2 && /^UL|^OL/.test(t2.nodeName.toUpperCase());
      }
      var D = T("HR");
      function H(t2) {
        return t2 && /^TD|^TH/.test(t2.nodeName.toUpperCase());
      }
      var j = T("BLOCKQUOTE");
      function B(t2) {
        return H(t2) || j(t2) || x(t2);
      }
      var O = T("A");
      var z = T("BODY");
      var M = m.isMSIE && m.browserVersion < 11 ? "&nbsp;" : "<br>";
      function U(t2) {
        return E(t2) ? t2.nodeValue.length : t2 ? t2.childNodes.length : 0;
      }
      function W(t2) {
        var e2 = U(t2);
        return 0 === e2 || (!E(t2) && 1 === e2 && t2.innerHTML === M || !(!C.all(t2.childNodes, E) || "" !== t2.innerHTML));
      }
      function K(t2) {
        P(t2) || U(t2) || (t2.innerHTML = M);
      }
      function q(t2, e2) {
        for (; t2; ) {
          if (e2(t2)) return t2;
          if (x(t2)) break;
          t2 = t2.parentNode;
        }
        return null;
      }
      function V(t2, e2) {
        e2 = e2 || g.fail;
        var o2 = [];
        return q(t2, function(t3) {
          return x(t3) || o2.push(t3), e2(t3);
        }), o2;
      }
      function _(t2, e2) {
        e2 = e2 || g.fail;
        for (var o2 = []; t2 && !e2(t2); ) o2.push(t2), t2 = t2.nextSibling;
        return o2;
      }
      function G(t2, e2) {
        var o2 = e2.nextSibling, n2 = e2.parentNode;
        return o2 ? n2.insertBefore(t2, o2) : n2.appendChild(t2), t2;
      }
      function Z(t2, e2, o2) {
        return r().each(e2, function(e3, n2) {
          !o2 && I(t2) && null === t2.firstChild && F(n2) && t2.appendChild(ut("br")), t2.appendChild(n2);
        }), t2;
      }
      function Y(t2) {
        return 0 === t2.offset;
      }
      function X(t2) {
        return t2.offset === U(t2.node);
      }
      function Q(t2) {
        return Y(t2) || X(t2);
      }
      function J(t2, e2) {
        for (; t2 && t2 !== e2; ) {
          if (0 !== et(t2)) return false;
          t2 = t2.parentNode;
        }
        return true;
      }
      function tt(t2, e2) {
        if (!e2) return false;
        for (; t2 && t2 !== e2; ) {
          if (et(t2) !== U(t2.parentNode) - 1) return false;
          t2 = t2.parentNode;
        }
        return true;
      }
      function et(t2) {
        for (var e2 = 0; t2 = t2.previousSibling; ) e2 += 1;
        return e2;
      }
      function ot(t2) {
        return !!(t2 && t2.childNodes && t2.childNodes.length);
      }
      function nt(t2, e2) {
        var o2, n2;
        if (0 === t2.offset) {
          if (x(t2.node)) return null;
          o2 = t2.node.parentNode, n2 = et(t2.node);
        } else ot(t2.node) ? n2 = U(o2 = t2.node.childNodes[t2.offset - 1]) : (o2 = t2.node, n2 = e2 ? 0 : t2.offset - 1);
        return { node: o2, offset: n2 };
      }
      function it(t2, e2) {
        var o2, n2;
        if (U(t2.node) === t2.offset) {
          if (x(t2.node)) return null;
          var i2 = at(t2.node);
          i2 ? (o2 = i2, n2 = 0) : (o2 = t2.node.parentNode, n2 = et(t2.node) + 1);
        } else ot(t2.node) ? (o2 = t2.node.childNodes[t2.offset], n2 = 0) : (o2 = t2.node, n2 = e2 ? U(t2.node) : t2.offset + 1);
        return { node: o2, offset: n2 };
      }
      function rt(t2, e2) {
        var o2, n2 = 0;
        if (U(t2.node) === t2.offset) {
          if (x(t2.node)) return null;
          o2 = t2.node.parentNode, n2 = et(t2.node) + 1, x(o2) && (o2 = t2.node.nextSibling, n2 = 0);
        } else ot(t2.node) ? (o2 = t2.node.childNodes[t2.offset], n2 = 0) : (o2 = t2.node, n2 = e2 ? U(t2.node) : t2.offset + 1);
        return { node: o2, offset: n2 };
      }
      function at(t2) {
        if (t2.nextSibling && t2.parent === t2.nextSibling.parent) return E(t2.nextSibling) ? t2.nextSibling : at(t2.nextSibling);
      }
      function st(t2, e2) {
        return t2.node === e2.node && t2.offset === e2.offset;
      }
      function lt(t2, e2) {
        var o2 = e2 && e2.isSkipPaddingBlankHTML, n2 = e2 && e2.isNotSplitEdgePoint, i2 = e2 && e2.isDiscardEmptySplits;
        if (i2 && (o2 = true), Q(t2) && (E(t2.node) || n2)) {
          if (Y(t2)) return t2.node;
          if (X(t2)) return t2.node.nextSibling;
        }
        if (E(t2.node)) return t2.node.splitText(t2.offset);
        var r2 = _(t2.node.childNodes[t2.offset]), a2 = G(t2.node.cloneNode(false), t2.node);
        return Z(a2, r2), o2 || (K(t2.node), K(a2)), i2 && (W(t2.node) && dt(t2.node), W(a2)) ? (dt(a2), t2.node.nextSibling) : a2;
      }
      function ct(t2, e2, o2) {
        var n2 = V(e2.node, g.eq(t2));
        if (!n2.length) return null;
        if (1 === n2.length) return lt(e2, o2);
        if (n2.length > 2) {
          var i2 = n2.slice(0, n2.length - 1).find(function(t3) {
            return t3.nextSibling;
          });
          if (i2 && 0 != e2.offset && X(e2)) {
            var r2, a2 = i2.nextSibling;
            1 == a2.nodeType ? (n2 = V(r2 = a2.childNodes[0], g.eq(t2)), e2 = { node: r2, offset: 0 }) : 3 != a2.nodeType || a2.data.match(/[\n\r]/g) || (n2 = V(r2 = a2, g.eq(t2)), e2 = { node: r2, offset: 0 });
          }
        }
        return n2.reduce(function(t3, n3) {
          return t3 === e2.node && (t3 = lt(e2, o2)), lt({ node: n3, offset: t3 ? et(t3) : U(n3) }, o2);
        });
      }
      function ut(t2) {
        return document.createElement(t2);
      }
      function dt(t2, e2) {
        if (t2 && t2.parentNode) {
          if (t2.removeNode) return t2.removeNode(e2);
          var o2 = t2.parentNode;
          if (!e2) {
            for (var n2 = [], i2 = 0, r2 = t2.childNodes.length; i2 < r2; i2++) n2.push(t2.childNodes[i2]);
            for (var a2 = 0, s2 = n2.length; a2 < s2; a2++) o2.insertBefore(n2[a2], t2);
          }
          o2.removeChild(t2);
        }
      }
      var ft = T("TEXTAREA");
      function ht(t2, e2) {
        var o2 = ft(t2[0]) ? t2.val() : t2.html();
        return e2 ? o2.replace(/[\n\r]/g, "") : o2;
      }
      const pt = { NBSP_CHAR: S, ZERO_WIDTH_NBSP_CHAR: "\uFEFF", blank: M, emptyPara: "<p>".concat(M, "</p>"), makePredByNodeName: T, isEditable: x, isControlSizing: function(t2) {
        return t2 && r()(t2).hasClass("note-control-sizing");
      }, isText: E, isElement: function(t2) {
        return t2 && 1 === t2.nodeType;
      }, isVoid: P, isPara: N, isPurePara: function(t2) {
        return N(t2) && !I(t2);
      }, isHeading: function(t2) {
        return t2 && /^H[1-7]/.test(t2.nodeName.toUpperCase());
      }, isInline: L, isBlock: g.not(L), isBodyInline: function(t2) {
        return L(t2) && !q(t2, N);
      }, isBody: z, isParaInline: function(t2) {
        return L(t2) && !!q(t2, N);
      }, isPre: $, isList: F, isTable: R, isData: A, isCell: H, isBlockquote: j, isBodyContainer: B, isAnchor: O, isDiv: T("DIV"), isLi: I, isBR: T("BR"), isSpan: T("SPAN"), isB: T("B"), isU: T("U"), isS: T("S"), isI: T("I"), isImg: T("IMG"), isTextarea: ft, deepestChildIsEmpty: function(t2) {
        do {
          if (null === t2.firstElementChild || "" === t2.firstElementChild.innerHTML) break;
        } while (t2 = t2.firstElementChild);
        return W(t2);
      }, isEmpty: W, isEmptyAnchor: g.and(O, W), isClosestSibling: function(t2, e2) {
        return t2.nextSibling === e2 || t2.previousSibling === e2;
      }, withClosestSiblings: function(t2, e2) {
        e2 = e2 || g.ok;
        var o2 = [];
        return t2.previousSibling && e2(t2.previousSibling) && o2.push(t2.previousSibling), o2.push(t2), t2.nextSibling && e2(t2.nextSibling) && o2.push(t2.nextSibling), o2;
      }, nodeLength: U, isLeftEdgePoint: Y, isRightEdgePoint: X, isEdgePoint: Q, isLeftEdgeOf: J, isRightEdgeOf: tt, isLeftEdgePointOf: function(t2, e2) {
        return Y(t2) && J(t2.node, e2);
      }, isRightEdgePointOf: function(t2, e2) {
        return X(t2) && tt(t2.node, e2);
      }, prevPoint: nt, nextPoint: it, nextPointWithEmptyNode: rt, isSamePoint: st, isVisiblePoint: function(t2) {
        if (E(t2.node) || !ot(t2.node) || W(t2.node)) return true;
        var e2 = t2.node.childNodes[t2.offset - 1], o2 = t2.node.childNodes[t2.offset];
        return !((e2 && !P(e2) || o2 && !P(o2)) && !R(o2));
      }, prevPointUntil: function(t2, e2) {
        for (; t2; ) {
          if (e2(t2)) return t2;
          t2 = nt(t2);
        }
        return null;
      }, nextPointUntil: function(t2, e2) {
        for (; t2; ) {
          if (e2(t2)) return t2;
          t2 = it(t2);
        }
        return null;
      }, isCharPoint: function(t2) {
        if (!E(t2.node)) return false;
        var e2 = t2.node.nodeValue.charAt(t2.offset - 1);
        return e2 && " " !== e2 && e2 !== S;
      }, isSpacePoint: function(t2) {
        if (!E(t2.node)) return false;
        var e2 = t2.node.nodeValue.charAt(t2.offset - 1);
        return " " === e2 || e2 === S;
      }, walkPoint: function(t2, e2, o2, n2) {
        for (var i2 = t2; i2 && i2.node && (o2(i2), !st(i2, e2)); ) {
          i2 = rt(i2, n2 && t2.node !== i2.node && e2.node !== i2.node);
        }
      }, ancestor: q, singleChildAncestor: function(t2, e2) {
        for (t2 = t2.parentNode; t2 && 1 === U(t2); ) {
          if (e2(t2)) return t2;
          if (x(t2)) break;
          t2 = t2.parentNode;
        }
        return null;
      }, listAncestor: V, lastAncestor: function(t2, e2) {
        var o2 = V(t2);
        return C.last(o2.filter(e2));
      }, listNext: _, listPrev: function(t2, e2) {
        e2 = e2 || g.fail;
        for (var o2 = []; t2 && !e2(t2); ) o2.push(t2), t2 = t2.previousSibling;
        return o2;
      }, listDescendant: function(t2, e2) {
        var o2 = [];
        return e2 = e2 || g.ok, function n2(i2) {
          t2 !== i2 && e2(i2) && o2.push(i2);
          for (var r2 = 0, a2 = i2.childNodes.length; r2 < a2; r2++) n2(i2.childNodes[r2]);
        }(t2), o2;
      }, commonAncestor: function(t2, e2) {
        for (var o2 = V(t2), n2 = e2; n2; n2 = n2.parentNode) if (o2.indexOf(n2) > -1) return n2;
        return null;
      }, wrap: function(t2, e2) {
        var o2 = t2.parentNode, n2 = r()("<" + e2 + ">")[0];
        return o2.insertBefore(n2, t2), n2.appendChild(t2), n2;
      }, insertAfter: G, appendChildNodes: Z, position: et, hasChildren: ot, makeOffsetPath: function(t2, e2) {
        return V(e2, g.eq(t2)).map(et).reverse();
      }, fromOffsetPath: function(t2, e2) {
        for (var o2 = t2, n2 = 0, i2 = e2.length; n2 < i2; n2++) o2 = o2.childNodes.length <= e2[n2] ? o2.childNodes[o2.childNodes.length - 1] : o2.childNodes[e2[n2]];
        return o2;
      }, splitTree: ct, splitPoint: function(t2, e2) {
        var o2, n2, i2 = e2 ? N : B, r2 = V(t2.node, i2), a2 = C.last(r2) || t2.node;
        i2(a2) ? (o2 = r2[r2.length - 2], n2 = a2) : n2 = (o2 = a2).parentNode;
        var s2 = o2 && ct(o2, t2, { isSkipPaddingBlankHTML: e2, isNotSplitEdgePoint: e2 });
        return s2 || n2 !== t2.node || (s2 = t2.node.childNodes[t2.offset]), { rightNode: s2, container: n2 };
      }, create: ut, createText: function(t2) {
        return document.createTextNode(t2);
      }, remove: dt, removeWhile: function(t2, e2) {
        for (; t2 && !x(t2) && e2(t2); ) {
          var o2 = t2.parentNode;
          dt(t2), t2 = o2;
        }
      }, replace: function(t2, e2) {
        if (t2.nodeName.toUpperCase() === e2.toUpperCase()) return t2;
        var o2 = ut(e2);
        return t2.style.cssText && (o2.style.cssText = t2.style.cssText), Z(o2, C.from(t2.childNodes)), G(o2, t2), dt(t2), o2;
      }, html: function(t2, e2) {
        var o2 = ht(t2);
        if (e2) {
          o2 = (o2 = o2.replace(/<(\/?)(\b(?!!)[^>\s]*)(.*?)(\s*\/?>)/g, function(t3, e3, o3) {
            o3 = o3.toUpperCase();
            var n2 = /^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(o3) && !!e3, i2 = /^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(o3);
            return t3 + (n2 || i2 ? "\n" : "");
          })).trim();
        }
        return o2;
      }, value: ht, posFromPlaceholder: function(t2) {
        var e2 = r()(t2), o2 = e2.offset(), n2 = e2.outerHeight(true);
        return { left: o2.left, top: o2.top + n2 };
      }, attachEvents: function(t2, e2) {
        Object.keys(e2).forEach(function(o2) {
          t2.on(o2, e2[o2]);
        });
      }, detachEvents: function(t2, e2) {
        Object.keys(e2).forEach(function(o2) {
          t2.off(o2, e2[o2]);
        });
      }, isCustomStyleTag: function(t2) {
        return t2 && !E(t2) && C.contains(t2.classList, "note-styletag");
      } };
      function mt(t2) {
        return mt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, mt(t2);
      }
      function vt(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, gt(n2.key), n2);
        }
      }
      function gt(t2) {
        var e2 = function(t3, e3) {
          if ("object" != mt(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != mt(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == mt(e2) ? e2 : e2 + "";
      }
      var bt = function() {
        return t2 = function t3(e3, o3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.$note = e3, this.memos = {}, this.modules = {}, this.layoutInfo = {}, this.options = r().extend(true, {}, o3), r().summernote.ui = r().summernote.ui_template(this.options), this.ui = r().summernote.ui, this.initialize();
        }, e2 = [{ key: "initialize", value: function() {
          return this.layoutInfo = this.ui.createLayout(this.$note), this._initialize(), this.$note.hide(), this;
        } }, { key: "destroy", value: function() {
          this._destroy(), this.$note.removeData("summernote"), this.ui.removeLayout(this.$note, this.layoutInfo);
        } }, { key: "reset", value: function() {
          var t3 = this.isDisabled();
          this.code(pt.emptyPara), this._destroy(), this._initialize(), t3 && this.disable();
        } }, { key: "_initialize", value: function() {
          var t3 = this;
          this.options.id = g.uniqueId(r().now()), this.options.container = this.options.container || this.layoutInfo.editor;
          var e3 = r().extend({}, this.options.buttons);
          Object.keys(e3).forEach(function(o4) {
            t3.memo("button." + o4, e3[o4]);
          });
          var o3 = r().extend({}, this.options.modules, r().summernote.plugins || {});
          Object.keys(o3).forEach(function(e4) {
            t3.module(e4, o3[e4], true);
          }), Object.keys(this.modules).forEach(function(e4) {
            t3.initializeModule(e4);
          });
        } }, { key: "_destroy", value: function() {
          var t3 = this;
          Object.keys(this.modules).reverse().forEach(function(e3) {
            t3.removeModule(e3);
          }), Object.keys(this.memos).forEach(function(e3) {
            t3.removeMemo(e3);
          }), this.triggerEvent("destroy", this);
        } }, { key: "code", value: function(t3) {
          var e3 = this.invoke("codeview.isActivated");
          if (void 0 === t3) return this.invoke("codeview.sync"), e3 ? this.layoutInfo.codable.val() : this.layoutInfo.editable.html();
          e3 ? this.invoke("codeview.sync", t3) : this.layoutInfo.editable.html(t3), this.$note.val(t3), this.triggerEvent("change", t3, this.layoutInfo.editable);
        } }, { key: "isDisabled", value: function() {
          return "false" === this.layoutInfo.editable.attr("contenteditable");
        } }, { key: "enable", value: function() {
          this.layoutInfo.editable.attr("contenteditable", true), this.invoke("toolbar.activate", true), this.triggerEvent("disable", false), this.options.editing = true;
        } }, { key: "disable", value: function() {
          this.invoke("codeview.isActivated") && this.invoke("codeview.deactivate"), this.layoutInfo.editable.attr("contenteditable", false), this.options.editing = false, this.invoke("toolbar.deactivate", true), this.triggerEvent("disable", true);
        } }, { key: "triggerEvent", value: function() {
          var t3 = C.head(arguments), e3 = C.tail(C.from(arguments)), o3 = this.options.callbacks[g.namespaceToCamel(t3, "on")];
          o3 && o3.apply(this.$note[0], e3), this.$note.trigger("summernote." + t3, e3);
        } }, { key: "initializeModule", value: function(t3) {
          var e3 = this.modules[t3];
          e3.shouldInitialize = e3.shouldInitialize || g.ok, e3.shouldInitialize() && (e3.initialize && e3.initialize(), e3.events && pt.attachEvents(this.$note, e3.events));
        } }, { key: "module", value: function(t3, e3, o3) {
          if (1 === arguments.length) return this.modules[t3];
          this.modules[t3] = new e3(this), o3 || this.initializeModule(t3);
        } }, { key: "removeModule", value: function(t3) {
          var e3 = this.modules[t3];
          e3.shouldInitialize() && (e3.events && pt.detachEvents(this.$note, e3.events), e3.destroy && e3.destroy()), delete this.modules[t3];
        } }, { key: "memo", value: function(t3, e3) {
          if (1 === arguments.length) return this.memos[t3];
          this.memos[t3] = e3;
        } }, { key: "removeMemo", value: function(t3) {
          this.memos[t3] && this.memos[t3].destroy && this.memos[t3].destroy(), delete this.memos[t3];
        } }, { key: "createInvokeHandlerAndUpdateState", value: function(t3, e3) {
          var o3 = this;
          return function(n2) {
            o3.createInvokeHandler(t3, e3)(n2), o3.invoke("buttons.updateCurrentStyle");
          };
        } }, { key: "createInvokeHandler", value: function(t3, e3) {
          var o3 = this;
          return function(n2) {
            n2.preventDefault();
            var i2 = r()(n2.target);
            o3.invoke(t3, e3 || i2.closest("[data-value]").data("value"), i2);
          };
        } }, { key: "invoke", value: function() {
          var t3 = C.head(arguments), e3 = C.tail(C.from(arguments)), o3 = t3.split("."), n2 = o3.length > 1, i2 = n2 && C.head(o3), r2 = n2 ? C.last(o3) : C.head(o3), a2 = this.modules[i2 || "editor"];
          return !i2 && this[r2] ? this[r2].apply(this, e3) : a2 && a2[r2] && a2.shouldInitialize() ? a2[r2].apply(a2, e3) : void 0;
        } }], e2 && vt(t2.prototype, e2), o2 && vt(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function yt(t2) {
        return yt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, yt(t2);
      }
      function kt(t2) {
        return kt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, kt(t2);
      }
      function wt(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Ct(n2.key), n2);
        }
      }
      function Ct(t2) {
        var e2 = function(t3, e3) {
          if ("object" != kt(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != kt(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == kt(e2) ? e2 : e2 + "";
      }
      function St(t2, e2) {
        var o2, n2, i2 = t2.parentElement(), r2 = document.body.createTextRange(), a2 = C.from(i2.childNodes);
        for (o2 = 0; o2 < a2.length; o2++) if (!pt.isText(a2[o2])) {
          if (r2.moveToElementText(a2[o2]), r2.compareEndPoints("StartToStart", t2) >= 0) break;
          n2 = a2[o2];
        }
        if (0 !== o2 && pt.isText(a2[o2 - 1])) {
          var s2 = document.body.createTextRange(), l2 = null;
          s2.moveToElementText(n2 || i2), s2.collapse(!n2), l2 = n2 ? n2.nextSibling : i2.firstChild;
          var c2 = t2.duplicate();
          c2.setEndPoint("StartToStart", s2);
          for (var u2 = c2.text.replace(/[\r\n]/g, "").length; u2 > l2.nodeValue.length && l2.nextSibling; ) u2 -= l2.nodeValue.length, l2 = l2.nextSibling;
          l2.nodeValue;
          e2 && l2.nextSibling && pt.isText(l2.nextSibling) && u2 === l2.nodeValue.length && (u2 -= l2.nodeValue.length, l2 = l2.nextSibling), i2 = l2, o2 = u2;
        }
        return { cont: i2, offset: o2 };
      }
      function xt(t2) {
        var e2 = document.body.createTextRange(), o2 = function t3(e3, o3) {
          var n2, i2;
          if (pt.isText(e3)) {
            var r2 = pt.listPrev(e3, g.not(pt.isText)), a2 = C.last(r2).previousSibling;
            n2 = a2 || e3.parentNode, o3 += C.sum(C.tail(r2), pt.nodeLength), i2 = !a2;
          } else {
            if (n2 = e3.childNodes[o3] || e3, pt.isText(n2)) return t3(n2, 0);
            o3 = 0, i2 = false;
          }
          return { node: n2, collapseToStart: i2, offset: o3 };
        }(t2.node, t2.offset);
        return e2.moveToElementText(o2.node), e2.collapse(o2.collapseToStart), e2.moveStart("character", o2.offset), e2;
      }
      r().fn.extend({ summernote: function() {
        var t2 = yt(C.head(arguments)), e2 = "string" === t2, o2 = "object" === t2, n2 = r().extend({}, r().summernote.options, o2 ? C.head(arguments) : {});
        n2.langInfo = r().extend(true, {}, r().summernote.lang["en-US"], r().summernote.lang[n2.lang]), n2.icons = r().extend(true, {}, r().summernote.options.icons, n2.icons), n2.tooltip = "auto" === n2.tooltip ? !m.isSupportTouch : n2.tooltip, this.each(function(t3, e3) {
          var o3 = r()(e3);
          if (!o3.data("summernote")) {
            var i3 = new bt(o3, n2);
            o3.data("summernote", i3), o3.data("summernote").triggerEvent("init", i3.layoutInfo);
          }
        });
        var i2 = this.first();
        if (i2.length) {
          var a2 = i2.data("summernote");
          if (e2) return a2.invoke.apply(a2, C.from(arguments));
          n2.focus && a2.invoke("editor.focus");
        }
        return this;
      } });
      var Tt = function() {
        function t2(e3, o3, n3, i2) {
          !function(t3, e4) {
            if (!(t3 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t2), this.sc = e3, this.so = o3, this.ec = n3, this.eo = i2, this.isOnEditable = this.makeIsOn(pt.isEditable), this.isOnList = this.makeIsOn(pt.isList), this.isOnAnchor = this.makeIsOn(pt.isAnchor), this.isOnCell = this.makeIsOn(pt.isCell), this.isOnData = this.makeIsOn(pt.isData);
        }
        return e2 = t2, o2 = [{ key: "nativeRange", value: function() {
          if (m.isW3CRangeSupport) {
            var t3 = document.createRange();
            return t3.setStart(this.sc, this.so), t3.setEnd(this.ec, this.eo), t3;
          }
          var e3 = xt({ node: this.sc, offset: this.so });
          return e3.setEndPoint("EndToEnd", xt({ node: this.ec, offset: this.eo })), e3;
        } }, { key: "getPoints", value: function() {
          return { sc: this.sc, so: this.so, ec: this.ec, eo: this.eo };
        } }, { key: "getStartPoint", value: function() {
          return { node: this.sc, offset: this.so };
        } }, { key: "getEndPoint", value: function() {
          return { node: this.ec, offset: this.eo };
        } }, { key: "select", value: function() {
          var t3 = this.nativeRange();
          if (m.isW3CRangeSupport) {
            var e3 = document.getSelection();
            e3.rangeCount > 0 && e3.removeAllRanges(), e3.addRange(t3);
          } else t3.select();
          return this;
        } }, { key: "scrollIntoView", value: function(t3) {
          var e3 = r()(t3).height();
          return t3.scrollTop + e3 < this.sc.offsetTop && (t3.scrollTop += Math.abs(t3.scrollTop + e3 - this.sc.offsetTop)), this;
        } }, { key: "normalize", value: function() {
          var e3 = function(t3, e4) {
            if (!t3) return t3;
            if (pt.isVisiblePoint(t3) && (!pt.isEdgePoint(t3) || pt.isRightEdgePoint(t3) && !e4 || pt.isLeftEdgePoint(t3) && e4 || pt.isRightEdgePoint(t3) && e4 && pt.isVoid(t3.node.nextSibling) || pt.isLeftEdgePoint(t3) && !e4 && pt.isVoid(t3.node.previousSibling) || pt.isBlock(t3.node) && pt.isEmpty(t3.node))) return t3;
            var o4 = pt.ancestor(t3.node, pt.isBlock), n4 = false;
            if (!n4) {
              var i2 = pt.prevPoint(t3) || { node: null };
              n4 = (pt.isLeftEdgePointOf(t3, o4) || pt.isVoid(i2.node)) && !e4;
            }
            var r2 = false;
            if (!r2) {
              var a2 = pt.nextPoint(t3) || { node: null };
              r2 = (pt.isRightEdgePointOf(t3, o4) || pt.isVoid(a2.node)) && e4;
            }
            if (n4 || r2) {
              if (pt.isVisiblePoint(t3)) return t3;
              e4 = !e4;
            }
            return (e4 ? pt.nextPointUntil(pt.nextPoint(t3), pt.isVisiblePoint) : pt.prevPointUntil(pt.prevPoint(t3), pt.isVisiblePoint)) || t3;
          }, o3 = e3(this.getEndPoint(), false), n3 = this.isCollapsed() ? o3 : e3(this.getStartPoint(), true);
          return new t2(n3.node, n3.offset, o3.node, o3.offset);
        } }, { key: "nodes", value: function(t3, e3) {
          t3 = t3 || g.ok;
          var o3 = e3 && e3.includeAncestor, n3 = e3 && e3.fullyContains, i2 = this.getStartPoint(), r2 = this.getEndPoint(), a2 = [], s2 = [];
          return pt.walkPoint(i2, r2, function(e4) {
            var i3;
            pt.isEditable(e4.node) || (n3 ? (pt.isLeftEdgePoint(e4) && s2.push(e4.node), pt.isRightEdgePoint(e4) && C.contains(s2, e4.node) && (i3 = e4.node)) : i3 = o3 ? pt.ancestor(e4.node, t3) : e4.node, i3 && t3(i3) && a2.push(i3));
          }, true), C.unique(a2);
        } }, { key: "commonAncestor", value: function() {
          return pt.commonAncestor(this.sc, this.ec);
        } }, { key: "expand", value: function(e3) {
          var o3 = pt.ancestor(this.sc, e3), n3 = pt.ancestor(this.ec, e3);
          if (!o3 && !n3) return new t2(this.sc, this.so, this.ec, this.eo);
          var i2 = this.getPoints();
          return o3 && (i2.sc = o3, i2.so = 0), n3 && (i2.ec = n3, i2.eo = pt.nodeLength(n3)), new t2(i2.sc, i2.so, i2.ec, i2.eo);
        } }, { key: "collapse", value: function(e3) {
          return e3 ? new t2(this.sc, this.so, this.sc, this.so) : new t2(this.ec, this.eo, this.ec, this.eo);
        } }, { key: "splitText", value: function() {
          var e3 = this.sc === this.ec, o3 = this.getPoints();
          return pt.isText(this.ec) && !pt.isEdgePoint(this.getEndPoint()) && this.ec.splitText(this.eo), pt.isText(this.sc) && !pt.isEdgePoint(this.getStartPoint()) && (o3.sc = this.sc.splitText(this.so), o3.so = 0, e3 && (o3.ec = o3.sc, o3.eo = this.eo - this.so)), new t2(o3.sc, o3.so, o3.ec, o3.eo);
        } }, { key: "deleteContents", value: function() {
          if (this.isCollapsed()) return this;
          var e3 = this.splitText(), o3 = e3.nodes(null, { fullyContains: true }), n3 = pt.prevPointUntil(e3.getStartPoint(), function(t3) {
            return !C.contains(o3, t3.node);
          }), i2 = [];
          return r().each(o3, function(t3, e4) {
            var o4 = e4.parentNode;
            n3.node !== o4 && 1 === pt.nodeLength(o4) && i2.push(o4), pt.remove(e4, false);
          }), r().each(i2, function(t3, e4) {
            pt.remove(e4, false);
          }), new t2(n3.node, n3.offset, n3.node, n3.offset).normalize();
        } }, { key: "makeIsOn", value: function(t3) {
          return function() {
            var e3 = pt.ancestor(this.sc, t3);
            return !!e3 && e3 === pt.ancestor(this.ec, t3);
          };
        } }, { key: "isLeftEdgeOf", value: function(t3) {
          if (!pt.isLeftEdgePoint(this.getStartPoint())) return false;
          var e3 = pt.ancestor(this.sc, t3);
          return e3 && pt.isLeftEdgeOf(this.sc, e3);
        } }, { key: "isCollapsed", value: function() {
          return this.sc === this.ec && this.so === this.eo;
        } }, { key: "wrapBodyInlineWithPara", value: function() {
          if (pt.isBodyContainer(this.sc) && pt.isEmpty(this.sc)) return this.sc.innerHTML = pt.emptyPara, new t2(this.sc.firstChild, 0, this.sc.firstChild, 0);
          var e3, o3 = this.normalize();
          if (pt.isParaInline(this.sc) || pt.isPara(this.sc)) return o3;
          if (pt.isInline(o3.sc)) {
            var n3 = pt.listAncestor(o3.sc, g.not(pt.isInline));
            e3 = C.last(n3), pt.isInline(e3) || (e3 = n3[n3.length - 2] || o3.sc.childNodes[o3.so]);
          } else e3 = o3.sc.childNodes[o3.so > 0 ? o3.so - 1 : 0];
          if (e3) {
            var i2 = pt.listPrev(e3, pt.isParaInline).reverse();
            if ((i2 = i2.concat(pt.listNext(e3.nextSibling, pt.isParaInline))).length) {
              var r2 = pt.wrap(C.head(i2), "p");
              pt.appendChildNodes(r2, C.tail(i2));
            }
          }
          return this.normalize();
        } }, { key: "insertNode", value: function(t3) {
          var e3 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], o3 = this;
          (pt.isText(t3) || pt.isInline(t3)) && (o3 = this.wrapBodyInlineWithPara().deleteContents());
          var n3 = pt.splitPoint(o3.getStartPoint(), pt.isInline(t3));
          return n3.rightNode ? (n3.rightNode.parentNode.insertBefore(t3, n3.rightNode), pt.isEmpty(n3.rightNode) && (e3 || pt.isPara(t3)) && n3.rightNode.parentNode.removeChild(n3.rightNode)) : n3.container.appendChild(t3), t3;
        } }, { key: "pasteHTML", value: function(t3) {
          t3 = ((t3 || "") + "").trim(t3);
          var e3 = r()("<div></div>").html(t3)[0], o3 = C.from(e3.childNodes), n3 = this, i2 = false;
          return n3.so >= 0 && (o3 = o3.reverse(), i2 = true), o3 = o3.map(function(t4) {
            return n3.insertNode(t4, !pt.isInline(t4));
          }), i2 && (o3 = o3.reverse()), o3;
        } }, { key: "toString", value: function() {
          var t3 = this.nativeRange();
          return m.isW3CRangeSupport ? t3.toString() : t3.text;
        } }, { key: "getWordRange", value: function(e3) {
          var o3 = this.getEndPoint();
          if (!pt.isCharPoint(o3)) return this;
          var n3 = pt.prevPointUntil(o3, function(t3) {
            return !pt.isCharPoint(t3);
          });
          return e3 && (o3 = pt.nextPointUntil(o3, function(t3) {
            return !pt.isCharPoint(t3);
          })), new t2(n3.node, n3.offset, o3.node, o3.offset);
        } }, { key: "getWordsRange", value: function(e3) {
          var o3 = this.getEndPoint(), n3 = function(t3) {
            return !pt.isCharPoint(t3) && !pt.isSpacePoint(t3);
          };
          if (n3(o3)) return this;
          var i2 = pt.prevPointUntil(o3, n3);
          return e3 && (o3 = pt.nextPointUntil(o3, n3)), new t2(i2.node, i2.offset, o3.node, o3.offset);
        } }, { key: "getWordsMatchRange", value: function(e3) {
          var o3 = this.getEndPoint(), n3 = pt.prevPointUntil(o3, function(n4) {
            if (!pt.isCharPoint(n4) && !pt.isSpacePoint(n4)) return true;
            var i3 = new t2(n4.node, n4.offset, o3.node, o3.offset), r3 = e3.exec(i3.toString());
            return r3 && 0 === r3.index;
          }), i2 = new t2(n3.node, n3.offset, o3.node, o3.offset), r2 = i2.toString(), a2 = e3.exec(r2);
          return a2 && a2[0].length === r2.length ? i2 : null;
        } }, { key: "bookmark", value: function(t3) {
          return { s: { path: pt.makeOffsetPath(t3, this.sc), offset: this.so }, e: { path: pt.makeOffsetPath(t3, this.ec), offset: this.eo } };
        } }, { key: "paraBookmark", value: function(t3) {
          return { s: { path: C.tail(pt.makeOffsetPath(C.head(t3), this.sc)), offset: this.so }, e: { path: C.tail(pt.makeOffsetPath(C.last(t3), this.ec)), offset: this.eo } };
        } }, { key: "getClientRects", value: function() {
          return this.nativeRange().getClientRects();
        } }], o2 && wt(e2.prototype, o2), n2 && wt(e2, n2), Object.defineProperty(e2, "prototype", { writable: false }), e2;
        var e2, o2, n2;
      }();
      const Et = { create: function(t2, e2, o2, n2) {
        if (4 === arguments.length) return new Tt(t2, e2, o2, n2);
        if (2 === arguments.length) return new Tt(t2, e2, o2 = t2, n2 = e2);
        var i2 = this.createFromSelection();
        if (!i2 && 1 === arguments.length) {
          var r2 = arguments[0];
          return pt.isEditable(r2) && (r2 = r2.lastChild), this.createFromBodyElement(r2, pt.emptyPara === arguments[0].innerHTML);
        }
        return i2;
      }, createFromBodyElement: function(t2) {
        var e2 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
        return this.createFromNode(t2).collapse(e2);
      }, createFromSelection: function() {
        var t2, e2, o2, n2;
        if (m.isW3CRangeSupport) {
          var i2 = document.getSelection();
          if (!i2 || 0 === i2.rangeCount) return null;
          if (pt.isBody(i2.anchorNode)) return null;
          var r2 = i2.getRangeAt(0);
          t2 = r2.startContainer, e2 = r2.startOffset, o2 = r2.endContainer, n2 = r2.endOffset;
        } else {
          var a2 = document.selection.createRange(), s2 = a2.duplicate();
          s2.collapse(false);
          var l2 = a2;
          l2.collapse(true);
          var c2 = St(l2, true), u2 = St(s2, false);
          pt.isText(c2.node) && pt.isLeftEdgePoint(c2) && pt.isTextNode(u2.node) && pt.isRightEdgePoint(u2) && u2.node.nextSibling === c2.node && (c2 = u2), t2 = c2.cont, e2 = c2.offset, o2 = u2.cont, n2 = u2.offset;
        }
        return new Tt(t2, e2, o2, n2);
      }, createFromNode: function(t2) {
        var e2 = t2, o2 = 0, n2 = t2, i2 = pt.nodeLength(n2);
        return pt.isVoid(e2) && (o2 = pt.listPrev(e2).length - 1, e2 = e2.parentNode), pt.isBR(n2) ? (i2 = pt.listPrev(n2).length - 1, n2 = n2.parentNode) : pt.isVoid(n2) && (i2 = pt.listPrev(n2).length, n2 = n2.parentNode), this.create(e2, o2, n2, i2);
      }, createFromNodeBefore: function(t2) {
        return this.createFromNode(t2).collapse(true);
      }, createFromNodeAfter: function(t2) {
        return this.createFromNode(t2).collapse();
      }, createFromBookmark: function(t2, e2) {
        var o2 = pt.fromOffsetPath(t2, e2.s.path), n2 = e2.s.offset, i2 = pt.fromOffsetPath(t2, e2.e.path), r2 = e2.e.offset;
        return new Tt(o2, n2, i2, r2);
      }, createFromParaBookmark: function(t2, e2) {
        var o2 = t2.s.offset, n2 = t2.e.offset, i2 = pt.fromOffsetPath(C.head(e2), t2.s.path), r2 = pt.fromOffsetPath(C.last(e2), t2.e.path);
        return new Tt(i2, o2, r2, n2);
      } };
      var Pt = { BACKSPACE: 8, TAB: 9, ENTER: 13, ESCAPE: 27, SPACE: 32, DELETE: 46, LEFT: 37, UP: 38, RIGHT: 39, DOWN: 40, NUM0: 48, NUM1: 49, NUM2: 50, NUM3: 51, NUM4: 52, NUM5: 53, NUM6: 54, NUM7: 55, NUM8: 56, B: 66, E: 69, I: 73, J: 74, K: 75, L: 76, R: 82, S: 83, U: 85, V: 86, Y: 89, Z: 90, SLASH: 191, LEFTBRACKET: 219, BACKSLASH: 220, RIGHTBRACKET: 221, HOME: 36, END: 35, PAGEUP: 33, PAGEDOWN: 34 };
      const Nt = { isEdit: function(t2) {
        return C.contains([Pt.BACKSPACE, Pt.TAB, Pt.ENTER, Pt.SPACE, Pt.DELETE], t2);
      }, isRemove: function(t2) {
        return C.contains([Pt.BACKSPACE, Pt.DELETE], t2);
      }, isMove: function(t2) {
        return C.contains([Pt.LEFT, Pt.UP, Pt.RIGHT, Pt.DOWN], t2);
      }, isNavigation: function(t2) {
        return C.contains([Pt.HOME, Pt.END, Pt.PAGEUP, Pt.PAGEDOWN], t2);
      }, nameFromCode: g.invertObject(Pt), code: Pt };
      function $t(t2) {
        return $t = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, $t(t2);
      }
      function It(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Rt(n2.key), n2);
        }
      }
      function Rt(t2) {
        var e2 = function(t3, e3) {
          if ("object" != $t(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != $t(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == $t(e2) ? e2 : e2 + "";
      }
      var At = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.stack = [], this.stackOffset = -1, this.context = e3, this.$editable = e3.layoutInfo.editable, this.editable = this.$editable[0];
        }, (e2 = [{ key: "makeSnapshot", value: function() {
          var t3 = Et.create(this.editable);
          return { contents: this.$editable.html(), bookmark: t3 && t3.isOnEditable() ? t3.bookmark(this.editable) : { s: { path: [], offset: 0 }, e: { path: [], offset: 0 } } };
        } }, { key: "applySnapshot", value: function(t3) {
          null !== t3.contents && this.$editable.html(t3.contents), null !== t3.bookmark && Et.createFromBookmark(this.editable, t3.bookmark).select();
        } }, { key: "rewind", value: function() {
          this.$editable.html() !== this.stack[this.stackOffset].contents && this.recordUndo(), this.stackOffset = 0, this.applySnapshot(this.stack[this.stackOffset]);
        } }, { key: "commit", value: function() {
          this.stack = [], this.stackOffset = -1, this.recordUndo();
        } }, { key: "reset", value: function() {
          this.stack = [], this.stackOffset = -1, this.$editable.html(""), this.recordUndo();
        } }, { key: "undo", value: function() {
          this.$editable.html() !== this.stack[this.stackOffset].contents && this.recordUndo(), this.stackOffset > 0 && (this.stackOffset--, this.applySnapshot(this.stack[this.stackOffset]));
        } }, { key: "redo", value: function() {
          this.stack.length - 1 > this.stackOffset && (this.stackOffset++, this.applySnapshot(this.stack[this.stackOffset]));
        } }, { key: "recordUndo", value: function() {
          this.stackOffset++, this.stack.length > this.stackOffset && (this.stack = this.stack.slice(0, this.stackOffset)), this.stack.push(this.makeSnapshot()), this.stack.length > this.context.options.historyLimit && (this.stack.shift(), this.stackOffset -= 1);
        } }]) && It(t2.prototype, e2), o2 && It(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Lt(t2) {
        return Lt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Lt(t2);
      }
      function Ft(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Dt(n2.key), n2);
        }
      }
      function Dt(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Lt(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Lt(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Lt(e2) ? e2 : e2 + "";
      }
      var Ht = function() {
        return t2 = function t3() {
          !function(t4, e3) {
            if (!(t4 instanceof e3)) throw new TypeError("Cannot call a class as a function");
          }(this, t3);
        }, e2 = [{ key: "jQueryCSS", value: function(t3, e3) {
          var o3 = {};
          return r().each(e3, function(e4, n2) {
            o3[n2] = t3.css(n2);
          }), o3;
        } }, { key: "fromNode", value: function(t3) {
          var e3 = this.jQueryCSS(t3, ["font-family", "font-size", "text-align", "list-style-type", "line-height"]) || {}, o3 = t3[0].style.fontSize || e3["font-size"];
          return e3["font-size"] = parseInt(o3, 10), e3["font-size-unit"] = o3.match(/[a-z%]+$/), e3;
        } }, { key: "stylePara", value: function(t3, e3) {
          r().each(t3.nodes(pt.isPara, { includeAncestor: true }), function(t4, o3) {
            r()(o3).css(e3);
          });
        } }, { key: "styleNodes", value: function(t3, e3) {
          t3 = t3.splitText();
          var o3 = e3 && e3.nodeName || "SPAN", n2 = !(!e3 || !e3.expandClosestSibling), i2 = !(!e3 || !e3.onlyPartialContains);
          if (t3.isCollapsed()) return [t3.insertNode(pt.create(o3))];
          var a2 = pt.makePredByNodeName(o3), s2 = t3.nodes(pt.isText, { fullyContains: true }).map(function(t4) {
            return pt.singleChildAncestor(t4, a2) || pt.wrap(t4, o3);
          });
          if (n2) {
            if (i2) {
              var l2 = t3.nodes();
              a2 = g.and(a2, function(t4) {
                return C.contains(l2, t4);
              });
            }
            return s2.map(function(t4) {
              var e4 = pt.withClosestSiblings(t4, a2), o4 = C.head(e4), n3 = C.tail(e4);
              return r().each(n3, function(t5, e5) {
                pt.appendChildNodes(o4, e5.childNodes), pt.remove(e5);
              }), C.head(e4);
            });
          }
          return s2;
        } }, { key: "current", value: function(t3) {
          var e3 = r()(pt.isElement(t3.sc) ? t3.sc : t3.sc.parentNode), o3 = this.fromNode(e3);
          try {
            o3 = r().extend(o3, { "font-bold": document.queryCommandState("bold") ? "bold" : "normal", "font-italic": document.queryCommandState("italic") ? "italic" : "normal", "font-underline": document.queryCommandState("underline") ? "underline" : "normal", "font-subscript": document.queryCommandState("subscript") ? "subscript" : "normal", "font-superscript": document.queryCommandState("superscript") ? "superscript" : "normal", "font-strikethrough": document.queryCommandState("strikethrough") ? "strikethrough" : "normal", "font-family": document.queryCommandValue("fontname") || o3["font-family"] });
          } catch (t4) {
          }
          if (t3.isOnList()) {
            var n2 = ["circle", "disc", "disc-leading-zero", "square"].indexOf(o3["list-style-type"]) > -1;
            o3["list-style"] = n2 ? "unordered" : "ordered";
          } else o3["list-style"] = "none";
          var i2 = pt.ancestor(t3.sc, pt.isPara);
          if (i2 && i2.style["line-height"]) o3["line-height"] = i2.style.lineHeight;
          else {
            var a2 = parseInt(o3["line-height"], 10) / parseInt(o3["font-size"], 10);
            o3["line-height"] = a2.toFixed(1);
          }
          return o3.anchor = t3.isOnAnchor() && pt.ancestor(t3.sc, pt.isAnchor), o3.ancestors = pt.listAncestor(t3.sc, pt.isEditable), o3.range = t3, o3;
        } }], e2 && Ft(t2.prototype, e2), o2 && Ft(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function jt(t2) {
        return jt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, jt(t2);
      }
      function Bt(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Ot(n2.key), n2);
        }
      }
      function Ot(t2) {
        var e2 = function(t3, e3) {
          if ("object" != jt(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != jt(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == jt(e2) ? e2 : e2 + "";
      }
      var zt = function() {
        return t2 = function t3() {
          !function(t4, e3) {
            if (!(t4 instanceof e3)) throw new TypeError("Cannot call a class as a function");
          }(this, t3);
        }, e2 = [{ key: "insertOrderedList", value: function(t3) {
          this.toggleList("OL", t3);
        } }, { key: "insertUnorderedList", value: function(t3) {
          this.toggleList("UL", t3);
        } }, { key: "indent", value: function(t3) {
          var e3 = this, o3 = Et.create(t3).wrapBodyInlineWithPara(), n2 = o3.nodes(pt.isPara, { includeAncestor: true }), i2 = C.clusterBy(n2, g.peq2("parentNode"));
          r().each(i2, function(t4, o4) {
            var n3 = C.head(o4);
            if (pt.isLi(n3)) {
              var i3 = e3.findList(n3.previousSibling);
              i3 ? o4.map(function(t5) {
                return i3.appendChild(t5);
              }) : (e3.wrapList(o4, n3.parentNode.nodeName), o4.map(function(t5) {
                return t5.parentNode;
              }).map(function(t5) {
                return e3.appendToPrevious(t5);
              }));
            } else r().each(o4, function(t5, e4) {
              r()(e4).css("marginLeft", function(t6, e5) {
                return (parseInt(e5, 10) || 0) + 25;
              });
            });
          }), o3.select();
        } }, { key: "outdent", value: function(t3) {
          var e3 = this, o3 = Et.create(t3).wrapBodyInlineWithPara(), n2 = o3.nodes(pt.isPara, { includeAncestor: true }), i2 = C.clusterBy(n2, g.peq2("parentNode"));
          r().each(i2, function(t4, o4) {
            var n3 = C.head(o4);
            pt.isLi(n3) ? e3.releaseList([o4]) : r().each(o4, function(t5, e4) {
              r()(e4).css("marginLeft", function(t6, e5) {
                return (e5 = parseInt(e5, 10) || 0) > 25 ? e5 - 25 : "";
              });
            });
          }), o3.select();
        } }, { key: "toggleList", value: function(t3, e3) {
          var o3 = this, n2 = Et.create(e3).wrapBodyInlineWithPara(), i2 = n2.nodes(pt.isPara, { includeAncestor: true }), a2 = n2.paraBookmark(i2), s2 = C.clusterBy(i2, g.peq2("parentNode"));
          if (C.find(i2, pt.isPurePara)) {
            var l2 = [];
            r().each(s2, function(e4, n3) {
              l2 = l2.concat(o3.wrapList(n3, t3));
            }), i2 = l2;
          } else {
            var c2 = n2.nodes(pt.isList, { includeAncestor: true }).filter(function(e4) {
              return !r().nodeName(e4, t3);
            });
            c2.length ? r().each(c2, function(e4, o4) {
              pt.replace(o4, t3);
            }) : i2 = this.releaseList(s2, true);
          }
          Et.createFromParaBookmark(a2, i2).select();
        } }, { key: "wrapList", value: function(t3, e3) {
          var o3 = C.head(t3), n2 = C.last(t3), i2 = pt.isList(o3.previousSibling) && o3.previousSibling, r2 = pt.isList(n2.nextSibling) && n2.nextSibling, a2 = i2 || pt.insertAfter(pt.create(e3 || "UL"), n2);
          return t3 = t3.map(function(t4) {
            return pt.isPurePara(t4) ? pt.replace(t4, "LI") : t4;
          }), pt.appendChildNodes(a2, t3, true), r2 && (pt.appendChildNodes(a2, C.from(r2.childNodes), true), pt.remove(r2)), t3;
        } }, { key: "releaseList", value: function(t3, e3) {
          var o3 = this, n2 = [];
          return r().each(t3, function(t4, i2) {
            var a2 = C.head(i2), s2 = C.last(i2), l2 = e3 ? pt.lastAncestor(a2, pt.isList) : a2.parentNode, c2 = l2.parentNode;
            if ("LI" === l2.parentNode.nodeName) i2.map(function(t5) {
              var e4 = o3.findNextSiblings(t5);
              c2.nextSibling ? c2.parentNode.insertBefore(t5, c2.nextSibling) : c2.parentNode.appendChild(t5), e4.length && (o3.wrapList(e4, l2.nodeName), t5.appendChild(e4[0].parentNode));
            }), 0 === l2.children.length && c2.removeChild(l2), 0 === c2.childNodes.length && c2.parentNode.removeChild(c2);
            else {
              var u2 = l2.childNodes.length > 1 ? pt.splitTree(l2, { node: s2.parentNode, offset: pt.position(s2) + 1 }, { isSkipPaddingBlankHTML: true }) : null, d2 = pt.splitTree(l2, { node: a2.parentNode, offset: pt.position(a2) }, { isSkipPaddingBlankHTML: true });
              i2 = e3 ? pt.listDescendant(d2, pt.isLi) : C.from(d2.childNodes).filter(pt.isLi), !e3 && pt.isList(l2.parentNode) || (i2 = i2.map(function(t5) {
                return pt.replace(t5, "P");
              })), r().each(C.from(i2).reverse(), function(t5, e4) {
                pt.insertAfter(e4, l2);
              });
              var f2 = C.compact([l2, d2, u2]);
              r().each(f2, function(t5, e4) {
                var o4 = [e4].concat(pt.listDescendant(e4, pt.isList));
                r().each(o4.reverse(), function(t6, e5) {
                  pt.nodeLength(e5) || pt.remove(e5, true);
                });
              });
            }
            n2 = n2.concat(i2);
          }), n2;
        } }, { key: "appendToPrevious", value: function(t3) {
          return t3.previousSibling ? pt.appendChildNodes(t3.previousSibling, [t3]) : this.wrapList([t3], "LI");
        } }, { key: "findList", value: function(t3) {
          return t3 ? C.find(t3.children, function(t4) {
            return ["OL", "UL"].indexOf(t4.nodeName) > -1;
          }) : null;
        } }, { key: "findNextSiblings", value: function(t3) {
          for (var e3 = []; t3.nextSibling; ) e3.push(t3.nextSibling), t3 = t3.nextSibling;
          return e3;
        } }], e2 && Bt(t2.prototype, e2), o2 && Bt(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Mt(t2) {
        return Mt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Mt(t2);
      }
      function Ut(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Wt(n2.key), n2);
        }
      }
      function Wt(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Mt(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Mt(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Mt(e2) ? e2 : e2 + "";
      }
      var Kt = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.bullet = new zt(), this.options = e3.options;
        }, e2 = [{ key: "insertTab", value: function(t3, e3) {
          var o3 = pt.createText(new Array(e3 + 1).join(pt.NBSP_CHAR));
          (t3 = t3.deleteContents()).insertNode(o3, true), (t3 = Et.create(o3, e3)).select();
        } }, { key: "insertParagraph", value: function(t3, e3) {
          e3 = (e3 = (e3 = e3 || Et.create(t3)).deleteContents()).wrapBodyInlineWithPara();
          var o3, n2 = pt.ancestor(e3.sc, pt.isPara);
          if (n2) {
            if (pt.isLi(n2) && (pt.isEmpty(n2) || pt.deepestChildIsEmpty(n2))) return void this.bullet.toggleList(n2.parentNode.nodeName);
            var i2 = null;
            if (1 === this.options.blockquoteBreakingLevel ? i2 = pt.ancestor(n2, pt.isBlockquote) : 2 === this.options.blockquoteBreakingLevel && (i2 = pt.lastAncestor(n2, pt.isBlockquote)), i2) {
              o3 = r()(pt.emptyPara)[0], pt.isRightEdgePoint(e3.getStartPoint()) && pt.isBR(e3.sc.nextSibling) && r()(e3.sc.nextSibling).remove();
              var a2 = pt.splitTree(i2, e3.getStartPoint(), { isDiscardEmptySplits: true });
              a2 ? a2.parentNode.insertBefore(o3, a2) : pt.insertAfter(o3, i2);
            } else {
              o3 = pt.splitTree(n2, e3.getStartPoint());
              var s2 = pt.listDescendant(n2, pt.isEmptyAnchor);
              s2 = s2.concat(pt.listDescendant(o3, pt.isEmptyAnchor)), r().each(s2, function(t4, e4) {
                pt.remove(e4);
              }), (pt.isHeading(o3) || pt.isPre(o3) || pt.isCustomStyleTag(o3)) && pt.isEmpty(o3) && (o3 = pt.replace(o3, "p"));
            }
          } else {
            var l2 = e3.sc.childNodes[e3.so];
            o3 = r()(pt.emptyPara)[0], l2 ? e3.sc.insertBefore(o3, l2) : e3.sc.appendChild(o3);
          }
          Et.create(o3, 0).normalize().select().scrollIntoView(t3);
        } }], e2 && Ut(t2.prototype, e2), o2 && Ut(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function qt(t2) {
        return qt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, qt(t2);
      }
      function Vt(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, _t(n2.key), n2);
        }
      }
      function _t(t2) {
        var e2 = function(t3, e3) {
          if ("object" != qt(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != qt(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == qt(e2) ? e2 : e2 + "";
      }
      var Gt = function t2(e2, o2, n2, i2) {
        var r2 = { colPos: 0, rowPos: 0 }, a2 = [], s2 = [];
        function l2(t3, e3, o3, n3, i3, r3, s3) {
          var l3 = { baseRow: o3, baseCell: n3, isRowSpan: i3, isColSpan: r3, isVirtual: s3 };
          a2[t3] || (a2[t3] = []), a2[t3][e3] = l3;
        }
        function c2(t3, e3, o3, n3) {
          return { baseCell: t3.baseCell, action: e3, virtualTable: { rowIndex: o3, cellIndex: n3 } };
        }
        function u2(t3, e3) {
          if (!a2[t3]) return e3;
          if (!a2[t3][e3]) return e3;
          for (var o3 = e3; a2[t3][o3]; ) if (o3++, !a2[t3][o3]) return o3;
        }
        function d2(t3, e3) {
          var o3 = u2(t3.rowIndex, e3.cellIndex), n3 = e3.colSpan > 1, i3 = e3.rowSpan > 1, a3 = t3.rowIndex === r2.rowPos && e3.cellIndex === r2.colPos;
          l2(t3.rowIndex, o3, t3, e3, i3, n3, false);
          var s3 = e3.attributes.rowSpan ? parseInt(e3.attributes.rowSpan.value, 10) : 0;
          if (s3 > 1) for (var c3 = 1; c3 < s3; c3++) {
            var d3 = t3.rowIndex + c3;
            f2(d3, o3, e3, a3), l2(d3, o3, t3, e3, true, n3, true);
          }
          var h3 = e3.attributes.colSpan ? parseInt(e3.attributes.colSpan.value, 10) : 0;
          if (h3 > 1) for (var p3 = 1; p3 < h3; p3++) {
            var m2 = u2(t3.rowIndex, o3 + p3);
            f2(t3.rowIndex, m2, e3, a3), l2(t3.rowIndex, m2, t3, e3, i3, true, true);
          }
        }
        function f2(t3, e3, o3, n3) {
          t3 === r2.rowPos && r2.colPos >= o3.cellIndex && o3.cellIndex <= e3 && !n3 && r2.colPos++;
        }
        function h2(e3) {
          switch (o2) {
            case t2.where.Column:
              if (e3.isColSpan) return t2.resultAction.SubtractSpanCount;
              break;
            case t2.where.Row:
              if (!e3.isVirtual && e3.isRowSpan) return t2.resultAction.AddCell;
              if (e3.isRowSpan) return t2.resultAction.SubtractSpanCount;
          }
          return t2.resultAction.RemoveCell;
        }
        function p2(e3) {
          switch (o2) {
            case t2.where.Column:
              if (e3.isColSpan) return t2.resultAction.SumSpanCount;
              if (e3.isRowSpan && e3.isVirtual) return t2.resultAction.Ignore;
              break;
            case t2.where.Row:
              if (e3.isRowSpan) return t2.resultAction.SumSpanCount;
              if (e3.isColSpan && e3.isVirtual) return t2.resultAction.Ignore;
          }
          return t2.resultAction.AddCell;
        }
        this.getActionList = function() {
          for (var e3 = o2 === t2.where.Row ? r2.rowPos : -1, i3 = o2 === t2.where.Column ? r2.colPos : -1, l3 = 0, u3 = true; u3; ) {
            var d3 = e3 >= 0 ? e3 : l3, f3 = i3 >= 0 ? i3 : l3, m2 = a2[d3];
            if (!m2) return u3 = false, s2;
            var v2 = m2[f3];
            if (!v2) return u3 = false, s2;
            var g2 = t2.resultAction.Ignore;
            switch (n2) {
              case t2.requestAction.Add:
                g2 = p2(v2);
                break;
              case t2.requestAction.Delete:
                g2 = h2(v2);
            }
            s2.push(c2(v2, g2, d3, f3)), l3++;
          }
          return s2;
        }, e2 && e2.tagName && ("td" === e2.tagName.toLowerCase() || "th" === e2.tagName.toLowerCase()) && (r2.colPos = e2.cellIndex, e2.parentElement && e2.parentElement.tagName && "tr" === e2.parentElement.tagName.toLowerCase() && (r2.rowPos = e2.parentElement.rowIndex)), function() {
          for (var t3 = i2.rows, e3 = 0; e3 < t3.length; e3++) for (var o3 = t3[e3].cells, n3 = 0; n3 < o3.length; n3++) d2(t3[e3], o3[n3]);
        }();
      };
      Gt.where = { Row: 0, Column: 1 }, Gt.requestAction = { Add: 0, Delete: 1 }, Gt.resultAction = { Ignore: 0, SubtractSpanCount: 1, RemoveCell: 2, AddCell: 3, SumSpanCount: 4 };
      var Zt = function() {
        return t2 = function t3() {
          !function(t4, e3) {
            if (!(t4 instanceof e3)) throw new TypeError("Cannot call a class as a function");
          }(this, t3);
        }, e2 = [{ key: "tab", value: function(t3, e3) {
          var o3 = pt.ancestor(t3.commonAncestor(), pt.isCell), n2 = pt.ancestor(o3, pt.isTable), i2 = pt.listDescendant(n2, pt.isCell), r2 = C[e3 ? "prev" : "next"](i2, o3);
          r2 && Et.create(r2, 0).select();
        } }, { key: "addRow", value: function(t3, e3) {
          for (var o3 = pt.ancestor(t3.commonAncestor(), pt.isCell), n2 = r()(o3).closest("tr"), i2 = this.recoverAttributes(n2), a2 = r()("<tr" + i2 + "></tr>"), s2 = new Gt(o3, Gt.where.Row, Gt.requestAction.Add, r()(n2).closest("table")[0]).getActionList(), l2 = 0; l2 < s2.length; l2++) {
            var c2 = s2[l2], u2 = this.recoverAttributes(c2.baseCell);
            switch (c2.action) {
              case Gt.resultAction.AddCell:
                a2.append("<td" + u2 + ">" + pt.blank + "</td>");
                break;
              case Gt.resultAction.SumSpanCount:
                if ("top" === e3 && (c2.baseCell.parent ? c2.baseCell.closest("tr").rowIndex : 0) <= n2[0].rowIndex) {
                  var d2 = r()("<div></div>").append(r()("<td" + u2 + ">" + pt.blank + "</td>").removeAttr("rowspan")).html();
                  a2.append(d2);
                  break;
                }
                var f2 = parseInt(c2.baseCell.rowSpan, 10);
                f2++, c2.baseCell.setAttribute("rowSpan", f2);
            }
          }
          if ("top" === e3) n2.before(a2);
          else {
            if (o3.rowSpan > 1) {
              var h2 = n2[0].rowIndex + (o3.rowSpan - 2);
              return void r()(r()(n2).parent().find("tr")[h2]).after(r()(a2));
            }
            n2.after(a2);
          }
        } }, { key: "addCol", value: function(t3, e3) {
          var o3 = pt.ancestor(t3.commonAncestor(), pt.isCell), n2 = r()(o3).closest("tr");
          r()(n2).siblings().push(n2);
          for (var i2 = new Gt(o3, Gt.where.Column, Gt.requestAction.Add, r()(n2).closest("table")[0]).getActionList(), a2 = 0; a2 < i2.length; a2++) {
            var s2 = i2[a2], l2 = this.recoverAttributes(s2.baseCell);
            switch (s2.action) {
              case Gt.resultAction.AddCell:
                "right" === e3 ? r()(s2.baseCell).after("<td" + l2 + ">" + pt.blank + "</td>") : r()(s2.baseCell).before("<td" + l2 + ">" + pt.blank + "</td>");
                break;
              case Gt.resultAction.SumSpanCount:
                if ("right" === e3) {
                  var c2 = parseInt(s2.baseCell.colSpan, 10);
                  c2++, s2.baseCell.setAttribute("colSpan", c2);
                } else r()(s2.baseCell).before("<td" + l2 + ">" + pt.blank + "</td>");
            }
          }
        } }, { key: "recoverAttributes", value: function(t3) {
          var e3 = "";
          if (!t3) return e3;
          for (var o3 = t3.attributes || [], n2 = 0; n2 < o3.length; n2++) "id" !== o3[n2].name.toLowerCase() && o3[n2].specified && (e3 += " " + o3[n2].name + "='" + o3[n2].value + "'");
          return e3;
        } }, { key: "deleteRow", value: function(t3) {
          for (var e3 = pt.ancestor(t3.commonAncestor(), pt.isCell), o3 = r()(e3).closest("tr"), n2 = o3.children("td, th").index(r()(e3)), i2 = o3[0].rowIndex, a2 = new Gt(e3, Gt.where.Row, Gt.requestAction.Delete, r()(o3).closest("table")[0]).getActionList(), s2 = 0; s2 < a2.length; s2++) if (a2[s2]) {
            var l2 = a2[s2].baseCell, c2 = a2[s2].virtualTable, u2 = l2.rowSpan && l2.rowSpan > 1, d2 = u2 ? parseInt(l2.rowSpan, 10) : 0;
            switch (a2[s2].action) {
              case Gt.resultAction.Ignore:
                continue;
              case Gt.resultAction.AddCell:
                var f2 = o3.next("tr")[0];
                if (!f2) continue;
                var h2 = o3[0].cells[n2];
                u2 && (d2 > 2 ? (d2--, f2.insertBefore(h2, f2.cells[n2]), f2.cells[n2].setAttribute("rowSpan", d2), f2.cells[n2].innerHTML = "") : 2 === d2 && (f2.insertBefore(h2, f2.cells[n2]), f2.cells[n2].removeAttribute("rowSpan"), f2.cells[n2].innerHTML = ""));
                continue;
              case Gt.resultAction.SubtractSpanCount:
                u2 && (d2 > 2 ? (d2--, l2.setAttribute("rowSpan", d2), c2.rowIndex !== i2 && l2.cellIndex === n2 && (l2.innerHTML = "")) : 2 === d2 && (l2.removeAttribute("rowSpan"), c2.rowIndex !== i2 && l2.cellIndex === n2 && (l2.innerHTML = "")));
                continue;
              case Gt.resultAction.RemoveCell:
                continue;
            }
          }
          o3.remove();
        } }, { key: "deleteCol", value: function(t3) {
          for (var e3 = pt.ancestor(t3.commonAncestor(), pt.isCell), o3 = r()(e3).closest("tr"), n2 = o3.children("td, th").index(r()(e3)), i2 = new Gt(e3, Gt.where.Column, Gt.requestAction.Delete, r()(o3).closest("table")[0]).getActionList(), a2 = 0; a2 < i2.length; a2++) if (i2[a2]) switch (i2[a2].action) {
            case Gt.resultAction.Ignore:
              continue;
            case Gt.resultAction.SubtractSpanCount:
              var s2 = i2[a2].baseCell;
              if (s2.colSpan && s2.colSpan > 1) {
                var l2 = s2.colSpan ? parseInt(s2.colSpan, 10) : 0;
                l2 > 2 ? (l2--, s2.setAttribute("colSpan", l2), s2.cellIndex === n2 && (s2.innerHTML = "")) : 2 === l2 && (s2.removeAttribute("colSpan"), s2.cellIndex === n2 && (s2.innerHTML = ""));
              }
              continue;
            case Gt.resultAction.RemoveCell:
              pt.remove(i2[a2].baseCell, true);
              continue;
          }
        } }, { key: "createTable", value: function(t3, e3, o3) {
          for (var n2, i2 = [], a2 = 0; a2 < t3; a2++) i2.push("<td>" + pt.blank + "</td>");
          n2 = i2.join("");
          for (var s2, l2 = [], c2 = 0; c2 < e3; c2++) l2.push("<tr>" + n2 + "</tr>");
          s2 = l2.join("");
          var u2 = r()("<table>" + s2 + "</table>");
          return o3 && o3.tableClassName && u2.addClass(o3.tableClassName), u2[0];
        } }, { key: "deleteTable", value: function(t3) {
          var e3 = pt.ancestor(t3.commonAncestor(), pt.isCell);
          r()(e3).closest("table").remove();
        } }], e2 && Vt(t2.prototype, e2), o2 && Vt(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Yt(t2) {
        return Yt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Yt(t2);
      }
      function Xt(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Qt(n2.key), n2);
        }
      }
      function Qt(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Yt(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Yt(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Yt(e2) ? e2 : e2 + "";
      }
      var Jt = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, te = /^(\+?\d{1,3}[\s-]?)?(\d{1,4})[\s-]?(\d{1,4})[\s-]?(\d{1,4})$/, ee = /^([A-Za-z][A-Za-z0-9+-.]*\:|#|\/)/, oe = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.$note = e3.layoutInfo.note, this.$editor = e3.layoutInfo.editor, this.$editable = e3.layoutInfo.editable, this.options = e3.options, this.lang = this.options.langInfo, this.editable = this.$editable[0], this.lastRange = null, this.snapshot = null, this.style = new Ht(), this.table = new Zt(), this.typing = new Kt(e3), this.bullet = new zt(), this.history = new At(e3), this.context.memo("help.escape", this.lang.help.escape), this.context.memo("help.undo", this.lang.help.undo), this.context.memo("help.redo", this.lang.help.redo), this.context.memo("help.tab", this.lang.help.tab), this.context.memo("help.untab", this.lang.help.untab), this.context.memo("help.insertParagraph", this.lang.help.insertParagraph), this.context.memo("help.insertOrderedList", this.lang.help.insertOrderedList), this.context.memo("help.insertUnorderedList", this.lang.help.insertUnorderedList), this.context.memo("help.indent", this.lang.help.indent), this.context.memo("help.outdent", this.lang.help.outdent), this.context.memo("help.formatPara", this.lang.help.formatPara), this.context.memo("help.insertHorizontalRule", this.lang.help.insertHorizontalRule), this.context.memo("help.fontName", this.lang.help.fontName);
          for (var n2 = ["bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyLeft", "justifyCenter", "justifyRight", "justifyFull", "formatBlock", "removeFormat", "backColor"], i2 = 0, a2 = n2.length; i2 < a2; i2++) this[n2[i2]] = /* @__PURE__ */ function(t4) {
            return function(e4) {
              o3.beforeCommand(), document.execCommand(t4, false, e4), o3.afterCommand(true);
            };
          }(n2[i2]), this.context.memo("help." + n2[i2], this.lang.help[n2[i2]]);
          this.fontName = this.wrapCommand(function(t4) {
            return o3.fontStyling("font-family", m.validFontName(t4));
          }), this.fontSize = this.wrapCommand(function(t4) {
            var e4 = o3.currentStyle()["font-size-unit"];
            return o3.fontStyling("font-size", t4 + e4);
          }), this.fontSizeUnit = this.wrapCommand(function(t4) {
            var e4 = o3.currentStyle()["font-size"];
            return o3.fontStyling("font-size", e4 + t4);
          });
          for (var s2 = 1; s2 <= 6; s2++) this["formatH" + s2] = /* @__PURE__ */ function(t4) {
            return function() {
              o3.formatBlock("H" + t4);
            };
          }(s2), this.context.memo("help.formatH" + s2, this.lang.help["formatH" + s2]);
          this.insertParagraph = this.wrapCommand(function() {
            o3.typing.insertParagraph(o3.editable);
          }), this.insertOrderedList = this.wrapCommand(function() {
            o3.bullet.insertOrderedList(o3.editable);
          }), this.insertUnorderedList = this.wrapCommand(function() {
            o3.bullet.insertUnorderedList(o3.editable);
          }), this.indent = this.wrapCommand(function() {
            o3.bullet.indent(o3.editable);
          }), this.outdent = this.wrapCommand(function() {
            o3.bullet.outdent(o3.editable);
          }), this.insertNode = this.wrapCommand(function(t4) {
            o3.isLimited(r()(t4).text().length) || (o3.getLastRange().insertNode(t4), o3.setLastRange(Et.createFromNodeAfter(t4).select()));
          }), this.insertText = this.wrapCommand(function(t4) {
            if (!o3.isLimited(t4.length)) {
              var e4 = o3.getLastRange().insertNode(pt.createText(t4));
              o3.setLastRange(Et.create(e4, pt.nodeLength(e4)).select());
            }
          }), this.pasteHTML = this.wrapCommand(function(t4) {
            if (!o3.isLimited(t4.length)) {
              t4 = o3.context.invoke("codeview.purify", t4);
              var e4 = o3.getLastRange().pasteHTML(t4);
              o3.setLastRange(Et.createFromNodeAfter(C.last(e4)).select());
            }
          }), this.formatBlock = this.wrapCommand(function(t4, e4) {
            var n3 = o3.options.callbacks.onApplyCustomStyle;
            n3 ? n3.call(o3, e4, o3.context, o3.onFormatBlock) : o3.onFormatBlock(t4, e4);
          }), this.insertHorizontalRule = this.wrapCommand(function() {
            var t4 = o3.getLastRange().insertNode(pt.create("HR"));
            t4.nextSibling && o3.setLastRange(Et.create(t4.nextSibling, 0).normalize().select());
          }), this.lineHeight = this.wrapCommand(function(t4) {
            o3.style.stylePara(o3.getLastRange(), { lineHeight: t4 });
          }), this.createLink = this.wrapCommand(function(t4) {
            var e4 = [], n3 = t4.url, i3 = t4.text, a3 = t4.isNewWindow, s3 = o3.options.linkAddNoReferrer, l2 = o3.options.linkAddNoOpener, c2 = t4.range || o3.getLastRange(), u2 = i3.length - c2.toString().length;
            if (!(u2 > 0 && o3.isLimited(u2))) {
              var d2 = c2.toString() !== i3;
              "string" == typeof n3 && (n3 = n3.trim()), n3 = o3.options.onCreateLink ? o3.options.onCreateLink(n3) : o3.checkLinkUrl(n3);
              var f2 = [];
              if (d2) {
                var h2 = (c2 = c2.deleteContents()).insertNode(r()("<A></A>").text(i3)[0]);
                f2.push(h2);
              } else f2 = o3.style.styleNodes(c2, { nodeName: "A", expandClosestSibling: true, onlyPartialContains: true });
              r().each(f2, function(t5, o4) {
                r()(o4).attr("href", n3), a3 ? (r()(o4).attr("target", "_blank"), s3 && e4.push("noreferrer"), l2 && e4.push("noopener"), e4.length && r()(o4).attr("rel", e4.join(" "))) : r()(o4).removeAttr("target");
              }), o3.setLastRange(o3.createRangeFromList(f2).select());
            }
          }), this.color = this.wrapCommand(function(t4) {
            var e4 = t4.foreColor, o4 = t4.backColor;
            e4 && document.execCommand("foreColor", false, e4), o4 && document.execCommand("backColor", false, o4);
          }), this.foreColor = this.wrapCommand(function(t4) {
            document.execCommand("foreColor", false, t4);
          }), this.insertTable = this.wrapCommand(function(t4) {
            var e4 = t4.split("x");
            o3.getLastRange().deleteContents().insertNode(o3.table.createTable(e4[0], e4[1], o3.options));
          }), this.removeMedia = this.wrapCommand(function() {
            var t4 = r()(o3.restoreTarget()).parent();
            t4.closest("figure").length ? t4.closest("figure").remove() : t4 = r()(o3.restoreTarget()).detach(), o3.setLastRange(Et.createFromSelection(t4).select()), o3.context.triggerEvent("media.delete", t4, o3.$editable);
          }), this.floatMe = this.wrapCommand(function(t4) {
            var e4 = r()(o3.restoreTarget());
            e4.toggleClass("note-float-left", "left" === t4), e4.toggleClass("note-float-right", "right" === t4), e4.css("float", "none" === t4 ? "" : t4);
          }), this.resize = this.wrapCommand(function(t4) {
            var e4 = r()(o3.restoreTarget());
            0 === (t4 = parseFloat(t4)) ? e4.css("width", "") : e4.css({ width: 100 * t4 + "%", height: "" });
          });
        }, e2 = [{ key: "initialize", value: function() {
          var t3 = this;
          this.$editable.on("keydown", function(e3) {
            if (e3.keyCode === Nt.code.ENTER && t3.context.triggerEvent("enter", e3), t3.context.triggerEvent("keydown", e3), t3.snapshot = t3.history.makeSnapshot(), t3.hasKeyShortCut = false, e3.isDefaultPrevented() || (t3.options.shortcuts ? t3.hasKeyShortCut = t3.handleKeyMap(e3) : t3.preventDefaultEditableShortCuts(e3)), t3.isLimited(1, e3)) {
              var o3 = t3.getLastRange();
              if (o3.eo - o3.so == 0) return false;
            }
            t3.setLastRange(), t3.options.recordEveryKeystroke && false === t3.hasKeyShortCut && t3.history.recordUndo();
          }).on("keyup", function(e3) {
            t3.setLastRange(), t3.context.triggerEvent("keyup", e3);
          }).on("focus", function(e3) {
            t3.setLastRange(), t3.context.triggerEvent("focus", e3);
          }).on("blur", function(e3) {
            t3.context.triggerEvent("blur", e3);
          }).on("mousedown", function(e3) {
            t3.context.triggerEvent("mousedown", e3);
          }).on("mouseup", function(e3) {
            t3.setLastRange(), t3.history.recordUndo(), t3.context.triggerEvent("mouseup", e3);
          }).on("scroll", function(e3) {
            t3.context.triggerEvent("scroll", e3);
          }).on("paste", function(e3) {
            t3.setLastRange(), t3.context.triggerEvent("paste", e3);
          }).on("copy", function(e3) {
            t3.context.triggerEvent("copy", e3);
          }).on("input", function() {
            t3.isLimited(0) && t3.snapshot && t3.history.applySnapshot(t3.snapshot);
          }), this.$editable.attr("spellcheck", this.options.spellCheck), this.$editable.attr("autocorrect", this.options.spellCheck), this.options.disableGrammar && this.$editable.attr("data-gramm", false), this.$editable.html(pt.html(this.$note) || pt.emptyPara), this.$editable.on(m.inputEventName, g.debounce(function() {
            t3.context.triggerEvent("change", t3.$editable.html(), t3.$editable);
          }, 10)), this.$editable.on("focusin", function(e3) {
            t3.context.triggerEvent("focusin", e3);
          }).on("focusout", function(e3) {
            t3.context.triggerEvent("focusout", e3);
          }), this.options.airMode ? this.options.overrideContextMenu && this.$editor.on("contextmenu", function(e3) {
            return t3.context.triggerEvent("contextmenu", e3), false;
          }) : (this.options.width && this.$editor.outerWidth(this.options.width), this.options.height && this.$editable.outerHeight(this.options.height), this.options.maxHeight && this.$editable.css("max-height", this.options.maxHeight), this.options.minHeight && this.$editable.css("min-height", this.options.minHeight)), this.history.recordUndo(), this.setLastRange();
        } }, { key: "destroy", value: function() {
          this.$editable.off();
        } }, { key: "handleKeyMap", value: function(t3) {
          var e3 = this.options.keyMap[m.isMac ? "mac" : "pc"], o3 = [];
          t3.metaKey && o3.push("CMD"), t3.ctrlKey && !t3.altKey && o3.push("CTRL"), t3.shiftKey && o3.push("SHIFT");
          var n2 = Nt.nameFromCode[t3.keyCode];
          n2 && o3.push(n2);
          var i2 = e3[o3.join("+")];
          if ("TAB" !== n2 || this.options.tabDisable) if (i2) {
            if (false !== this.context.invoke(i2)) return t3.preventDefault(), true;
          } else Nt.isEdit(t3.keyCode) && (Nt.isRemove(t3.keyCode) && this.context.invoke("removed"), this.afterCommand());
          else this.afterCommand();
          return false;
        } }, { key: "preventDefaultEditableShortCuts", value: function(t3) {
          (t3.ctrlKey || t3.metaKey) && C.contains([66, 73, 85], t3.keyCode) && t3.preventDefault();
        } }, { key: "isLimited", value: function(t3, e3) {
          return t3 = t3 || 0, (void 0 === e3 || !(Nt.isMove(e3.keyCode) || Nt.isNavigation(e3.keyCode) || e3.ctrlKey || e3.metaKey || C.contains([Nt.code.BACKSPACE, Nt.code.DELETE], e3.keyCode))) && this.options.maxTextLength > 0 && this.$editable.text().length + t3 > this.options.maxTextLength;
        } }, { key: "checkLinkUrl", value: function(t3) {
          return Jt.test(t3) ? "mailto://" + t3 : te.test(t3) ? "tel://" + t3 : ee.test(t3) ? t3 : "http://" + t3;
        } }, { key: "createRange", value: function() {
          return this.focus(), this.setLastRange(), this.getLastRange();
        } }, { key: "createRangeFromList", value: function(t3) {
          var e3 = Et.createFromNodeBefore(C.head(t3)).getStartPoint(), o3 = Et.createFromNodeAfter(C.last(t3)).getEndPoint();
          return Et.create(e3.node, e3.offset, o3.node, o3.offset);
        } }, { key: "setLastRange", value: function(t3) {
          t3 ? this.lastRange = t3 : (this.lastRange = Et.create(this.editable), 0 === r()(this.lastRange.sc).closest(".note-editable").length && (this.lastRange = Et.createFromBodyElement(this.editable)));
        } }, { key: "getLastRange", value: function() {
          return this.lastRange || this.setLastRange(), this.lastRange;
        } }, { key: "saveRange", value: function(t3) {
          t3 && this.getLastRange().collapse().select();
        } }, { key: "restoreRange", value: function() {
          this.lastRange && (this.lastRange.select(), this.focus());
        } }, { key: "saveTarget", value: function(t3) {
          this.$editable.data("target", t3);
        } }, { key: "clearTarget", value: function() {
          this.$editable.removeData("target");
        } }, { key: "restoreTarget", value: function() {
          return this.$editable.data("target");
        } }, { key: "currentStyle", value: function() {
          var t3 = Et.create();
          return t3 && (t3 = t3.normalize()), t3 ? this.style.current(t3) : this.style.fromNode(this.$editable);
        } }, { key: "styleFromNode", value: function(t3) {
          return this.style.fromNode(t3);
        } }, { key: "undo", value: function() {
          this.context.triggerEvent("before.command", this.$editable.html()), this.history.undo(), this.context.triggerEvent("change", this.$editable.html(), this.$editable);
        } }, { key: "commit", value: function() {
          this.context.triggerEvent("before.command", this.$editable.html()), this.history.commit(), this.context.triggerEvent("change", this.$editable.html(), this.$editable);
        } }, { key: "redo", value: function() {
          this.context.triggerEvent("before.command", this.$editable.html()), this.history.redo(), this.context.triggerEvent("change", this.$editable.html(), this.$editable);
        } }, { key: "beforeCommand", value: function() {
          this.context.triggerEvent("before.command", this.$editable.html()), document.execCommand("styleWithCSS", false, this.options.styleWithCSS), this.focus();
        } }, { key: "afterCommand", value: function(t3) {
          this.normalizeContent(), this.history.recordUndo(), t3 || this.context.triggerEvent("change", this.$editable.html(), this.$editable);
        } }, { key: "tab", value: function() {
          var t3 = this.getLastRange();
          if (t3.isCollapsed() && t3.isOnCell()) this.table.tab(t3);
          else {
            if (0 === this.options.tabSize) return false;
            this.isLimited(this.options.tabSize) || (this.beforeCommand(), this.typing.insertTab(t3, this.options.tabSize), this.afterCommand());
          }
        } }, { key: "untab", value: function() {
          var t3 = this.getLastRange();
          if (t3.isCollapsed() && t3.isOnCell()) this.table.tab(t3, true);
          else if (0 === this.options.tabSize) return false;
        } }, { key: "wrapCommand", value: function(t3) {
          return function() {
            this.beforeCommand(), t3.apply(this, arguments), this.afterCommand();
          };
        } }, { key: "removed", value: function(t3, e3, o3) {
          (t3 = Et.create()).isCollapsed() && t3.isOnCell() && (o3 = (e3 = t3.ec).tagName) && 1 === e3.childElementCount && "BR" === e3.childNodes[0].tagName && ("P" === o3 ? e3.remove() : ["TH", "TD"].indexOf(o3) >= 0 && e3.firstChild.remove());
        } }, { key: "insertImage", value: function(t3, e3) {
          var o3, n2 = this;
          return (o3 = t3, r().Deferred(function(t4) {
            var e4 = r()("<img>");
            e4.one("load", function() {
              e4.off("error abort"), t4.resolve(e4);
            }).one("error abort", function() {
              e4.off("load").detach(), t4.reject(e4);
            }).css({ display: "none" }).appendTo(document.body).attr("src", o3);
          }).promise()).then(function(t4) {
            n2.beforeCommand(), "function" == typeof e3 ? e3(t4) : ("string" == typeof e3 && t4.attr("data-filename", e3), t4.css("width", Math.min(n2.$editable.width(), t4.width()))), t4.show(), n2.getLastRange().insertNode(t4[0]), n2.setLastRange(Et.createFromNodeAfter(t4[0]).select()), n2.afterCommand();
          }).fail(function(t4) {
            n2.context.triggerEvent("image.upload.error", t4);
          });
        } }, { key: "insertImagesAsDataURL", value: function(t3) {
          var e3 = this;
          r().each(t3, function(t4, o3) {
            var n2 = o3.name;
            e3.options.maximumImageFileSize && e3.options.maximumImageFileSize < o3.size ? e3.context.triggerEvent("image.upload.error", e3.lang.image.maximumFileSizeError) : function(t5) {
              return r().Deferred(function(e4) {
                r().extend(new FileReader(), { onload: function(t6) {
                  var o4 = t6.target.result;
                  e4.resolve(o4);
                }, onerror: function(t6) {
                  e4.reject(t6);
                } }).readAsDataURL(t5);
              }).promise();
            }(o3).then(function(t5) {
              return e3.insertImage(t5, n2);
            }).fail(function() {
              e3.context.triggerEvent("image.upload.error");
            });
          });
        } }, { key: "insertImagesOrCallback", value: function(t3) {
          this.options.callbacks.onImageUpload ? this.context.triggerEvent("image.upload", t3) : this.insertImagesAsDataURL(t3);
        } }, { key: "getSelectedText", value: function() {
          var t3 = this.getLastRange();
          return t3.isOnAnchor() && (t3 = Et.createFromNode(pt.ancestor(t3.sc, pt.isAnchor))), t3.toString();
        } }, { key: "onFormatBlock", value: function(t3, e3) {
          if (document.execCommand("FormatBlock", false, m.isMSIE ? "<" + t3 + ">" : t3), e3 && e3.length && (e3[0].tagName.toUpperCase() !== t3.toUpperCase() && (e3 = e3.find(t3)), e3 && e3.length)) {
            var o3 = this.createRange(), n2 = r()([o3.sc, o3.ec]).closest(t3);
            n2.removeClass();
            var i2 = e3[0].className || "";
            i2 && n2.addClass(i2);
          }
        } }, { key: "formatPara", value: function() {
          this.formatBlock("P");
        } }, { key: "fontStyling", value: function(t3, e3) {
          var o3 = this.getLastRange();
          if ("" !== o3) {
            var n2 = this.style.styleNodes(o3);
            if (this.$editor.find(".note-status-output").html(""), r()(n2).css(t3, e3), o3.isCollapsed()) {
              var i2 = C.head(n2);
              i2 && !pt.nodeLength(i2) && (i2.innerHTML = pt.ZERO_WIDTH_NBSP_CHAR, Et.createFromNode(i2.firstChild).select(), this.setLastRange(), this.$editable.data("bogus", i2));
            } else o3.select();
          } else {
            var a2 = r().now();
            this.$editor.find(".note-status-output").html('<div id="note-status-output-' + a2 + '" class="alert alert-info">' + this.lang.output.noSelection + "</div>"), setTimeout(function() {
              r()("#note-status-output-" + a2).remove();
            }, 5e3);
          }
        } }, { key: "unlink", value: function() {
          var t3 = this.getLastRange();
          if (t3.isOnAnchor()) {
            var e3 = pt.ancestor(t3.sc, pt.isAnchor);
            (t3 = Et.createFromNode(e3)).select(), this.setLastRange(), this.beforeCommand(), document.execCommand("unlink"), this.afterCommand();
          }
        } }, { key: "getLinkInfo", value: function() {
          this.hasFocus() || this.focus();
          var t3 = this.getLastRange().expand(pt.isAnchor), e3 = r()(C.head(t3.nodes(pt.isAnchor))), o3 = { range: t3, text: t3.toString(), url: e3.length ? e3.attr("href") : "" };
          return e3.length && (o3.isNewWindow = "_blank" === e3.attr("target")), o3;
        } }, { key: "addRow", value: function(t3) {
          var e3 = this.getLastRange(this.$editable);
          e3.isCollapsed() && e3.isOnCell() && (this.beforeCommand(), this.table.addRow(e3, t3), this.afterCommand());
        } }, { key: "addCol", value: function(t3) {
          var e3 = this.getLastRange(this.$editable);
          e3.isCollapsed() && e3.isOnCell() && (this.beforeCommand(), this.table.addCol(e3, t3), this.afterCommand());
        } }, { key: "deleteRow", value: function() {
          var t3 = this.getLastRange(this.$editable);
          t3.isCollapsed() && t3.isOnCell() && (this.beforeCommand(), this.table.deleteRow(t3), this.afterCommand());
        } }, { key: "deleteCol", value: function() {
          var t3 = this.getLastRange(this.$editable);
          t3.isCollapsed() && t3.isOnCell() && (this.beforeCommand(), this.table.deleteCol(t3), this.afterCommand());
        } }, { key: "deleteTable", value: function() {
          var t3 = this.getLastRange(this.$editable);
          t3.isCollapsed() && t3.isOnCell() && (this.beforeCommand(), this.table.deleteTable(t3), this.afterCommand());
        } }, { key: "resizeTo", value: function(t3, e3, o3) {
          var n2;
          if (o3) {
            var i2 = t3.y / t3.x, r2 = e3.data("ratio");
            n2 = { width: r2 > i2 ? t3.x : t3.y / r2, height: r2 > i2 ? t3.x * r2 : t3.y };
          } else n2 = { width: t3.x, height: t3.y };
          e3.css(n2);
        } }, { key: "hasFocus", value: function() {
          return this.$editable.is(":focus");
        } }, { key: "focus", value: function() {
          this.hasFocus() || this.$editable.trigger("focus");
        } }, { key: "isEmpty", value: function() {
          return pt.isEmpty(this.$editable[0]) || pt.emptyPara === this.$editable.html();
        } }, { key: "empty", value: function() {
          this.context.invoke("code", pt.emptyPara);
        } }, { key: "normalizeContent", value: function() {
          this.$editable[0].normalize();
        } }], e2 && Xt(t2.prototype, e2), o2 && Xt(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function ne(t2) {
        return ne = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, ne(t2);
      }
      function ie(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, re(n2.key), n2);
        }
      }
      function re(t2) {
        var e2 = function(t3, e3) {
          if ("object" != ne(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != ne(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == ne(e2) ? e2 : e2 + "";
      }
      var ae = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.options = e3.options, this.$editable = e3.layoutInfo.editable;
        }, (e2 = [{ key: "initialize", value: function() {
          this.$editable.on("paste", this.pasteByEvent.bind(this));
        } }, { key: "pasteByEvent", value: function(t3) {
          var e3 = this;
          if (!this.context.isDisabled()) {
            var o3 = t3.originalEvent.clipboardData;
            if (o3 && o3.items && o3.items.length) {
              var n2 = o3.files, i2 = o3.getData("Text");
              n2.length > 0 && this.options.allowClipboardImagePasting && (this.context.invoke("editor.insertImagesOrCallback", n2), t3.preventDefault()), i2.length > 0 && this.context.invoke("editor.isLimited", i2.length) && t3.preventDefault();
            } else if (window.clipboardData) {
              var r2 = window.clipboardData.getData("text");
              this.context.invoke("editor.isLimited", r2.length) && t3.preventDefault();
            }
            setTimeout(function() {
              e3.context.invoke("editor.afterCommand");
            }, 10);
          }
        } }]) && ie(t2.prototype, e2), o2 && ie(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function se(t2) {
        return se = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, se(t2);
      }
      function le(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, ce(n2.key), n2);
        }
      }
      function ce(t2) {
        var e2 = function(t3, e3) {
          if ("object" != se(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != se(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == se(e2) ? e2 : e2 + "";
      }
      var ue = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.$eventListener = r()(document), this.$editor = e3.layoutInfo.editor, this.$editable = e3.layoutInfo.editable, this.options = e3.options, this.lang = this.options.langInfo, this.documentEventHandlers = {}, this.$dropzone = r()(['<div class="note-dropzone">', '<div class="note-dropzone-message"></div>', "</div>"].join("")).prependTo(this.$editor);
        }, e2 = [{ key: "initialize", value: function() {
          this.options.disableDragAndDrop ? (this.documentEventHandlers.onDrop = function(t3) {
            t3.preventDefault();
          }, this.$eventListener = this.$dropzone, this.$eventListener.on("drop", this.documentEventHandlers.onDrop)) : this.attachDragAndDropEvent();
        } }, { key: "attachDragAndDropEvent", value: function() {
          var t3 = this, e3 = r()(), o3 = this.$dropzone.find(".note-dropzone-message");
          this.documentEventHandlers.onDragenter = function(n2) {
            var i2 = t3.context.invoke("codeview.isActivated"), r2 = t3.$editor.width() > 0 && t3.$editor.height() > 0;
            i2 || e3.length || !r2 || (t3.$editor.addClass("dragover"), t3.$dropzone.width(t3.$editor.width()), t3.$dropzone.height(t3.$editor.height()), o3.text(t3.lang.image.dragImageHere)), e3 = e3.add(n2.target);
          }, this.documentEventHandlers.onDragleave = function(o4) {
            (e3 = e3.not(o4.target)).length && "BODY" !== o4.target.nodeName || (e3 = r()(), t3.$editor.removeClass("dragover"));
          }, this.documentEventHandlers.onDrop = function() {
            e3 = r()(), t3.$editor.removeClass("dragover");
          }, this.$eventListener.on("dragenter", this.documentEventHandlers.onDragenter).on("dragleave", this.documentEventHandlers.onDragleave).on("drop", this.documentEventHandlers.onDrop), this.$dropzone.on("dragenter", function() {
            t3.$dropzone.addClass("hover"), o3.text(t3.lang.image.dropImage);
          }).on("dragleave", function() {
            t3.$dropzone.removeClass("hover"), o3.text(t3.lang.image.dragImageHere);
          }), this.$dropzone.on("drop", function(e4) {
            var o4 = e4.originalEvent.dataTransfer;
            e4.preventDefault(), o4 && o4.files && o4.files.length ? (t3.$editable.trigger("focus"), t3.context.invoke("editor.insertImagesOrCallback", o4.files)) : r().each(o4.types, function(e5, n2) {
              if (!(n2.toLowerCase().indexOf("_moz_") > -1)) {
                var i2 = o4.getData(n2);
                n2.toLowerCase().indexOf("text") > -1 ? t3.context.invoke("editor.pasteHTML", i2) : r()(i2).each(function(e6, o5) {
                  t3.context.invoke("editor.insertNode", o5);
                });
              }
            });
          }).on("dragover", false);
        } }, { key: "destroy", value: function() {
          var t3 = this;
          Object.keys(this.documentEventHandlers).forEach(function(e3) {
            t3.$eventListener.off(e3.slice(2).toLowerCase(), t3.documentEventHandlers[e3]);
          }), this.documentEventHandlers = {};
        } }], e2 && le(t2.prototype, e2), o2 && le(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function de(t2) {
        return de = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, de(t2);
      }
      function fe(t2, e2) {
        var o2 = "undefined" != typeof Symbol && t2[Symbol.iterator] || t2["@@iterator"];
        if (!o2) {
          if (Array.isArray(t2) || (o2 = function(t3, e3) {
            if (t3) {
              if ("string" == typeof t3) return he(t3, e3);
              var o3 = {}.toString.call(t3).slice(8, -1);
              return "Object" === o3 && t3.constructor && (o3 = t3.constructor.name), "Map" === o3 || "Set" === o3 ? Array.from(t3) : "Arguments" === o3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o3) ? he(t3, e3) : void 0;
            }
          }(t2)) || e2 && t2 && "number" == typeof t2.length) {
            o2 && (t2 = o2);
            var n2 = 0, i2 = function() {
            };
            return { s: i2, n: function() {
              return n2 >= t2.length ? { done: true } : { done: false, value: t2[n2++] };
            }, e: function(t3) {
              throw t3;
            }, f: i2 };
          }
          throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
        }
        var r2, a2 = true, s2 = false;
        return { s: function() {
          o2 = o2.call(t2);
        }, n: function() {
          var t3 = o2.next();
          return a2 = t3.done, t3;
        }, e: function(t3) {
          s2 = true, r2 = t3;
        }, f: function() {
          try {
            a2 || null == o2.return || o2.return();
          } finally {
            if (s2) throw r2;
          }
        } };
      }
      function he(t2, e2) {
        (null == e2 || e2 > t2.length) && (e2 = t2.length);
        for (var o2 = 0, n2 = Array(e2); o2 < e2; o2++) n2[o2] = t2[o2];
        return n2;
      }
      function pe(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, me(n2.key), n2);
        }
      }
      function me(t2) {
        var e2 = function(t3, e3) {
          if ("object" != de(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != de(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == de(e2) ? e2 : e2 + "";
      }
      var ve = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.$editor = e3.layoutInfo.editor, this.$editable = e3.layoutInfo.editable, this.$codable = e3.layoutInfo.codable, this.options = e3.options, this.CodeMirrorConstructor = window.CodeMirror, this.options.codemirror.CodeMirrorConstructor && (this.CodeMirrorConstructor = this.options.codemirror.CodeMirrorConstructor);
        }, e2 = [{ key: "sync", value: function(t3) {
          var e3 = this.isActivated(), o3 = this.CodeMirrorConstructor;
          e3 && (t3 ? o3 ? this.$codable.data("cmEditor").getDoc().setValue(t3) : this.$codable.val(t3) : o3 && this.$codable.data("cmEditor").save());
        } }, { key: "initialize", value: function() {
          var t3 = this;
          this.$codable.on("keyup", function(e3) {
            e3.keyCode === Nt.code.ESCAPE && t3.deactivate();
          });
        } }, { key: "isActivated", value: function() {
          return this.$editor.hasClass("codeview");
        } }, { key: "toggle", value: function() {
          this.isActivated() ? this.deactivate() : this.activate(), this.context.triggerEvent("codeview.toggled");
        } }, { key: "purify", value: function(t3) {
          if (this.options.codeviewFilter && (t3 = t3.replace(this.options.codeviewFilterRegex, ""), this.options.codeviewIframeFilter)) {
            var e3 = this.options.codeviewIframeWhitelistSrc.concat(this.options.codeviewIframeWhitelistSrcBase);
            t3 = t3.replace(/(<iframe.*?>.*?(?:<\/iframe>)?)/gi, function(t4) {
              if (/<.+src(?==?('|"|\s)?)[\s\S]+src(?=('|"|\s)?)[^>]*?>/i.test(t4)) return "";
              var o3, n2 = fe(e3);
              try {
                for (n2.s(); !(o3 = n2.n()).done; ) {
                  var i2 = o3.value;
                  if (new RegExp('src="(https?:)?//' + i2.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&") + '/(.+)"').test(t4)) return t4;
                }
              } catch (t5) {
                n2.e(t5);
              } finally {
                n2.f();
              }
              return "";
            });
          }
          return t3;
        } }, { key: "activate", value: function() {
          var t3 = this, e3 = this.CodeMirrorConstructor;
          if (this.$codable.val(pt.html(this.$editable, this.options.prettifyHtml)), this.$codable.height(this.$editable.height()), this.context.invoke("toolbar.updateCodeview", true), this.context.invoke("airPopover.updateCodeview", true), this.$editor.addClass("codeview"), this.$codable.trigger("focus"), e3) {
            var o3 = e3.fromTextArea(this.$codable[0], this.options.codemirror);
            if (this.options.codemirror.tern) {
              var n2 = new e3.TernServer(this.options.codemirror.tern);
              o3.ternServer = n2, o3.on("cursorActivity", function(t4) {
                n2.updateArgHints(t4);
              });
            }
            o3.on("blur", function(e4) {
              t3.context.triggerEvent("blur.codeview", o3.getValue(), e4);
            }), o3.on("change", function() {
              t3.context.triggerEvent("change.codeview", o3.getValue(), o3);
            }), o3.setSize(null, this.$editable.outerHeight()), this.$codable.data("cmEditor", o3);
          } else this.$codable.on("blur", function(e4) {
            t3.context.triggerEvent("blur.codeview", t3.$codable.val(), e4);
          }), this.$codable.on("input", function() {
            t3.context.triggerEvent("change.codeview", t3.$codable.val(), t3.$codable);
          });
        } }, { key: "deactivate", value: function() {
          if (this.CodeMirrorConstructor) {
            var t3 = this.$codable.data("cmEditor");
            this.$codable.val(t3.getValue()), t3.toTextArea();
          }
          var e3 = this.purify(pt.value(this.$codable, this.options.prettifyHtml) || pt.emptyPara), o3 = this.$editable.html() !== e3;
          this.$editable.html(e3), this.$editable.height(this.options.height ? this.$codable.height() : "auto"), this.$editor.removeClass("codeview"), o3 && this.context.triggerEvent("change", this.$editable.html(), this.$editable), this.$editable.trigger("focus"), this.context.invoke("toolbar.updateCodeview", false), this.context.invoke("airPopover.updateCodeview", false);
        } }, { key: "destroy", value: function() {
          this.isActivated() && this.deactivate();
        } }], e2 && pe(t2.prototype, e2), o2 && pe(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function ge(t2) {
        return ge = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, ge(t2);
      }
      function be(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, ye(n2.key), n2);
        }
      }
      function ye(t2) {
        var e2 = function(t3, e3) {
          if ("object" != ge(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != ge(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == ge(e2) ? e2 : e2 + "";
      }
      var ke = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.$document = r()(document), this.$statusbar = e3.layoutInfo.statusbar, this.$editable = e3.layoutInfo.editable, this.$codable = e3.layoutInfo.codable, this.options = e3.options;
        }, (e2 = [{ key: "initialize", value: function() {
          var t3 = this;
          this.options.airMode || this.options.disableResizeEditor ? this.destroy() : this.$statusbar.on("mousedown touchstart", function(e3) {
            e3.preventDefault(), e3.stopPropagation();
            var o3 = t3.$editable.offset().top - t3.$document.scrollTop(), n2 = t3.$codable.offset().top - t3.$document.scrollTop(), i2 = function(e4) {
              var i3 = "mousemove" == e4.type ? e4 : e4.originalEvent.touches[0], r2 = i3.clientY - (o3 + 24), a2 = i3.clientY - (n2 + 24);
              r2 = t3.options.minheight > 0 ? Math.max(r2, t3.options.minheight) : r2, r2 = t3.options.maxHeight > 0 ? Math.min(r2, t3.options.maxHeight) : r2, a2 = t3.options.minheight > 0 ? Math.max(a2, t3.options.minheight) : a2, a2 = t3.options.maxHeight > 0 ? Math.min(a2, t3.options.maxHeight) : a2, t3.$editable.height(r2), t3.$codable.height(a2);
            };
            t3.$document.on("mousemove touchmove", i2).one("mouseup touchend", function() {
              t3.$document.off("mousemove touchmove", i2);
            });
          });
        } }, { key: "destroy", value: function() {
          this.$statusbar.off(), this.$statusbar.addClass("locked");
        } }]) && be(t2.prototype, e2), o2 && be(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function we(t2) {
        return we = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, we(t2);
      }
      function Ce(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Se(n2.key), n2);
        }
      }
      function Se(t2) {
        var e2 = function(t3, e3) {
          if ("object" != we(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != we(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == we(e2) ? e2 : e2 + "";
      }
      var xe = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.$editor = e3.layoutInfo.editor, this.$toolbar = e3.layoutInfo.toolbar, this.$editable = e3.layoutInfo.editable, this.$codable = e3.layoutInfo.codable, this.$window = r()(window), this.$scrollbar = r()("html, body"), this.scrollbarClassName = "note-fullscreen-body", this.onResize = function() {
            o3.resizeTo({ h: o3.$window.height() - o3.$toolbar.outerHeight() });
          };
        }, (e2 = [{ key: "resizeTo", value: function(t3) {
          this.$editable.css("height", t3.h), this.$codable.css("height", t3.h), this.$codable.data("cmeditor") && this.$codable.data("cmeditor").setsize(null, t3.h);
        } }, { key: "toggle", value: function() {
          this.$editor.toggleClass("fullscreen");
          var t3 = this.isFullscreen();
          this.$scrollbar.toggleClass(this.scrollbarClassName, t3), t3 ? (this.$editable.data("orgHeight", this.$editable.css("height")), this.$editable.data("orgMaxHeight", this.$editable.css("maxHeight")), this.$editable.css("maxHeight", ""), this.$window.on("resize", this.onResize).trigger("resize")) : (this.$window.off("resize", this.onResize), this.resizeTo({ h: this.$editable.data("orgHeight") }), this.$editable.css("maxHeight", this.$editable.css("orgMaxHeight"))), this.context.invoke("toolbar.updateFullscreen", t3);
        } }, { key: "isFullscreen", value: function() {
          return this.$editor.hasClass("fullscreen");
        } }, { key: "destroy", value: function() {
          this.$scrollbar.removeClass(this.scrollbarClassName);
        } }]) && Ce(t2.prototype, e2), o2 && Ce(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Te(t2) {
        return Te = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Te(t2);
      }
      function Ee(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Pe(n2.key), n2);
        }
      }
      function Pe(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Te(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Te(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Te(e2) ? e2 : e2 + "";
      }
      var Ne = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.$document = r()(document), this.$editingArea = e3.layoutInfo.editingArea, this.options = e3.options, this.lang = this.options.langInfo, this.events = { "summernote.mousedown": function(t4, e4) {
            o3.update(e4.target, e4) && e4.preventDefault();
          }, "summernote.keyup summernote.scroll summernote.change summernote.dialog.shown": function() {
            o3.update();
          }, "summernote.disable summernote.blur": function() {
            o3.hide();
          }, "summernote.codeview.toggled": function() {
            o3.update();
          } };
        }, e2 = [{ key: "initialize", value: function() {
          var t3 = this;
          this.$handle = r()(['<div class="note-handle">', '<div class="note-control-selection">', '<div class="note-control-selection-bg"></div>', '<div class="note-control-holder note-control-nw"></div>', '<div class="note-control-holder note-control-ne"></div>', '<div class="note-control-holder note-control-sw"></div>', '<div class="', this.options.disableResizeImage ? "note-control-holder" : "note-control-sizing", ' note-control-se"></div>', this.options.disableResizeImage ? "" : '<div class="note-control-selection-info"></div>', "</div>", "</div>"].join("")).prependTo(this.$editingArea), this.$handle.on("mousedown", function(e3) {
            if (pt.isControlSizing(e3.target)) {
              e3.preventDefault(), e3.stopPropagation();
              var o3 = t3.$handle.find(".note-control-selection").data("target"), n2 = o3.offset(), i2 = t3.$document.scrollTop(), r2 = function(e4) {
                t3.context.invoke("editor.resizeTo", { x: e4.clientX - n2.left, y: e4.clientY - (n2.top - i2) }, o3, !e4.shiftKey), t3.update(o3[0], e4);
              };
              t3.$document.on("mousemove", r2).one("mouseup", function(e4) {
                e4.preventDefault(), t3.$document.off("mousemove", r2), t3.context.invoke("editor.afterCommand");
              }), o3.data("ratio") || o3.data("ratio", o3.height() / o3.width());
            }
          }), this.$handle.on("wheel", function(e3) {
            e3.preventDefault(), t3.update();
          });
        } }, { key: "destroy", value: function() {
          this.$handle.remove();
        } }, { key: "update", value: function(t3, e3) {
          if (this.context.isDisabled()) return false;
          var o3 = pt.isImg(t3), n2 = this.$handle.find(".note-control-selection");
          if (this.context.invoke("imagePopover.update", t3, e3), o3) {
            var i2 = r()(t3), a2 = this.$editingArea[0].getBoundingClientRect(), s2 = t3.getBoundingClientRect();
            n2.css({ display: "block", left: s2.left - a2.left, top: s2.top - a2.top, width: s2.width, height: s2.height }).data("target", i2);
            var l2 = new Image();
            l2.src = i2.attr("src");
            var c2 = s2.width + "x" + s2.height + " (" + this.lang.image.original + ": " + l2.width + "x" + l2.height + ")";
            n2.find(".note-control-selection-info").text(c2), this.context.invoke("editor.saveTarget", t3);
          } else this.hide();
          return o3;
        } }, { key: "hide", value: function() {
          this.context.invoke("editor.clearTarget"), this.$handle.children().hide();
        } }], e2 && Ee(t2.prototype, e2), o2 && Ee(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function $e(t2) {
        return $e = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, $e(t2);
      }
      function Ie(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Re(n2.key), n2);
        }
      }
      function Re(t2) {
        var e2 = function(t3, e3) {
          if ("object" != $e(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != $e(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == $e(e2) ? e2 : e2 + "";
      }
      var Ae = /^([A-Za-z][A-Za-z0-9+-.]*\:[\/]{2}|tel:|mailto:[A-Z0-9._%+-]+@|xmpp:[A-Z0-9._%+-]+@)?(www\.)?(.+)$/i, Le = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.options = e3.options, this.$editable = e3.layoutInfo.editable, this.events = { "summernote.keyup": function(t4, e4) {
            e4.isDefaultPrevented() || o3.handleKeyup(e4);
          }, "summernote.keydown": function(t4, e4) {
            o3.handleKeydown(e4);
          } };
        }, (e2 = [{ key: "initialize", value: function() {
          this.lastWordRange = null;
        } }, { key: "destroy", value: function() {
          this.lastWordRange = null;
        } }, { key: "replace", value: function() {
          if (this.lastWordRange) {
            var t3 = this.lastWordRange.toString(), e3 = t3.match(Ae);
            if (e3 && (e3[1] || e3[2])) {
              var o3 = e3[1] ? t3 : "http://" + t3, n2 = this.options.showDomainOnlyForAutolink ? t3.replace(/^(?:https?:\/\/)?(?:tel?:?)?(?:mailto?:?)?(?:xmpp?:?)?(?:www\.)?/i, "").split("/")[0] : t3, i2 = r()("<a></a>").html(n2).attr("href", o3)[0];
              this.context.options.linkTargetBlank && r()(i2).attr("target", "_blank"), this.lastWordRange.insertNode(i2), this.lastWordRange = null, this.context.invoke("editor.focus"), this.context.triggerEvent("change", this.$editable.html(), this.$editable);
            }
          }
        } }, { key: "handleKeydown", value: function(t3) {
          if (C.contains([Nt.code.ENTER, Nt.code.SPACE], t3.keyCode)) {
            var e3 = this.context.invoke("editor.createRange").getWordRange();
            this.lastWordRange = e3;
          }
        } }, { key: "handleKeyup", value: function(t3) {
          (Nt.code.SPACE === t3.keyCode || Nt.code.ENTER === t3.keyCode && !t3.shiftKey) && this.replace();
        } }]) && Ie(t2.prototype, e2), o2 && Ie(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Fe(t2) {
        return Fe = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Fe(t2);
      }
      function De(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, He(n2.key), n2);
        }
      }
      function He(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Fe(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Fe(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Fe(e2) ? e2 : e2 + "";
      }
      var je = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.$note = e3.layoutInfo.note, this.events = { "summernote.change": function() {
            o3.$note.val(e3.invoke("code"));
          } };
        }, (e2 = [{ key: "shouldInitialize", value: function() {
          return pt.isTextarea(this.$note[0]);
        } }]) && De(t2.prototype, e2), o2 && De(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Be(t2) {
        return Be = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Be(t2);
      }
      function Oe(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, ze(n2.key), n2);
        }
      }
      function ze(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Be(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Be(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Be(e2) ? e2 : e2 + "";
      }
      var Me = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.options = e3.options.replace || {}, this.keys = [Nt.code.ENTER, Nt.code.SPACE, Nt.code.PERIOD, Nt.code.COMMA, Nt.code.SEMICOLON, Nt.code.SLASH], this.previousKeydownCode = null, this.events = { "summernote.keyup": function(t4, e4) {
            e4.isDefaultPrevented() || o3.handleKeyup(e4);
          }, "summernote.keydown": function(t4, e4) {
            o3.handleKeydown(e4);
          } };
        }, (e2 = [{ key: "shouldInitialize", value: function() {
          return !!this.options.match;
        } }, { key: "initialize", value: function() {
          this.lastWord = null;
        } }, { key: "destroy", value: function() {
          this.lastWord = null;
        } }, { key: "replace", value: function() {
          if (this.lastWord) {
            var t3 = this, e3 = this.lastWord.toString();
            this.options.match(e3, function(e4) {
              if (e4) {
                var o3 = "";
                if ("string" == typeof e4 ? o3 = pt.createText(e4) : e4 instanceof jQuery ? o3 = e4[0] : e4 instanceof Node && (o3 = e4), !o3) return;
                t3.lastWord.insertNode(o3), t3.lastWord = null, t3.context.invoke("editor.focus");
              }
            });
          }
        } }, { key: "handleKeydown", value: function(t3) {
          if (this.previousKeydownCode && C.contains(this.keys, this.previousKeydownCode)) this.previousKeydownCode = t3.keyCode;
          else {
            if (C.contains(this.keys, t3.keyCode)) {
              var e3 = this.context.invoke("editor.createRange").getWordRange();
              this.lastWord = e3;
            }
            this.previousKeydownCode = t3.keyCode;
          }
        } }, { key: "handleKeyup", value: function(t3) {
          C.contains(this.keys, t3.keyCode) && this.replace();
        } }]) && Oe(t2.prototype, e2), o2 && Oe(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Ue(t2) {
        return Ue = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Ue(t2);
      }
      function We(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Ke(n2.key), n2);
        }
      }
      function Ke(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Ue(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Ue(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Ue(e2) ? e2 : e2 + "";
      }
      var qe = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.$editingArea = e3.layoutInfo.editingArea, this.options = e3.options, true === this.options.inheritPlaceholder && (this.options.placeholder = this.context.$note.attr("placeholder") || this.options.placeholder), this.events = { "summernote.init summernote.change": function() {
            o3.update();
          }, "summernote.codeview.toggled": function() {
            o3.update();
          } };
        }, (e2 = [{ key: "shouldInitialize", value: function() {
          return !!this.options.placeholder;
        } }, { key: "initialize", value: function() {
          var t3 = this;
          this.$placeholder = r()('<div class="note-placeholder"></div>'), this.$placeholder.on("click", function() {
            t3.context.invoke("focus");
          }).html(this.options.placeholder).prependTo(this.$editingArea), this.update();
        } }, { key: "destroy", value: function() {
          this.$placeholder.remove();
        } }, { key: "update", value: function() {
          var t3 = !this.context.invoke("codeview.isActivated") && this.context.invoke("editor.isEmpty");
          this.$placeholder.toggle(t3);
        } }]) && We(t2.prototype, e2), o2 && We(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Ve(t2) {
        return Ve = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Ve(t2);
      }
      function _e(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Ge(n2.key), n2);
        }
      }
      function Ge(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Ve(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Ve(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Ve(e2) ? e2 : e2 + "";
      }
      var Ze = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.ui = r().summernote.ui, this.context = e3, this.$toolbar = e3.layoutInfo.toolbar, this.options = e3.options, this.lang = this.options.langInfo, this.invertedKeyMap = g.invertObject(this.options.keyMap[m.isMac ? "mac" : "pc"]);
        }, e2 = [{ key: "representShortcut", value: function(t3) {
          var e3 = this.invertedKeyMap[t3];
          return this.options.shortcuts && e3 ? (m.isMac && (e3 = e3.replace("CMD", "⌘").replace("SHIFT", "⇧")), " (" + (e3 = e3.replace("BACKSLASH", "\\").replace("SLASH", "/").replace("LEFTBRACKET", "[").replace("RIGHTBRACKET", "]")) + ")") : "";
        } }, { key: "button", value: function(t3) {
          return !this.options.tooltip && t3.tooltip && delete t3.tooltip, t3.container = this.options.container, this.ui.button(t3);
        } }, { key: "initialize", value: function() {
          this.addToolbarButtons(), this.addImagePopoverButtons(), this.addLinkPopoverButtons(), this.addTablePopoverButtons(), this.fontInstalledMap = {};
        } }, { key: "destroy", value: function() {
          delete this.fontInstalledMap;
        } }, { key: "isFontInstalled", value: function(t3) {
          return Object.prototype.hasOwnProperty.call(this.fontInstalledMap, t3) || (this.fontInstalledMap[t3] = m.isFontInstalled(t3) || C.contains(this.options.fontNamesIgnoreCheck, t3)), this.fontInstalledMap[t3];
        } }, { key: "isFontDeservedToAdd", value: function(t3) {
          return "" !== (t3 = t3.toLowerCase()) && this.isFontInstalled(t3) && -1 === m.genericFontFamilies.indexOf(t3);
        } }, { key: "colorPalette", value: function(t3, e3, o3, n2) {
          var i2 = this;
          return this.ui.buttonGroup({ className: "note-color " + t3, children: [this.button({ className: "note-current-color-button", contents: this.ui.icon(this.options.icons.font + " note-recent-color"), tooltip: e3, click: function(t4) {
            var e4 = r()(t4.currentTarget);
            o3 && n2 ? i2.context.invoke("editor.color", { backColor: e4.attr("data-backColor"), foreColor: e4.attr("data-foreColor") }) : o3 ? i2.context.invoke("editor.color", { backColor: e4.attr("data-backColor") }) : n2 && i2.context.invoke("editor.color", { foreColor: e4.attr("data-foreColor") });
          }, callback: function(t4) {
            var e4 = t4.find(".note-recent-color");
            o3 && (e4.css("background-color", i2.options.colorButton.backColor), t4.attr("data-backColor", i2.options.colorButton.backColor)), n2 ? (e4.css("color", i2.options.colorButton.foreColor), t4.attr("data-foreColor", i2.options.colorButton.foreColor)) : e4.css("color", "transparent");
          } }), this.button({ className: "dropdown-toggle", contents: this.ui.dropdownButtonContents("", this.options), tooltip: this.lang.color.more, data: { toggle: "dropdown" } }), this.ui.dropdown({ items: (o3 ? ['<div class="note-palette">', '<div class="note-palette-title">' + this.lang.color.background + "</div>", "<div>", '<button type="button" class="note-color-reset btn btn-light btn-default" data-event="backColor" data-value="transparent">', this.lang.color.transparent, "</button>", "</div>", '<div class="note-holder" data-event="backColor"><!-- back colors --></div>', "<div>", '<button type="button" class="note-color-select btn btn-light btn-default" data-event="openPalette" data-value="backColorPicker-' + this.options.id + '">', this.lang.color.cpSelect, "</button>", '<input type="color" id="backColorPicker-' + this.options.id + '" class="note-btn note-color-select-btn" value="' + this.options.colorButton.backColor + '" data-event="backColorPalette-' + this.options.id + '">', "</div>", '<div class="note-holder-custom" id="backColorPalette-' + this.options.id + '" data-event="backColor"></div>', "</div>"].join("") : "") + (n2 ? ['<div class="note-palette">', '<div class="note-palette-title">' + this.lang.color.foreground + "</div>", "<div>", '<button type="button" class="note-color-reset btn btn-light btn-default" data-event="removeFormat" data-value="foreColor">', this.lang.color.resetToDefault, "</button>", "</div>", '<div class="note-holder" data-event="foreColor"><!-- fore colors --></div>', "<div>", '<button type="button" class="note-color-select btn btn-light btn-default" data-event="openPalette" data-value="foreColorPicker-' + this.options.id + '">', this.lang.color.cpSelect, "</button>", '<input type="color" id="foreColorPicker-' + this.options.id + '" class="note-btn note-color-select-btn" value="' + this.options.colorButton.foreColor + '" data-event="foreColorPalette-' + this.options.id + '">', "</div>", '<div class="note-holder-custom" id="foreColorPalette-' + this.options.id + '" data-event="foreColor"></div>', "</div>"].join("") : ""), callback: function(t4) {
            t4.find(".note-holder").each(function(t5, e5) {
              var o4 = r()(e5);
              o4.append(i2.ui.palette({ colors: i2.options.colors, colorsName: i2.options.colorsName, eventName: o4.data("event"), container: i2.options.container, tooltip: i2.options.tooltip }).render());
            });
            var e4 = [["#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF", "#FFFFFF"]];
            t4.find(".note-holder-custom").each(function(t5, o4) {
              var n3 = r()(o4);
              n3.append(i2.ui.palette({ colors: e4, colorsName: e4, eventName: n3.data("event"), container: i2.options.container, tooltip: i2.options.tooltip }).render());
            }), t4.find("input[type=color]").each(function(e5, o4) {
              r()(o4).on("change", function() {
                var e6 = t4.find("#" + r()(this).data("event")).find(".note-color-btn").first(), o5 = this.value.toUpperCase();
                e6.css("background-color", o5).attr("aria-label", o5).attr("data-value", o5).attr("data-original-title", o5), e6.trigger("click");
              });
            });
          }, click: function(e4) {
            e4.stopPropagation();
            var o4 = r()("." + t3).find(".note-dropdown-menu"), n3 = r()(e4.target), a2 = n3.data("event"), s2 = n3.attr("data-value");
            if ("openPalette" === a2) {
              var l2 = o4.find("#" + s2), c2 = r()(o4.find("#" + l2.data("event")).find(".note-color-row")[0]), u2 = c2.find(".note-color-btn").last().detach(), d2 = l2.val();
              u2.css("background-color", d2).attr("aria-label", d2).attr("data-value", d2).attr("data-original-title", d2), c2.prepend(u2), l2.trigger("click");
            } else {
              if (C.contains(["backColor", "foreColor"], a2)) {
                var f2 = "backColor" === a2 ? "background-color" : "color", h2 = n3.closest(".note-color").find(".note-recent-color"), p2 = n3.closest(".note-color").find(".note-current-color-button");
                h2.css(f2, s2), p2.attr("data-" + a2, s2);
              }
              i2.context.invoke("editor." + a2, s2);
            }
          } })] }).render();
        } }, { key: "addToolbarButtons", value: function() {
          var t3 = this;
          this.context.memo("button.style", function() {
            return t3.ui.buttonGroup([t3.button({ className: "dropdown-toggle", contents: t3.ui.dropdownButtonContents(t3.ui.icon(t3.options.icons.magic), t3.options), tooltip: t3.lang.style.style, data: { toggle: "dropdown" } }), t3.ui.dropdown({ className: "dropdown-style", items: t3.options.styleTags, title: t3.lang.style.style, template: function(e4) {
              "string" == typeof e4 && (e4 = { tag: e4, title: Object.prototype.hasOwnProperty.call(t3.lang.style, e4) ? t3.lang.style[e4] : e4 });
              var o4 = e4.tag, n3 = e4.title;
              return "<" + o4 + (e4.style ? ' style="' + e4.style + '" ' : "") + (e4.className ? ' class="' + e4.className + '"' : "") + ">" + n3 + "</" + o4 + ">";
            }, click: t3.context.createInvokeHandler("editor.formatBlock") })]).render();
          });
          for (var e3 = function() {
            var e4 = t3.options.styleTags[o3];
            t3.context.memo("button.style." + e4, function() {
              return t3.button({ className: "note-btn-style-" + e4, contents: '<div data-value="' + e4 + '">' + e4.toUpperCase() + "</div>", tooltip: t3.lang.style[e4], click: t3.context.createInvokeHandler("editor.formatBlock") }).render();
            });
          }, o3 = 0, n2 = this.options.styleTags.length; o3 < n2; o3++) e3();
          this.context.memo("button.bold", function() {
            return t3.button({ className: "note-btn-bold", contents: t3.ui.icon(t3.options.icons.bold), tooltip: t3.lang.font.bold + t3.representShortcut("bold"), click: t3.context.createInvokeHandlerAndUpdateState("editor.bold") }).render();
          }), this.context.memo("button.italic", function() {
            return t3.button({ className: "note-btn-italic", contents: t3.ui.icon(t3.options.icons.italic), tooltip: t3.lang.font.italic + t3.representShortcut("italic"), click: t3.context.createInvokeHandlerAndUpdateState("editor.italic") }).render();
          }), this.context.memo("button.underline", function() {
            return t3.button({ className: "note-btn-underline", contents: t3.ui.icon(t3.options.icons.underline), tooltip: t3.lang.font.underline + t3.representShortcut("underline"), click: t3.context.createInvokeHandlerAndUpdateState("editor.underline") }).render();
          }), this.context.memo("button.clear", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.eraser), tooltip: t3.lang.font.clear + t3.representShortcut("removeFormat"), click: t3.context.createInvokeHandler("editor.removeFormat") }).render();
          }), this.context.memo("button.strikethrough", function() {
            return t3.button({ className: "note-btn-strikethrough", contents: t3.ui.icon(t3.options.icons.strikethrough), tooltip: t3.lang.font.strikethrough + t3.representShortcut("strikethrough"), click: t3.context.createInvokeHandlerAndUpdateState("editor.strikethrough") }).render();
          }), this.context.memo("button.superscript", function() {
            return t3.button({ className: "note-btn-superscript", contents: t3.ui.icon(t3.options.icons.superscript), tooltip: t3.lang.font.superscript, click: t3.context.createInvokeHandlerAndUpdateState("editor.superscript") }).render();
          }), this.context.memo("button.subscript", function() {
            return t3.button({ className: "note-btn-subscript", contents: t3.ui.icon(t3.options.icons.subscript), tooltip: t3.lang.font.subscript, click: t3.context.createInvokeHandlerAndUpdateState("editor.subscript") }).render();
          }), this.context.memo("button.fontname", function() {
            var e4 = t3.context.invoke("editor.currentStyle");
            return t3.options.addDefaultFonts && r().each(e4["font-family"].split(","), function(e5, o4) {
              o4 = o4.trim().replace(/['"]+/g, ""), t3.isFontDeservedToAdd(o4) && -1 === t3.options.fontNames.indexOf(o4) && t3.options.fontNames.push(o4);
            }), t3.ui.buttonGroup([t3.button({ className: "dropdown-toggle", contents: t3.ui.dropdownButtonContents('<span class="note-current-fontname"></span>', t3.options), tooltip: t3.lang.font.name, data: { toggle: "dropdown" } }), t3.ui.dropdownCheck({ className: "dropdown-fontname", checkClassName: t3.options.icons.menuCheck, items: t3.options.fontNames.filter(t3.isFontInstalled.bind(t3)), title: t3.lang.font.name, template: function(t4) {
              return '<span style="font-family: ' + m.validFontName(t4) + '">' + t4 + "</span>";
            }, click: t3.context.createInvokeHandlerAndUpdateState("editor.fontName") })]).render();
          }), this.context.memo("button.fontsize", function() {
            return t3.ui.buttonGroup([t3.button({ className: "dropdown-toggle", contents: t3.ui.dropdownButtonContents('<span class="note-current-fontsize"></span>', t3.options), tooltip: t3.lang.font.size, data: { toggle: "dropdown" } }), t3.ui.dropdownCheck({ className: "dropdown-fontsize", checkClassName: t3.options.icons.menuCheck, items: t3.options.fontSizes, title: t3.lang.font.size, click: t3.context.createInvokeHandlerAndUpdateState("editor.fontSize") })]).render();
          }), this.context.memo("button.fontsizeunit", function() {
            return t3.ui.buttonGroup([t3.button({ className: "dropdown-toggle", contents: t3.ui.dropdownButtonContents('<span class="note-current-fontsizeunit"></span>', t3.options), tooltip: t3.lang.font.sizeunit, data: { toggle: "dropdown" } }), t3.ui.dropdownCheck({ className: "dropdown-fontsizeunit", checkClassName: t3.options.icons.menuCheck, items: t3.options.fontSizeUnits, title: t3.lang.font.sizeunit, click: t3.context.createInvokeHandlerAndUpdateState("editor.fontSizeUnit") })]).render();
          }), this.context.memo("button.color", function() {
            return t3.colorPalette("note-color-all", t3.lang.color.recent, true, true);
          }), this.context.memo("button.forecolor", function() {
            return t3.colorPalette("note-color-fore", t3.lang.color.foreground, false, true);
          }), this.context.memo("button.backcolor", function() {
            return t3.colorPalette("note-color-back", t3.lang.color.background, true, false);
          }), this.context.memo("button.ul", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.unorderedlist), tooltip: t3.lang.lists.unordered + t3.representShortcut("insertUnorderedList"), click: t3.context.createInvokeHandler("editor.insertUnorderedList") }).render();
          }), this.context.memo("button.ol", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.orderedlist), tooltip: t3.lang.lists.ordered + t3.representShortcut("insertOrderedList"), click: t3.context.createInvokeHandler("editor.insertOrderedList") }).render();
          });
          var i2 = this.button({ contents: this.ui.icon(this.options.icons.alignLeft), tooltip: this.lang.paragraph.left + this.representShortcut("justifyLeft"), click: this.context.createInvokeHandler("editor.justifyLeft") }), a2 = this.button({ contents: this.ui.icon(this.options.icons.alignCenter), tooltip: this.lang.paragraph.center + this.representShortcut("justifyCenter"), click: this.context.createInvokeHandler("editor.justifyCenter") }), s2 = this.button({ contents: this.ui.icon(this.options.icons.alignRight), tooltip: this.lang.paragraph.right + this.representShortcut("justifyRight"), click: this.context.createInvokeHandler("editor.justifyRight") }), l2 = this.button({ contents: this.ui.icon(this.options.icons.alignJustify), tooltip: this.lang.paragraph.justify + this.representShortcut("justifyFull"), click: this.context.createInvokeHandler("editor.justifyFull") }), c2 = this.button({ contents: this.ui.icon(this.options.icons.outdent), tooltip: this.lang.paragraph.outdent + this.representShortcut("outdent"), click: this.context.createInvokeHandler("editor.outdent") }), u2 = this.button({ contents: this.ui.icon(this.options.icons.indent), tooltip: this.lang.paragraph.indent + this.representShortcut("indent"), click: this.context.createInvokeHandler("editor.indent") });
          this.context.memo("button.justifyLeft", g.invoke(i2, "render")), this.context.memo("button.justifyCenter", g.invoke(a2, "render")), this.context.memo("button.justifyRight", g.invoke(s2, "render")), this.context.memo("button.justifyFull", g.invoke(l2, "render")), this.context.memo("button.outdent", g.invoke(c2, "render")), this.context.memo("button.indent", g.invoke(u2, "render")), this.context.memo("button.paragraph", function() {
            return t3.ui.buttonGroup([t3.button({ className: "dropdown-toggle", contents: t3.ui.dropdownButtonContents(t3.ui.icon(t3.options.icons.alignLeft), t3.options), tooltip: t3.lang.paragraph.paragraph, data: { toggle: "dropdown" } }), t3.ui.dropdown([t3.ui.buttonGroup({ className: "note-align", children: [i2, a2, s2, l2] }), t3.ui.buttonGroup({ className: "note-list", children: [c2, u2] })])]).render();
          }), this.context.memo("button.height", function() {
            return t3.ui.buttonGroup([t3.button({ className: "dropdown-toggle", contents: t3.ui.dropdownButtonContents(t3.ui.icon(t3.options.icons.textHeight), t3.options), tooltip: t3.lang.font.height, data: { toggle: "dropdown" } }), t3.ui.dropdownCheck({ items: t3.options.lineHeights, checkClassName: t3.options.icons.menuCheck, className: "dropdown-line-height", title: t3.lang.font.height, click: t3.context.createInvokeHandler("editor.lineHeight") })]).render();
          }), this.context.memo("button.table", function() {
            return t3.ui.buttonGroup([t3.button({ className: "dropdown-toggle", contents: t3.ui.dropdownButtonContents(t3.ui.icon(t3.options.icons.table), t3.options), tooltip: t3.lang.table.table, data: { toggle: "dropdown" } }), t3.ui.dropdown({ title: t3.lang.table.table, className: "note-table", items: ['<div class="note-dimension-picker">', '<div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"></div>', '<div class="note-dimension-picker-highlighted"></div>', '<div class="note-dimension-picker-unhighlighted"></div>', "</div>", '<div class="note-dimension-display">1 x 1</div>'].join("") })], { callback: function(e4) {
              e4.find(".note-dimension-picker-mousecatcher").css({ width: t3.options.insertTableMaxSize.col + "em", height: t3.options.insertTableMaxSize.row + "em" }).on("mousedown", t3.context.createInvokeHandler("editor.insertTable")).on("mousemove", t3.tableMoveHandler.bind(t3));
            } }).render();
          }), this.context.memo("button.link", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.link), tooltip: t3.lang.link.link + t3.representShortcut("linkDialog.show"), click: t3.context.createInvokeHandler("linkDialog.show") }).render();
          }), this.context.memo("button.picture", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.picture), tooltip: t3.lang.image.image, click: t3.context.createInvokeHandler("imageDialog.show") }).render();
          }), this.context.memo("button.video", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.video), tooltip: t3.lang.video.video, click: t3.context.createInvokeHandler("videoDialog.show") }).render();
          }), this.context.memo("button.hr", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.minus), tooltip: t3.lang.hr.insert + t3.representShortcut("insertHorizontalRule"), click: t3.context.createInvokeHandler("editor.insertHorizontalRule") }).render();
          }), this.context.memo("button.fullscreen", function() {
            return t3.button({ className: "btn-fullscreen note-codeview-keep", contents: t3.ui.icon(t3.options.icons.arrowsAlt), tooltip: t3.lang.options.fullscreen, click: t3.context.createInvokeHandler("fullscreen.toggle") }).render();
          }), this.context.memo("button.codeview", function() {
            return t3.button({ className: "btn-codeview note-codeview-keep", contents: t3.ui.icon(t3.options.icons.code), tooltip: t3.lang.options.codeview, click: t3.context.createInvokeHandler("codeview.toggle") }).render();
          }), this.context.memo("button.redo", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.redo), tooltip: t3.lang.history.redo + t3.representShortcut("redo"), click: t3.context.createInvokeHandler("editor.redo") }).render();
          }), this.context.memo("button.undo", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.undo), tooltip: t3.lang.history.undo + t3.representShortcut("undo"), click: t3.context.createInvokeHandler("editor.undo") }).render();
          }), this.context.memo("button.help", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.question), tooltip: t3.lang.options.help, click: t3.context.createInvokeHandler("helpDialog.show") }).render();
          });
        } }, { key: "addImagePopoverButtons", value: function() {
          var t3 = this;
          this.context.memo("button.resizeFull", function() {
            return t3.button({ contents: '<span class="note-fontsize-10">100%</span>', tooltip: t3.lang.image.resizeFull, click: t3.context.createInvokeHandler("editor.resize", "1") }).render();
          }), this.context.memo("button.resizeHalf", function() {
            return t3.button({ contents: '<span class="note-fontsize-10">50%</span>', tooltip: t3.lang.image.resizeHalf, click: t3.context.createInvokeHandler("editor.resize", "0.5") }).render();
          }), this.context.memo("button.resizeQuarter", function() {
            return t3.button({ contents: '<span class="note-fontsize-10">25%</span>', tooltip: t3.lang.image.resizeQuarter, click: t3.context.createInvokeHandler("editor.resize", "0.25") }).render();
          }), this.context.memo("button.resizeNone", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.rollback), tooltip: t3.lang.image.resizeNone, click: t3.context.createInvokeHandler("editor.resize", "0") }).render();
          }), this.context.memo("button.floatLeft", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.floatLeft), tooltip: t3.lang.image.floatLeft, click: t3.context.createInvokeHandler("editor.floatMe", "left") }).render();
          }), this.context.memo("button.floatRight", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.floatRight), tooltip: t3.lang.image.floatRight, click: t3.context.createInvokeHandler("editor.floatMe", "right") }).render();
          }), this.context.memo("button.floatNone", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.rollback), tooltip: t3.lang.image.floatNone, click: t3.context.createInvokeHandler("editor.floatMe", "none") }).render();
          }), this.context.memo("button.removeMedia", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.trash), tooltip: t3.lang.image.remove, click: t3.context.createInvokeHandler("editor.removeMedia") }).render();
          });
        } }, { key: "addLinkPopoverButtons", value: function() {
          var t3 = this;
          this.context.memo("button.linkDialogShow", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.link), tooltip: t3.lang.link.edit, click: t3.context.createInvokeHandler("linkDialog.show") }).render();
          }), this.context.memo("button.unlink", function() {
            return t3.button({ contents: t3.ui.icon(t3.options.icons.unlink), tooltip: t3.lang.link.unlink, click: t3.context.createInvokeHandler("editor.unlink") }).render();
          });
        } }, { key: "addTablePopoverButtons", value: function() {
          var t3 = this;
          this.context.memo("button.addRowUp", function() {
            return t3.button({ className: "btn-md", contents: t3.ui.icon(t3.options.icons.rowAbove), tooltip: t3.lang.table.addRowAbove, click: t3.context.createInvokeHandler("editor.addRow", "top") }).render();
          }), this.context.memo("button.addRowDown", function() {
            return t3.button({ className: "btn-md", contents: t3.ui.icon(t3.options.icons.rowBelow), tooltip: t3.lang.table.addRowBelow, click: t3.context.createInvokeHandler("editor.addRow", "bottom") }).render();
          }), this.context.memo("button.addColLeft", function() {
            return t3.button({ className: "btn-md", contents: t3.ui.icon(t3.options.icons.colBefore), tooltip: t3.lang.table.addColLeft, click: t3.context.createInvokeHandler("editor.addCol", "left") }).render();
          }), this.context.memo("button.addColRight", function() {
            return t3.button({ className: "btn-md", contents: t3.ui.icon(t3.options.icons.colAfter), tooltip: t3.lang.table.addColRight, click: t3.context.createInvokeHandler("editor.addCol", "right") }).render();
          }), this.context.memo("button.deleteRow", function() {
            return t3.button({ className: "btn-md", contents: t3.ui.icon(t3.options.icons.rowRemove), tooltip: t3.lang.table.delRow, click: t3.context.createInvokeHandler("editor.deleteRow") }).render();
          }), this.context.memo("button.deleteCol", function() {
            return t3.button({ className: "btn-md", contents: t3.ui.icon(t3.options.icons.colRemove), tooltip: t3.lang.table.delCol, click: t3.context.createInvokeHandler("editor.deleteCol") }).render();
          }), this.context.memo("button.deleteTable", function() {
            return t3.button({ className: "btn-md", contents: t3.ui.icon(t3.options.icons.trash), tooltip: t3.lang.table.delTable, click: t3.context.createInvokeHandler("editor.deleteTable") }).render();
          });
        } }, { key: "build", value: function(t3, e3) {
          for (var o3 = 0, n2 = e3.length; o3 < n2; o3++) {
            for (var i2 = e3[o3], r2 = Array.isArray(i2) ? i2[0] : i2, a2 = Array.isArray(i2) ? 1 === i2.length ? [i2[0]] : i2[1] : [i2], s2 = this.ui.buttonGroup({ className: "note-" + r2 }).render(), l2 = 0, c2 = a2.length; l2 < c2; l2++) {
              var u2 = this.context.memo("button." + a2[l2]);
              u2 && s2.append("function" == typeof u2 ? u2(this.context) : u2);
            }
            s2.appendTo(t3);
          }
        } }, { key: "updateCurrentStyle", value: function(t3) {
          var e3 = t3 || this.$toolbar, o3 = this.context.invoke("editor.currentStyle");
          if (this.updateBtnStates(e3, { ".note-btn-bold": function() {
            return "bold" === o3["font-bold"];
          }, ".note-btn-italic": function() {
            return "italic" === o3["font-italic"];
          }, ".note-btn-underline": function() {
            return "underline" === o3["font-underline"];
          }, ".note-btn-subscript": function() {
            return "subscript" === o3["font-subscript"];
          }, ".note-btn-superscript": function() {
            return "superscript" === o3["font-superscript"];
          }, ".note-btn-strikethrough": function() {
            return "strikethrough" === o3["font-strikethrough"];
          } }), o3["font-family"]) {
            var n2 = o3["font-family"].split(",").map(function(t4) {
              return t4.replace(/[\'\"]/g, "").replace(/\s+$/, "").replace(/^\s+/, "");
            }), i2 = C.find(n2, this.isFontInstalled.bind(this));
            e3.find(".dropdown-fontname a").each(function(t4, e4) {
              var o4 = r()(e4), n3 = o4.data("value") + "" == i2 + "";
              o4.toggleClass("checked", n3);
            }), e3.find(".note-current-fontname").text(i2).css("font-family", i2);
          }
          if (o3["font-size"]) {
            var a2 = o3["font-size"];
            e3.find(".dropdown-fontsize a").each(function(t4, e4) {
              var o4 = r()(e4), n3 = o4.data("value") + "" == a2 + "";
              o4.toggleClass("checked", n3);
            }), e3.find(".note-current-fontsize").text(a2);
            var s2 = o3["font-size-unit"];
            e3.find(".dropdown-fontsizeunit a").each(function(t4, e4) {
              var o4 = r()(e4), n3 = o4.data("value") + "" == s2 + "";
              o4.toggleClass("checked", n3);
            }), e3.find(".note-current-fontsizeunit").text(s2);
          }
          if (o3["line-height"]) {
            var l2 = o3["line-height"];
            e3.find(".dropdown-line-height a").each(function(t4, e4) {
              var o4 = r()(e4), n3 = r()(e4).data("value") + "" == l2 + "";
              o4.toggleClass("checked", n3);
            }), e3.find(".note-current-line-height").text(l2);
          }
        } }, { key: "updateBtnStates", value: function(t3, e3) {
          var o3 = this;
          r().each(e3, function(e4, n2) {
            o3.ui.toggleBtnActive(t3.find(e4), n2());
          });
        } }, { key: "tableMoveHandler", value: function(t3) {
          var e3, o3 = r()(t3.target.parentNode), n2 = o3.next(), i2 = o3.find(".note-dimension-picker-mousecatcher"), a2 = o3.find(".note-dimension-picker-highlighted"), s2 = o3.find(".note-dimension-picker-unhighlighted");
          if (void 0 === t3.offsetX) {
            var l2 = r()(t3.target).offset();
            e3 = { x: t3.pageX - l2.left, y: t3.pageY - l2.top };
          } else e3 = { x: t3.offsetX, y: t3.offsetY };
          var c2 = Math.ceil(e3.x / 18) || 1, u2 = Math.ceil(e3.y / 18) || 1;
          a2.css({ width: c2 + "em", height: u2 + "em" }), i2.data("value", c2 + "x" + u2), c2 > 3 && c2 < this.options.insertTableMaxSize.col && s2.css({ width: c2 + 1 + "em" }), u2 > 3 && u2 < this.options.insertTableMaxSize.row && s2.css({ height: u2 + 1 + "em" }), n2.html(c2 + " x " + u2);
        } }], e2 && _e(t2.prototype, e2), o2 && _e(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Ye(t2) {
        return Ye = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Ye(t2);
      }
      function Xe(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Qe(n2.key), n2);
        }
      }
      function Qe(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Ye(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Ye(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Ye(e2) ? e2 : e2 + "";
      }
      var Je = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.$window = r()(window), this.$document = r()(document), this.ui = r().summernote.ui, this.$note = e3.layoutInfo.note, this.$editor = e3.layoutInfo.editor, this.$toolbar = e3.layoutInfo.toolbar, this.$editable = e3.layoutInfo.editable, this.$statusbar = e3.layoutInfo.statusbar, this.options = e3.options, this.isFollowing = false, this.followScroll = this.followScroll.bind(this);
        }, (e2 = [{ key: "shouldInitialize", value: function() {
          return !this.options.airMode;
        } }, { key: "initialize", value: function() {
          var t3 = this;
          this.options.toolbar = this.options.toolbar || [], this.options.toolbar.length ? this.context.invoke("buttons.build", this.$toolbar, this.options.toolbar) : this.$toolbar.hide(), this.options.toolbarContainer && this.$toolbar.appendTo(this.options.toolbarContainer), this.changeContainer(false), this.$note.on("summernote.keyup summernote.mouseup summernote.change", function() {
            t3.context.invoke("buttons.updateCurrentStyle");
          }), this.context.invoke("buttons.updateCurrentStyle"), this.options.followingToolbar && this.$window.on("scroll resize", this.followScroll);
        } }, { key: "destroy", value: function() {
          this.$toolbar.children().remove(), this.options.followingToolbar && this.$window.off("scroll resize", this.followScroll);
        } }, { key: "followScroll", value: function() {
          if (this.$editor.hasClass("fullscreen")) return false;
          var t3 = this.$editor.outerHeight(), e3 = this.$editor.width(), o3 = this.$toolbar.height(), n2 = this.$statusbar.height(), i2 = 0;
          this.options.otherStaticBar && (i2 = r()(this.options.otherStaticBar).outerHeight());
          var a2 = this.$document.scrollTop(), s2 = this.$editor.offset().top, l2 = s2 - i2, c2 = s2 + t3 - i2 - o3 - n2;
          !this.isFollowing && a2 > l2 && a2 < c2 - o3 ? (this.isFollowing = true, this.$editable.css({ marginTop: this.$toolbar.outerHeight() }), this.$toolbar.css({ position: "fixed", top: i2, width: e3, zIndex: 1e3 })) : this.isFollowing && (a2 < l2 || a2 > c2) && (this.isFollowing = false, this.$toolbar.css({ position: "relative", top: 0, width: "100%", zIndex: "auto" }), this.$editable.css({ marginTop: "" }));
        } }, { key: "changeContainer", value: function(t3) {
          t3 ? this.$toolbar.prependTo(this.$editor) : this.options.toolbarContainer && this.$toolbar.appendTo(this.options.toolbarContainer), this.options.followingToolbar && this.followScroll();
        } }, { key: "updateFullscreen", value: function(t3) {
          this.ui.toggleBtnActive(this.$toolbar.find(".btn-fullscreen"), t3), this.changeContainer(t3);
        } }, { key: "updateCodeview", value: function(t3) {
          this.ui.toggleBtnActive(this.$toolbar.find(".btn-codeview"), t3), t3 ? this.deactivate() : this.activate();
        } }, { key: "activate", value: function(t3) {
          var e3 = this.$toolbar.find("button");
          t3 || (e3 = e3.not(".note-codeview-keep")), this.ui.toggleBtn(e3, true);
        } }, { key: "deactivate", value: function(t3) {
          var e3 = this.$toolbar.find("button");
          t3 || (e3 = e3.not(".note-codeview-keep")), this.ui.toggleBtn(e3, false);
        } }]) && Xe(t2.prototype, e2), o2 && Xe(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function to(t2) {
        return to = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, to(t2);
      }
      function eo(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, oo(n2.key), n2);
        }
      }
      function oo(t2) {
        var e2 = function(t3, e3) {
          if ("object" != to(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != to(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == to(e2) ? e2 : e2 + "";
      }
      var no = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, io = /^(\+?\d{1,3}[\s-]?)?(\d{1,4})[\s-]?(\d{1,4})[\s-]?(\d{1,4})$/, ro = /^([A-Za-z][A-Za-z0-9+-.]*\:|#|\/)/, ao = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.ui = r().summernote.ui, this.$body = r()(document.body), this.$editor = e3.layoutInfo.editor, this.options = e3.options, this.lang = this.options.langInfo, e3.memo("help.linkDialog.show", this.options.langInfo.help["linkDialog.show"]);
        }, (e2 = [{ key: "initialize", value: function() {
          var t3 = this.options.dialogsInBody ? this.$body : this.options.container, e3 = ['<div class="form-group note-form-group">', '<label for="note-dialog-link-txt-'.concat(this.options.id, '" class="note-form-label">').concat(this.lang.link.textToDisplay, "</label>"), '<input id="note-dialog-link-txt-'.concat(this.options.id, '" class="note-link-text form-control note-form-control note-input" type="text"/>'), "</div>", '<div class="form-group note-form-group">', '<label for="note-dialog-link-url-'.concat(this.options.id, '" class="note-form-label">').concat(this.lang.link.url, "</label>"), '<input id="note-dialog-link-url-'.concat(this.options.id, '" class="note-link-url form-control note-form-control note-input" type="text" value="http://"/>'), "</div>", this.options.disableLinkTarget ? "" : r()("<div></div>").append(this.ui.checkbox({ className: "sn-checkbox-open-in-new-window", text: this.lang.link.openInNewWindow, checked: true }).render()).html()].join(""), o3 = '<input type="button" href="#" class="'.concat("btn btn-primary note-btn note-btn-primary note-link-btn", '" value="').concat(this.lang.link.insert, '" disabled>');
          this.$dialog = this.ui.dialog({ className: "link-dialog", title: this.lang.link.insert, fade: this.options.dialogsFade, body: e3, footer: o3 }).render().appendTo(t3);
        } }, { key: "destroy", value: function() {
          this.ui.hideDialog(this.$dialog), this.$dialog.remove();
        } }, { key: "bindEnterKey", value: function(t3, e3) {
          t3.on("keypress", function(t4) {
            t4.keyCode === Nt.code.ENTER && (t4.preventDefault(), e3.trigger("click"));
          });
        } }, { key: "checkLinkUrl", value: function(t3) {
          return no.test(t3) ? "mailto://" + t3 : io.test(t3) ? "tel://" + t3 : ro.test(t3) ? t3 : "http://" + t3;
        } }, { key: "onCheckLinkUrl", value: function(t3) {
          var e3 = this;
          t3.on("blur", function(t4) {
            t4.target.value = "" == t4.target.value ? "" : e3.checkLinkUrl(t4.target.value);
          });
        } }, { key: "toggleLinkBtn", value: function(t3, e3, o3) {
          this.ui.toggleBtn(t3, e3.val() && o3.val());
        } }, { key: "showLinkDialog", value: function(t3) {
          var e3 = this;
          return r().Deferred(function(o3) {
            var n2 = e3.$dialog.find(".note-link-text"), i2 = e3.$dialog.find(".note-link-url"), r2 = e3.$dialog.find(".note-link-btn"), a2 = e3.$dialog.find(".sn-checkbox-open-in-new-window input[type=checkbox]");
            e3.ui.onDialogShown(e3.$dialog, function() {
              e3.context.triggerEvent("dialog.shown"), !t3.url && g.isValidUrl(t3.text) && (t3.url = e3.checkLinkUrl(t3.text)), n2.on("input paste propertychange", function() {
                var o4 = n2.val(), a3 = document.createElement("div");
                a3.innerText = o4, o4 = a3.innerHTML, t3.text = o4, e3.toggleLinkBtn(r2, n2, i2);
              }).val(t3.text), i2.on("input paste propertychange", function() {
                t3.text || n2.val(i2.val()), e3.toggleLinkBtn(r2, n2, i2);
              }).val(t3.url), m.isSupportTouch || i2.trigger("focus"), e3.toggleLinkBtn(r2, n2, i2), e3.bindEnterKey(i2, r2), e3.bindEnterKey(n2, r2), e3.onCheckLinkUrl(i2);
              var s2 = void 0 !== t3.isNewWindow ? t3.isNewWindow : e3.context.options.linkTargetBlank;
              a2.prop("checked", s2), r2.one("click", function(r3) {
                r3.preventDefault(), o3.resolve({ range: t3.range, url: i2.val(), text: n2.val(), isNewWindow: a2.is(":checked") }), e3.ui.hideDialog(e3.$dialog);
              });
            }), e3.ui.onDialogHidden(e3.$dialog, function() {
              n2.off(), i2.off(), r2.off(), "pending" === o3.state() && o3.reject();
            }), e3.ui.showDialog(e3.$dialog);
          }).promise();
        } }, { key: "show", value: function() {
          var t3 = this, e3 = this.context.invoke("editor.getLinkInfo");
          this.context.invoke("editor.saveRange"), this.showLinkDialog(e3).then(function(e4) {
            t3.context.invoke("editor.restoreRange"), t3.context.invoke("editor.createLink", e4);
          }).fail(function() {
            t3.context.invoke("editor.restoreRange");
          });
        } }]) && eo(t2.prototype, e2), o2 && eo(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function so(t2) {
        return so = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, so(t2);
      }
      function lo(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, co(n2.key), n2);
        }
      }
      function co(t2) {
        var e2 = function(t3, e3) {
          if ("object" != so(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != so(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == so(e2) ? e2 : e2 + "";
      }
      var uo = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.ui = r().summernote.ui, this.options = e3.options, this.events = { "summernote.keyup summernote.mouseup summernote.change summernote.scroll": function() {
            o3.update();
          }, "summernote.disable summernote.dialog.shown": function() {
            o3.hide();
          }, "summernote.blur": function(t4, e4) {
            e4.originalEvent && e4.originalEvent.relatedTarget && o3.$popover[0].contains(e4.originalEvent.relatedTarget) || o3.hide();
          } };
        }, (e2 = [{ key: "shouldInitialize", value: function() {
          return !C.isEmpty(this.options.popover.link);
        } }, { key: "initialize", value: function() {
          this.$popover = this.ui.popover({ className: "note-link-popover", callback: function(t4) {
            t4.find(".popover-content,.note-popover-content").prepend('<span><a target="_blank"></a>&nbsp;</span>');
          } }).render().appendTo(this.options.container);
          var t3 = this.$popover.find(".popover-content,.note-popover-content");
          this.context.invoke("buttons.build", t3, this.options.popover.link), this.$popover.on("mousedown", function(t4) {
            t4.preventDefault();
          });
        } }, { key: "destroy", value: function() {
          this.$popover.remove();
        } }, { key: "update", value: function() {
          if (this.context.invoke("editor.hasFocus")) {
            var t3 = this.context.invoke("editor.getLastRange");
            if (t3.isCollapsed() && t3.isOnAnchor()) {
              var e3 = pt.ancestor(t3.sc, pt.isAnchor), o3 = r()(e3).attr("href");
              this.$popover.find("a").attr("href", o3).text(o3);
              var n2 = pt.posFromPlaceholder(e3), i2 = r()(this.options.container).offset();
              n2.top -= i2.top, n2.left -= i2.left, this.$popover.css({ display: "block", left: n2.left, top: n2.top });
            } else this.hide();
          } else this.hide();
        } }, { key: "hide", value: function() {
          this.$popover.hide();
        } }]) && lo(t2.prototype, e2), o2 && lo(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function fo(t2) {
        return fo = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, fo(t2);
      }
      function ho(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, po(n2.key), n2);
        }
      }
      function po(t2) {
        var e2 = function(t3, e3) {
          if ("object" != fo(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != fo(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == fo(e2) ? e2 : e2 + "";
      }
      var mo = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.ui = r().summernote.ui, this.$body = r()(document.body), this.$editor = e3.layoutInfo.editor, this.options = e3.options, this.lang = this.options.langInfo;
        }, (e2 = [{ key: "initialize", value: function() {
          var t3 = "";
          if (this.options.maximumImageFileSize) {
            var e3 = Math.floor(Math.log(this.options.maximumImageFileSize) / Math.log(1024)), o3 = 1 * (this.options.maximumImageFileSize / Math.pow(1024, e3)).toFixed(2) + " " + " KMGTP"[e3] + "B";
            t3 = "<small>".concat(this.lang.image.maximumFileSize + " : " + o3, "</small>");
          }
          var n2 = this.options.dialogsInBody ? this.$body : this.options.container, i2 = ['<div class="form-group note-form-group note-group-select-from-files">', '<label for="note-dialog-image-file-' + this.options.id + '" class="note-form-label">' + this.lang.image.selectFromFiles + "</label>", '<input id="note-dialog-image-file-' + this.options.id + '" class="note-image-input form-control-file note-form-control note-input" ', ' type="file" name="files" accept="' + this.options.acceptImageFileTypes + '" multiple="multiple"/>', t3, "</div>", '<div class="form-group note-group-image-url">', '<label for="note-dialog-image-url-' + this.options.id + '" class="note-form-label">' + this.lang.image.url + "</label>", '<input id="note-dialog-image-url-' + this.options.id + '" class="note-image-url form-control note-form-control note-input" type="text"/>', "</div>"].join(""), r2 = '<input type="button" href="#" class="'.concat("btn btn-primary note-btn note-btn-primary note-image-btn", '" value="').concat(this.lang.image.insert, '" disabled>');
          this.$dialog = this.ui.dialog({ title: this.lang.image.insert, fade: this.options.dialogsFade, body: i2, footer: r2 }).render().appendTo(n2);
        } }, { key: "destroy", value: function() {
          this.ui.hideDialog(this.$dialog), this.$dialog.remove();
        } }, { key: "bindEnterKey", value: function(t3, e3) {
          t3.on("keypress", function(t4) {
            t4.keyCode === Nt.code.ENTER && (t4.preventDefault(), e3.trigger("click"));
          });
        } }, { key: "show", value: function() {
          var t3 = this;
          this.context.invoke("editor.saveRange"), this.showImageDialog().then(function(e3) {
            t3.ui.hideDialog(t3.$dialog), t3.context.invoke("editor.restoreRange"), "string" == typeof e3 ? t3.options.callbacks.onImageLinkInsert ? t3.context.triggerEvent("image.link.insert", e3) : t3.context.invoke("editor.insertImage", e3) : t3.context.invoke("editor.insertImagesOrCallback", e3);
          }).fail(function() {
            t3.context.invoke("editor.restoreRange");
          });
        } }, { key: "showImageDialog", value: function() {
          var t3 = this;
          return r().Deferred(function(e3) {
            var o3 = t3.$dialog.find(".note-image-input"), n2 = t3.$dialog.find(".note-image-url"), i2 = t3.$dialog.find(".note-image-btn");
            t3.ui.onDialogShown(t3.$dialog, function() {
              t3.context.triggerEvent("dialog.shown"), o3.replaceWith(o3.clone().on("change", function(t4) {
                e3.resolve(t4.target.files || t4.target.value);
              }).val("")), n2.on("input paste propertychange", function() {
                t3.ui.toggleBtn(i2, n2.val());
              }).val(""), m.isSupportTouch || n2.trigger("focus"), i2.on("click", function(t4) {
                t4.preventDefault(), e3.resolve(n2.val());
              }), t3.bindEnterKey(n2, i2);
            }), t3.ui.onDialogHidden(t3.$dialog, function() {
              o3.off(), n2.off(), i2.off(), "pending" === e3.state() && e3.reject();
            }), t3.ui.showDialog(t3.$dialog);
          });
        } }]) && ho(t2.prototype, e2), o2 && ho(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function vo(t2) {
        return vo = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, vo(t2);
      }
      function go(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, bo(n2.key), n2);
        }
      }
      function bo(t2) {
        var e2 = function(t3, e3) {
          if ("object" != vo(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != vo(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == vo(e2) ? e2 : e2 + "";
      }
      var yo = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.ui = r().summernote.ui, this.editable = e3.layoutInfo.editable[0], this.options = e3.options, this.events = { "summernote.disable summernote.dialog.shown": function() {
            o3.hide();
          }, "summernote.blur": function(t4, e4) {
            e4.originalEvent && e4.originalEvent.relatedTarget && o3.$popover[0].contains(e4.originalEvent.relatedTarget) || o3.hide();
          } };
        }, e2 = [{ key: "shouldInitialize", value: function() {
          return !C.isEmpty(this.options.popover.image);
        } }, { key: "initialize", value: function() {
          this.$popover = this.ui.popover({ className: "note-image-popover" }).render().appendTo(this.options.container);
          var t3 = this.$popover.find(".popover-content,.note-popover-content");
          this.context.invoke("buttons.build", t3, this.options.popover.image), this.$popover.on("mousedown", function(t4) {
            t4.preventDefault();
          });
        } }, { key: "destroy", value: function() {
          this.$popover.remove();
        } }, { key: "update", value: function(t3, e3) {
          if (pt.isImg(t3)) {
            var o3 = r()(t3).offset(), n2 = r()(this.options.container).offset(), i2 = {};
            this.options.popatmouse ? (i2.left = e3.pageX - 20, i2.top = e3.pageY) : i2 = o3, i2.top -= n2.top, i2.left -= n2.left, this.$popover.css({ display: "block", left: i2.left, top: i2.top });
          } else this.hide();
        } }, { key: "hide", value: function() {
          this.$popover.hide();
        } }], e2 && go(t2.prototype, e2), o2 && go(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function ko(t2) {
        return ko = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, ko(t2);
      }
      function wo(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Co(n2.key), n2);
        }
      }
      function Co(t2) {
        var e2 = function(t3, e3) {
          if ("object" != ko(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != ko(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == ko(e2) ? e2 : e2 + "";
      }
      var So = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.ui = r().summernote.ui, this.options = e3.options, this.events = { "summernote.mousedown": function(t4, e4) {
            o3.update(e4.target);
          }, "summernote.keyup summernote.scroll summernote.change": function() {
            o3.update();
          }, "summernote.disable summernote.dialog.shown": function() {
            o3.hide();
          }, "summernote.blur": function(t4, e4) {
            e4.originalEvent && e4.originalEvent.relatedTarget && o3.$popover[0].contains(e4.originalEvent.relatedTarget) || o3.hide();
          } };
        }, e2 = [{ key: "shouldInitialize", value: function() {
          return !C.isEmpty(this.options.popover.table);
        } }, { key: "initialize", value: function() {
          this.$popover = this.ui.popover({ className: "note-table-popover" }).render().appendTo(this.options.container);
          var t3 = this.$popover.find(".popover-content,.note-popover-content");
          this.context.invoke("buttons.build", t3, this.options.popover.table), m.isFF && document.execCommand("enableInlineTableEditing", false, false), this.$popover.on("mousedown", function(t4) {
            t4.preventDefault();
          });
        } }, { key: "destroy", value: function() {
          this.$popover.remove();
        } }, { key: "update", value: function(t3) {
          if (this.context.isDisabled()) return false;
          var e3 = pt.isCell(t3) || pt.isCell(null == t3 ? void 0 : t3.parentElement);
          if (e3) {
            var o3 = pt.posFromPlaceholder(t3), n2 = r()(this.options.container).offset();
            o3.top -= n2.top, o3.left -= n2.left, this.$popover.css({ display: "block", left: o3.left, top: o3.top });
          } else this.hide();
          return e3;
        } }, { key: "hide", value: function() {
          this.$popover.hide();
        } }], e2 && wo(t2.prototype, e2), o2 && wo(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function xo(t2) {
        return xo = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, xo(t2);
      }
      function To(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Eo(n2.key), n2);
        }
      }
      function Eo(t2) {
        var e2 = function(t3, e3) {
          if ("object" != xo(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != xo(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == xo(e2) ? e2 : e2 + "";
      }
      var Po = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.ui = r().summernote.ui, this.$body = r()(document.body), this.$editor = e3.layoutInfo.editor, this.options = e3.options, this.lang = this.options.langInfo;
        }, (e2 = [{ key: "initialize", value: function() {
          var t3 = this.options.dialogsInBody ? this.$body : this.options.container, e3 = ['<div class="form-group note-form-group row-fluid">', '<label for="note-dialog-video-url-'.concat(this.options.id, '" class="note-form-label">').concat(this.lang.video.url, ' <small class="text-muted">').concat(this.lang.video.providers, "</small></label>"), '<input id="note-dialog-video-url-'.concat(this.options.id, '" class="note-video-url form-control note-form-control note-input" type="text"/>'), "</div>"].join(""), o3 = '<input type="button" href="#" class="'.concat("btn btn-primary note-btn note-btn-primary note-video-btn", '" value="').concat(this.lang.video.insert, '" disabled>');
          this.$dialog = this.ui.dialog({ title: this.lang.video.insert, fade: this.options.dialogsFade, body: e3, footer: o3 }).render().appendTo(t3);
        } }, { key: "destroy", value: function() {
          this.ui.hideDialog(this.$dialog), this.$dialog.remove();
        } }, { key: "bindEnterKey", value: function(t3, e3) {
          t3.on("keypress", function(t4) {
            t4.keyCode === Nt.code.ENTER && (t4.preventDefault(), e3.trigger("click"));
          });
        } }, { key: "createVideoNode", value: function(t3) {
          var e3, o3 = t3.match(/(?:youtu\.be\/|youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=|shorts\/|live\/))([^&\n?]+)(?:.*[?&]t=([^&\n]+))?.*/), n2 = t3.match(/(?:\.|\/\/)drive\.google\.com\/file\/d\/(.[a-zA-Z0-9_-]*)\/view/), i2 = t3.match(/(?:www\.|\/\/)instagram\.com\/(reel|p)\/(.[a-zA-Z0-9_-]*)/), a2 = t3.match(/\/\/vine\.co\/v\/([a-zA-Z0-9]+)/), s2 = t3.match(/\/\/(player\.)?vimeo\.com\/([a-z]*\/)*(\d+)[?]?.*/), l2 = t3.match(/.+dailymotion.com\/(video|hub)\/([^_]+)[^#]*(#video=([^_&]+))?/), c2 = t3.match(/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/), u2 = t3.match(/\/\/(.*)\/videos\/watch\/([^?]*)(?:\?(?:start=(\w*))?(?:&stop=(\w*))?(?:&loop=([10]))?(?:&autoplay=([10]))?(?:&muted=([10]))?)?/), d2 = t3.match(/\/\/v\.qq\.com.*?vid=(.+)/), f2 = t3.match(/\/\/v\.qq\.com\/x?\/?(page|cover).*?\/([^\/]+)\.html\??.*/), h2 = t3.match(/^.+.(mp4|m4v)$/), p2 = t3.match(/^.+.(ogg|ogv)$/), m2 = t3.match(/^.+.(webm)$/), v2 = t3.match(/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/);
          if (o3 && 11 === o3[1].length) {
            var g2 = o3[1], b2 = 0;
            if (void 0 !== o3[2]) {
              var y2 = o3[2].match(/^(?:(\d+)h)?(?:(\d+)m)?(?:(\d+)s)?$/);
              if (y2) for (var k2 = [3600, 60, 1], w2 = 0, C2 = k2.length; w2 < C2; w2++) b2 += void 0 !== y2[w2 + 1] ? k2[w2] * parseInt(y2[w2 + 1], 10) : 0;
              else b2 = parseInt(o3[2], 10);
            }
            e3 = r()("<iframe>").attr("frameborder", 0).attr("src", "//www.youtube.com/embed/" + g2 + (b2 > 0 ? "?start=" + b2 : "")).attr("width", "640").attr("height", "360");
          } else if (n2 && n2[0].length) e3 = r()("<iframe>").attr("frameborder", 0).attr("src", "https://drive.google.com/file/d/" + n2[1] + "/preview").attr("width", "640").attr("height", "480");
          else if (i2 && i2[0].length) e3 = r()("<iframe>").attr("frameborder", 0).attr("src", "https://instagram.com/p/" + i2[2] + "/embed/").attr("width", "612").attr("height", "710").attr("scrolling", "no").attr("allowtransparency", "true");
          else if (a2 && a2[0].length) e3 = r()("<iframe>").attr("frameborder", 0).attr("src", a2[0] + "/embed/simple").attr("width", "600").attr("height", "600").attr("class", "vine-embed");
          else if (s2 && s2[3].length) e3 = r()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder", 0).attr("src", "//player.vimeo.com/video/" + s2[3]).attr("width", "640").attr("height", "360");
          else if (l2 && l2[2].length) e3 = r()("<iframe>").attr("frameborder", 0).attr("src", "//www.dailymotion.com/embed/video/" + l2[2]).attr("width", "640").attr("height", "360");
          else if (c2 && c2[1].length) e3 = r()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder", 0).attr("height", "498").attr("width", "510").attr("src", "//player.youku.com/embed/" + c2[1]);
          else if (u2 && u2[0].length) {
            var S2 = 0;
            "undefined" !== u2[2] && (S2 = u2[2]);
            var x2 = 0;
            "undefined" !== u2[3] && (x2 = u2[3]);
            var T2 = 0;
            "undefined" !== u2[4] && (T2 = u2[4]);
            var E2 = 0;
            "undefined" !== u2[5] && (E2 = u2[5]);
            var P2 = 0;
            "undefined" !== u2[6] && (P2 = u2[6]), e3 = r()('<iframe allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups">').attr("frameborder", 0).attr("src", "//" + u2[1] + "/videos/embed/" + u2[2] + "?loop=" + T2 + "&autoplay=" + E2 + "&muted=" + P2 + (S2 > 0 ? "&start=" + S2 : "") + (x2 > 0 ? "&end=" + b2 : "")).attr("width", "560").attr("height", "315");
          } else if (d2 && d2[1].length || f2 && f2[2].length) {
            var N2 = d2 && d2[1].length ? d2[1] : f2[2];
            e3 = r()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder", 0).attr("height", "310").attr("width", "500").attr("src", "https://v.qq.com/txp/iframe/player.html?vid=" + N2 + "&amp;auto=0");
          } else if (h2 || p2 || m2) e3 = r()("<video controls>").attr("src", t3).attr("width", "640").attr("height", "360");
          else {
            if (!v2 || !v2[0].length) return false;
            e3 = r()("<iframe>").attr("frameborder", 0).attr("src", "https://www.facebook.com/plugins/video.php?href=" + encodeURIComponent(v2[0]) + "&show_text=0&width=560").attr("width", "560").attr("height", "301").attr("scrolling", "no").attr("allowtransparency", "true");
          }
          return e3.addClass("note-video-clip"), e3[0];
        } }, { key: "show", value: function() {
          var t3 = this, e3 = this.context.invoke("editor.getSelectedText");
          this.context.invoke("editor.saveRange"), this.showVideoDialog(e3).then(function(e4) {
            t3.ui.hideDialog(t3.$dialog), t3.context.invoke("editor.restoreRange");
            var o3 = t3.createVideoNode(e4);
            o3 && t3.context.invoke("editor.insertNode", o3);
          }).fail(function() {
            t3.context.invoke("editor.restoreRange");
          });
        } }, { key: "showVideoDialog", value: function() {
          var t3 = this;
          return r().Deferred(function(e3) {
            var o3 = t3.$dialog.find(".note-video-url"), n2 = t3.$dialog.find(".note-video-btn");
            t3.ui.onDialogShown(t3.$dialog, function() {
              t3.context.triggerEvent("dialog.shown"), o3.on("input paste propertychange", function() {
                t3.ui.toggleBtn(n2, o3.val());
              }), m.isSupportTouch || o3.trigger("focus"), n2.on("click", function(t4) {
                t4.preventDefault(), e3.resolve(o3.val());
              }), t3.bindEnterKey(o3, n2);
            }), t3.ui.onDialogHidden(t3.$dialog, function() {
              o3.off(), n2.off(), "pending" === e3.state() && e3.reject();
            }), t3.ui.showDialog(t3.$dialog);
          });
        } }]) && To(t2.prototype, e2), o2 && To(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function No(t2) {
        return No = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, No(t2);
      }
      function $o(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Io(n2.key), n2);
        }
      }
      function Io(t2) {
        var e2 = function(t3, e3) {
          if ("object" != No(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != No(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == No(e2) ? e2 : e2 + "";
      }
      var Ro = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.ui = r().summernote.ui, this.$body = r()(document.body), this.$editor = e3.layoutInfo.editor, this.options = e3.options, this.lang = this.options.langInfo;
        }, e2 = [{ key: "initialize", value: function() {
          var t3 = this.options.dialogsInBody ? this.$body : this.options.container, e3 = ['<p class="text-center">', '<a href="http://summernote.org/" target="_blank" rel="noopener noreferrer">Summernote 0.9.1</a> · ', '<a href="https://github.com/summernote/summernote" target="_blank" rel="noopener noreferrer">Project</a> · ', '<a href="https://github.com/summernote/summernote/issues" target="_blank" rel="noopener noreferrer">Issues</a>', "</p>"].join("");
          this.$dialog = this.ui.dialog({ title: this.lang.options.help, fade: this.options.dialogsFade, body: this.createShortcutList(), footer: e3, callback: function(t4) {
            t4.find(".modal-body,.note-modal-body").css({ "max-height": 300, overflow: "scroll" });
          } }).render().appendTo(t3);
        } }, { key: "destroy", value: function() {
          this.ui.hideDialog(this.$dialog), this.$dialog.remove();
        } }, { key: "createShortcutList", value: function() {
          var t3 = this, e3 = this.options.keyMap[m.isMac ? "mac" : "pc"];
          return Object.keys(e3).map(function(o3) {
            var n2 = e3[o3], i2 = r()('<div><div class="help-list-item"></div></div>');
            return i2.append(r()("<label><kbd>" + o3 + "</kdb></label>").css({ width: 180, "margin-right": 10 })).append(r()("<span></span>").html(t3.context.memo("help." + n2) || n2)), i2.html();
          }).join("");
        } }, { key: "showHelpDialog", value: function() {
          var t3 = this;
          return r().Deferred(function(e3) {
            t3.ui.onDialogShown(t3.$dialog, function() {
              t3.context.triggerEvent("dialog.shown"), e3.resolve();
            }), t3.ui.showDialog(t3.$dialog);
          }).promise();
        } }, { key: "show", value: function() {
          var t3 = this;
          this.context.invoke("editor.saveRange"), this.showHelpDialog().then(function() {
            t3.context.invoke("editor.restoreRange");
          });
        } }], e2 && $o(t2.prototype, e2), o2 && $o(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Ao(t2) {
        return Ao = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Ao(t2);
      }
      function Lo(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Fo(n2.key), n2);
        }
      }
      function Fo(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Ao(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Ao(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Ao(e2) ? e2 : e2 + "";
      }
      var Do = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.ui = r().summernote.ui, this.options = e3.options, this.hidable = true, this.onContextmenu = false, this.pageX = null, this.pageY = null, this.events = { "summernote.contextmenu": function(t4) {
            o3.options.editing && (t4.preventDefault(), t4.stopPropagation(), o3.onContextmenu = true, o3.update(true));
          }, "summernote.mousedown": function(t4, e4) {
            o3.pageX = e4.pageX, o3.pageY = e4.pageY;
          }, "summernote.keyup summernote.mouseup summernote.scroll": function(t4, e4) {
            if (o3.options.editing && !o3.onContextmenu) {
              if ("keyup" == e4.type) {
                var n2 = o3.context.invoke("editor.getLastRange").getWordRange(), i2 = g.rect2bnd(C.last(n2.getClientRects()));
                o3.pageX = i2.left, o3.pageY = i2.top;
              } else o3.pageX = e4.pageX, o3.pageY = e4.pageY;
              o3.update();
            }
            o3.onContextmenu = false;
          }, "summernote.disable summernote.change summernote.dialog.shown summernote.blur": function() {
            o3.hide();
          }, "summernote.focusout": function() {
            o3.$popover.is(":active,:focus") || o3.hide();
          } };
        }, (e2 = [{ key: "shouldInitialize", value: function() {
          return this.options.airMode && !C.isEmpty(this.options.popover.air);
        } }, { key: "initialize", value: function() {
          var t3 = this;
          this.$popover = this.ui.popover({ className: "note-air-popover" }).render().appendTo(this.options.container);
          var e3 = this.$popover.find(".popover-content");
          this.context.invoke("buttons.build", e3, this.options.popover.air), this.$popover.on("mousedown", function() {
            t3.hidable = false;
          }), this.$popover.on("mouseup", function() {
            t3.hidable = true;
          });
        } }, { key: "destroy", value: function() {
          this.$popover.remove();
        } }, { key: "update", value: function(t3) {
          var e3 = this.context.invoke("editor.currentStyle");
          if (!e3.range || e3.range.isCollapsed() && !t3) this.hide();
          else {
            var o3 = { left: this.pageX, top: this.pageY }, n2 = r()(this.options.container).offset();
            o3.top -= n2.top, o3.left -= n2.left, this.$popover.css({ display: "block", left: Math.max(o3.left, 0) + -5, top: o3.top + 5 }), this.context.invoke("buttons.updateCurrentStyle", this.$popover);
          }
        } }, { key: "updateCodeview", value: function(t3) {
          this.ui.toggleBtnActive(this.$popover.find(".btn-codeview"), t3), t3 && this.hide();
        } }, { key: "hide", value: function() {
          this.hidable && this.$popover.hide();
        } }]) && Lo(t2.prototype, e2), o2 && Lo(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Ho(t2) {
        return Ho = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Ho(t2);
      }
      function jo(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Bo(n2.key), n2);
        }
      }
      function Bo(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Ho(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Ho(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Ho(e2) ? e2 : e2 + "";
      }
      var Oo = function() {
        return t2 = function t3(e3) {
          var o3 = this;
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.context = e3, this.ui = r().summernote.ui, this.$editable = e3.layoutInfo.editable, this.options = e3.options, this.hint = this.options.hint || [], this.direction = this.options.hintDirection || "bottom", this.hints = Array.isArray(this.hint) ? this.hint : [this.hint], this.events = { "summernote.keyup": function(t4, e4) {
            e4.isDefaultPrevented() || o3.handleKeyup(e4);
          }, "summernote.keydown": function(t4, e4) {
            o3.handleKeydown(e4);
          }, "summernote.disable summernote.dialog.shown summernote.blur": function() {
            o3.hide();
          } };
        }, e2 = [{ key: "shouldInitialize", value: function() {
          return this.hints.length > 0;
        } }, { key: "initialize", value: function() {
          var t3 = this;
          this.lastWordRange = null, this.matchingWord = null, this.$popover = this.ui.popover({ className: "note-hint-popover", hideArrow: true, direction: "" }).render().appendTo(this.options.container), this.$popover.hide(), this.$content = this.$popover.find(".popover-content,.note-popover-content"), this.$content.on("click", ".note-hint-item", function(e3) {
            t3.$content.find(".active").removeClass("active"), r()(e3.currentTarget).addClass("active"), t3.replace();
          }), this.$popover.on("mousedown", function(t4) {
            t4.preventDefault();
          });
        } }, { key: "destroy", value: function() {
          this.$popover.remove();
        } }, { key: "selectItem", value: function(t3) {
          this.$content.find(".active").removeClass("active"), t3.addClass("active"), this.$content[0].scrollTop = t3[0].offsetTop - this.$content.innerHeight() / 2;
        } }, { key: "moveDown", value: function() {
          var t3 = this.$content.find(".note-hint-item.active"), e3 = t3.next();
          if (e3.length) this.selectItem(e3);
          else {
            var o3 = t3.parent().next();
            o3.length || (o3 = this.$content.find(".note-hint-group").first()), this.selectItem(o3.find(".note-hint-item").first());
          }
        } }, { key: "moveUp", value: function() {
          var t3 = this.$content.find(".note-hint-item.active"), e3 = t3.prev();
          if (e3.length) this.selectItem(e3);
          else {
            var o3 = t3.parent().prev();
            o3.length || (o3 = this.$content.find(".note-hint-group").last()), this.selectItem(o3.find(".note-hint-item").last());
          }
        } }, { key: "replace", value: function() {
          var t3 = this.$content.find(".note-hint-item.active");
          if (t3.length) {
            var e3 = this.nodeFromItem(t3);
            if (null !== this.matchingWord && 0 === this.matchingWord.length) this.lastWordRange.so = this.lastWordRange.eo;
            else if (null !== this.matchingWord && this.matchingWord.length > 0 && !this.lastWordRange.isCollapsed()) {
              var o3 = this.lastWordRange.eo - this.lastWordRange.so - this.matchingWord.length;
              o3 > 0 && (this.lastWordRange.so += o3);
            }
            if (this.lastWordRange.insertNode(e3), "next" === this.options.hintSelect) {
              var n2 = document.createTextNode("");
              r()(e3).after(n2), Et.createFromNodeBefore(n2).select();
            } else Et.createFromNodeAfter(e3).select();
            this.lastWordRange = null, this.hide(), this.context.invoke("editor.focus"), this.context.triggerEvent("change", this.$editable.html(), this.$editable);
          }
        } }, { key: "nodeFromItem", value: function(t3) {
          var e3 = this.hints[t3.data("index")], o3 = t3.data("item"), n2 = e3.content ? e3.content(o3) : o3;
          return "string" == typeof n2 && (n2 = pt.createText(n2)), n2;
        } }, { key: "createItemTemplates", value: function(t3, e3) {
          var o3 = this.hints[t3];
          return e3.map(function(e4, n2) {
            var i2 = r()('<div class="note-hint-item"></div>');
            return i2.append(o3.template ? o3.template(e4) : e4 + ""), i2.data({ index: t3, item: e4 }), 0 === t3 && 0 === n2 && i2.addClass("active"), i2;
          });
        } }, { key: "handleKeydown", value: function(t3) {
          this.$popover.is(":visible") && (t3.keyCode === Nt.code.ENTER ? (t3.preventDefault(), this.replace()) : t3.keyCode === Nt.code.UP ? (t3.preventDefault(), this.moveUp()) : t3.keyCode === Nt.code.DOWN && (t3.preventDefault(), this.moveDown()));
        } }, { key: "searchKeyword", value: function(t3, e3, o3) {
          var n2 = this.hints[t3];
          if (n2 && n2.match.test(e3) && n2.search) {
            var i2 = n2.match.exec(e3);
            this.matchingWord = i2[0], n2.search(i2[1], o3);
          } else o3();
        } }, { key: "createGroup", value: function(t3, e3) {
          var o3 = this, n2 = r()('<div class="note-hint-group note-hint-group-' + t3 + '"></div>');
          return this.searchKeyword(t3, e3, function(e4) {
            (e4 = e4 || []).length && (n2.html(o3.createItemTemplates(t3, e4)), o3.show());
          }), n2;
        } }, { key: "handleKeyup", value: function(t3) {
          var e3 = this;
          if (!C.contains([Nt.code.ENTER, Nt.code.UP, Nt.code.DOWN], t3.keyCode)) {
            var o3, n2, i2 = this.context.invoke("editor.getLastRange");
            if ("words" === this.options.hintMode) {
              if (o3 = i2.getWordsRange(i2), n2 = o3.toString(), this.hints.forEach(function(t4) {
                if (t4.match.test(n2)) return o3 = i2.getWordsMatchRange(t4.match), false;
              }), !o3) return void this.hide();
              n2 = o3.toString();
            } else o3 = i2.getWordRange(), n2 = o3.toString();
            if (this.hints.length && n2) {
              this.$content.empty();
              var a2 = g.rect2bnd(C.last(o3.getClientRects())), s2 = r()(this.options.container).offset();
              a2 && (a2.top -= s2.top, a2.left -= s2.left, this.$popover.hide(), this.lastWordRange = o3, this.hints.forEach(function(t4, o4) {
                t4.match.test(n2) && e3.createGroup(o4, n2).appendTo(e3.$content);
              }), this.$content.find(".note-hint-item").first().addClass("active"), "top" === this.direction ? this.$popover.css({ left: a2.left, top: a2.top - this.$popover.outerHeight() - 5 }) : this.$popover.css({ left: a2.left, top: a2.top + a2.height + 5 }));
            } else this.hide();
          }
        } }, { key: "show", value: function() {
          this.$popover.show();
        } }, { key: "hide", value: function() {
          this.$popover.hide();
        } }], e2 && jo(t2.prototype, e2), o2 && jo(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function zo(t2) {
        return zo = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, zo(t2);
      }
      function Mo(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Uo(n2.key), n2);
        }
      }
      function Uo(t2) {
        var e2 = function(t3, e3) {
          if ("object" != zo(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != zo(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == zo(e2) ? e2 : e2 + "";
      }
      r().summernote = r().extend(r().summernote, { version: "0.9.1", plugins: {}, dom: pt, range: Et, lists: C, options: { langInfo: r().summernote.lang["en-US"], editing: true, modules: { editor: oe, clipboard: ae, dropzone: ue, codeview: ve, statusbar: ke, fullscreen: xe, handle: Ne, hintPopover: Oo, autoLink: Le, autoSync: je, autoReplace: Me, placeholder: qe, buttons: Ze, toolbar: Je, linkDialog: ao, linkPopover: uo, imageDialog: mo, imagePopover: yo, tablePopover: So, videoDialog: Po, helpDialog: Ro, airPopover: Do }, buttons: {}, lang: "en-US", followingToolbar: false, toolbarPosition: "top", otherStaticBar: "", codeviewKeepButton: false, toolbar: [["style", ["style"]], ["font", ["bold", "underline", "clear"]], ["fontname", ["fontname"]], ["color", ["color"]], ["para", ["ul", "ol", "paragraph"]], ["table", ["table"]], ["insert", ["link", "picture", "video"]], ["view", ["fullscreen", "codeview", "help"]]], popatmouse: true, popover: { image: [["resize", ["resizeFull", "resizeHalf", "resizeQuarter", "resizeNone"]], ["float", ["floatLeft", "floatRight", "floatNone"]], ["remove", ["removeMedia"]]], link: [["link", ["linkDialogShow", "unlink"]]], table: [["add", ["addRowDown", "addRowUp", "addColLeft", "addColRight"]], ["delete", ["deleteRow", "deleteCol", "deleteTable"]]], air: [["color", ["color"]], ["font", ["bold", "underline", "clear"]], ["para", ["ul", "paragraph"]], ["table", ["table"]], ["insert", ["link", "picture"]], ["view", ["fullscreen", "codeview"]]] }, linkAddNoReferrer: false, addLinkNoOpener: false, airMode: false, overrideContextMenu: false, width: null, height: null, linkTargetBlank: true, focus: false, tabDisable: false, tabSize: 4, styleWithCSS: false, shortcuts: true, textareaAutoSync: true, tooltip: "auto", container: null, maxTextLength: 0, blockquoteBreakingLevel: 2, spellCheck: true, disableGrammar: false, placeholder: null, inheritPlaceholder: false, recordEveryKeystroke: false, historyLimit: 200, showDomainOnlyForAutolink: false, hintMode: "word", hintSelect: "after", hintDirection: "bottom", styleTags: ["p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6"], fontNames: ["Arial", "Arial Black", "Comic Sans MS", "Courier New", "Helvetica Neue", "Helvetica", "Impact", "Lucida Grande", "Tahoma", "Times New Roman", "Verdana"], fontNamesIgnoreCheck: [], addDefaultFonts: true, fontSizes: ["8", "9", "10", "11", "12", "14", "18", "24", "36"], fontSizeUnits: ["px", "pt"], colors: [["#000000", "#424242", "#636363", "#9C9C94", "#CEC6CE", "#EFEFEF", "#F7F7F7", "#FFFFFF"], ["#FF0000", "#FF9C00", "#FFFF00", "#00FF00", "#00FFFF", "#0000FF", "#9C00FF", "#FF00FF"], ["#F7C6CE", "#FFE7CE", "#FFEFC6", "#D6EFD6", "#CEDEE7", "#CEE7F7", "#D6D6E7", "#E7D6DE"], ["#E79C9C", "#FFC69C", "#FFE79C", "#B5D6A5", "#A5C6CE", "#9CC6EF", "#B5A5D6", "#D6A5BD"], ["#E76363", "#F7AD6B", "#FFD663", "#94BD7B", "#73A5AD", "#6BADDE", "#8C7BC6", "#C67BA5"], ["#CE0000", "#E79439", "#EFC631", "#6BA54A", "#4A7B8C", "#3984C6", "#634AA5", "#A54A7B"], ["#9C0000", "#B56308", "#BD9400", "#397B21", "#104A5A", "#085294", "#311873", "#731842"], ["#630000", "#7B3900", "#846300", "#295218", "#083139", "#003163", "#21104A", "#4A1031"]], colorsName: [["Black", "Tundora", "Dove Gray", "Star Dust", "Pale Slate", "Gallery", "Alabaster", "White"], ["Red", "Orange Peel", "Yellow", "Green", "Cyan", "Blue", "Electric Violet", "Magenta"], ["Azalea", "Karry", "Egg White", "Zanah", "Botticelli", "Tropical Blue", "Mischka", "Twilight"], ["Tonys Pink", "Peach Orange", "Cream Brulee", "Sprout", "Casper", "Perano", "Cold Purple", "Careys Pink"], ["Mandy", "Rajah", "Dandelion", "Olivine", "Gulf Stream", "Viking", "Blue Marguerite", "Puce"], ["Guardsman Red", "Fire Bush", "Golden Dream", "Chelsea Cucumber", "Smalt Blue", "Boston Blue", "Butterfly Bush", "Cadillac"], ["Sangria", "Mai Tai", "Buddha Gold", "Forest Green", "Eden", "Venice Blue", "Meteorite", "Claret"], ["Rosewood", "Cinnamon", "Olive", "Parsley", "Tiber", "Midnight Blue", "Valentino", "Loulou"]], colorButton: { foreColor: "#000000", backColor: "#FFFF00" }, lineHeights: ["1.0", "1.2", "1.4", "1.5", "1.6", "1.8", "2.0", "3.0"], tableClassName: "table table-bordered", insertTableMaxSize: { col: 10, row: 10 }, dialogsInBody: false, dialogsFade: false, maximumImageFileSize: null, acceptImageFileTypes: "image/*", allowClipboardImagePasting: true, callbacks: { onBeforeCommand: null, onBlur: null, onBlurCodeview: null, onChange: null, onChangeCodeview: null, onDialogShown: null, onEnter: null, onFocus: null, onImageLinkInsert: null, onImageUpload: null, onImageUploadError: null, onInit: null, onKeydown: null, onKeyup: null, onMousedown: null, onMouseup: null, onPaste: null, onScroll: null }, codemirror: { mode: "text/html", htmlMode: true, lineNumbers: true }, codeviewFilter: true, codeviewFilterRegex: /<\/*(?:applet|b(?:ase|gsound|link)|embed|frame(?:set)?|ilayer|l(?:ayer|ink)|meta|object|s(?:cript|tyle)|t(?:itle|extarea)|xml)[^>]*?>/gi, codeviewIframeFilter: true, codeviewIframeWhitelistSrc: [], codeviewIframeWhitelistSrcBase: ["www.youtube.com", "www.youtube-nocookie.com", "www.facebook.com", "vine.co", "instagram.com", "player.vimeo.com", "www.dailymotion.com", "player.youku.com", "jumpingbean.tv", "v.qq.com"], keyMap: { pc: { ESC: "escape", ENTER: "insertParagraph", "CTRL+Z": "undo", "CTRL+Y": "redo", TAB: "tab", "SHIFT+TAB": "untab", "CTRL+B": "bold", "CTRL+I": "italic", "CTRL+U": "underline", "CTRL+SHIFT+S": "strikethrough", "CTRL+BACKSLASH": "removeFormat", "CTRL+SHIFT+L": "justifyLeft", "CTRL+SHIFT+E": "justifyCenter", "CTRL+SHIFT+R": "justifyRight", "CTRL+SHIFT+J": "justifyFull", "CTRL+SHIFT+NUM7": "insertUnorderedList", "CTRL+SHIFT+NUM8": "insertOrderedList", "CTRL+LEFTBRACKET": "outdent", "CTRL+RIGHTBRACKET": "indent", "CTRL+NUM0": "formatPara", "CTRL+NUM1": "formatH1", "CTRL+NUM2": "formatH2", "CTRL+NUM3": "formatH3", "CTRL+NUM4": "formatH4", "CTRL+NUM5": "formatH5", "CTRL+NUM6": "formatH6", "CTRL+ENTER": "insertHorizontalRule", "CTRL+K": "linkDialog.show" }, mac: { ESC: "escape", ENTER: "insertParagraph", "CMD+Z": "undo", "CMD+SHIFT+Z": "redo", TAB: "tab", "SHIFT+TAB": "untab", "CMD+B": "bold", "CMD+I": "italic", "CMD+U": "underline", "CMD+SHIFT+S": "strikethrough", "CMD+BACKSLASH": "removeFormat", "CMD+SHIFT+L": "justifyLeft", "CMD+SHIFT+E": "justifyCenter", "CMD+SHIFT+R": "justifyRight", "CMD+SHIFT+J": "justifyFull", "CMD+SHIFT+NUM7": "insertUnorderedList", "CMD+SHIFT+NUM8": "insertOrderedList", "CMD+LEFTBRACKET": "outdent", "CMD+RIGHTBRACKET": "indent", "CMD+NUM0": "formatPara", "CMD+NUM1": "formatH1", "CMD+NUM2": "formatH2", "CMD+NUM3": "formatH3", "CMD+NUM4": "formatH4", "CMD+NUM5": "formatH5", "CMD+NUM6": "formatH6", "CMD+ENTER": "insertHorizontalRule", "CMD+K": "linkDialog.show" } }, icons: { align: "note-icon-align", alignCenter: "note-icon-align-center", alignJustify: "note-icon-align-justify", alignLeft: "note-icon-align-left", alignRight: "note-icon-align-right", rowBelow: "note-icon-row-below", colBefore: "note-icon-col-before", colAfter: "note-icon-col-after", rowAbove: "note-icon-row-above", rowRemove: "note-icon-row-remove", colRemove: "note-icon-col-remove", indent: "note-icon-align-indent", outdent: "note-icon-align-outdent", arrowsAlt: "note-icon-arrows-alt", bold: "note-icon-bold", caret: "note-icon-caret", circle: "note-icon-circle", close: "note-icon-close", code: "note-icon-code", eraser: "note-icon-eraser", floatLeft: "note-icon-float-left", floatRight: "note-icon-float-right", font: "note-icon-font", frame: "note-icon-frame", italic: "note-icon-italic", link: "note-icon-link", unlink: "note-icon-chain-broken", magic: "note-icon-magic", menuCheck: "note-icon-menu-check", minus: "note-icon-minus", orderedlist: "note-icon-orderedlist", pencil: "note-icon-pencil", picture: "note-icon-picture", question: "note-icon-question", redo: "note-icon-redo", rollback: "note-icon-rollback", square: "note-icon-square", strikethrough: "note-icon-strikethrough", subscript: "note-icon-subscript", superscript: "note-icon-superscript", table: "note-icon-table", textHeight: "note-icon-text-height", trash: "note-icon-trash", underline: "note-icon-underline", undo: "note-icon-undo", unorderedlist: "note-icon-unorderedlist", video: "note-icon-video" } } });
      var Wo = function() {
        return t2 = function t3(e3, o3, n2, i2) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.markup = e3, this.children = o3, this.options = n2, this.callback = i2;
        }, (e2 = [{ key: "render", value: function(t3) {
          var e3 = r()(this.markup);
          if (this.options && this.options.contents && e3.html(this.options.contents), this.options && this.options.className && e3.addClass(this.options.className), this.options && this.options.data && r().each(this.options.data, function(t4, o4) {
            e3.attr("data-" + t4, o4);
          }), this.options && this.options.click && e3.on("click", this.options.click), this.children) {
            var o3 = e3.find(".note-children-container");
            this.children.forEach(function(t4) {
              t4.render(o3.length ? o3 : e3);
            });
          }
          return this.callback && this.callback(e3, this.options), this.options && this.options.callback && this.options.callback(e3), t3 && t3.append(e3), e3;
        } }]) && Mo(t2.prototype, e2), o2 && Mo(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      const Ko = function(t2, e2) {
        return function() {
          var o2 = "object" === zo(arguments[1]) ? arguments[1] : arguments[0], n2 = Array.isArray(arguments[0]) ? arguments[0] : [];
          return o2 && o2.children && (n2 = o2.children), new Wo(t2, n2, o2, e2);
        };
      };
      function qo(t2) {
        return qo = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, qo(t2);
      }
      function Vo(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, _o(n2.key), n2);
        }
      }
      function _o(t2) {
        var e2 = function(t3, e3) {
          if ("object" != qo(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != qo(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == qo(e2) ? e2 : e2 + "";
      }
      const Go = function() {
        return t2 = function t3(e3, o3) {
          if (function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.$node = e3, this.options = r().extend({}, { title: "", target: o3.container, trigger: "hover focus", placement: "bottom" }, o3), this.$tooltip = r()(['<div class="note-tooltip">', '<div class="note-tooltip-arrow"></div>', '<div class="note-tooltip-content"></div>', "</div>"].join("")), "manual" !== this.options.trigger) {
            var n2 = this.show.bind(this), i2 = this.hide.bind(this), a2 = this.toggle.bind(this);
            this.options.trigger.split(" ").forEach(function(t4) {
              "hover" === t4 ? (e3.off("mouseenter mouseleave"), e3.on("mouseenter", n2).on("mouseleave", i2)) : "click" === t4 ? e3.on("click", a2) : "focus" === t4 && e3.on("focus", n2).on("blur", i2);
            });
          }
        }, (e2 = [{ key: "show", value: function() {
          var t3 = this.$node, e3 = t3.offset(), o3 = r()(this.options.target).offset();
          e3.top -= o3.top, e3.left -= o3.left;
          var n2 = this.$tooltip, i2 = this.options.title || t3.attr("title") || t3.data("title"), a2 = this.options.placement || t3.data("placement");
          n2.addClass(a2), n2.find(".note-tooltip-content").text(i2), n2.appendTo(this.options.target);
          var s2 = t3.outerWidth(), l2 = t3.outerHeight(), c2 = n2.outerWidth(), u2 = n2.outerHeight();
          "bottom" === a2 ? n2.css({ top: e3.top + l2, left: e3.left + (s2 / 2 - c2 / 2) }) : "top" === a2 ? n2.css({ top: e3.top - u2, left: e3.left + (s2 / 2 - c2 / 2) }) : "left" === a2 ? n2.css({ top: e3.top + (l2 / 2 - u2 / 2), left: e3.left - c2 }) : "right" === a2 && n2.css({ top: e3.top + (l2 / 2 - u2 / 2), left: e3.left + s2 }), n2.addClass("in");
        } }, { key: "hide", value: function() {
          var t3 = this;
          this.$tooltip.removeClass("in"), setTimeout(function() {
            t3.$tooltip.remove();
          }, 200);
        } }, { key: "toggle", value: function() {
          this.$tooltip.hasClass("in") ? this.hide() : this.show();
        } }]) && Vo(t2.prototype, e2), o2 && Vo(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      function Zo(t2) {
        return Zo = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, Zo(t2);
      }
      function Yo(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, Xo(n2.key), n2);
        }
      }
      function Xo(t2) {
        var e2 = function(t3, e3) {
          if ("object" != Zo(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != Zo(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == Zo(e2) ? e2 : e2 + "";
      }
      var Qo = function() {
        return t2 = function t3(e3, o3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.$button = e3, this.options = r().extend({}, { target: o3.container }, o3), this.setEvent();
        }, e2 = [{ key: "setEvent", value: function() {
          var t3 = this;
          this.$button.on("click", function(e3) {
            t3.toggle(), e3.stopImmediatePropagation();
          });
        } }, { key: "clear", value: function() {
          var t3 = r()(".note-btn-group.open");
          t3.find(".note-btn.active").removeClass("active"), t3.removeClass("open");
        } }, { key: "show", value: function() {
          this.$button.addClass("active"), this.$button.parent().addClass("open");
          var t3 = this.$button.next(), e3 = t3.offset(), o3 = t3.outerWidth(), n2 = r()(window).width(), i2 = parseFloat(r()(this.options.target).css("margin-right"));
          e3.left + o3 > n2 - i2 ? t3.css("margin-left", n2 - i2 - (e3.left + o3)) : t3.css("margin-left", "");
        } }, { key: "hide", value: function() {
          this.$button.removeClass("active"), this.$button.parent().removeClass("open");
        } }, { key: "toggle", value: function() {
          var t3 = this.$button.parent().hasClass("open");
          this.clear(), t3 ? this.hide() : this.show();
        } }], e2 && Yo(t2.prototype, e2), o2 && Yo(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      r()(document).on("click.note-dropdown-menu", function(t2) {
        r()(t2.target).closest(".note-btn-group").length || (r()(".note-btn-group.open .note-btn.active").removeClass("active"), r()(".note-btn-group.open").removeClass("open"));
      }), r()(document).on("click.note-dropdown-menu", function(t2) {
        r()(t2.target).closest(".note-dropdown-menu").parent().removeClass("open"), r()(t2.target).closest(".note-dropdown-menu").parent().find(".note-btn.active").removeClass("active");
      });
      const Jo = Qo;
      function tn(t2) {
        return tn = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
          return typeof t3;
        } : function(t3) {
          return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
        }, tn(t2);
      }
      function en(t2, e2) {
        for (var o2 = 0; o2 < e2.length; o2++) {
          var n2 = e2[o2];
          n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, on(n2.key), n2);
        }
      }
      function on(t2) {
        var e2 = function(t3, e3) {
          if ("object" != tn(t3) || !t3) return t3;
          var o2 = t3[Symbol.toPrimitive];
          if (void 0 !== o2) {
            var n2 = o2.call(t3, e3 || "default");
            if ("object" != tn(n2)) return n2;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === e3 ? String : Number)(t3);
        }(t2, "string");
        return "symbol" == tn(e2) ? e2 : e2 + "";
      }
      const nn = function() {
        return t2 = function t3(e3) {
          !function(t4, e4) {
            if (!(t4 instanceof e4)) throw new TypeError("Cannot call a class as a function");
          }(this, t3), this.$modal = e3, this.$backdrop = r()('<div class="note-modal-backdrop"></div>');
        }, (e2 = [{ key: "show", value: function() {
          var t3 = this;
          this.$backdrop.appendTo(document.body).show(), this.$modal.addClass("open").show(), this.$modal.trigger("note.modal.show"), this.$modal.off("click", ".close").on("click", ".close", this.hide.bind(this)), this.$modal.on("keydown", function(e3) {
            27 === e3.which && (e3.preventDefault(), t3.hide());
          });
        } }, { key: "hide", value: function() {
          this.$modal.removeClass("open").hide(), this.$backdrop.hide(), this.$modal.trigger("note.modal.hide"), this.$modal.off("keydown");
        } }]) && en(t2.prototype, e2), o2 && en(t2, o2), Object.defineProperty(t2, "prototype", { writable: false }), t2;
        var t2, e2, o2;
      }();
      var rn = Ko('<div class="note-editor note-frame"></div>'), an = Ko('<div class="note-toolbar" role="toolbar"></div>'), sn = Ko('<div class="note-editing-area"></div>'), ln = Ko('<textarea class="note-codable" aria-multiline="true"></textarea>'), cn = Ko('<div class="note-editable" contentEditable="true" role="textbox" aria-multiline="true"></div>'), un = Ko(['<output class="note-status-output" role="status" aria-live="polite"></output>', '<div class="note-statusbar" role="status">', '<div class="note-resizebar" aria-label="resize">', '<div class="note-icon-bar"></div>', '<div class="note-icon-bar"></div>', '<div class="note-icon-bar"></div>', "</div>", "</div>"].join("")), dn = Ko('<div class="note-editor note-airframe"></div>'), fn = Ko(['<div class="note-editable" contentEditable="true" role="textbox" aria-multiline="true"></div>', '<output class="note-status-output" role="status" aria-live="polite"></output>'].join("")), hn = Ko('<div class="note-btn-group"></div>'), pn = Ko('<button type="button" class="note-btn" tabindex="-1"></button>', function(t2, e2) {
        e2 && e2.tooltip && (t2.attr({ "aria-label": e2.tooltip }), t2.data("_lite_tooltip", new Go(t2, { title: e2.tooltip, container: e2.container })).on("click", function(t3) {
          r()(t3.currentTarget).data("_lite_tooltip").hide();
        })), e2.contents && t2.html(e2.contents), e2 && e2.data && "dropdown" === e2.data.toggle && t2.data("_lite_dropdown", new Jo(t2, { container: e2.container })), e2 && e2.codeviewKeepButton && t2.addClass("note-codeview-keep");
      }), mn = Ko('<div class="note-dropdown-menu" role="list"></div>', function(t2, e2) {
        var o2 = Array.isArray(e2.items) ? e2.items.map(function(t3) {
          var o3 = "string" == typeof t3 ? t3 : t3.value || "", n2 = e2.template ? e2.template(t3) : t3, i2 = r()('<a class="note-dropdown-item" href="#" data-value="' + o3 + '" role="listitem" aria-label="' + o3 + '"></a>');
          return i2.html(n2).data("item", t3), i2;
        }) : e2.items;
        t2.html(o2).attr({ "aria-label": e2.title }), t2.on("click", "> .note-dropdown-item", function(t3) {
          var o3 = r()(this), n2 = o3.data("item"), i2 = o3.data("value");
          n2.click ? n2.click(o3) : e2.itemClick && e2.itemClick(t3, n2, i2);
        }), e2 && e2.codeviewKeepButton && t2.addClass("note-codeview-keep");
      }), vn = Ko('<div class="note-dropdown-menu note-check" role="list"></div>', function(t2, e2) {
        var o2 = Array.isArray(e2.items) ? e2.items.map(function(t3) {
          var o3 = "string" == typeof t3 ? t3 : t3.value || "", n2 = e2.template ? e2.template(t3) : t3, i2 = r()('<a class="note-dropdown-item" href="#" data-value="' + o3 + '" role="listitem" aria-label="' + t3 + '"></a>');
          return i2.html([In(e2.checkClassName), " ", n2]).data("item", t3), i2;
        }) : e2.items;
        t2.html(o2).attr({ "aria-label": e2.title }), t2.on("click", "> .note-dropdown-item", function(t3) {
          var o3 = r()(this), n2 = o3.data("item"), i2 = o3.data("value");
          n2.click ? n2.click(o3) : e2.itemClick && e2.itemClick(t3, n2, i2);
        }), e2 && e2.codeviewKeepButton && t2.addClass("note-codeview-keep");
      }), gn = function(t2, e2) {
        return t2 + " " + In(e2.icons.caret, "span");
      }, bn = function(t2, e2) {
        return hn([pn({ className: "dropdown-toggle", contents: t2.title + " " + In("note-icon-caret"), tooltip: t2.tooltip, data: { toggle: "dropdown" } }), mn({ className: t2.className, items: t2.items, template: t2.template, itemClick: t2.itemClick })], { callback: e2 }).render();
      }, yn = function(t2, e2) {
        return hn([pn({ className: "dropdown-toggle", contents: t2.title + " " + In("note-icon-caret"), tooltip: t2.tooltip, data: { toggle: "dropdown" } }), vn({ className: t2.className, checkClassName: t2.checkClassName, items: t2.items, template: t2.template, itemClick: t2.itemClick })], { callback: e2 }).render();
      }, kn = function(t2) {
        return hn([pn({ className: "dropdown-toggle", contents: t2.title + " " + In("note-icon-caret"), tooltip: t2.tooltip, data: { toggle: "dropdown" } }), mn([hn({ className: "note-align", children: t2.items[0] }), hn({ className: "note-list", children: t2.items[1] })])]).render();
      }, wn = function(t2) {
        return hn([pn({ className: "dropdown-toggle", contents: t2.title + " " + In("note-icon-caret"), tooltip: t2.tooltip, data: { toggle: "dropdown" } }), mn({ className: "note-table", items: ['<div class="note-dimension-picker">', '<div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"></div>', '<div class="note-dimension-picker-highlighted"></div>', '<div class="note-dimension-picker-unhighlighted"></div>', "</div>", '<div class="note-dimension-display">1 x 1</div>'].join("") })], { callback: function(e2) {
          e2.find(".note-dimension-picker-mousecatcher").css({ width: t2.col + "em", height: t2.row + "em" }).on("mouseup", t2.itemClick).on("mousemove", function(e3) {
            !function(t3, e4, o2) {
              var n2, i2 = r()(t3.target.parentNode), a2 = i2.next(), s2 = i2.find(".note-dimension-picker-mousecatcher"), l2 = i2.find(".note-dimension-picker-highlighted"), c2 = i2.find(".note-dimension-picker-unhighlighted");
              if (void 0 === t3.offsetX) {
                var u2 = r()(t3.target).offset();
                n2 = { x: t3.pageX - u2.left, y: t3.pageY - u2.top };
              } else n2 = { x: t3.offsetX, y: t3.offsetY };
              var d2 = Math.ceil(n2.x / 18) || 1, f2 = Math.ceil(n2.y / 18) || 1;
              l2.css({ width: d2 + "em", height: f2 + "em" }), s2.data("value", d2 + "x" + f2), d2 > 3 && d2 < e4 && c2.css({ width: d2 + 1 + "em" }), f2 > 3 && f2 < o2 && c2.css({ height: f2 + 1 + "em" }), a2.html(d2 + " x " + f2);
            }(e3, t2.col, t2.row);
          });
        } }).render();
      }, Cn = Ko('<div class="note-color-palette"></div>', function(t2, e2) {
        for (var o2 = [], n2 = 0, i2 = e2.colors.length; n2 < i2; n2++) {
          for (var a2 = e2.eventName, s2 = e2.colors[n2], l2 = e2.colorsName[n2], c2 = [], u2 = 0, d2 = s2.length; u2 < d2; u2++) {
            var f2 = s2[u2], h2 = l2[u2];
            c2.push(['<button type="button" class="note-btn note-color-btn"', 'style="background-color:', f2, '" ', 'data-event="', a2, '" ', 'data-value="', f2, '" ', 'data-title="', h2, '" ', 'aria-label="', h2, '" ', 'data-toggle="button" tabindex="-1"></button>'].join(""));
          }
          o2.push('<div class="note-color-row">' + c2.join("") + "</div>");
        }
        t2.html(o2.join("")), t2.find(".note-color-btn").each(function() {
          r()(this).data("_lite_tooltip", new Go(r()(this), { container: e2.container }));
        });
      }), Sn = function(t2, e2) {
        return hn({ className: "note-color", children: [pn({ className: "note-current-color-button", contents: t2.title, tooltip: t2.lang.color.recent, click: t2.currentClick, callback: function(t3) {
          var o2 = t3.find(".note-recent-color");
          "foreColor" !== e2 && (o2.css("background-color", "#FFFF00"), t3.attr("data-backColor", "#FFFF00"));
        } }), pn({ className: "dropdown-toggle", contents: In("note-icon-caret"), tooltip: t2.lang.color.more, data: { toggle: "dropdown" } }), mn({ items: ["<div>", '<div class="note-btn-group btn-background-color">', '<div class="note-palette-title">' + t2.lang.color.background + "</div>", "<div>", '<button type="button" class="note-color-reset note-btn note-btn-block" data-event="backColor" data-value="transparent">', t2.lang.color.transparent, "</button>", "</div>", '<div class="note-holder" data-event="backColor"></div>', '<div class="btn-sm">', '<input type="color" id="html5bcp" class="note-btn btn-default" value="#21104A" style="width:100%;" data-value="cp">', '<button type="button" class="note-color-reset btn" data-event="backColor" data-value="cpbackColor">', t2.lang.color.cpSelect, "</button>", "</div>", "</div>", '<div class="note-btn-group btn-foreground-color">', '<div class="note-palette-title">' + t2.lang.color.foreground + "</div>", "<div>", '<button type="button" class="note-color-reset note-btn note-btn-block" data-event="removeFormat" data-value="foreColor">', t2.lang.color.resetToDefault, "</button>", "</div>", '<div class="note-holder" data-event="foreColor"></div>', '<div class="btn-sm">', '<input type="color" id="html5fcp" class="note-btn btn-default" value="#21104A" style="width:100%;" data-value="cp">', '<button type="button" class="note-color-reset btn" data-event="foreColor" data-value="cpforeColor">', t2.lang.color.cpSelect, "</button>", "</div>", "</div>", "</div>"].join(""), callback: function(o2) {
          o2.find(".note-holder").each(function() {
            var e3 = r()(this);
            e3.append(Cn({ colors: t2.colors, eventName: e3.data("event") }).render());
          }), "fore" === e2 ? (o2.find(".btn-background-color").hide(), o2.css({ "min-width": "210px" })) : "back" === e2 && (o2.find(".btn-foreground-color").hide(), o2.css({ "min-width": "210px" }));
        }, click: function(o2) {
          var n2 = r()(o2.target), i2 = n2.data("event"), a2 = n2.data("value"), s2 = document.getElementById("html5fcp").value, l2 = document.getElementById("html5bcp").value;
          if ("cp" === a2 ? o2.stopPropagation() : "cpbackColor" === a2 ? a2 = l2 : "cpforeColor" === a2 && (a2 = s2), i2 && a2) {
            var c2 = "backColor" === i2 ? "background-color" : "color", u2 = n2.closest(".note-color").find(".note-recent-color"), d2 = n2.closest(".note-color").find(".note-current-color-button");
            u2.css(c2, a2), d2.attr("data-" + i2, a2), "fore" === e2 ? t2.itemClick("foreColor", a2) : "back" === e2 ? t2.itemClick("backColor", a2) : t2.itemClick(i2, a2);
          }
        } })] }).render();
      }, xn = Ko('<div class="note-modal" aria-hidden="false" tabindex="-1" role="dialog"></div>', function(t2, e2) {
        e2.fade && t2.addClass("fade"), t2.attr({ "aria-label": e2.title }), t2.html(['<div class="note-modal-content">', e2.title ? '<div class="note-modal-header"><button type="button" class="close" aria-label="Close" aria-hidden="true"><i class="note-icon-close"></i></button><h4 class="note-modal-title">' + e2.title + "</h4></div>" : "", '<div class="note-modal-body">' + e2.body + "</div>", e2.footer ? '<div class="note-modal-footer">' + e2.footer + "</div>" : "", "</div>"].join("")), t2.data("modal", new nn(t2, e2));
      }), Tn = function(t2) {
        var e2 = '<div class="note-form-group"><label for="note-dialog-video-url-' + t2.id + '" class="note-form-label">' + t2.lang.video.url + ' <small class="text-muted">' + t2.lang.video.providers + '</small></label><input id="note-dialog-video-url-' + t2.id + '" class="note-video-url note-input" type="text"/></div>', o2 = ['<button type="button" href="#" class="note-btn note-btn-primary note-video-btn disabled" disabled>', t2.lang.video.insert, "</button>"].join("");
        return xn({ title: t2.lang.video.insert, fade: t2.fade, body: e2, footer: o2 }).render();
      }, En = function(t2) {
        var e2 = '<div class="note-form-group note-group-select-from-files"><label for="note-dialog-image-file-' + t2.id + '" class="note-form-label">' + t2.lang.image.selectFromFiles + '</label><input id="note-dialog-image-file-' + t2.id + '" class="note-note-image-input note-input" type="file" name="files" accept="image/*" multiple="multiple"/>' + t2.imageLimitation + '</div><div class="note-form-group"><label for="note-dialog-image-url-' + t2.id + '" class="note-form-label">' + t2.lang.image.url + '</label><input id="note-dialog-image-url-' + t2.id + '" class="note-image-url note-input" type="text"/></div>', o2 = ['<button href="#" type="button" class="note-btn note-btn-primary note-btn-large note-image-btn disabled" disabled>', t2.lang.image.insert, "</button>"].join("");
        return xn({ title: t2.lang.image.insert, fade: t2.fade, body: e2, footer: o2 }).render();
      }, Pn = function(t2) {
        var e2 = '<div class="note-form-group"><label for="note-dialog-link-txt-' + t2.id + '" class="note-form-label">' + t2.lang.link.textToDisplay + '</label><input id="note-dialog-link-txt-' + t2.id + '" class="note-link-text note-input" type="text"/></div><div class="note-form-group"><label for="note-dialog-link-url-' + t2.id + '" class="note-form-label">' + t2.lang.link.url + '</label><input id="note-dialog-link-url-' + t2.id + '" class="note-link-url note-input" type="text" value="http://"/></div>' + (t2.disableLinkTarget ? "" : '<div class="checkbox"><label for="note-dialog-link-nw-' + t2.id + '"><input id="note-dialog-link-nw-' + t2.id + '" type="checkbox" checked> ' + t2.lang.link.openInNewWindow + "</label></div>"), o2 = ['<button href="#" type="button" class="note-btn note-btn-primary note-link-btn disabled" disabled>', t2.lang.link.insert, "</button>"].join("");
        return xn({ className: "link-dialog", title: t2.lang.link.insert, fade: t2.fade, body: e2, footer: o2 }).render();
      }, Nn = Ko(['<div class="note-popover bottom">', '<div class="note-popover-arrow"></div>', '<div class="popover-content note-children-container"></div>', "</div>"].join(""), function(t2, e2) {
        var o2 = void 0 !== e2.direction ? e2.direction : "bottom";
        t2.addClass(o2).hide(), e2.hideArrow && t2.find(".note-popover-arrow").hide();
      }), $n = Ko('<div class="checkbox"></div>', function(t2, e2) {
        t2.html(["<label" + (e2.id ? ' for="note-' + e2.id + '"' : "") + ">", '<input role="checkbox" type="checkbox"' + (e2.id ? ' id="note-' + e2.id + '"' : ""), e2.checked ? " checked" : "", ' aria-checked="' + (e2.checked ? "true" : "false") + '"/>', e2.text ? e2.text : "", "</label>"].join(""));
      }), In = function(t2, e2) {
        return t2.match(/^</) ? t2 : "<" + (e2 = e2 || "i") + ' class="' + t2 + '"></' + e2 + ">";
      };
      return r().summernote = r().extend(r().summernote, { ui_template: function(t2) {
        return { editor: rn, toolbar: an, editingArea: sn, codable: ln, editable: cn, statusbar: un, airEditor: dn, airEditable: fn, buttonGroup: hn, button: pn, dropdown: mn, dropdownCheck: vn, dropdownButton: bn, dropdownButtonContents: gn, dropdownCheckButton: yn, paragraphDropdownButton: kn, tableDropdownButton: wn, colorDropdownButton: Sn, palette: Cn, dialog: xn, videoDialog: Tn, imageDialog: En, linkDialog: Pn, popover: Nn, checkbox: $n, icon: In, options: t2, toggleBtn: function(t3, e2) {
          t3.toggleClass("disabled", !e2), t3.attr("disabled", !e2);
        }, toggleBtnActive: function(t3, e2) {
          t3.toggleClass("active", e2);
        }, check: function(t3, e2) {
          t3.find(".checked").removeClass("checked"), t3.find('[data-value="' + e2 + '"]').addClass("checked");
        }, onDialogShown: function(t3, e2) {
          t3.one("note.modal.show", e2);
        }, onDialogHidden: function(t3, e2) {
          t3.one("note.modal.hide", e2);
        }, showDialog: function(t3) {
          t3.data("modal").show();
        }, hideDialog: function(t3) {
          t3.data("modal").hide();
        }, getPopoverContent: function(t3) {
          return t3.find(".note-popover-content");
        }, getDialogBody: function(t3) {
          return t3.find(".note-modal-body");
        }, createLayout: function(e2) {
          var o2 = (t2.airMode ? dn([sn([ln(), fn()])]) : "bottom" === t2.toolbarPosition ? rn([sn([ln(), cn()]), an(), un()]) : rn([an(), sn([ln(), cn()]), un()])).render();
          return o2.insertAfter(e2), { note: e2, editor: o2, toolbar: o2.find(".note-toolbar"), editingArea: o2.find(".note-editing-area"), editable: o2.find(".note-editable"), codable: o2.find(".note-codable"), statusbar: o2.find(".note-statusbar") };
        }, removeLayout: function(t3, e2) {
          t3.html(e2.editable.html()), e2.editor.remove(), t3.off("summernote"), t3.show();
        } };
      }, interface: "lite" }), {};
    })());
  }
});
export default require_summernote_lite_min();
/*! Bundled license information:

summernote/dist/summernote-lite.min.js:
  (*! Summernote v0.9.1 | (c) 2013~ Hackerwins and contributors | MIT license *)
*/
//# sourceMappingURL=summernote_dist_summernote-lite__min__js.js.map
